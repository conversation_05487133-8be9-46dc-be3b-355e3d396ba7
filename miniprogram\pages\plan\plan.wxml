<!-- 学习计划页面 -->
<view class="container">
  <!-- 顶部统计 -->
  <view class="stats-bar">
    <view class="stat-item">
      <text class="stat-value">{{todayCount}}</text>
      <text class="stat-label">今日目标</text>
    </view>
    <view class="stat-item">
      <text class="stat-value">{{learnedCount}}</text>
      <text class="stat-label">已学习</text>
    </view>
    <view class="stat-item">
      <text class="stat-value">{{remainingCount}}</text>
      <text class="stat-label">剩余</text>
    </view>
  </view>

  <!-- 进度条 -->
  <view class="progress-bar">
    <view class="progress" style="width: {{progress}}%"></view>
  </view>

  <!-- 计划卡片 -->
  <view class="plan-card">
    <view class="card-header">
      <text class="card-title">学习计划</text>
      <view class="card-actions">
        <button 
          class="action-button edit"
          bindtap="onEditPlan"
          wx:if="{{!isEditing}}"
        >编辑</button>
        <button 
          class="action-button save"
          bindtap="onSavePlan"
          wx:if="{{isEditing}}"
        >保存</button>
      </view>
    </view>

    <view class="plan-content">
      <!-- 每日目标 -->
      <view class="plan-item">
        <text class="item-label">每日目标</text>
        <view class="item-content">
          <text class="item-value" wx:if="{{!isEditing}}">{{plan.dailyGoal}}个单词</text>
          <input 
            class="item-input"
            type="number"
            value="{{plan.dailyGoal}}"
            bindinput="onDailyGoalInput"
            wx:else
          />
        </view>
      </view>

      <!-- 学习时间 -->
      <view class="plan-item">
        <text class="item-label">学习时间</text>
        <view class="item-content">
          <text class="item-value" wx:if="{{!isEditing}}">{{plan.studyTime}}分钟</text>
          <input 
            class="item-input"
            type="number"
            value="{{plan.studyTime}}"
            bindinput="onStudyTimeInput"
            wx:else
          />
        </view>
      </view>

      <!-- 提醒时间 -->
      <view class="plan-item">
        <text class="item-label">提醒时间</text>
        <view class="item-content">
          <text class="item-value" wx:if="{{!isEditing}}">{{plan.reminderTime}}</text>
          <picker 
            mode="time" 
            value="{{plan.reminderTime}}"
            bindchange="onReminderTimeChange"
            wx:else
          >
            <view class="picker-value">{{plan.reminderTime}}</view>
          </picker>
        </view>
      </view>
    </view>
  </view>

  <!-- 学习记录 -->
  <view class="record-card">
    <view class="card-header">
      <text class="card-title">学习记录</text>
    </view>

    <view class="record-list" wx:if="{{records.length > 0}}">
      <view 
        class="record-item"
        wx:for="{{records}}"
        wx:key="date"
      >
        <view class="record-date">
          <text class="date-text">{{item.date}}</text>
          <text class="week-text">{{item.week}}</text>
        </view>
        <view class="record-stats">
          <view class="stat-item">
            <text class="stat-value">{{item.learnedCount}}</text>
            <text class="stat-label">已学习</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{item.correctRate}}%</text>
            <text class="stat-label">正确率</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{item.studyTime}}分钟</text>
            <text class="stat-label">学习时长</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <image class="empty-icon" src="/images/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无学习记录</text>
      <text class="empty-tip">开始学习后会自动记录</text>
    </view>
  </view>

  <!-- 开始学习按钮 -->
  <view class="action-bar">
    <button 
      class="action-button start"
      bindtap="onStartLearning"
      wx:if="{{remainingCount > 0}}"
    >开始学习</button>
    <button 
      class="action-button review"
      bindtap="onStartReview"
      wx:if="{{learnedCount > 0}}"
    >复习今日单词</button>
  </view>
</view> 