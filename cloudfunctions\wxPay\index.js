const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const tenpay = require('tenpay') // 需 npm install tenpay

const config = {
  appid: 'wxa09648cc6a1a0bdd',
  mchid: '你的商户号',
  partner<PERSON>ey: '你的API密钥',
  notify_url: 'https://你的域名/pay/notify'
}

const api = new tenpay(config)

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { body, out_trade_no, total_fee } = event
  try {
    const result = await api.getPayParams({
      out_trade_no,
      body,
      total_fee,
      openid: wxContext.OPENID
    })
    return { code: 200, data: result }
  } catch (e) {
    return { code: 500, message: '支付下单失败', error: e }
  }
} 