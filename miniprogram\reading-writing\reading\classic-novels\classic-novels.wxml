<view class="container">
  <view class="novels-list">
    <view class="novel-card"
          wx:for="{{novels}}"
          wx:key="id"
          bindtap="onNovelTap"
          data-id="{{item.id}}">
      <view class="novel-cover">
        <text class="cover-placeholder">📖</text>
      </view>

      <view class="novel-info">
        <view class="novel-header">
          <text class="novel-title">{{item.title}}</text>
          <text class="novel-chinese">{{item.chineseTitle}}</text>
        </view>

        <view class="novel-meta">
          <text class="novel-author">{{item.author}}</text>
          <text class="novel-year">{{item.year}}</text>
        </view>

        <text class="novel-description">{{item.description}}</text>

        <view class="novel-details">
          <view class="detail-item">
            <text class="detail-label">难度：</text>
            <text class="difficulty-badge difficulty-{{item.difficulty}}">{{item.difficultyText}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">页数：</text>
            <text class="detail-value">{{item.pages}}页</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">类型：</text>
            <text class="detail-value">{{item.genre}}</text>
          </view>
        </view>

        <view class="novel-footer">
          <view class="novel-tags">
            <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">#{{tag}}</text>
          </view>
          <view class="novel-rating">
            <text class="rating-text">⭐ {{item.rating}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>