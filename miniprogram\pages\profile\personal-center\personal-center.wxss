page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.container {
  position: relative;
  min-height: 100vh;
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 30rpx;
}

.nav-left {
  width: 80rpx;
  display: flex;
  align-items: center;
}

.nav-icon {
  font-size: 48rpx;
  color: #333;
  font-weight: 300;
}

.nav-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.nav-actions {
  display: flex;
  gap: 20rpx;
}

.nav-action {
  font-size: 28rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  border: none;
  transition: all 0.3s ease;
  min-width: 80rpx;
  text-align: center;
}

.nav-action.cancel {
  color: #999;
  background: transparent;
}

.nav-action.save {
  color: #fff;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
  border: none;
  outline: none;
  line-height: 1;
}

.nav-action.save::after {
  border: none;
}

.nav-action.save .save-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
}

.nav-action.save:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.4);
}

/* 用户信息卡片 */
.user-profile-card {
  margin-left: 30rpx;
  margin-right: 30rpx;
  margin-bottom: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
}

/* 头像区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;
}

.avatar-container {
  position: relative;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 6rpx solid white;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* 头像编辑包装器 */
.avatar-edit-wrapper {
  position: relative;
}

/* 头像按钮样式 */
.avatar-button {
  position: relative;
  padding: 0;
  margin: 0;
  background: none !important;
  border: none;
  border-radius: 50%;
  overflow: visible;
  width: 160rpx;
  height: 160rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-button::after {
  border: none !important;
}

.avatar-button .user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 6rpx solid white;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* 头像编辑遮罩 */
.avatar-edit-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-button:hover .avatar-edit-overlay,
.avatar-button:active .avatar-edit-overlay {
  opacity: 1;
}

.avatar-edit-overlay .edit-icon {
  font-size: 32rpx;
  color: white;
  margin-bottom: 8rpx;
}

.avatar-edit-overlay .edit-text {
  font-size: 20rpx;
  color: white;
}

.avatar-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 表单区域 */
.info-form {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-bottom: 40rpx;
}

.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.form-label {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.label-icon {
  font-size: 28rpx;
  width: 32rpx;
  text-align: center;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-value-container {
  flex: 2;
  display: flex;
  justify-content: flex-end;
}

.form-input {
  flex: 1;
  text-align: right;
  font-size: 28rpx;
  color: #333;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #4A90E2;
  background: #fff;
  box-shadow: 0 0 0 4rpx rgba(74, 144, 226, 0.1);
}

.input-placeholder {
  color: #999;
}

.form-value {
  text-align: right;
  font-size: 28rpx;
  color: #666;
}

.form-value.vip {
  color: #ff6b6b;
  font-weight: bold;
}

/* 操作提示区域 */
.tips-section {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 20rpx;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 28rpx;
  width: 32rpx;
  text-align: center;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

 