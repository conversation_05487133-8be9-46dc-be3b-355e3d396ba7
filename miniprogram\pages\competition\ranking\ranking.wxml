<!--pages/competition/ranking/ranking.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="header-bg"></view>
    <view class="header-content">
      <view class="competition-info">
        <text class="competition-name">{{competitionName}}</text>
        <text class="competition-mode">{{modeText}}竞赛排行榜</text>
      </view>
      <view class="participants-info">
        <text class="participants-count">{{totalParticipants}}人参与</text>
        <text class="user-rank" wx:if="{{userRank}}">我的排名: 第{{userRank}}名</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载排行榜中...</text>
  </view>

  <!-- 排行榜内容 -->
  <view class="ranking-content" wx:else>
    <!-- 前三名特殊展示 -->
    <view class="top-three" wx:if="{{rankings.length > 0}}">
      <!-- 第二名 -->
      <view class="top-item second" wx:if="{{rankings.length >= 2}}">
        <view class="rank-crown">🥈</view>
        <view class="rank-number">2</view>
        <view class="user-info">
          <text class="user-name">{{rankings[1].userName}}</text>
          <text class="user-score">{{rankings[1].score}}</text>
          <text class="user-detail">{{rankings[1].detail}}</text>
          <text class="user-sub-detail">{{rankings[1].subDetail}}</text>
        </view>
      </view>

      <!-- 第一名 -->
      <view class="top-item first" wx:if="{{rankings.length >= 1}}">
        <view class="rank-crown">👑</view>
        <view class="rank-number">1</view>
        <view class="user-info">
          <text class="user-name">{{rankings[0].userName}}</text>
          <text class="user-score">{{rankings[0].score}}</text>
          <text class="user-detail">{{rankings[0].detail}}</text>
          <text class="user-sub-detail">{{rankings[0].subDetail}}</text>
        </view>
        <view class="winner-effects">
          <view class="star">⭐</view>
          <view class="star">⭐</view>
          <view class="star">⭐</view>
        </view>
      </view>

      <!-- 第三名 -->
      <view class="top-item third" wx:if="{{rankings.length >= 3}}">
        <view class="rank-crown">🥉</view>
        <view class="rank-number">3</view>
        <view class="user-info">
          <text class="user-name">{{rankings[2].userName}}</text>
          <text class="user-score">{{rankings[2].score}}</text>
          <text class="user-detail">{{rankings[2].detail}}</text>
          <text class="user-sub-detail">{{rankings[2].subDetail}}</text>
        </view>
      </view>
    </view>

    <!-- 其他排名 -->
    <view class="ranking-list" wx:if="{{rankings.length > 3}}">
      <view class="list-header">
        <text class="list-title">完整排行榜</text>
      </view>
      
      <view class="ranking-item {{item.rank === userRank ? 'current-user' : ''}}" 
            wx:for="{{rankings}}" 
            wx:key="rank"
            wx:if="{{item.rank > 3}}">
        <view class="rank-section">
          <text class="rank-number">{{item.rank}}</text>
        </view>
        
        <view class="user-section">
          <view class="user-main">
            <text class="user-name">{{item.userName}}</text>
            <text class="submit-time">{{item.submitTime}}</text>
          </view>
          <view class="user-stats">
            <text class="score">{{item.score}}</text>
            <text class="detail">{{item.detail}}</text>
            <text class="sub-detail">{{item.subDetail}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{rankings.length === 0}}">
      <text class="empty-icon">🏁</text>
      <text class="empty-text">暂无参与记录</text>
      <text class="empty-tip">快来参与竞赛吧！</text>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions" wx:if="{{!loading}}">
    <button class="action-btn participate-btn" bindtap="onParticipate">
      <text class="btn-text">参与竞赛</text>
    </button>
    <button class="action-btn share-btn" open-type="share">
      <text class="btn-text">分享排行榜</text>
    </button>
  </view>
</view> 