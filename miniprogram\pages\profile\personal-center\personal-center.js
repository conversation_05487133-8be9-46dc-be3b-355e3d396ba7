const util = require('../../../utils/util.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    isEditing: true, // 默认进入编辑状态
    editForm: {
      nickName: ''
    },
    canChooseAvatar: false,
    canUseNickname: false,
    pageOptions: {},
    isFromLogin: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('=== 个人中心页面加载 ===', options);
    
    // 保存页面参数
    this.setData({
      pageOptions: options || {}
    });
    
    // 获取状态栏高度并设置CSS变量
    const systemInfo = wx.getSystemInfoSync()
    const statusBarHeight = systemInfo.statusBarHeight || 44
    
    console.log('状态栏高度:', statusBarHeight)
    
    // 动态设置导航栏样式
    const navHeight = statusBarHeight + 44 // 状态栏高度 + 导航栏高度(44px)
    const contentTop = navHeight + 60 // 导航栏高度 + 更大间距(60px)
    
    // 设置页面样式
    this.setData({
      statusBarHeight: statusBarHeight,
      navHeight: navHeight,
      contentTop: contentTop
    })
    
    // 检查功能支持
    this.checkFeatureSupport();
    
    // 加载用户信息
    this.loadUserInfo();
    
    // 如果是从登录页面来的完善信息
    if (options && options.action === 'complete' && options.fromLogin) {
      this.setData({
        isFromLogin: true
      });
    }
  },

  /**
   * 检查功能支持
   */
  checkFeatureSupport() {
    const systemInfo = wx.getSystemInfoSync()
    console.log('基础库版本:', systemInfo.SDKVersion)
    
    // 检查是否支持头像选择
    const canChooseAvatar = wx.canIUse('button.open-type.chooseAvatar')
    console.log('是否支持头像选择:', canChooseAvatar)
    
    // 检查是否支持昵称输入
    const canUseNickname = wx.canIUse('input.type.nickname')
    console.log('是否支持昵称输入:', canUseNickname)
    
    this.setData({
      canChooseAvatar,
      canUseNickname
    })
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      const app = getApp()
      const userInfo = await app.getUserInfo()
      
      console.log('加载的用户信息:', userInfo)
      
      // 生成短ID
      const shortUserId = util.getShortUserId(userInfo._id)
      
      this.setData({
        userInfo,
        shortUserId,  // 添加短ID到数据中
        editForm: {
          nickName: userInfo.wechatInfo?.nickName || userInfo.username || ''
        }
      })
    } catch (error) {
      console.error('加载用户信息失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 编辑模式切换
   */
  onToggleEdit() {
    if (this.data.isEditing) {
      // 如果当前是编辑状态，则保存
      this.onSaveUserInfo()
    } else {
      // 如果当前不是编辑状态，则进入编辑（虽然默认就是编辑状态）
      this.setData({
        isEditing: true,
        editForm: {
          nickName: this.data.userInfo.wechatInfo?.nickName || this.data.userInfo.username || ''
        }
      })
    }
  },

  /**
   * 取消编辑
   */
  onCancelEdit() {
    wx.showModal({
      title: '确认退出',
      content: '您有未保存的修改，确定要退出吗？',
      confirmText: '退出',
      cancelText: '继续编辑',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack()
        }
      }
    })
  },

  /**
   * 保存用户信息
   */
  async onSaveUserInfo() {
    try {
      const { editForm } = this.data
      
      if (!editForm.nickName.trim()) {
        wx.showToast({
          title: '请输入昵称',
          icon: 'none'
        })
        return
      }

      // 检查是否有修改
      const currentNickName = this.data.userInfo.wechatInfo?.nickName || this.data.userInfo.username || ''
      if (editForm.nickName.trim() === currentNickName) {
        // 没有修改，直接返回
        wx.showToast({
          title: '信息已保存',
          icon: 'success'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
        return
      }

      wx.showLoading({
        title: '保存中...',
        mask: true
      })

      // 调用云函数更新用户信息
      const res = await wx.cloud.callFunction({
        name: 'updateUserInfo',
        data: {
          updateType: 'profile',
          updateData: {
            nickName: editForm.nickName.trim()
          }
        }
      })

      console.log('更新用户信息结果:', res)

      if (res.result && res.result.code === 200) {
        // 获取更新后的用户信息
        const updatedUserInfo = res.result.data
        
        // 更新当前页面数据
        this.setData({
          userInfo: updatedUserInfo,
          isEditing: false
        })

        // 同时更新全局用户信息
        const app = getApp()
        // 保持原有token
        const currentToken = app.globalData.token
        app.setUserInfo(updatedUserInfo, currentToken)

        wx.hideLoading()
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        throw new Error(res.result?.message || '保存失败')
      }

    } catch (error) {
      wx.hideLoading()
      console.error('保存用户信息失败:', error)
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'none'
      })
    }
  },

  /**
   * 表单输入处理 - 实现自动保存
   */
  onFormInput(e) {
    console.log('表单输入:', e)
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    console.log(`字段 ${field} 的值:`, value)
    
    this.setData({
      [`editForm.${field}`]: value
    })

    // 自动保存（防抖处理）
    if (this.saveTimer) {
      clearTimeout(this.saveTimer)
    }
    
    this.saveTimer = setTimeout(() => {
      this.autoSaveUserInfo(field, value)
    }, 1000) // 1秒后自动保存
  },

  /**
   * 自动保存用户信息
   */
  async autoSaveUserInfo(field, value) {
    try {
      if (field !== 'nickName' || !value.trim()) {
        return
      }

      // 检查是否有修改
      const currentNickName = this.data.userInfo.wechatInfo?.nickName || this.data.userInfo.username || ''
      if (value.trim() === currentNickName) {
        return // 没有修改，不需要保存
      }

      console.log('自动保存昵称:', value)

      // 调用云函数更新用户信息
      const res = await wx.cloud.callFunction({
        name: 'updateUserInfo',
        data: {
          updateType: 'profile',
          updateData: {
            nickName: value.trim()
          }
        }
      })

      console.log('自动保存结果:', res)

      if (res.result && res.result.code === 200) {
        // 获取更新后的用户信息
        const updatedUserInfo = res.result.data
        
        this.setData({
          userInfo: updatedUserInfo
        })

        // 同时更新全局用户信息
        const app = getApp()
        // 保持原有token
        const currentToken = app.globalData.token
        app.setUserInfo(updatedUserInfo, currentToken)

        // 显示保存成功提示
        wx.showToast({
          title: '已自动保存',
          icon: 'success',
          duration: 1500
        })

      } else {
        console.error('自动保存失败:', res.result?.message)
      }

    } catch (error) {
      console.error('自动保存用户信息失败:', error)
    }
  },

  /**
   * 选择头像（新方式）
   */
  async onChooseAvatar(e) {
    try {
      console.log('onChooseAvatar 被调用', e)
      const { avatarUrl } = e.detail
      console.log('选择的头像临时路径:', avatarUrl)
      
      if (!avatarUrl) {
        console.log('没有获取到头像URL，使用备用方法')
        this.onFallbackChooseAvatar()
        return
      }
      
      await this.uploadAvatar(avatarUrl)
      
    } catch (error) {
      console.error('选择头像失败:', error)
      wx.showToast({
        title: error.message || '头像更新失败',
        icon: 'none'
      })
    }
  },

  /**
   * 备用头像选择方法
   */
  async onFallbackChooseAvatar() {
    console.log('备用头像选择方法被调用')
    
    try {
      const res = await wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        sizeType: ['compressed']
      })
      
      if (res.tempFiles && res.tempFiles.length > 0) {
        const tempFilePath = res.tempFiles[0].tempFilePath
        console.log('选择的图片路径:', tempFilePath)
        
        await this.uploadAvatar(tempFilePath)
      }
    } catch (error) {
      console.error('备用头像选择失败:', error)
      if (error.errMsg && error.errMsg.includes('cancel')) {
        return // 用户取消选择，不显示错误
      }
      wx.showToast({
        title: '头像选择失败',
        icon: 'none'
      })
    }
  },

  /**
   * 上传头像
   */
  async uploadAvatar(filePath) {
    try {
      wx.showLoading({
        title: '上传中...',
        mask: true
      })
      
      // 上传到云存储
      const uploadRes = await wx.cloud.uploadFile({
        cloudPath: `avatars/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.jpg`,
        filePath: filePath
      })

      console.log('上传结果:', uploadRes)

      if (uploadRes.fileID) {
        // 调用云函数更新头像
        const updateRes = await wx.cloud.callFunction({
          name: 'updateUserInfo',
          data: {
            updateType: 'avatar',
            updateData: {
              avatarUrl: uploadRes.fileID
            }
          }
        })

        console.log('更新结果:', updateRes)

        if (updateRes.result && updateRes.result.code === 200) {
          // 获取更新后的用户信息
          const updatedUserInfo = updateRes.result.data
          
          this.setData({
            userInfo: updatedUserInfo
          })

          // 同时更新全局用户信息
          const app = getApp()
          // 保持原有token
          const currentToken = app.globalData.token
          app.setUserInfo(updatedUserInfo, currentToken)

          wx.hideLoading()
          wx.showToast({
            title: '头像更新成功',
            icon: 'success'
          })
        } else {
          throw new Error(updateRes.result?.message || '头像更新失败')
        }
      } else {
        throw new Error('头像上传失败')
      }
    } catch (error) {
      wx.hideLoading()
      throw error
    }
  },

  /**
   * 返回上一页
   */
  onGoBack() {
    // 清除保存定时器
    if (this.saveTimer) {
      clearTimeout(this.saveTimer)
    }
    
    // 如果是从登录页面来的，返回到我的页面
    if (this.data.isFromLogin) {
      wx.switchTab({
        url: '/pages/profile/profile'
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '墨词自习室 - 个人中心',
      path: '/pages/index/index',
      imageUrl: '/assets/icons/logo.png'
    }
  }
}) 