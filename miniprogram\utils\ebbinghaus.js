/**
 * 艾宾浩斯记忆曲线工具函数
 * 实现智能复习提醒和记忆强化算法
 */

// 艾宾浩斯记忆曲线间隔（天）
const EBBINGHAUS_INTERVALS = [1, 2, 4, 7, 15, 30, 60, 120];

// 掌握程度等级
const MASTERY_LEVELS = {
  UNKNOWN: 0,      // 未学习
  DIFFICULT: 1,    // 困难
  NORMAL: 2,       // 一般
  EASY: 3,         // 简单
  FAMILIAR: 4,     // 熟悉
  MASTERED: 5,     // 掌握
  PERFECT: 6       // 完全掌握
};

/**
 * 计算下次复习时间
 * @param {number} masteryLevel 掌握程度 (1-6)
 * @param {Date|number} lastReviewTime 上次复习时间
 * @param {boolean} isCorrect 本次是否答对
 * @returns {Object} 复习信息
 */
function calculateNextReviewTime(masteryLevel, lastReviewTime, isCorrect = true) {
  const now = new Date();
  const lastReview = new Date(lastReviewTime);
  
  // 根据答题结果调整掌握程度
  let newMasteryLevel = masteryLevel;
  if (isCorrect) {
    // 答对了，提升掌握程度
    newMasteryLevel = Math.min(masteryLevel + 1, MASTERY_LEVELS.PERFECT);
  } else {
    // 答错了，降低掌握程度
    newMasteryLevel = Math.max(masteryLevel - 1, MASTERY_LEVELS.DIFFICULT);
  }
  
  // 根据掌握程度确定复习间隔
  const intervalIndex = Math.min(newMasteryLevel - 1, EBBINGHAUS_INTERVALS.length - 1);
  const intervalDays = EBBINGHAUS_INTERVALS[Math.max(0, intervalIndex)];
  
  // 计算下次复习时间
  const nextReviewTime = new Date(lastReview.getTime() + intervalDays * 24 * 60 * 60 * 1000);
  
  return {
    nextReviewTime: nextReviewTime,
    intervalDays: intervalDays,
    newMasteryLevel: newMasteryLevel,
    isReviewDue: now >= nextReviewTime,
    daysUntilReview: Math.ceil((nextReviewTime - now) / (24 * 60 * 60 * 1000))
  };
}

/**
 * 获取需要复习的词汇列表
 * @param {Array} wordProgressList 词汇进度列表
 * @returns {Array} 需要复习的词汇列表
 */
function getReviewWords(wordProgressList) {
  if (!Array.isArray(wordProgressList)) {
    return [];
  }
  
  const now = new Date();
  const reviewWords = [];
  
  wordProgressList.forEach(wordData => {
    if (wordData.masteryLevel && wordData.lastReviewTime) {
      const reviewInfo = calculateNextReviewTime(
        wordData.masteryLevel, 
        wordData.lastReviewTime, 
        true // 假设上次是正确的，用于计算当前是否需要复习
      );
      
      if (reviewInfo.isReviewDue) {
        reviewWords.push({
          ...wordData,
          ...reviewInfo,
          priority: calculateReviewPriority(wordData.masteryLevel, reviewInfo.daysUntilReview)
        });
      }
    }
  });
  
  // 按优先级排序：优先级高的在前
  reviewWords.sort((a, b) => b.priority - a.priority);
  
  return reviewWords;
}

/**
 * 计算复习优先级
 * @param {number} masteryLevel 掌握程度
 * @param {number} daysOverdue 过期天数（负数表示过期）
 * @returns {number} 优先级分数
 */
function calculateReviewPriority(masteryLevel, daysOverdue) {
  // 掌握程度越低，优先级越高
  const masteryScore = (MASTERY_LEVELS.PERFECT - masteryLevel) * 10;
  
  // 过期时间越长，优先级越高
  const overdueScore = Math.max(0, -daysOverdue) * 2;
  
  return masteryScore + overdueScore;
}

/**
 * 更新词汇学习记录
 * @param {Object} wordData 词汇数据
 * @param {boolean} isCorrect 是否答对
 * @returns {Object} 更新后的词汇数据
 */
function updateWordProgress(wordData, isCorrect) {
  const now = new Date();
  const currentMasteryLevel = wordData.masteryLevel || MASTERY_LEVELS.DIFFICULT;
  const lastReviewTime = wordData.lastReviewTime || now;
  
  // 计算新的复习信息
  const reviewInfo = calculateNextReviewTime(currentMasteryLevel, lastReviewTime, isCorrect);
  
  // 更新词汇数据
  const updatedWordData = {
    ...wordData,
    masteryLevel: reviewInfo.newMasteryLevel,
    lastReviewTime: now,
    nextReviewTime: reviewInfo.nextReviewTime,
    reviewCount: (wordData.reviewCount || 0) + 1,
    correctCount: (wordData.correctCount || 0) + (isCorrect ? 1 : 0),
    wrongCount: (wordData.wrongCount || 0) + (isCorrect ? 0 : 1),
    accuracy: ((wordData.correctCount || 0) + (isCorrect ? 1 : 0)) / ((wordData.reviewCount || 0) + 1) * 100
  };
  
  return updatedWordData;
}

/**
 * 获取掌握程度文本
 * @param {number} masteryLevel 掌握程度
 * @returns {string} 掌握程度文本
 */
function getMasteryLevelText(masteryLevel) {
  const levelTexts = {
    [MASTERY_LEVELS.UNKNOWN]: '未学习',
    [MASTERY_LEVELS.DIFFICULT]: '困难',
    [MASTERY_LEVELS.NORMAL]: '一般',
    [MASTERY_LEVELS.EASY]: '简单',
    [MASTERY_LEVELS.FAMILIAR]: '熟悉',
    [MASTERY_LEVELS.MASTERED]: '掌握',
    [MASTERY_LEVELS.PERFECT]: '完全掌握'
  };
  
  return levelTexts[masteryLevel] || '未知';
}

/**
 * 生成复习计划
 * @param {Array} wordProgressList 词汇进度列表
 * @param {number} dailyReviewLimit 每日复习限制
 * @returns {Object} 复习计划
 */
function generateReviewPlan(wordProgressList, dailyReviewLimit = 50) {
  const reviewWords = getReviewWords(wordProgressList);
  const todayReviewWords = reviewWords.slice(0, dailyReviewLimit);
  
  // 按掌握程度分组
  const groupedByMastery = {};
  todayReviewWords.forEach(word => {
    const level = word.masteryLevel;
    if (!groupedByMastery[level]) {
      groupedByMastery[level] = [];
    }
    groupedByMastery[level].push(word);
  });
  
  return {
    totalReviewWords: reviewWords.length,
    todayReviewWords: todayReviewWords,
    groupedByMastery: groupedByMastery,
    reviewStats: {
      difficult: (groupedByMastery[MASTERY_LEVELS.DIFFICULT] || []).length,
      normal: (groupedByMastery[MASTERY_LEVELS.NORMAL] || []).length,
      easy: (groupedByMastery[MASTERY_LEVELS.EASY] || []).length,
      familiar: (groupedByMastery[MASTERY_LEVELS.FAMILIAR] || []).length
    }
  };
}

module.exports = {
  MASTERY_LEVELS,
  EBBINGHAUS_INTERVALS,
  calculateNextReviewTime,
  getReviewWords,
  updateWordProgress,
  getMasteryLevelText,
  generateReviewPlan,
  calculateReviewPriority
};
