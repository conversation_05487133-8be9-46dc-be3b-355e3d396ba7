Component({
  data: {
    selected: 0,
    color: "#8E8E93",
    selectedColor: "#4A90E2",
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页",
        icon: "🏠",
        activeIcon: "🏠"
      },
      {
        pagePath: "/pages/wordbank/wordbank", 
        text: "词库",
        icon: "📚",
        activeIcon: "📚"
      },
      {
        pagePath: "/pages/mistakes/mistakes",
        text: "错题本", 
        icon: "📝",
        activeIcon: "📝"
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        icon: "👤",
        activeIcon: "👤"
      }
    ]
  },

  attached() {
    // 获取当前页面路径并设置选中状态
    this.updateSelectedIndex();
  },

  pageLifetimes: {
    show() {
      // 页面显示时更新选中状态
      this.updateSelectedIndex();
    }
  },

  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      
      // 添加触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      wx.switchTab({ url });
      this.setSelected(url);
    },

    setSelected(url) {
      const list = this.data.list;
      for (let i = 0; i < list.length; i++) {
        if (list[i].pagePath.indexOf(url) !== -1) {
          this.setData({
            selected: i
          });
          break;
        }
      }
    },

    updateSelectedIndex() {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const url = currentPage.route;
        this.setSelected(url);
      }
    }
  }
}); 