const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const usersCollection = db.collection('users')

exports.main = async (event, context) => {
  const { userId } = event

  try {
    // 获取用户统计数据
    const user = await usersCollection.doc(userId).get()
    const stats = user.data.stats

    // 获取最近7天的统计数据
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const sevenDaysAgo = new Date(today)
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6)

    const recentStats = stats.dailyStats
      .filter(s => {
        const date = new Date(s.date)
        return date >= sevenDaysAgo && date <= today
      })
      .sort((a, b) => new Date(a.date) - new Date(b.date))

    // 补充缺失的日期
    const allDates = []
    for (let i = 0; i < 7; i++) {
      const date = new Date(sevenDaysAgo)
      date.setDate(date.getDate() + i)
      allDates.push(date.toISOString().split('T')[0])
    }

    const completeStats = allDates.map(date => {
      const stat = recentStats.find(s => s.date === date)
      return stat || {
        date,
        time: 0,
        words: 0,
        mastered: 0,
        correctRate: 0
      }
    })

    return {
      code: 200,
      message: '获取成功',
      data: {
        total: {
          time: stats.totalTime,
          words: stats.totalWords,
          mastered: stats.masteredWords,
          correctRate: stats.correctRate,
          streakDays: stats.streakDays
        },
        recent: completeStats,
        weekly: stats.weeklyStats,
        monthly: stats.monthlyStats
      }
    }
  } catch (error) {
    console.error(error)
    return {
      code: 500,
      message: '服务器错误'
    }
  }
} 