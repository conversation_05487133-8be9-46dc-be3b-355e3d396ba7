<!-- 拼写练习页面 -->
<view class="container" wx:if="{{!isRedirecting}}">
  
  <!-- 听写模式界面 -->
  <view class="dictation-mode" wx:if="{{practiceMode === 'dictation'}}">
    
    <!-- 顶部信息栏 -->
    <view class="header-info">
      <view class="progress-section">
        <view class="progress-text">第 {{currentIndex + 1}} 个 / 共 {{total}} 个单词</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progress}}%"></view>
        </view>
      </view>
      <view class="word-count">听写练习</view>
    </view>

    <!-- 单个单词听写界面 -->
    <view class="single-word-dictation" wx:if="{{currentWord}}">
      <!-- 播放状态显示 -->
      <view class="play-status-card">
        <view class="status-content">
          <view class="status-icon" wx:if="{{playingStage === 'waiting'}}">🎧</view>
          <view class="status-icon" wx:if="{{playingStage === 'loading'}}">⏳</view>
          <view class="status-icon" wx:if="{{playingStage === 'playing'}}">🔊</view>
          <view class="status-icon" wx:if="{{playingStage === 'completed'}}">✅</view>

          <view class="status-text" wx:if="{{playingStage === 'waiting'}}">准备听写</view>
          <view class="status-text" wx:if="{{playingStage === 'loading'}}">加载中...</view>
          <view class="status-text" wx:if="{{playingStage === 'playing'}}">正在播放...</view>
          <view class="status-text" wx:if="{{playingStage === 'completed'}}">播放完成</view>
        </view>

        <!-- 倒计时显示 -->
        <view class="countdown-display" wx:if="{{countdown > 0}}">
          <text class="countdown-text">{{countdown}}</text>
        </view>
      </view>

      <!-- 输入区域 -->
      <view class="word-input-section">
        <view class="input-card">
          <view class="input-label">请输入听到的单词</view>
          <view class="input-container">
            <input
              class="dictation-input {{userInput ? 'has-content' : ''}} {{showAnswer ? (isCorrect ? 'correct' : 'wrong') : ''}}"
              placeholder="请输入单词..."
              value="{{userInput}}"
              bindinput="onDictationInputChange"
              bindconfirm="onDictationInputConfirm"
              focus="{{inputFocused}}"
              disabled="{{showAnswer}}"
            />
            <button
              class="submit-word-btn {{userInput ? 'active' : 'disabled'}}"
              bindtap="onSubmitDictationAnswer"
              disabled="{{!userInput || showAnswer}}"
            >
              提交
            </button>
          </view>

          <!-- 答案显示 -->
          <view class="dictation-answer-display" wx:if="{{showAnswer}}">
            <view class="answer-result {{isCorrect ? 'correct' : 'wrong'}}">
              <text class="result-icon">{{isCorrect ? '✓' : '✗'}}</text>
              <text class="result-text">{{isCorrect ? '回答正确' : '回答错误'}}</text>
            </view>
            <view class="correct-word" wx:if="{{!isCorrect}}">
              <text class="word-label">正确答案：</text>
              <text class="word-text">{{currentWord.words || currentWord.word}}</text>
            </view>
            <view class="word-meaning" wx:if="{{currentWord.meaning}}">
              <text class="meaning-label">中文释义：</text>
              <text class="meaning-text">{{currentWord.meaning}}</text>
            </view>
            <view class="word-phonetic" wx:if="{{currentWord.phonetic}}">
              <text class="phonetic-text">[{{currentWord.phonetic}}]</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 控制按钮 -->
      <view class="dictation-controls">
        <button
          class="control-btn replay-btn"
          bindtap="replayCurrentWord"
          disabled="{{autoPlaying}}"
          wx:if="{{playingStage === 'completed' && !showAnswer}}"
        >
          <text class="btn-icon">🔄</text>
          <text class="btn-text">重新播放</text>
        </button>

        <button
          class="control-btn next-btn"
          bindtap="nextWord"
          wx:if="{{showAnswer}}"
        >
          <text class="btn-icon">➡️</text>
          <text class="btn-text">{{currentIndex >= total - 1 ? '完成练习' : '下一个'}}</text>
        </button>
      </view>
    </view>



    <!-- 听写输入区域（测试模式 - 已废弃，使用单个单词模式） -->
    <view class="dictation-page" wx:if="{{false}}">
      <view class="words-input-area">
        <view class="input-row" wx:for="{{inputRows}}" wx:key="*this" wx:for-index="rowIndex">
          <view 
            class="input-item" 
            wx:for="{{item}}" 
            wx:key="globalIndex"
            wx:for-item="wordData"
            wx:for-index="colIndex"
          >
            <view class="input-label">第{{wordData.globalIndex + 1}}个单词</view>
            <input 
              class="word-input {{userInputs[wordData.globalIndex] && userInputs[wordData.globalIndex].trim() ? 'has-content' : ''}}"
              placeholder="听写单词..."
              value="{{userInputs[wordData.globalIndex] || ''}}"
              data-index="{{wordData.globalIndex}}"
              bindinput="onInputChange"
              bindconfirm="onInputConfirm"
              focus="{{focusedInput === wordData.globalIndex}}"
              auto-focus="{{wordData.globalIndex === 0}}"
            />
          </view>
        </view>
      </view>

      <!-- 倒计时显示 -->
      <view class="countdown-section" wx:if="{{showCountdown}}">
        <view class="countdown-card">
          <view class="countdown-title">⏰ 确认和检查时间</view>
          <view class="countdown-timer">{{countdownSeconds}}</view>
          <view class="countdown-desc">秒后自动提交答案</view>
        </view>
      </view>

      <!-- 页面操作按钮（测试模式） -->
      <view class="page-actions">
        <!-- 音频播放未完成提示 -->
        <view class="audio-playing-tip" wx:if="{{!audioPlayCompleted && currentPageIndex >= totalPages - 1}}">
          <text class="tip-icon">🔊</text>
          <text class="tip-text">请等待音频播放完成后再提交答案</text>
        </view>

        <!-- 提交答案按钮 -->
        <button 
          class="action-btn submit-btn"
          bindtap="submitAllAnswers"
          disabled="{{!audioPlayCompleted}}"
          hover-class="btn-hover"
          wx:if="{{currentPageIndex >= totalPages - 1 && audioPlayCompleted}}"
        >
          <text class="btn-icon">📝</text>
          <text class="btn-text">提交答案</text>
        </button>

        <!-- 下一页按钮 -->
        <button 
          class="action-btn next-btn"
          bindtap="nextPage"
          disabled="{{currentPageIndex >= totalPages - 1}}"
          hover-class="btn-hover"
          wx:if="{{currentPageIndex < totalPages - 1}}"
        >
          <text class="btn-icon">→</text>
          <text class="btn-text">下一页</text>
        </button>
      </view>
    </view>

    <!-- 听写练习区域（练习模式 - 已废弃，使用单个单词模式） -->
    <view class="dictation-practice-page" wx:if="{{false}}">
      <view class="words-display-area">
        <view 
          class="word-card-practice" 
          wx:for="{{currentPageWords}}" 
          wx:key="index"
          wx:for-index="wordIndex"
        >
          <view class="word-number">第{{wordIndex + 1}}个单词</view>
          
          <!-- 点击显示区域 -->
          <view class="reveal-sections">
            <!-- 英文单词 -->
            <view class="word-reveal-section">
              <view class="reveal-btn {{revealedWords[wordIndex] ? 'revealed' : ''}}" 
                    bindtap="toggleWordReveal" 
                    data-index="{{wordIndex}}">
                <text class="reveal-title">{{revealedWords[wordIndex] ? '英文单词' : '点击查看英文单词'}}</text>
                <text class="reveal-icon">{{revealedWords[wordIndex] ? '👁️' : '👀'}}</text>
              </view>
              <view class="word-content" wx:if="{{revealedWords[wordIndex]}}">
                <text class="word-text-large">{{item.words || item.word}}</text>
              </view>
            </view>

            <!-- 音标 -->
            <view class="phonetic-reveal-section" wx:if="{{item.phonetic}}">
              <view class="reveal-btn {{revealedPhonetics[wordIndex] ? 'revealed' : ''}}" 
                    bindtap="togglePhoneticReveal" 
                    data-index="{{wordIndex}}">
                <text class="reveal-title">{{revealedPhonetics[wordIndex] ? '音标' : '点击查看音标'}}</text>
                <text class="reveal-icon">{{revealedPhonetics[wordIndex] ? '🔊' : '🔉'}}</text>
              </view>
              <text class="phonetic-text-large" wx:if="{{revealedPhonetics[wordIndex]}}">{{item.phonetic}}</text>
            </view>

            <!-- 中文含义 -->
            <view class="meaning-reveal-section">
              <view class="reveal-btn {{revealedMeanings[wordIndex] ? 'revealed' : ''}}" 
                    bindtap="toggleMeaningReveal" 
                    data-index="{{wordIndex}}">
                <text class="reveal-title">{{revealedMeanings[wordIndex] ? '中文含义' : '点击查看中文含义'}}</text>
                <text class="reveal-icon">{{revealedMeanings[wordIndex] ? '💡' : '❓'}}</text>
              </view>
              <text class="meaning-text-large" wx:if="{{revealedMeanings[wordIndex]}}">{{item.meaning}}</text>
            </view>

            <!-- 例句 -->
            <view class="example-reveal-section" wx:if="{{item.example}}">
              <view class="reveal-btn {{revealedExamples[wordIndex] ? 'revealed' : ''}}" 
                    bindtap="toggleExampleReveal" 
                    data-index="{{wordIndex}}">
                <text class="reveal-title">{{revealedExamples[wordIndex] ? '例句' : '点击查看例句'}}</text>
                <text class="reveal-icon">{{revealedExamples[wordIndex] ? '📖' : '📝'}}</text>
              </view>
              <text class="example-text-large" wx:if="{{revealedExamples[wordIndex]}}">{{item.example}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 练习模式操作按钮 -->
      <view class="practice-page-actions">
        <!-- 显示全部按钮 -->
        <button 
          class="action-btn reveal-all-btn"
          bindtap="revealAllWords"
          hover-class="btn-hover"
        >
          <text class="btn-icon">👁️</text>
          <text class="btn-text">显示全部</text>
        </button>

        <!-- 隐藏全部按钮 -->
        <button 
          class="action-btn hide-all-btn"
          bindtap="hideAllWords"
          hover-class="btn-hover"
        >
          <text class="btn-icon">👀</text>
          <text class="btn-text">隐藏全部</text>
        </button>

        <!-- 下一页按钮 -->
        <button 
          class="action-btn next-btn"
          bindtap="nextPage"
          disabled="{{currentPageIndex >= totalPages - 1}}"
          hover-class="btn-hover"
          wx:if="{{currentPageIndex < totalPages - 1}}"
        >
          <text class="btn-icon">→</text>
          <text class="btn-text">下一页</text>
        </button>

        <!-- 完成练习按钮 -->
        <button 
          class="action-btn finish-btn"
          bindtap="finishPractice"
          hover-class="btn-hover"
          wx:if="{{currentPageIndex >= totalPages - 1}}"
        >
          <text class="btn-icon">🎯</text>
          <text class="btn-text">完成练习</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 测试结果页面 -->
  <view class="test-result-modal" wx:if="{{showTestResult}}">
    <view class="result-container">
      <!-- 结果标题 -->
      <view class="result-header">
        <view class="result-title">📊 听写测试结果</view>
        <view class="result-close" bindtap="closeTestResult">✕</view>
      </view>

      <!-- 统计概览 -->
      <view class="stats-overview">
        <view class="stats-card">
          <view class="stat-item">
            <view class="stat-value">{{overallStats.totalWords}}</view>
            <view class="stat-label">总词数</view>
          </view>
          <view class="stat-item">
            <view class="stat-value correct">{{overallStats.correctCount}}</view>
            <view class="stat-label">正确</view>
          </view>
          <view class="stat-item">
            <view class="stat-value wrong">{{overallStats.wrongCount}}</view>
            <view class="stat-label">错误</view>
          </view>
          <view class="stat-item">
            <view class="stat-value accuracy">{{overallStats.accuracyRate}}%</view>
            <view class="stat-label">准确率</view>
          </view>
        </view>
        <view class="test-time">
          <text class="time-label">用时：</text>
          <text class="time-value">{{overallStats.formattedTime}}</text>
        </view>
      </view>



      <!-- 错词结果 -->
      <view class="answer-results-section">
        <view class="section-header">
          <view class="section-title">📝 错词结果</view>
          <view class="select-actions" wx:if="{{overallStats.wrongCount > 0}}">
            <text class="select-btn primary" bindtap="selectAllWrongWords">全选</text>
          </view>
        </view>

        <!-- 有错词时显示列表 -->
        <view class="results-list" wx:if="{{overallStats.wrongCount > 0}}">
          <view
            class="result-item wrong {{selectedWrongWords[item.wrongIndex] ? 'selected' : ''}} {{item.isPhrase ? 'phrase-mode' : ''}}"
            wx:for="{{testResults}}"
            wx:key="index"
            wx:if="{{!item.isCorrect}}"
            bindtap="toggleWrongWordSelection"
            data-index="{{item.wrongIndex}}"
          >
            <view class="item-number">{{item.wrongIndex + 1}}</view>
            <view class="word-content-dictation">
              <text class="word-text-dictation">{{item.correctAnswer || item.word.words || item.word.word || '未知'}}</text>
              <text class="answer-label-dictation">您答：</text>
              <text class="user-answer-dictation">{{item.userAnswer || item.userInput || '无'}}</text>
              <text class="separator-dictation">｜</text>
              <text class="answer-label-dictation">正答：</text>
              <text class="correct-answer-dictation">{{item.correctAnswer || item.word.words || item.word.word || ''}}</text>
            </view>
            <view class="result-status">
              <view class="select-checkbox">
                <text class="checkbox">{{selectedWrongWords[item.wrongIndex] ? '☑️' : '☐'}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 无错词时显示提示 -->
        <view class="no-wrong-words" wx:else>
          <view class="no-wrong-icon">🎉</view>
          <view class="no-wrong-text">暂无错词，太棒了！</view>
          <view class="no-wrong-desc">全部答对，继续保持！</view>
        </view>
      </view>

      <!-- 全部正确的情况 -->
      <view class="perfect-score" wx:if="{{overallStats.wrongCount === 0}}">
        <view class="perfect-icon">🎉</view>
        <view class="perfect-title">完美表现！</view>
        <view class="perfect-desc">恭喜您全部答对，听写水平很棒！</view>
      </view>

      <!-- 错词重测选项 -->
      <view class="retry-options-section" wx:if="{{overallStats.wrongCount > 0}}">
        <view class="retry-title">
          <text class="retry-title-text">💡 选中错词后进行进一步测试</text>
        </view>

        <!-- 测试模式选项 - 一行显示 -->
        <view class="retry-modes">
          <button class="mode-btn retry-dictation" bindtap="retrySelectedWords">
            <view class="mode-icon">🎧</view>
            <view class="mode-text">听写</view>
          </button>
          <button class="mode-btn retry-en2cn" bindtap="retryWithEnToCn">
            <view class="mode-icon">🇬🇧</view>
            <view class="mode-text">英译汉</view>
          </button>
          <button class="mode-btn retry-cn2en" bindtap="retryWithCnToEn">
            <view class="mode-icon">🇨🇳</view>
            <view class="mode-text">汉译英</view>
          </button>
          <button class="mode-btn retry-game" bindtap="retryWithGame">
            <view class="mode-icon">🎮</view>
            <view class="mode-text">消消乐</view>
          </button>
        </view>

        <!-- 分享选项 - 单独一行 -->
        <view class="share-option">
          <button class="share-btn" bindtap="shareSelectedWords">
            <view class="share-icon">📤</view>
            <view class="share-text">分享给他人测试</view>
          </button>
        </view>
      </view>

      <!-- 🔧 分组测试提示（仅在有下一组时显示） -->
      <view class="group-test-section" wx:if="{{groupInfo && groupInfo.hasNextGroup}}">
        <view class="group-info">
          <view class="group-title">📚 分组测试进度</view>
          <view class="group-progress">
            <text class="current-group">第{{groupInfo.currentGroup}}组完成</text>
            <text class="total-groups">还有{{groupInfo.remainingGroups}}组</text>
          </view>
        </view>
        <button class="next-group-btn" bindtap="goToNextGroup">
          <text class="btn-icon">▶️</text>
          <text class="btn-text">进入下一组</text>
        </button>
      </view>

      <!-- 底部操作 -->
      <view class="result-footer">
        <!-- 竞赛模式下的四个按钮 -->
        <view class="competition-buttons" wx:if="{{competitionMode}}">
          <!-- 挑战下一关 -->
          <button class="footer-btn primary" bindtap="goToNextCompetitionLevel" wx:if="{{hasNextLevel && competitionCompleted}}">
            挑战下一关
          </button>
          <button class="footer-btn disabled" wx:elif="{{competitionCompleted}}">
            已完成全部关卡
          </button>
          <button class="footer-btn disabled" wx:else>
            完成当前关卡后解锁
          </button>

          <!-- 返回关卡列表 -->
          <button class="footer-btn secondary" bindtap="backToLevelSelect" wx:if="{{masterCompetitionId}}">
            返回关卡列表
          </button>
          <button class="footer-btn secondary" bindtap="closeTestResult" wx:else>
            返回主页
          </button>

          <!-- 查看排行榜 -->
          <button class="footer-btn secondary" bindtap="viewCompetitionRanking">
            查看排行榜
          </button>

          <!-- 分享成绩 -->
          <button class="footer-btn secondary" bindtap="shareTestResult">
            分享成绩
          </button>
        </view>

        <!-- 非竞赛模式下的按钮 -->
        <view class="normal-buttons" wx:else>
          <button class="footer-btn share-result" bindtap="shareTestResult">分享成绩</button>
          <button class="footer-btn secondary" bindtap="closeTestResult">返回</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 拼写模式界面 -->
  <view class="spelling-mode" wx:if="{{practiceMode === 'spelling'}}">
    <!-- 进度指示器 -->
    <view class="progress-container">
      <view class="progress-info">
        <text class="progress-text">{{currentWordIndex + 1}} / {{totalWords}}</text>
      </view>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{progress}}%"></view>
      </view>
    </view>

    <!-- 单词卡片 -->
    <view class="word-card">
      <!-- 播放按钮 -->
      <view class="audio-section">
        <button class="play-button {{isPlaying ? 'playing' : ''}}" bindtap="playCurrentWord">
          <text class="play-icon">{{isPlaying ? '🔊' : '🔊'}}</text>
          <text class="play-text">{{isPlaying ? '播放中...' : '点击发音'}}</text>
        </button>
      </view>

      <!-- 单词显示 -->
      <view class="word-display">
        <view class="word-content">
          <text class="word-text">{{currentWord.words}}</text>
          <text class="phonetic" wx:if="{{currentWord.phonetic && showMeaning}}">{{currentWord.phonetic}}</text>
        </view>
        
        <!-- 中文释义 -->
        <view class="meaning-section" wx:if="{{currentWord.meaning && showMeaning}}">
          <text class="meaning-text">{{currentWord.meaning}}</text>
        </view>
      </view>

      <!-- 拼写输入 -->
      <view class="spelling-input">
        <input 
          class="spell-input"
          placeholder="请输入单词拼写..."
          value="{{userSpelling}}"
          bindinput="onSpellingInput"
          bindconfirm="checkSpelling"
          focus="{{!showMeaning}}"
        />
        <button class="check-spelling-btn" bindtap="checkSpelling" disabled="{{!userSpelling.trim()}}">
          检查
        </button>
      </view>
    </view>

    <!-- 控制按钮 -->
    <view class="controls">
      <button class="control-btn hint-btn" bindtap="toggleMeaning">
        {{showMeaning ? '隐藏释义' : '显示释义'}}
      </button>
      
      <button class="control-btn skip-btn" bindtap="nextWord">
        跳过
      </button>
    </view>

    <!-- 统计信息 -->
    <view class="stats">
      <view class="stat-item">
        <text class="stat-label">正确</text>
        <text class="stat-value correct">{{correctCount}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">错误</text>
        <text class="stat-value wrong">{{wrongCount}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">跳过</text>
        <text class="stat-value skipped">{{skippedCount}}</text>
      </view>
    </view>
  </view>

  <!-- 简单完成提示（拼写模式） -->
  <view class="completion-modal" wx:if="{{showCompletion}}">
    <view class="modal-content">
      <view class="modal-title">练习完成</view>
      <view class="statistics">
        <view class="stat-item">
          <text class="stat-label">总用时</text>
          <text class="stat-value">{{formatTime(totalTime / 1000)}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">正确率</text>
          <text class="stat-value">{{correctRate}}%</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">错题数</text>
          <text class="stat-value">{{mistakes.length}}</text>
        </view>
      </view>
      <view class="modal-buttons">
        <button 
          class="modal-btn" 
          bindtap="onAddToMistakes"
          wx:if="{{mistakes.length > 0}}"
        >加入错题本</button>
        <button 
          class="modal-btn" 
          bindtap="onRetryMistakes"
          wx:if="{{mistakes.length > 0}}"
        >错题重练</button>
        <button 
          class="modal-btn primary" 
          bindtap="onBackToHome"
        >返回首页</button>
      </view>
    </view>
  </view>

  <!-- 详细结果页面（听写模式） -->
  <view class="result-page" wx:if="{{showResultPage}}">
    <view class="result-header">
      <view class="result-title">听写结果</view>
      <view class="result-summary">
        <view class="summary-item">
          <text class="number">{{correctCount}}</text>
          <text class="label">正确</text>
        </view>
        <view class="summary-item error">
          <text class="number">{{total - correctCount}}</text>
          <text class="label">错误</text>
        </view>
        <view class="summary-item">
          <text class="number">{{correctRate}}%</text>
          <text class="label">正确率</text>
        </view>
      </view>
    </view>

    <view class="result-actions">
      <button class="action-btn secondary" bindtap="onSelectAllMistakes">全选错题</button>
      <button class="action-btn primary" bindtap="onRetrySelected">重新听写</button>
    </view>

    <view class="result-list">
      <view 
        class="result-item {{item.isCorrect ? 'correct' : 'wrong'}} {{item.isSelected ? 'selected' : ''}}"
        wx:for="{{resultDetails}}"
        wx:key="word"
        bindtap="onSelectMistake"
        data-index="{{index}}"
      >
        <view class="item-header">
          <view class="word-section">
            <text class="word">{{item.word}}</text>
            <text class="meaning">{{item.meaning}}</text>
          </view>
          <view class="status-section">
            <text class="status-icon">{{item.isCorrect ? '✓' : '✗'}}</text>
          </view>
        </view>
        <view class="item-content" wx:if="{{!item.isCorrect}}">
          <view class="answer-comparison">
            <view class="user-answer">
              <text class="label">您的答案：</text>
              <text class="answer">{{item.userAnswer || '(未输入)'}}</text>
            </view>
            <view class="correct-answer">
              <text class="label">正确答案：</text>
              <text class="answer">{{item.word}}</text>
            </view>
          </view>
        </view>
        <view class="select-indicator" wx:if="{{!item.isCorrect}}">
          <text class="checkbox">{{item.isSelected ? '☑️' : '☐'}}</text>
        </view>
      </view>
    </view>

    <view class="result-footer">
      <button class="footer-btn" bindtap="onBackToHome">返回首页</button>
    </view>
  </view>

  <!-- 隐藏的画布，用于生成分享图片 -->
  <canvas 
    canvas-id="dictationResultCanvas" 
    style="width: 375px; height: 600px; position: fixed; top: -1000px; left: -1000px;"
  ></canvas>

  <!-- 分享弹窗 -->
  <view class="share-modal" wx:if="{{showShareModal}}" bindtap="closeShareModal">
    <view class="share-content" catchtap="">
      <view class="share-header">
        <text class="share-title">分享测试成绩</text>
        <text class="close-btn" bindtap="closeShareModal">✕</text>
      </view>
      
      <view class="share-info">
        <text class="share-desc">选择分享方式，与朋友分享你的测试成绩</text>
        <text class="share-note">让朋友看到你的学习成果</text>
      </view>

      <view class="share-buttons">
        <button 
          class="wechat-share-btn" 
          open-type="share"
          bindtap="onShareButtonTap"
        >
          <text class="share-btn-icon">📤</text>
          <text class="share-btn-text">分享给朋友</text>
        </button>
        
        <button 
          class="copy-btn" 
          bindtap="saveDictationResultToAlbum"
        >
          <text class="copy-btn-icon">💾</text>
          <text class="copy-btn-text">保存到相册</text>
        </button>
      </view>
    </view>
  </view>

</view>