const app = getApp();
const optionGenerator = require('../../../utils/option-generator');

Page({
  data: {
    // 任务数据
    taskData: null,
    mode: 'practice', // practice-练习模式，test-测试模式
    testType: 'en2zh', // en2zh-英译汉，zh2en-汉译英，game-单词消消乐
    timeLimit: 'unlimited', // unlimited-不限时，10-10秒，15-15秒，20-20秒

    // 练习数据
    words: [], // 单词列表
    currentIndex: 0, // 当前单词索引
    currentWord: null, // 当前单词数据
    total: 0, // 总单词数
    progress: 0, // 进度百分比

    // 测试模式数据
    options: [], // 选项列表
    selectedOption: -1, // 选中的选项索引
    correctCount: 0, // 正确数量
    mistakes: [], // 错题列表

    // 单词消消乐数据
    gameBoard: [], // 游戏板
    selectedCells: [], // 选中的格子
    matchedPairs: 0, // 已匹配的对数

    // 计时器数据
    remainingTime: 0, // 剩余时间
    timer: null, // 计时器
    startTime: 0, // 开始时间
    totalTime: 0, // 总用时

    // 完成数据
    showCompletion: false, // 是否显示完成提示
    correctRate: 0 // 正确率
  },

  onLoad(options) {
    // 解析任务数据
    const taskData = JSON.parse(options.data);
    this.setData({ 
      taskData,
      mode: taskData.mode,
      testType: taskData.testType,
      timeLimit: taskData.timeLimit
    });

    // 加载单词数据
    this.loadWords();
  },

  onUnload() {
    // 清除计时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },

  // 加载单词数据
  async loadWords() {
    const db = wx.cloud.database();
    let query = db.collection('libraries');

    // 根据词库ID和分册ID查询
    if (this.data.taskData.libraryId) {
      query = query.where({
        library_id: this.data.taskData.libraryId
      });
    }

    try {
      const res = await query.get();
      const words = res.data;
      
      // 随机打乱单词顺序
      this.shuffleArray(words);
      
      this.setData({
        words,
        total: words.length,
        currentWord: words[0]
      });

      // 如果是测试模式，生成选项
      if (this.data.mode === 'test') {
        this.generateOptions();
      }

      // 如果是单词消消乐，初始化游戏板
      if (this.data.testType === 'game') {
        this.initGameBoard();
      }

      // 如果是限时模式，启动计时器
      if (this.data.timeLimit !== 'unlimited') {
        this.startTimer();
      }

      // 记录开始时间
      this.setData({
        startTime: Date.now()
      });
    } catch (error) {
      console.error('加载单词失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 随机打乱数组
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  },

  // 生成选项
  generateOptions() {
    const currentWord = this.data.currentWord;
    const allWords = this.data.words;

    let result = {};

    if (this.data.testType === 'en2zh') {
      // 英译汉：生成4个中文释义选项
      result = optionGenerator.generateWordOptions(allWords, 'en_to_cn', currentWord, 4);
    } else if (this.data.testType === 'zh2en') {
      // 汉译英：生成4个英文单词选项
      result = optionGenerator.generateWordOptions(allWords, 'cn_to_en', currentWord, 4);
    }

    // 随机打乱选项顺序
    if (result.options && result.options.length > 0) {
      this.shuffleArray(result.options);
    }

    this.setData({
      options: result.options || []
    });
  },

  // 初始化游戏板
  initGameBoard() {
    const words = this.data.words.slice(0, 8); // 取前8个单词
    const pairs = [];
    
    // 为每个单词创建一对（英文和中文）
    words.forEach(word => {
      pairs.push(
        { text: word.word, type: 'en', matched: false },
        { text: word.definition, type: 'zh', matched: false }
      );
    });

    // 随机打乱顺序
    this.shuffleArray(pairs);

    // 将pairs分成4行，每行4个
    const gameBoard = [];
    for (let i = 0; i < 4; i++) {
      gameBoard.push(pairs.slice(i * 4, (i + 1) * 4));
    }

    this.setData({
      gameBoard,
      selectedCells: [],
      matchedPairs: 0
    });
  },

  // 启动计时器
  startTimer() {
    const timeLimit = parseInt(this.data.timeLimit);
    this.setData({
      remainingTime: timeLimit,
      timer: setInterval(() => {
        if (this.data.remainingTime > 0) {
          this.setData({
            remainingTime: this.data.remainingTime - 1
          });
        } else {
          // 时间到，自动进入下一题
          this.onNextWord();
        }
      }, 1000)
    });
  },

  // 播放音频
  playAudio() {
    if (!this.data.currentWord.audioUrl) return;

    const innerAudioContext = wx.createInnerAudioContext();
    innerAudioContext.src = this.data.currentWord.audioUrl;
    innerAudioContext.play();
  },

  // 选择选项
  onOptionSelect(e) {
    const index = e.currentTarget.dataset.index;
    const selectedOption = this.data.options[index];
    const currentWord = this.data.currentWord;
    let isCorrect = false;

    if (this.data.testType === 'en2zh') {
      isCorrect = selectedOption === currentWord.definition;
    } else if (this.data.testType === 'zh2en') {
      isCorrect = selectedOption === currentWord.word;
    }

    this.setData({ selectedOption: index });

    // 延迟显示结果
    setTimeout(() => {
      if (isCorrect) {
        this.setData({
          correctCount: this.data.correctCount + 1
        });
      } else {
        // 添加到错题列表
        const mistakes = this.data.mistakes;
        mistakes.push(currentWord);
        this.setData({ mistakes });
      }

      // 进入下一题
      this.onNextWord();
    }, 500);
  },

  // 点击游戏格子
  onGameCellTap(e) {
    const { row, col } = e.currentTarget.dataset;
    const cell = this.data.gameBoard[row][col];

    // 如果格子已匹配或已选中，则忽略
    if (cell.matched || this.data.selectedCells.some(c => c.row === row && c.col === col)) {
      return;
    }

    const selectedCells = [...this.data.selectedCells, { row, col, cell }];
    this.setData({ selectedCells });

    // 如果选中了两个格子
    if (selectedCells.length === 2) {
      const [cell1, cell2] = selectedCells;
      const isMatch = this.checkGameMatch(cell1.cell, cell2.cell);

      if (isMatch) {
        // 匹配成功
        const gameBoard = this.data.gameBoard;
        gameBoard[cell1.row][cell1.col].matched = true;
        gameBoard[cell2.row][cell2.col].matched = true;

        this.setData({
          gameBoard,
          selectedCells: [],
          matchedPairs: this.data.matchedPairs + 1
        });

        // 检查是否完成所有匹配
        if (this.data.matchedPairs === 8) {
          this.onComplete();
        }
      } else {
        // 匹配失败，延迟后取消选中
        setTimeout(() => {
          this.setData({ selectedCells: [] });
        }, 1000);
      }
    }
  },

  // 检查游戏匹配
  checkGameMatch(cell1, cell2) {
    // 确保一个是英文，一个是中文
    if (cell1.type === cell2.type) return false;

    // 查找对应的单词
    const word = this.data.words.find(w => 
      (w.word === cell1.text && w.definition === cell2.text) ||
      (w.word === cell2.text && w.definition === cell1.text)
    );

    return !!word;
  },

  // 下一个单词
  onNextWord() {
    const nextIndex = this.data.currentIndex + 1;
    
    if (nextIndex < this.data.total) {
      this.setData({
        currentIndex: nextIndex,
        currentWord: this.data.words[nextIndex],
        selectedOption: -1,
        progress: (nextIndex / this.data.total) * 100
      });

      // 如果是测试模式，生成新的选项
      if (this.data.mode === 'test') {
        this.generateOptions();
      }

      // 如果是限时模式，重置计时器
      if (this.data.timeLimit !== 'unlimited') {
        clearInterval(this.data.timer);
        this.startTimer();
      }
    } else {
      this.onComplete();
    }
  },

  // 完成练习
  onComplete() {
    // 清除计时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }

    // 计算总用时
    const totalTime = Math.floor((Date.now() - this.data.startTime) / 1000);

    // 计算正确率
    const correctRate = this.data.mode === 'test' 
      ? Math.floor((this.data.correctCount / this.data.total) * 100)
      : 0;

    this.setData({
      showCompletion: true,
      totalTime,
      correctRate
    });
  },

  // 加入错题本
  async onAddToMistakes() {
    const db = wx.cloud.database();
    const mistakes = this.data.mistakes;

    try {
      // 批量添加错题
      for (const mistake of mistakes) {
        await db.collection('mistake_word').add({
          data: {
            ...mistake,
            createTime: db.serverDate(),
            openid: app.globalData.openid
          }
        });
      }

      wx.showToast({
        title: '已加入错题本',
        icon: 'success'
      });
    } catch (error) {
      console.error('添加错题失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    }
  },

  // 错题重练
  onRetryMistakes() {
    if (this.data.mistakes.length === 0) return;

    // 构建任务数据
    const taskData = {
      ...this.data.taskData,
      words: this.data.mistakes
    };

    // 跳转到练习页面
    wx.redirectTo({
      url: `/pages/task/practice/practice?data=${JSON.stringify(taskData)}`
    });
  },

  // 返回首页
  onBackToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
}); 