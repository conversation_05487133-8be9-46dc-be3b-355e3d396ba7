/* 学习进度页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 有进度容器 */
.progress-container {
  padding-bottom: 120rpx;
}

/* 头部卡片 */
.header-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.library-info {
  flex: 1;
}

.library-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.mode-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  display: inline-block;
}

/* 进度圆环 */
.progress-circle {
  width: 120rpx;
  height: 120rpx;
  position: relative;
}

.circle-bg {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #667eea 0deg, #e0e0e0 0deg);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-fill {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #667eea 0deg, transparent 0deg);
  transition: transform 0.6s ease;
}

.circle-inner {
  width: 80rpx;
  height: 80rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.progress-text {
  font-size: 20rpx;
  font-weight: bold;
  color: #667eea;
}

/* 进度详情 */
.progress-details {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.detail-item {
  text-align: center;
}

.detail-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.detail-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 统计卡片 */
.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.stats-title {
  margin-bottom: 24rpx;
  text-align: center;
}

.title-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #333;
}

.stat-value.correct {
  color: #4CAF50;
}

.stat-value.wrong {
  color: #f44336;
}

.stat-label {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.action-btn::after {
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border: 2rpx solid #667eea;
}

.action-btn.tertiary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.action-btn:active {
  transform: scale(0.95);
}

/* 无进度容器 */
.no-progress-container {
  height: calc(100vh - 160rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.no-progress-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  max-width: 500rpx;
  width: 100%;
}

.no-progress-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.no-progress-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.no-progress-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40rpx;
}

.start-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.start-btn::after {
  border: none;
}

.start-btn:active {
  transform: scale(0.95);
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.nav-btn {
  width: 100%;
  height: 80rpx;
  background: transparent;
  color: #667eea;
  border: 2rpx solid #667eea;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.nav-btn::after {
  border: none;
}

.nav-btn:active {
  transform: scale(0.95);
} 