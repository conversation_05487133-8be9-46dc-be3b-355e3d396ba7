<!--学习进度页面-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-content">
      <text class="loading-text">加载进度中...</text>
    </view>
  </view>

  <!-- 有学习进度 -->
  <view class="progress-container" wx:if="{{!loading && hasProgress}}">
    <!-- 头部信息 -->
    <view class="header-card">
      <view class="library-info">
        <text class="library-name">{{libraryName}}</text>
        <text class="mode-text">{{modeText}}</text>
      </view>
      <view class="progress-circle">
        <view class="circle-bg">
          <view class="circle-fill" style="transform: rotate({{progressPercent * 3.6}}deg)"></view>
          <view class="circle-inner">
            <text class="progress-text">{{progressPercent}}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 进度详情 -->
    <view class="progress-details">
      <view class="detail-item">
        <text class="detail-label">已学习</text>
        <text class="detail-value">{{learnedWords}}/{{totalWords}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">上次学习</text>
        <text class="detail-value">{{lastStudyTime}}</text>
      </view>
    </view>

    <!-- 学习统计 -->
    <view class="stats-card">
      <view class="stats-title">
        <text class="title-text">学习统计</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value correct">{{correctCount}}</text>
          <text class="stat-label">答对题数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value wrong">{{wrongCount}}</text>
          <text class="stat-label">答错题数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{accuracy}}%</text>
          <text class="stat-label">正确率</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{formatStudyTime(totalStudyTime)}}</text>
          <text class="stat-label">学习时长</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn primary" bindtap="continueProgress">
        继续上次进度
      </button>
      <button class="action-btn secondary" bindtap="restartProgress">
        重新开始
      </button>
      <button class="action-btn tertiary" bindtap="viewDetailedStats">
        查看详细统计
      </button>
    </view>
  </view>

  <!-- 无学习进度 -->
  <view class="no-progress-container" wx:if="{{!loading && !hasProgress}}">
    <view class="no-progress-card">
      <view class="no-progress-icon">📚</view>
      <text class="no-progress-title">开始新的学习之旅</text>
      <text class="no-progress-desc">
        {{libraryName}} - {{modeText}}
        <br/>
        还没有学习记录，点击下方按钮开始学习吧！
      </text>
      <button class="start-btn" bindtap="goToWordSelection">
        开始学习
      </button>
    </view>
  </view>

  <!-- 底部导航 -->
  <view class="bottom-nav">
    <button class="nav-btn" bindtap="goBack">
      返回
    </button>
  </view>
</view> 