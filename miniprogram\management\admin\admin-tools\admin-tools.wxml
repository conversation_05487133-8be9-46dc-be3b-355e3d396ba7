<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <view class="header-content">
      <view class="header-icon">🔧</view>
      <view class="header-text">
        <text class="header-title">管理员工具</text>
        <text class="header-desc">系统管理和配置中心</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-title">内容管理</view>
      
      <view class="menu-item" bindtap="goToNoticeManage">
        <view class="menu-icon-wrapper blue">
          <text class="menu-icon">📢</text>
        </view>
        <view class="menu-content">
          <text class="menu-title-text">编辑通知</text>
          <text class="menu-desc">管理首页通知栏内容</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>
      
      <view class="menu-item" bindtap="goToFeedbackManage">
        <view class="menu-icon-wrapper orange">
          <text class="menu-icon">💬</text>
          <view class="badge" wx:if="{{unreadFeedbackCount > 0}}">
            <text class="badge-text">{{unreadFeedbackCount > 99 ? '99+' : unreadFeedbackCount}}</text>
          </view>
        </view>
        <view class="menu-content">
          <text class="menu-title-text">管理用户反馈</text>
          <text class="menu-desc">查看和回复用户反馈
            <text wx:if="{{unreadFeedbackCount > 0}}" class="unread-hint">（{{unreadFeedbackCount}}条未读）</text>
          </text>
        </view>
        <text class="menu-arrow">›</text>
      </view>
    </view>



    <!-- 快捷操作 -->
    <view class="menu-group">
      <view class="menu-title">快捷操作</view>
      
      <view class="quick-actions">
        <view class="quick-action-item" bindtap="goToNoticeManage">
          <view class="quick-icon">📝</view>
          <text class="quick-text">发布通知</text>
        </view>
        
        <view class="quick-action-item" bindtap="goToFeedbackManage">
          <view class="quick-icon">
            📨
            <view class="quick-badge" wx:if="{{unreadFeedbackCount > 0}}">
              <text class="quick-badge-text">{{unreadFeedbackCount}}</text>
            </view>
          </view>
          <text class="quick-text">未读反馈</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 状态信息 -->
  <view class="status-section">
    <view class="status-item">
      <view class="status-label">系统状态</view>
      <view class="status-value running">正常运行</view>
    </view>
    <view class="status-item">
      <view class="status-label">待处理反馈</view>
      <view class="status-value {{unreadFeedbackCount > 0 ? 'warning' : 'success'}}">
        {{unreadFeedbackCount}}条
      </view>
    </view>
  </view>
</view> 