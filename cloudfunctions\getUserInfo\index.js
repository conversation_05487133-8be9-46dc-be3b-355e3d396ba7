const cloud = require('wx-server-sdk')
const crypto = require('crypto')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()
const usersCollection = db.collection('users')

function generateToken() {
  return crypto.randomBytes(32).toString('hex')
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const currentOpenid = wxContext.OPENID
  const { openid } = event
  
  // 验证请求权限：只能查询自己的信息
  const targetOpenid = openid || currentOpenid
  if (targetOpenid !== currentOpenid) {
    return {
      success: false,
      message: '无权限访问其他用户信息'
    }
  }
  
  console.log('获取用户信息 - openid:', targetOpenid)

  try {
    // 查找用户信息
    const userRes = await usersCollection.where({ openid: targetOpenid }).get()
    
    if (userRes.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    let userInfo = userRes.data[0]
    
    // 生成新的token
    const newToken = generateToken()
    
    // 更新最后登录时间和token
    await usersCollection.doc(userInfo._id).update({
      data: {
        token: newToken,
        lastLoginTime: new Date()
      }
    })
    
    // 更新返回的用户信息
    userInfo.token = newToken
    
    console.log('用户信息获取成功:', {
      id: userInfo._id,
      username: userInfo.username,
      nickName: userInfo.wechatInfo?.nickName
    })
    
    return {
      success: true,
      message: '获取用户信息成功',
      data: {
        userInfo: userInfo,
        token: newToken,
        openid: targetOpenid
      }
    }
    
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return {
      success: false,
      message: '服务器错误',
      error: error.message
    }
  }
} 