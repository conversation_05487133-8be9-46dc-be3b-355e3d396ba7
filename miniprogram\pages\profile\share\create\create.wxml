<!--pages/profile/share/create/create.wxml-->
<view class="create-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="nav-left" bindtap="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="nav-center">
        <text class="nav-title">创建分享测试</text>
      </view>
      <view class="nav-right"></view>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="header-icon">🎯</view>
      <view class="header-title">选择测试类型</view>
      <view class="header-subtitle">为你的学生或朋友创建专属测试</view>
    </view>

    <!-- 测试类型列表 -->
    <view class="test-types">
      <view 
        class="test-type-card {{item.color}}" 
        wx:for="{{testTypes}}" 
        wx:key="id"
        bindtap="selectTestType"
        data-id="{{item.id}}"
      >
        <view class="card-background">
          <view class="bg-circle circle1"></view>
          <view class="bg-circle circle2"></view>
          <view class="bg-circle circle3"></view>
        </view>
        
        <view class="card-content">
          <view class="type-icon">{{item.icon}}</view>
          <view class="type-info">
            <view class="type-title">{{item.title}}</view>
            <view class="type-subtitle">{{item.subtitle}}</view>
          </view>
          <view class="arrow-icon">→</view>
        </view>
        
        <!-- 模式预览 -->
        <view class="modes-preview">
          <view 
            class="mode-tag" 
            wx:for="{{item.modes}}" 
            wx:for-item="mode"
            wx:key="id"
            wx:if="{{index < 3}}"
          >
            <text class="mode-icon">{{mode.icon}}</text>
            <text class="mode-name">{{mode.name}}</text>
          </view>
          <view class="mode-tag more" wx:if="{{item.modes.length > 3}}">
            <text class="more-text">+{{item.modes.length - 3}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能说明 -->
    <view class="feature-description">
      <view class="desc-title">💡 功能特色</view>
      <view class="desc-content">
        <view class="desc-item">
          <text class="desc-icon">📊</text>
          <text class="desc-text">自动统计测试数据和成绩分析</text>
        </view>
        <view class="desc-item">
          <text class="desc-icon">👥</text>
          <text class="desc-text">支持多人参与，实时查看访问者</text>
        </view>
        <view class="desc-item">
          <text class="desc-icon">🔗</text>
          <text class="desc-text">一键分享链接或微信卡片</text>
        </view>
        <view class="desc-item">
          <text class="desc-icon">🎨</text>
          <text class="desc-text">自定义测试内容和难度设置</text>
        </view>
      </view>
    </view>
  </view>
</view> 