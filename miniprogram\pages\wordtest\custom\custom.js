Page({

  /**
   * 页面的初始数据
   */
  data: {
    inputText: '',
    words: [],
    isProcessing: false,
    selectedModes: [],
    availableModes: [
      { id: 'en2zh', name: '英译汉', icon: '🔤', desc: '看英文选中文' },
      { id: 'zh2en', name: '汉译英', icon: '🈳', desc: '看中文写英文' },
      { id: 'dictation', name: '听写', icon: '✏️', desc: '听音拼写单词' },
      { id: 'puzzle', name: '消消乐', icon: '🧩', desc: '趣味配对游戏' }
    ],
    showImagePicker: false,
    showWordInputModal: false,
    tempWord: {
      word: '',
      definition: '',
      phonetic: '',
      example: ''
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化模式列表
    this.updateModeItems();
  },



  /**
   * 更新模式列表的选中状态
   */
  updateModeItems() {
    const modeItems = this.data.availableModes.map(mode => ({
      ...mode,
      selected: this.data.selectedModes.includes(mode.id)
    }));
    this.setData({ modeItems });
  },

  /**
   * 输入框内容变化
   */
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    });
  },

  /**
   * 选择图片
   */
  onChooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const imagePath = res.tempFiles[0].tempFilePath;
        this.recognizeText(imagePath);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 识别图片中的文字
   */
  recognizeText(imagePath) {
    wx.showLoading({
      title: '识别中...',
      mask: true
    });

    // 使用微信小程序OCR接口
    wx.ocr.recognize({
      path: imagePath,
      success: (res) => {
        wx.hideLoading();
        const recognizedText = res.result;
        this.setData({
          inputText: this.data.inputText + '\n' + recognizedText
        });
        wx.showToast({
          title: '识别成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('OCR识别失败:', err);
        wx.showToast({
          title: '识别失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * AI智能整理
   */
  async onAIProcess() {
    if (!this.data.inputText.trim()) {
      wx.showToast({
        title: '请先输入或上传内容',
        icon: 'none'
      });
      return;
    }

    this.setData({ isProcessing: true });
    wx.showLoading({
      title: 'AI处理中...',
      mask: true
    });

    try {
      // 先尝试本地智能解析
      const localWords = this.parseWordsLocally(this.data.inputText.trim());
      
      if (localWords.length > 0) {
        wx.hideLoading();
        this.setData({ 
          words: localWords,
          isProcessing: false 
        });
        
        wx.showToast({
          title: `识别到${localWords.length}个单词`,
          icon: 'success'
        });
        return;
      }

      // 如果本地解析失败，提示用户手动输入
      wx.hideLoading();
      
      wx.showModal({
        title: '无法自动识别',
        content: '请使用手动添加功能或批量添加功能来输入单词',
        showCancel: false,
        confirmText: '好的',
        success: () => {
          this.setData({ isProcessing: false });
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('处理失败:', error);
      this.setData({ isProcessing: false });
      wx.showToast({
        title: '处理失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 本地智能解析单词
   */
  parseWordsLocally(text) {
    const words = [];
    const lines = text.split(/\r?\n/).filter(line => line.trim());
    
    for (let line of lines) {
      line = line.trim();
      if (!line) continue;

      // 支持多种格式解析
      const parsed = this.parseWordLine(line);
      if (parsed) {
        words.push(parsed);
      }
    }

    // 如果按行解析失败，尝试按空格或逗号分割
    if (words.length === 0) {
      const simpleWords = text.split(/[,，\s\n\r]+/)
        .filter(word => word.trim() && /^[a-zA-Z]+$/.test(word.trim()))
        .map(word => ({
          word: word.trim().toLowerCase(),
          definition: '',
          phonetic: '',
          example: ''
        }));
      
      return simpleWords;
    }

    return words;
  },

  /**
   * 解析单行单词数据
   */
  parseWordLine(line) {
    // 格式1: apple /ˈæpl/ 苹果 I like to eat apple.
    let match = line.match(/^([a-zA-Z]+)\s+(\/[^\/]+\/)\s+([^\.!?]*[^a-zA-Z])\s*(.*)$/);
    if (match) {
      return {
        word: match[1].trim().toLowerCase(),
        phonetic: match[2].trim(),
        definition: match[3].trim(),
        example: match[4].trim()
      };
    }

    // 格式2: apple 苹果 I like to eat apple.
    match = line.match(/^([a-zA-Z]+)\s+([^a-zA-Z]+)\s*(.*)$/);
    if (match) {
      const possibleExample = match[3].trim();
      // 判断是否为英文例句
      const isExample = /^[A-Z].*[.!?]$/.test(possibleExample) || possibleExample.includes(match[1]);
      
      return {
        word: match[1].trim().toLowerCase(),
        phonetic: '',
        definition: match[2].trim(),
        example: isExample ? possibleExample : ''
      };
    }

    // 格式3: apple /ˈæpl/ 苹果
    match = line.match(/^([a-zA-Z]+)\s+(\/[^\/]+\/)\s+(.+)$/);
    if (match) {
      return {
        word: match[1].trim().toLowerCase(),
        phonetic: match[2].trim(),
        definition: match[3].trim(),
        example: ''
      };
    }

    // 格式4: apple 苹果
    match = line.match(/^([a-zA-Z]+)\s+(.+)$/);
    if (match && !/^[a-zA-Z\s]+$/.test(match[2])) {
      return {
        word: match[1].trim().toLowerCase(),
        phonetic: '',
        definition: match[2].trim(),
        example: ''
      };
    }

    // 格式5: 只有英文单词
    if (/^[a-zA-Z]+$/.test(line)) {
      return {
        word: line.toLowerCase(),
        phonetic: '',
        definition: '',
        example: ''
      };
    }

    return null;
  },

  /**
   * 手动添加单词
   */
  onManualAdd() {
    this.setData({
      showWordInputModal: true,
      tempWord: {
        word: '',
        definition: '',
        phonetic: '',
        example: ''
      }
    });
  },

  /**
   * 关闭单词输入弹窗
   */
  onCloseWordInput() {
    this.setData({
      showWordInputModal: false
    });
  },

  /**
   * 单词输入
   */
  onWordInput(e) {
    this.setData({
      'tempWord.word': e.detail.value.toLowerCase()
    });
  },

  /**
   * 释义输入
   */
  onDefinitionInput(e) {
    this.setData({
      'tempWord.definition': e.detail.value
    });
  },

  /**
   * 音标输入
   */
  onPhoneticInput(e) {
    let phonetic = e.detail.value;
    // 自动添加音标符号
    if (phonetic && !phonetic.startsWith('/')) {
      phonetic = '/' + phonetic;
    }
    if (phonetic && !phonetic.endsWith('/') && phonetic !== '/') {
      phonetic = phonetic + '/';
    }
    this.setData({
      'tempWord.phonetic': phonetic
    });
  },

  /**
   * 例句输入
   */
  onExampleInput(e) {
    this.setData({
      'tempWord.example': e.detail.value
    });
  },

  /**
   * 确认添加单词
   */
  onConfirmAddWord() {
    const { word, definition } = this.data.tempWord;
    
    if (!word.trim()) {
      wx.showToast({
        title: '请输入单词',
        icon: 'none'
      });
      return;
    }

    // 检查是否重复
    const exists = this.data.words.find(w => w.word.toLowerCase() === word.toLowerCase());
    if (exists) {
      wx.showToast({
        title: '单词已存在',
        icon: 'none'
      });
      return;
    }

    const newWord = {
      word: word.trim().toLowerCase(),
      definition: definition.trim() || '暂无释义',
      phonetic: this.data.tempWord.phonetic.trim(),
      example: this.data.tempWord.example.trim()
    };

    this.setData({
      words: [...this.data.words, newWord],
      showWordInputModal: false
    });

    wx.showToast({
      title: '添加成功',
      icon: 'success'
    });
  },

  /**
   * 批量添加
   */
  onBatchAdd() {
    wx.showModal({
      title: '批量添加单词',
      content: '请输入单词（支持多种格式，每行一个）',
      editable: true,
      placeholderText: '例如：\napple 苹果\nbanana /bəˈnɑːnə/ 香蕉',
      success: (res) => {
        if (res.confirm && res.content) {
          const newWords = this.parseWordsLocally(res.content);
          
          if (newWords.length > 0) {
            // 去重
            const existingWords = new Set(this.data.words.map(w => w.word.toLowerCase()));
            const uniqueWords = newWords.filter(w => !existingWords.has(w.word.toLowerCase()));
            
            this.setData({
              words: [...this.data.words, ...uniqueWords]
            });
            
            wx.showToast({
              title: `添加了${uniqueWords.length}个单词`,
              icon: 'success'
            });
          } else {
            wx.showToast({
              title: '未识别到有效单词',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 删除单词
   */
  onDeleteWord(e) {
    const index = e.currentTarget.dataset.index;
    wx.showModal({
      title: '确认删除',
      content: `确定要删除单词"${this.data.words[index].word}"吗？`,
      success: (res) => {
        if (res.confirm) {
          const words = [...this.data.words];
          words.splice(index, 1);
          this.setData({ words });
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 编辑单词
   */
  onEditWord(e) {
    const index = e.currentTarget.dataset.index;
    const word = this.data.words[index];
    
    this.setData({
      showWordInputModal: true,
      tempWord: { ...word },
      editingIndex: index
    });
  },

  /**
   * 确认编辑单词
   */
  onConfirmEditWord() {
    const { word, definition } = this.data.tempWord;
    
    if (!word.trim()) {
      wx.showToast({
        title: '请输入单词',
        icon: 'none'
      });
      return;
    }

    const words = [...this.data.words];
    words[this.data.editingIndex] = {
      word: word.trim().toLowerCase(),
      definition: definition.trim() || '暂无释义',
      phonetic: this.data.tempWord.phonetic.trim(),
      example: this.data.tempWord.example.trim()
    };

    this.setData({
      words,
      showWordInputModal: false,
      editingIndex: undefined
    });

    wx.showToast({
      title: '修改成功',
      icon: 'success'
    });
  },

  /**
   * 选择模式
   */
  onModeToggle(e) {
    const modeId = e.currentTarget.dataset.id;
    
    // 单选模式：如果点击的是已选中的模式，则取消选择；否则选择新模式
    const selectedModes = this.data.selectedModes.includes(modeId) ? [] : [modeId];
    
    this.setData({ selectedModes }, () => {
      // 更新模式列表的选中状态
      this.updateModeItems();
    });
  },

  /**
   * 开始练习
   */
  onStartPractice() {
    if (this.data.words.length === 0) {
      wx.showToast({
        title: '请先添加单词',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.selectedModes.length === 0) {
      wx.showToast({
        title: '请选择练习模式',
        icon: 'none'
      });
      return;
    }

    // 验证单词数据完整性
    const incompleteWords = this.data.words.filter(word => !word.definition || !word.definition.trim());
    if (incompleteWords.length > 0 && this.data.selectedModes.includes('zh2en')) {
      wx.showModal({
        title: '数据不完整',
        content: `有${incompleteWords.length}个单词缺少中文释义，汉译英模式需要完整的释义。是否现在补充？`,
        success: (res) => {
          if (res.confirm) {
            this.fillMissingDefinitions();
          }
        }
      });
      return;
    }

    // 将单词数据存储到全局数据中
    const app = getApp();
    const formattedWords = this.data.words.map(word => ({
      ...word,
      // 兼容不同的字段名
      words: word.word,
      meaning: word.definition,
      meanings: [{
        partOfSpeech: 'n.',
        definitions: [{
          definition: word.definition,
          example: word.example || ''
        }]
      }]
    }));

    // 根据选择的模式跳转到对应的模式选择页面
    const mode = this.data.selectedModes[0]; // 使用第一个选中的模式
    
    // 存储学习数据到全局
    app.globalData.learningData = {
      words: formattedWords,
      libraryId: 'custom',
      libraryName: '自定义单词'
    };

    wx.showLoading({
      title: '准备检测...',
      mask: true
    });

    // 直接跳转到对应模式的练习/测试选择界面
    setTimeout(() => {
      wx.hideLoading();

      // 根据模式直接跳转到对应的模式选择页面
      this.navigateToModeSelect(mode, formattedWords);
    }, 300);
  },

  /**
   * 直接跳转到对应模式的练习/测试选择界面
   */
  navigateToModeSelect(mode, formattedWords) {
    const app = getApp();

    switch (mode) {
      case 'en2zh':
        // 英译汉模式 - 跳转到模式选择页面
        app.globalData.learningData = {
          words: formattedWords,
          libraryId: 'custom',
          libraryName: '自定义单词',
          mode: 'select'
        };
        wx.navigateTo({
          url: '/pages/wordtest/mode-select/mode-select?testMode=en_to_cn&libraryId=custom'
        });
        break;

      case 'zh2en':
        // 汉译英模式 - 跳转到模式选择页面
        app.globalData.learningData = {
          words: formattedWords,
          libraryId: 'custom',
          libraryName: '自定义单词',
          mode: 'select'
        };
        wx.navigateTo({
          url: '/pages/wordtest/mode-select/mode-select?testMode=cn_to_en&libraryId=custom'
        });
        break;

      case 'dictation':
        // 听写模式 - 跳转到模式选择页面
        app.globalData.customWords = formattedWords;
        wx.navigateTo({
          url: '/pages/spelling/mode-select/mode-select?libraryId=custom'
        });
        break;

      case 'puzzle':
        // 消消乐模式 - 跳转到模式选择页面
        app.globalData.learningData = {
          words: formattedWords,
          libraryId: 'custom',
          libraryName: '自定义单词',
          mode: 'select'
        };
        wx.navigateTo({
          url: '/pages/wordtest/mode-select/mode-select?testMode=elimination&libraryId=custom'
        });
        break;

      default:
        wx.showToast({
          title: '该模式暂未开放',
          icon: 'none'
        });
    }
  },

  /**
   * 开始练习模式
   */
  startPracticeMode(mode, formattedWords) {
    const app = getApp();
      
      switch (mode) {
        case 'en2zh':
        // 英译汉练习模式
        app.globalData.learningData = {
          words: formattedWords,
          libraryId: 'custom',
          libraryName: '自定义单词',
          mode: 'practice'
        };
          wx.navigateTo({
          url: '/pages/wordtest/test/test?testMode=en_to_cn&isPractice=true&libraryId=custom'
          });
          break;

        case 'zh2en':
        // 汉译英练习模式
        app.globalData.learningData = {
          words: formattedWords,
          libraryId: 'custom',
          libraryName: '自定义单词',
          mode: 'practice'
        };
          wx.navigateTo({
          url: '/pages/wordtest/test/test?testMode=cn_to_en&isPractice=true&libraryId=custom'
          });
          break;

        case 'dictation':
        // 听写练习模式
          app.globalData.customWords = formattedWords;
          wx.navigateTo({
          url: '/pages/spelling/practice/practice?mode=custom'
          });
          break;

        case 'puzzle':
        // 消消乐练习模式
          const gameWords = formattedWords.map(word => ({
            english: word.word.toUpperCase(),
            chinese: word.definition
          }));
          
          app.globalData.eliminationGameData = {
            words: gameWords,
            currentGroup: 1,
            totalGroups: 1,
            allWords: gameWords,
            libraryId: 'custom',
            libraryName: '自定义单词'
          };
          
          wx.navigateTo({
          url: '/pages/task/puzzle/puzzle?mode=custom&group=1&total=1'
        });
        break;

      default:
              wx.showToast({
          title: '该模式暂未支持',
                icon: 'none'
              });
            }
  },

  /**
   * 开始测试模式
   */
  startTestMode(mode, formattedWords) {
    const app = getApp();
    
    switch (mode) {
      case 'en2zh':
        // 英译汉测试模式 - 跳转到模式选择页面
        app.globalData.learningData = {
          words: formattedWords,
          libraryId: 'custom',
          libraryName: '自定义单词',
          mode: 'test'
        };
        wx.navigateTo({
          url: '/pages/wordtest/mode-select/mode-select?testMode=en_to_cn&libraryId=custom'
        });
        break;

      case 'zh2en':
        // 汉译英测试模式 - 跳转到模式选择页面
        app.globalData.learningData = {
          words: formattedWords,
          libraryId: 'custom',
          libraryName: '自定义单词',
          mode: 'test'
        };
        wx.navigateTo({
          url: '/pages/wordtest/mode-select/mode-select?testMode=cn_to_en&libraryId=custom'
        });
        break;

      case 'dictation':
        // 听写测试模式 - 跳转到模式选择页面
        app.globalData.customWords = formattedWords;
        wx.navigateTo({
          url: '/pages/spelling/mode-select/mode-select?libraryId=custom'
        });
        break;

      case 'puzzle':
        // 消消乐测试模式 - 跳转到模式选择页面
        wx.navigateTo({
          url: '/pages/wordtest/mode-select/mode-select?testMode=elimination&libraryId=custom'
        });
        break;

        default:
          wx.showToast({
            title: '该模式暂未支持',
            icon: 'none'
          });
      }
  },

  /**
   * 创建单词竞赛
   */
  async createCompetition(mode, formattedWords) {
    const app = getApp();
    
    // 检查用户登录状态
    if (!app.isLoggedIn()) {
      wx.showModal({
        title: '需要登录',
        content: '创建竞赛需要登录，是否立即登录？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 输入竞赛名称
    wx.showModal({
      title: '创建竞赛',
      content: '请输入竞赛名称：',
      editable: true,
      placeholderText: '例如：我的单词挑战',
      success: async (res) => {
        if (res.confirm && res.content.trim()) {
          const competitionName = res.content.trim();
          
          wx.showLoading({
            title: '创建中...',
            mask: true
          });

          try {
            // 自定义单词竞赛不能使用索引存储，因为词汇是用户自定义的
            // 这些词汇不在系统词库中，必须传递完整数据
            const useIndexBasedStorage = false;

            // 调用云函数创建竞赛
            const result = await wx.cloud.callFunction({
              name: 'createCompetition',
              data: {
                name: competitionName,
                mode: mode,
                // 超级优化：基于索引存储时只传递必要信息
                words: useIndexBasedStorage ?
                  [{
                    _id: formattedWords[0]._id,
                    words: formattedWords[0].words,
                    totalCount: formattedWords.length
                  }] :
                  formattedWords,
                libraryId: 'custom',
                libraryName: '自定义单词',
                // 自定义竞赛通常不需要乱序，但为了兼容性还是传递
                isRandomOrder: false
              }
            });

            wx.hideLoading();

            if (result.result.success) {
              wx.showModal({
                title: '创建成功',
                content: `竞赛"${competitionName}"创建成功！现在可以分享给好友参与了。`,
                showCancel: true,
                cancelText: '稍后分享',
                confirmText: '立即分享',
                success: (shareRes) => {
                  if (shareRes.confirm) {
                    // 跳转到竞赛页面进行分享
                    wx.navigateTo({
                      url: `/pages/competition/competition?highlightId=${result.result.competitionId}`
                    });
                  } else {
                    // 跳转到竞赛列表
                    wx.navigateTo({
                      url: '/pages/competition/competition'
                    });
                  }
                }
              });
            } else {
              throw new Error(result.result.message || '创建失败');
            }
          } catch (error) {
            wx.hideLoading();
            console.error('创建竞赛失败:', error);
            wx.showToast({
              title: '创建失败，请重试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 补充缺失的释义
   */
  fillMissingDefinitions() {
    const incompleteWords = this.data.words.filter(word => !word.definition || !word.definition.trim());
    if (incompleteWords.length === 0) return;

    const word = incompleteWords[0];
    const index = this.data.words.findIndex(w => w.word === word.word);
    
    wx.showModal({
      title: '补充释义',
      content: `请为单词"${word.word}"输入中文释义：`,
      editable: true,
      placeholderText: '输入中文释义',
      success: (res) => {
        if (res.confirm && res.content.trim()) {
          const words = [...this.data.words];
          words[index].definition = res.content.trim();
          this.setData({ words });
          
          // 继续补充下一个
          setTimeout(() => {
            this.fillMissingDefinitions();
          }, 100);
        }
      }
    });
  },

  /**
   * 清空所有单词
   */
  onClearAll() {
    if (this.data.words.length === 0) return;
    
    wx.showModal({
      title: '确认清空',
      content: `确定要清空所有${this.data.words.length}个单词吗？`,
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.setData({ words: [] });
          wx.showToast({
            title: '已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 导入示例数据
   */
  onImportExample() {
    const exampleWords = [
      { word: 'hello', definition: '你好', phonetic: '/həˈloʊ/', example: 'Hello, nice to meet you.' },
      { word: 'world', definition: '世界', phonetic: '/wɜːrld/', example: 'Welcome to the world of English.' },
      { word: 'love', definition: '爱', phonetic: '/lʌv/', example: 'I love learning English.' },
      { word: 'book', definition: '书', phonetic: '/bʊk/', example: 'This is a good book.' },
      { word: 'study', definition: '学习', phonetic: '/ˈstʌdi/', example: 'I study English every day.' },
      { word: 'apple', definition: '苹果', phonetic: '/ˈæpl/', example: 'I like to eat apples.' },
      { word: 'water', definition: '水', phonetic: '/ˈwɔːtər/', example: 'Please give me some water.' },
      { word: 'happy', definition: '快乐的', phonetic: '/ˈhæpi/', example: 'I am very happy today.' }
    ];

    wx.showModal({
      title: '导入示例',
      content: `确定要导入${exampleWords.length}个示例单词吗？\n\n包含：hello, world, love, book, study, apple, water, happy`,
      success: (res) => {
        if (res.confirm) {
          // 过滤掉已存在的单词，避免重复
          const existingWords = this.data.words.map(w => w.word.toLowerCase());
          const newWords = exampleWords.filter(word => 
            !existingWords.includes(word.word.toLowerCase())
          );

          if (newWords.length === 0) {
            wx.showToast({
              title: '示例单词已存在',
              icon: 'none'
            });
            return;
          }

          this.setData({
            words: [...this.data.words, ...newWords]
          });

          wx.showToast({
            title: `成功导入${newWords.length}个单词`,
            icon: 'success'
          });
        }
      }
    });
  }
}); 