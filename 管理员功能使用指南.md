# 管理员功能使用指南

## 🔑 管理员账号配置

### 已配置的管理员账号
- **账号密码登录**：`15547663399`
- **微信登录**：用户ID `5938DE76`

### 账号绑定说明
两个账号已自动绑定，登录任意一个账号都会被识别为管理员，享有完整的管理员权限。

---

## 🛠️ 管理员工具菜单

### 访问路径
**我的** → **管理员工具**（仅管理员可见）

### 功能特色
- **实时角标**：显示未读反馈数量
- **便捷操作**：一键进入各管理功能
- **状态监控**：实时显示系统状态

---

## 📢 通知管理功能

### 功能介绍
管理首页滚动通知栏内容，支持实时发布和编辑。

### 使用步骤
1. **进入通知管理**
   - 路径：管理员工具 → 编辑通知
   - 或直接访问：`pages/admin/notice/notice`

2. **发布新通知**
   - 在输入框中输入通知内容（最多200字）
   - 点击"发布通知"按钮
   - 首页通知栏立即更新

3. **管理现有通知**
   - 点击任意通知项
   - 可选择：编辑内容、启用/停用、删除通知
   - 只有启用状态的通知会在首页显示

### 通知效果
- 橙色渐变背景，📢图标
- 文字从右向左滚动播放
- 20秒循环一次
- 响应式适配不同屏幕

---

## 💬 用户反馈管理

### 功能特色
- **实时提醒**：未读反馈会显示角标
- **在线回复**：直接回复用户反馈
- **状态管理**：pending/replied/closed三种状态
- **设备信息**：自动收集用户设备信息便于问题排查

### 使用步骤

#### 1. 查看未读反馈
- **角标提醒**：管理员工具菜单会显示未读数量
- **快速访问**：管理员工具 → 管理用户反馈
- **角标清除**：进入反馈页面后角标自动清除

#### 2. 处理反馈
点击任意反馈项，可选择：
- **回复用户**：输入回复内容，用户可查看回复
- **标记已读**：将状态改为已读
- **删除反馈**：永久删除该反馈

#### 3. 回复功能
- 支持修改已有回复
- 用户可通过"我的反馈"查看回复
- 回复后状态自动改为"已回复"

---

## 🎯 角标功能说明

### 角标显示位置
1. **个人中心**：管理员工具菜单项
2. **管理员工具页面**：反馈管理功能
3. **快捷操作**：未读反馈图标

### 角标更新逻辑
- **实时统计**：基于数据库中pending状态的反馈
- **自动清除**：进入反馈管理页面后自动清除
- **多页面同步**：在任何页面查看反馈都会同步清除角标

### 角标样式
- 红色圆形背景
- 白色数字显示
- 超过99显示"99+"
- 阴影效果突出显示

---

## 🚀 快速操作指南

### 发布紧急通知
1. 打开小程序
2. 进入"我的"页面
3. 点击"管理员工具"
4. 选择"编辑通知"
5. 输入通知内容并发布
6. 用户立即可在首页看到

### 处理用户反馈
1. 观察管理员工具是否有红色角标
2. 点击进入"管理用户反馈"
3. 查看具体反馈内容
4. 点击反馈项选择"回复用户"
5. 输入回复内容并确认

---

## 📊 数据统计

### 云数据库集合

#### notices（通知）
```json
{
  "_id": "通知ID",
  "content": "通知内容",
  "status": "active|inactive",
  "createTime": "创建时间",
  "updateTime": "更新时间",
  "createdBy": "创建者ID"
}
```

#### feedbacks（反馈）
```json
{
  "_id": "反馈ID",
  "userId": "用户ID",
  "userNickName": "用户昵称",
  "problemText": "问题反馈",
  "suggestionText": "建议内容",
  "deviceInfo": "设备信息",
  "status": "pending|replied|closed",
  "adminReply": "管理员回复",
  "createTime": "创建时间",
  "replyTime": "回复时间"
}
```

---

## 🔧 云函数说明

### 已部署的云函数
- **getNotice**：获取当前活跃通知
- **manageNotice**：管理通知（增删改查）
- **manageFeedback**：管理反馈（增删改查）
- **getUnreadFeedbackCount**：获取未读反馈数量（高效统计）

### 部署步骤
```bash
# 在开发者工具中
右键 cloudfunctions/getNotice → 上传并部署
右键 cloudfunctions/manageNotice → 上传并部署
右键 cloudfunctions/manageFeedback → 上传并部署
右键 cloudfunctions/getUnreadFeedbackCount → 上传并部署
```

---

## 💡 使用技巧

### 高效管理反馈
1. **批量处理**：按状态筛选反馈
2. **快速回复**：使用模板回复常见问题
3. **及时响应**：关注角标提醒，及时处理用户反馈

### 通知内容建议
1. **重要通知**：系统维护、功能更新
2. **活动信息**：学习活动、比赛通知
3. **用户引导**：新功能介绍、使用指南

### 权限安全
- 管理员身份自动识别，无需手动配置
- 支持多种登录方式的账号绑定
- 非管理员用户无法看到管理功能

---

## 📞 技术支持

如需添加新的管理员账号或修改现有功能，请联系技术团队。

**当前管理员账号**：
- 账号密码：15547663399
- 微信登录：5938DE76

所有管理功能均为实时生效，无需重启小程序或更新版本！ 