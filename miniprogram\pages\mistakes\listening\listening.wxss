.container {
  padding: 0;
  background: #f8f9fa;
  min-height: 100vh;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  justify-content: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.filter-item {
  display: flex;
  align-items: center;
  padding: 15rpx 25rpx;
  margin: 0 10rpx;
  background: white;
  border: 1rpx solid #dee2e6;
  border-radius: 20rpx;
  font-size: 28rpx;
  color: #6c757d;
  transition: all 0.3s;
}

.filter-item.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* 操作按钮栏样式 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: white;
  border-bottom: 1rpx solid #e9ecef;
}

.practice-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.3);
}

.manage-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 15rpx 20rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 1rpx solid #dee2e6;
}

.action-btn.primary {
  background: #007bff;
  color: white;
}

.btn-icon {
  font-size: 24rpx;
  font-weight: bold;
}

/* 批量操作栏样式 */
.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background: #d1ecf1;
  border-bottom: 1rpx solid #bee5eb;
  min-height: 80rpx;
}

.batch-info {
  font-size: 28rpx;
  color: #0c5460;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.batch-buttons {
  display: flex;
  gap: 8rpx;
  flex-shrink: 0;
  flex-wrap: nowrap;
}

.batch-btn {
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  border: none;
  background: #17a2b8;
  color: white;
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160rpx;
}

.batch-btn.primary {
  background: #007bff;
  color: white;
}

.batch-btn.danger {
  background: #dc3545;
  color: white;
}

/* 错题列表样式 */
.mistake-list {
  padding: 20rpx;
}

.mistake-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: white;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.mistake-item.edit-mode {
  padding-left: 60rpx;
  position: relative;
}

.mistake-item.selected {
  background: #d1ecf1;
  border: 2rpx solid #17a2b8;
}

.mistake-checkbox {
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}

.checkbox-icon {
  font-size: 32rpx;
}

.mistake-info {
  flex: 1;
}

.mistake-word {
  font-size: 36rpx;
  font-weight: bold;
  color: #212529;
  margin-bottom: 8rpx;
}

.mistake-phonetic {
  font-size: 28rpx;
  color: #17a2b8;
  margin-bottom: 8rpx;
}

.mistake-meaning {
  font-size: 30rpx;
  color: #495057;
  margin-bottom: 8rpx;
}

.mistake-example {
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.mistake-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #adb5bd;
}

.mistake-actions {
  margin-left: 15rpx;
}

.action-btn.small {
  padding: 10rpx 15rpx;
  font-size: 24rpx;
  background: #28a745;
  color: white;
  border-radius: 12rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #6c757d;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #adb5bd;
  margin-bottom: 40rpx;
}

.empty-action {
  background: #17a2b8;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  border: none;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx;
  font-size: 28rpx;
  color: #6c757d;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.add-mistake-modal {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #212529;
}

.modal-close {
  font-size: 40rpx;
  color: #6c757d;
  line-height: 1;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #495057;
  margin-bottom: 10rpx;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #dee2e6;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #495057;
  background: #f8f9fa;
}

.form-textarea {
  min-height: 120rpx;
  resize: none;
}

.form-input:focus, .form-textarea:focus {
  border-color: #17a2b8;
  background: white;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #e9ecef;
}

.modal-btn {
  padding: 20rpx 40rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn.cancel {
  background: #f8f9fa;
  color: #6c757d;
}

.modal-btn.confirm {
  background: #17a2b8;
  color: white;
} 