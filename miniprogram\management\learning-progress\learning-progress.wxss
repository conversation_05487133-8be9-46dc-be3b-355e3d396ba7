/* pages/profile/learning-progress/learning-progress.wxss */
.progress-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  padding-bottom: 100rpx;
}

/* 页面标题 */
.page-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20rpx;
}

.header-actions {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 16rpx;
  margin-top: 10rpx;
}

.edit-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 25rpx;
  padding: 12rpx 30rpx;
  font-size: 24rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  min-width: 80rpx;
}

.edit-btn.active {
  background: #ff6b6b;
  border-color: #ff6b6b;
}



.edit-btn::after {
  border: none;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  margin-top: 100rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  display: block;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
  display: block;
}

.empty-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 40rpx;
  display: block;
  line-height: 1.5;
}

.empty-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #ffffff;
  border: none;
  border-radius: 25rpx;
  padding: 16rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 20rpx rgba(79, 172, 254, 0.3);
}

.empty-btn::after {
  border: none;
}

/* 编辑模式工具栏 */
.edit-toolbar {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-info {
  display: flex;
  align-items: center;
}

.selected-count {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}

.toolbar-actions {
  display: flex;
  gap: 16rpx;
}

.batch-btn {
  background: #ff6b6b;
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.batch-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
}

.batch-btn::after {
  border: none;
}

/* 复习统计 */
.review-stats {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  backdrop-filter: blur(10rpx);
}

.stats-header {
  margin-bottom: 20rpx;
}

.stats-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}

.stats-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #ffc107;
}

.stats-breakdown {
  margin-top: 16rpx;
}

.breakdown-title {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 12rpx;
  display: block;
}

.breakdown-items {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.breakdown-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(255, 255, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.level-label {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  color: #ffffff;
}

.level-label.difficult {
  background: #ff4757;
}

.level-label.normal {
  background: #ffa502;
}

.level-label.easy {
  background: #2ed573;
}

.level-count {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 学习进度列表 */
.progress-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.progress-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.progress-card.edit-mode {
  padding-left: 20rpx;
}

.progress-card.selected {
  border-color: #4facfe;
  background: rgba(79, 172, 254, 0.2);
}

/* 选择框 */
.progress-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
}

.selector-circle {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.selector-circle.selected {
  background: #4facfe;
  border-color: #4facfe;
}

.selector-check {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 进度信息 */
.progress-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.progress-header {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.title-row {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.progress-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #ffffff;
  flex: 1;
  line-height: 1.3;
  word-break: break-all;
}

.mode-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.mode-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
}

.last-study-time {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.progress-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.stat-number {
  font-size: 24rpx;
  font-weight: bold;
  color: #ffffff;
}

.stat-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.progress-bar-container {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.progress-bar {
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 操作卡片 */
.action-cards {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  min-width: 200rpx;
}

.action-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  cursor: pointer;
}

.action-card.primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4rpx 12rpx rgba(79, 172, 254, 0.3);
}

.action-card.review {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.3);
}

.action-card.review.disabled {
  background: rgba(255, 255, 255, 0.1);
  box-shadow: none;
  opacity: 0.6;
  cursor: not-allowed;
}

.action-card:active:not(.disabled) {
  transform: scale(0.98);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #ffffff;
}

.card-progress {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.card-desc {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

.delete-btn {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 2rpx solid rgba(255, 107, 107, 0.3);
  border-radius: 12rpx;
  padding: 12rpx 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  text-align: center;
  transition: all 0.3s ease;
  margin-top: 8rpx;
}

.delete-btn::after {
  border: none;
}

/* 使用说明 */
.help-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-top: 30rpx;
}

.help-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.help-item {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}


