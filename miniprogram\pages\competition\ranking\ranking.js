Page({
  data: {
    competitionId: '',
    mode: '',
    competitionName: '',
    rankings: [],
    loading: true,
    modeText: '',
    totalParticipants: 0,
    userRank: null // 当前用户排名
  },

  onLoad(options) {
    const { competitionId, mode } = options;
    
    if (!competitionId || !mode) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      competitionId,
      mode,
      modeText: this.getModeText(mode)
    });

    this.loadRankingData();
  },

  // 获取模式文本
  getModeText(mode) {
    const modeMap = {
      'en2zh': '英译汉',
      'zh2en': '汉译英', 
      'dictation': '听写',
      'elimination': '消消乐'
    };
    return modeMap[mode] || '未知模式';
  },

  // 加载排行榜数据
  async loadRankingData() {
    try {
      this.setData({ loading: true });

      // 获取排行榜数据
      const result = await wx.cloud.callFunction({
        name: 'getCompetitionRanking',
        data: {
          competitionId: this.data.competitionId,
          mode: this.data.mode
        }
      });

      if (result.result.success) {
        const competitionType = result.result.type || 'single';
        const rankings = this.formatRankingData(result.result.data, competitionType);
        const currentUser = wx.getStorageSync('userInfo') || {};
        
        // 查找当前用户排名
        let userRank = null;
        if (currentUser.openid) {
          const userIndex = rankings.findIndex(item => item.openId === currentUser.openid);
          if (userIndex !== -1) {
            userRank = userIndex + 1;
          }
        }

        // 更新竞赛名称显示
        let competitionTitle = result.result.competitionName || '单词竞赛';
        if (competitionType === 'master') {
          competitionTitle += ` (多关卡排行榜)`;
        }

        this.setData({
          rankings,
          competitionName: competitionTitle,
          totalParticipants: result.result.total || rankings.length,
          userRank,
          loading: false,
          competitionType: competitionType,
          totalLevels: result.result.totalLevels || 0
        });
      } else {
        throw new Error(result.result.message || '获取排行榜失败');
      }
    } catch (error) {
      console.error('获取排行榜失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 格式化排行榜数据
  formatRankingData(data, competitionType) {
    const { mode } = this.data;
    console.log('原始排行榜数据:', data);
    
    return data.map((item, index) => {
      const rank = index + 1;
      let displayData = {};
      
      console.log(`排名${rank}用户:`, {
        userName: item.userName,
        openId: item.openId
      });

      // 根据竞赛类型和模式格式化显示数据
      if (competitionType === 'master') {
        // 多关卡竞赛显示统计数据
        console.log(`多关卡用户${rank}数据:`, {
          totalScore: item.totalScore,
          averageScore: item.averageScore,
          completedLevels: item.completedLevels,
          completionRate: item.completionRate,
          totalDuration: item.totalDuration,
          overallAccuracy: item.overallAccuracy
        });
        
        // 根据模式决定显示内容
        if (mode === 'elimination') {
          // 消消乐模式：不显示正确率
          displayData = {
            score: `${item.totalScore || 0}分 (平均${item.averageScore || 0}分)`,
            detail: `完成关卡: ${item.completedLevels || 0}关 (${item.completionRate || 0}%)`,
            subDetail: `总用时: ${this.formatDuration(item.totalDuration || 0)}`
          };
        } else {
          // 其他模式：显示正确率
          displayData = {
            score: `${item.totalScore || 0}分 (平均${item.averageScore || 0}分)`,
            detail: `完成关卡: ${item.completedLevels || 0}关 (${item.completionRate || 0}%)`,
            subDetail: `总用时: ${this.formatDuration(item.totalDuration || 0)} | 总正确率: ${item.overallAccuracy || 0}%`
          };
        }
      } else {
        // 单一竞赛显示原有数据
        switch (mode) {
          case 'en2zh':
          case 'zh2en':
            displayData = {
              score: `${item.score}分`,
              detail: `正确率: ${item.accuracy}%`,
              subDetail: `用时: ${this.formatDuration(item.duration)}`
            };
            break;
          case 'dictation':
            displayData = {
              score: `${item.score}分`,
              detail: `正确: ${item.correctCount}/${item.totalCount}`,
              subDetail: `用时: ${this.formatDuration(item.duration)}`
            };
            break;
          case 'elimination':
            displayData = {
              score: `${item.score}分`,
              detail: `消除: ${item.eliminatedPairs}对`,
              subDetail: `用时: ${this.formatDuration(item.duration)}`
            };
            break;
        }
      }

      return {
        rank,
        openId: item.openId,
        userName: item.userName || '匿名用户', // 直接使用云函数返回的昵称
        submitTime: this.formatTime(item.submitTime),
        ...displayData,
        // 添加排名样式标识
        isTop3: rank <= 3,
        isFirst: rank === 1,
        isSecond: rank === 2,
        isThird: rank === 3,
        // 多关卡特有数据
        completedLevels: item.completedLevels || 0,
        completionRate: item.completionRate || 0
      };
    });
  },

  // 格式化时长
  formatDuration(duration) {
    if (duration === null || duration === undefined) return '未知';
    if (duration === 0) return '0秒';
    
    const totalSeconds = Math.floor(duration);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    
    if (minutes > 0) {
      return `${minutes}分${seconds}秒`;
    } else {
      return `${seconds}秒`;
    }
  },

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days}天前`;
    } else if (hours > 0) {
      return `${hours}小时前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return '刚刚';
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadRankingData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 参与竞赛
  onParticipate() {
    // 跳转回竞赛页面参与
    wx.navigateTo({
      url: `/pages/competition/competition?mode=${this.data.mode}&highlightId=${this.data.competitionId}`
    });
  },

  // 分享排行榜
  onShareAppMessage() {
    return {
      title: `${this.data.competitionName} - ${this.data.modeText}竞赛排行榜`,
      path: `/pages/competition/ranking/ranking?competitionId=${this.data.competitionId}&mode=${this.data.mode}`
    };
  }
}); 