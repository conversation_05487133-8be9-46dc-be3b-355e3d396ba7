const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const mistakesCollection = db.collection('mistakes')
const usersCollection = db.collection('users')

exports.main = async (event, context) => {
  const { userId, wordId, isCorrect, answer, correctAnswer } = event

  try {
    const now = new Date()

    // 更新学习记录
    const learningRecord = await db.collection('learning_records')
      .where({
        userId,
        wordId
      })
      .get()

    if (learningRecord.data.length > 0) {
      const record = learningRecord.data[0]
      const masteryLevel = isCorrect ? Math.min(record.masteryLevel + 1, 5) : Math.max(record.masteryLevel - 1, 1)
      
      // 计算下次复习时间
      let nextReviewTime = null
      if (isCorrect) {
        const days = Math.pow(2, masteryLevel - 1) // 1, 2, 4, 8, 16天
        nextReviewTime = new Date(now.getTime() + days * 24 * 60 * 60 * 1000)
      } else {
        nextReviewTime = new Date(now.getTime() + 24 * 60 * 60 * 1000) // 24小时后
      }

      await db.collection('learning_records').doc(record._id).update({
        data: {
          reviewTimes: record.reviewTimes + 1,
          lastReviewTime: now,
          nextReviewTime,
          masteryLevel
        }
      })
    }

    // 如果答错，添加到错题本
    if (!isCorrect) {
      const mistake = await mistakesCollection.where({
        userId,
        wordId,
        status: 'unreviewed'
      }).get()

      if (mistake.data.length === 0) {
        await mistakesCollection.add({
          data: {
            userId,
            wordId,
            createTime: now,
            lastReviewTime: null,
            reviewTimes: 0,
            status: 'unreviewed',
            notes: '',
            wrongAnswers: [{
              time: now,
              answer,
              correctAnswer
            }]
          }
        })
      } else {
        await mistakesCollection.doc(mistake.data[0]._id).update({
          data: {
            wrongAnswers: db.command.push({
              time: now,
              answer,
              correctAnswer
            })
          }
        })
      }
    }

    // 更新用户统计数据
    const user = await usersCollection.doc(userId).get()
    const stats = user.data.stats

    // 更新今日统计
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayStats = stats.dailyStats.find(s => new Date(s.date).getTime() === today.getTime())
    
    if (todayStats) {
      todayStats.correctRate = (todayStats.correctRate * (todayStats.words - 1) + (isCorrect ? 1 : 0)) / todayStats.words
    }

    // 更新总体统计
    stats.correctRate = (stats.correctRate * (stats.totalWords - 1) + (isCorrect ? 1 : 0)) / stats.totalWords

    await usersCollection.doc(userId).update({
      data: {
        stats: stats
      }
    })

    return {
      code: 200,
      message: '保存成功'
    }
  } catch (error) {
    console.error(error)
    return {
      code: 500,
      message: '服务器错误'
    }
  }
} 