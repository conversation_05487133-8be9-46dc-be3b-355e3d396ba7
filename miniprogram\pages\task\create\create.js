const app = getApp();

Page({
  data: {
    taskName: '', // 任务名称
    mode: 'practice', // 模式：practice-练习模式，test-测试模式
    testType: 'en2zh', // 测试类型：en2zh-英译汉，zh2en-汉译英，game-单词消消乐
    timeLimit: 'unlimited', // 时间限制：unlimited-不限时，10-10秒，15-15秒，20-20秒
    selectedWords: [], // 选中的单词
    taskType: '', // 任务类型：word-单词检测，phrase-短语检测，custom-自定义检测
    libraryId: '', // 词库ID
    unitId: '' // 分册ID
  },

  onLoad(options) {
    // 获取任务类型和词库信息
    const { type, library_id, unit_id } = options;
    this.setData({
      taskType: type,
      libraryId: library_id,
      unitId: unit_id
    });

    // 设置默认任务名称
    this.setDefaultTaskName();
  },

  // 设置默认任务名称
  setDefaultTaskName() {
    let defaultName = '';
    if (this.data.taskType === 'word') {
      defaultName = '单词检测';
    } else if (this.data.taskType === 'phrase') {
      defaultName = '短语检测';
    } else if (this.data.taskType === 'custom') {
      defaultName = '自定义检测';
    }

    if (this.data.libraryId) {
      defaultName += `-${this.data.libraryId}`;
    }
    if (this.data.unitId) {
      defaultName += `-${this.data.unitId}`;
    }

    this.setData({ taskName: defaultName });
  },

  // 任务名称输入
  onTaskNameInput(e) {
    this.setData({
      taskName: e.detail.value
    });
  },

  // 选择模式
  onModeSelect(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({ mode });
  },

  // 选择测试类型
  onTestTypeSelect(e) {
    const testType = e.currentTarget.dataset.type;
    this.setData({ testType });
  },

  // 选择时间限制
  onTimeLimitSelect(e) {
    const timeLimit = e.currentTarget.dataset.limit;
    this.setData({ timeLimit });
  },

  // 开始练习
  onStartPractice() {
    if (!this.data.taskName) {
      wx.showToast({
        title: '请输入任务名称',
        icon: 'none'
      });
      return;
    }

    // 构建任务数据
    const taskData = {
      name: this.data.taskName,
      type: this.data.taskType,
      mode: this.data.mode,
      libraryId: this.data.libraryId,
      unitId: this.data.unitId,
      testType: this.data.testType,
      timeLimit: this.data.timeLimit
    };

    // 根据测试类型跳转到不同页面
    if (this.data.testType === 'game') {
      // 消消乐模式需要先加载词汇数据
      this.startEliminationGame();
    } else {
      // 其他模式跳转到practice页面
      wx.navigateTo({
        url: `/pages/task/practice/practice?data=${JSON.stringify(taskData)}`
      });
    }
  },

  // 开始消消乐游戏
  async startEliminationGame() {
    try {
      wx.showLoading({
        title: '加载词汇数据...',
        mask: true
      });

      // 这里需要根据taskType、libraryId、unitId加载对应的词汇
      // 由于这个页面原本是用于创建任务的，我们需要引导用户去选择词汇
      wx.hideLoading();
      
      wx.showModal({
        title: '消消乐游戏',
        content: '消消乐游戏需要选择具体的词汇。是否要前往词库选择词汇？',
        confirmText: '选择词汇',
        cancelText: '取消',
                 success: (res) => {
           if (res.confirm) {
             // 设置全局返回标识
             const app = getApp();
             app.globalData.returnToElimination = true;
             
             // 跳转到词库选择页面
             wx.navigateTo({
               url: `/pages/wordbank/wordbank`
             });
           }
         }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('加载词汇数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 创建分享
  onCreateShare() {
    if (!this.data.taskName) {
      wx.showToast({
        title: '请输入任务名称',
        icon: 'none'
      });
      return;
    }

    // 构建任务数据
    const taskData = {
      name: this.data.taskName,
      type: this.data.taskType,
      mode: this.data.mode,
      libraryId: this.data.libraryId,
      unitId: this.data.unitId,
      testType: this.data.testType,
      timeLimit: this.data.timeLimit,
      creator: app.globalData.openid
    };

    // 保存任务到数据库
    const db = wx.cloud.database();
    db.collection('tasks').add({
      data: {
        ...taskData,
        createTime: db.serverDate(),
        status: 'active'
      },
      success: (res) => {
        // 生成分享卡片
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline']
        });

        // 跳转到分享页面
        wx.navigateTo({
          url: `/pages/task/share/share?taskId=${res._id}`
        });
      },
      fail: (error) => {
        console.error('创建任务失败:', error);
        wx.showToast({
          title: '创建失败',
          icon: 'none'
        });
      }
    });
  }
}); 