.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 统计信息栏样式 */
.statistics-bar {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6c757d;
}

/* 模式筛选栏样式 */
.mode-filter-bar {
  display: flex;
  justify-content: space-around;
  padding: 15rpx 20rpx;
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.mode-filter-item {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #6c757d;
  background: #f8f9fa;
  transition: all 0.3s;
  text-align: center;
  min-width: 120rpx;
}

.mode-filter-item.active {
  background: #007bff;
  color: white;
}

/* 操作栏样式 */
.action-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  padding: 24rpx 32rpx;
}

.main-actions {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.review-btn {
  background: #007AFF;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.export-btn {
  background: #28a745;
  color: white;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}

.manage-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: 1px solid #ddd;
  background: white;
  color: #333;
}

.manage-btn {
  background: #f8f8f8;
}

.sort-btn {
  background: #f0f8ff;
  color: #007AFF;
  border-color: #007AFF;
}



.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.3s;
  white-space: nowrap;
  min-width: 0;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 1rpx solid #dee2e6;
}

.action-btn.primary {
  background: #007bff;
  color: white;
}

.btn-icon {
  font-size: 24rpx;
  font-weight: bold;
}

/* 批量操作栏样式 */
.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background: #fff3cd;
  border-bottom: 1rpx solid #ffeaa7;
  min-height: 80rpx;
}

.batch-info {
  font-size: 28rpx;
  color: #856404;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.batch-buttons {
  display: flex;
  gap: 8rpx;
  flex-shrink: 0;
  flex-wrap: nowrap;
}

.batch-btn {
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  border: none;
  background: #ffc107;
  color: #212529;
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160rpx;
}

.batch-btn.secondary {
  background: #6c757d;
  color: white;
}

.batch-btn.primary {
  background: #007bff;
  color: white;
}

.batch-btn.danger {
  background: #dc3545;
  color: white;
}

/* 错词列表样式 */
.word-list {
  padding: 20rpx;
}

.word-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: white;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.word-item.edit-mode {
  padding-left: 60rpx;
  position: relative;
}

.word-item.selected {
  background: #e3f2fd;
  border: 2rpx solid #007bff;
}

.word-checkbox {
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}

.checkbox-icon {
  font-size: 32rpx;
}

.word-info {
  flex: 1;
}

.word-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.word-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #212529;
}

.error-count {
  font-size: 24rpx;
  color: #ff6b6b;
  background: #ffe0e0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.word-phonetic {
  font-size: 28rpx;
  color: #007bff;
  margin-bottom: 8rpx;
}

.word-meaning {
  font-size: 30rpx;
  color: #495057;
  margin-bottom: 8rpx;
}

.word-example {
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.word-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #adb5bd;
}

.word-actions {
  margin-left: 15rpx;
}

.action-btn.small {
  padding: 10rpx 15rpx;
  font-size: 24rpx;
  background: #28a745;
  color: white;
  border-radius: 12rpx;
}

/* 分页组件样式 */
.pagination {
  background: white;
  padding: 24rpx 32rpx;
  border-top: 1px solid #f0f0f0;
  margin-top: 20rpx;
}

.page-info {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.page-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.page-btn {
  padding: 12rpx 24rpx;
  border-radius: 6rpx;
  font-size: 26rpx;
  background: #f8f8f8;
  border: 1px solid #ddd;
  color: #333;
}

.page-btn[disabled] {
  opacity: 0.5;
  color: #999;
}

.page-jump {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #666;
}

.page-input {
  width: 80rpx;
  height: 60rpx;
  border: 1px solid #ddd;
  border-radius: 4rpx;
  text-align: center;
  font-size: 26rpx;
  padding: 0 8rpx;
}

.page-current {
  color: #007AFF;
  font-weight: bold;
  text-decoration: underline;
  cursor: pointer;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #6c757d;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #adb5bd;
  margin-bottom: 40rpx;
}

.empty-action {
  background: #007bff;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  border: none;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.add-word-modal {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #212529;
}

.modal-close {
  font-size: 40rpx;
  color: #6c757d;
  line-height: 1;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #495057;
  margin-bottom: 10rpx;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #dee2e6;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #495057;
  background: #f8f9fa;
}

.form-textarea {
  min-height: 120rpx;
  resize: none;
}

.form-input:focus, .form-textarea:focus {
  border-color: #007bff;
  background: white;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #e9ecef;
}

.modal-btn {
  padding: 20rpx 40rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn.cancel {
  background: #f8f9fa;
  color: #6c757d;
}

.modal-btn.confirm {
  background: #007bff;
  color: white;
} 