const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const mistakesCollection = db.collection('mistakes')
const wordsCollection = db.collection('words')

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const {
    userId,
    type,
    libraryId,
    testMode,
    skip = 0,
    limit = 20
  } = event

  try {
    // 使用openid作为用户标识
    const openid = userId || wxContext.OPENID
    let query = mistakesCollection.where({
      $or: [
        { userId: openid },
        { openid: openid }
      ]
    })

    // 按类型过滤
    if (type) {
      query = query.where({ type })
    }

    // 按词库过滤
    if (libraryId) {
      query = query.where({ libraryId })
    }

    // 按测试模式过滤
    if (testMode) {
      query = query.where({ testMode })
    }

    // 按时间倒序排列
    const res = await query
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()

    console.log(`获取错词: 用户${openid}, 词库${libraryId}, 模式${testMode}, 数量${res.data.length}`)

    return {
      success: true,
      code: 200,
      data: res.data,
      total: res.data.length
    }
  } catch (e) {
    console.error('获取错词失败:', e)
    return {
      success: false,
      code: 500,
      message: '获取失败',
      error: e.message
    }
  }
}