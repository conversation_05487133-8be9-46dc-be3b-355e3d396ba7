<view class="container">
  <!-- 任务名称 -->
  <view class="section">
    <view class="section-title">任务名称</view>
    <input class="task-name-input" placeholder="请输入任务名称" value="{{taskName}}" bindinput="onTaskNameInput" />
  </view>

  <!-- 选择模式 -->
  <view class="section">
    <view class="section-title">选择模式</view>
    <view class="mode-list">
      <view class="mode-item {{mode === 'practice' ? 'active' : ''}}" bindtap="onModeSelect" data-mode="practice">
        <view class="mode-name">练习模式</view>
        <view class="mode-desc">直接背诵，不计入错题本</view>
      </view>
      <view class="mode-item {{mode === 'test' ? 'active' : ''}}" bindtap="onModeSelect" data-mode="test">
        <view class="mode-name">测试模式</view>
        <view class="mode-desc">计入错题本</view>
      </view>
    </view>
  </view>

  <!-- 练习/测试类型选择 -->
  <view class="section">
    <view class="section-title">{{mode === 'practice' ? '练习类型' : '测试类型'}}</view>
    <view class="test-type-list">
      <view class="test-type-item {{testType === 'en2zh' ? 'active' : ''}}" bindtap="onTestTypeSelect" data-type="en2zh">
        <view class="test-type-name">英译汉</view>
      </view>
      <view class="test-type-item {{testType === 'zh2en' ? 'active' : ''}}" bindtap="onTestTypeSelect" data-type="zh2en">
        <view class="test-type-name">汉译英</view>
      </view>
      <view class="test-type-item {{testType === 'game' ? 'active' : ''}}" bindtap="onTestTypeSelect" data-type="game">
        <view class="test-type-name">单词消消乐</view>
      </view>
    </view>
  </view>

  <!-- 挑战设置（仅测试模式显示） -->
  <block wx:if="{{mode === 'test'}}">
    <view class="section">
      <view class="section-title">挑战设置</view>
      <view class="challenge-list">
        <view class="challenge-item">
          <view class="challenge-name">时间限制</view>
          <view class="challenge-options">
            <view class="challenge-option {{timeLimit === 'unlimited' ? 'active' : ''}}" bindtap="onTimeLimitSelect" data-limit="unlimited">
              不限时
            </view>
            <view class="challenge-option {{timeLimit === '10' ? 'active' : ''}}" bindtap="onTimeLimitSelect" data-limit="10">
              10秒
            </view>
            <view class="challenge-option {{timeLimit === '15' ? 'active' : ''}}" bindtap="onTimeLimitSelect" data-limit="15">
              15秒
            </view>
            <view class="challenge-option {{timeLimit === '20' ? 'active' : ''}}" bindtap="onTimeLimitSelect" data-limit="20">
              20秒
            </view>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- 底部按钮 -->
  <view class="bottom-buttons">
    <button class="btn btn-primary" bindtap="onStartPractice">开始练习</button>
    <button class="btn btn-share" bindtap="onCreateShare">创建分享</button>
  </view>
</view> 