.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-container {
  background: #fff;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 600rpx;
}

.header {
  text-align: center;
  margin-bottom: 48rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.qrcode-wrapper {
  text-align: center;
  margin-bottom: 48rpx;
}

.qrcode-image {
  width: 400rpx;
  height: 400rpx;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  background: #f8f9fa;
}

.qrcode-tip {
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #999;
}

.service-info {
  margin-bottom: 48rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.info-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  width: 40rpx;
  text-align: center;
}

.info-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

.actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
}

.action-btn.primary:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.action-btn.secondary:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}
