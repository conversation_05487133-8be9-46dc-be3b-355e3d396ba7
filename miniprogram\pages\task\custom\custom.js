const app = getApp();

Page({
  data: {
    inputText: '', // 输入的文本
    showResult: false, // 是否显示检测结果
    words: [], // 检测到的单词列表
    showMnemonicModal: false, // 是否显示记忆提示弹窗
    currentWord: null, // 当前查看的单词
    shareMode: false // 是否为分享模式
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { shareMode } = options;
    
    // 检查是否为分享模式创建测试
    if (shareMode === 'create') {
      console.log('=== 自定义测试分享模式 ===');
      
      this.setData({
        shareMode: true
      });
    }
  },

  // 输入文本变化
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    });
  },

  // 开始检测
  async onStartDetection() {
    if (!this.data.inputText) return;

    wx.showLoading({
      title: '正在检测...'
    });

    try {
      // 调用云函数进行文本检测
      const res = await wx.cloud.callFunction({
        name: 'detectWords',
        data: {
          text: this.data.inputText
        }
      });

      // 处理检测结果
      const words = res.result.words.map(word => ({
        ...word,
        mnemonic: this.generateMnemonic(word)
      }));

      this.setData({
        words,
        showResult: true
      });

      wx.hideLoading();
    } catch (error) {
      console.error('检测失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '检测失败',
        icon: 'none'
      });
    }
  },

  // 生成记忆提示
  generateMnemonic(word) {
    // 生成简单的记忆提示
    return `记忆提示：${word.word} 的发音类似于...`;
  },

  // 播放音频
  playAudio(e) {
    const word = e.currentTarget.dataset.word;
    if (!word.audioUrl) return;

    const innerAudioContext = wx.createInnerAudioContext();
    innerAudioContext.src = word.audioUrl;
    innerAudioContext.play();
  },

  // 显示记忆提示
  showMnemonic(e) {
    const word = e.currentTarget.dataset.word;
    this.setData({
      currentWord: word,
      showMnemonicModal: true
    });
  },

  // 隐藏记忆提示
  hideMnemonic() {
    this.setData({
      showMnemonicModal: false,
      currentWord: null
    });
  },

  // 添加到词库
  async addToLibrary(e) {
    const word = e.currentTarget.dataset.word;
    const db = wx.cloud.database();

    try {
      // 检查单词是否已存在
      const existRes = await db.collection('libraries').where({
        word: word.word
      }).get();

      if (existRes.data.length > 0) {
        wx.showToast({
          title: '单词已存在',
          icon: 'none'
        });
        return;
      }

      // 添加单词到词库
      await db.collection('libraries').add({
        data: {
          ...word,
          createTime: db.serverDate(),
          openid: app.globalData.openid
        }
      });

      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('添加失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    }
  },

  // 开始练习
  onStartPractice() {
    if (this.data.words.length === 0) return;

    if (this.data.shareMode) {
      // 分享模式 - 创建分享测试
      this.createCustomShareTest();
    } else {
      // 普通模式 - 开始练习
      this.startNormalPractice();
    }
  },

  // 开始普通练习
  startNormalPractice() {
    // 显示测试类型选择
    wx.showActionSheet({
      itemList: ['英译汉', '汉译英', '单词消消乐'],
      success: (res) => {
        let testType = '';
        let targetUrl = '';
        
        switch (res.tapIndex) {
          case 0: // 英译汉
            testType = 'en2zh';
            targetUrl = `/pages/task/practice/practice?data=${JSON.stringify({
              mode: 'practice',
              testType: 'en2zh',
              timeLimit: 'unlimited',
              words: this.data.words
            })}`;
            break;
          case 1: // 汉译英
            testType = 'zh2en';
            targetUrl = `/pages/task/practice/practice?data=${JSON.stringify({
              mode: 'practice',
              testType: 'zh2en',
              timeLimit: 'unlimited',
              words: this.data.words
            })}`;
            break;
          case 2: // 消消乐
            // 转换数据格式为消消乐需要的格式
            const gameWords = this.data.words.map(word => ({
              english: word.word,
              chinese: word.definition
            }));
            
            const app = getApp();
            app.globalData.eliminationGameData = {
              words: gameWords,
              currentGroup: 1,
              totalGroups: 1,
              allWords: gameWords,
              libraryId: 'custom',
              libraryName: '自定义单词'
            };
            
            targetUrl = '/pages/task/puzzle/puzzle?mode=custom&gameMode=practice&group=1&total=1';
            break;
        }
        
        wx.navigateTo({
          url: targetUrl,
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '跳转失败',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  // 创建自定义分享测试
  async createCustomShareTest() {
    const { words } = this.data;

    if (words.length === 0) {
      wx.showToast({
        title: '请先检测单词',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '创建分享测试...',
      mask: true
    });

    try {
      // 获取当前用户信息
      const currentUser = wx.getStorageSync('userInfo') || {};
      const shareId = 'custom_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

      // 创建分享测试数据
      const shareTestData = {
        shareId: shareId,
        testMode: 'custom',
        libraryId: 'custom_words',
        libraryName: '自定义词汇',
        words: words,
        createdBy: currentUser.nickName || '匿名用户',
        creatorInfo: {
          openid: currentUser.openid,
          nickName: currentUser.nickName,
          avatarUrl: currentUser.avatarUrl
        },
        createTime: Date.now(),
        visitors: [],
        results: []
      };

      // 保存到本地存储（只保存必要的元数据）
      try {
        const shareTests = wx.getStorageSync('shareTests') || {};

        // 为了避免本地存储大小限制，只保存必要的元数据
        const lightShareTestData = {
          shareId: shareTestData.shareId,
          testMode: shareTestData.testMode,
          libraryId: shareTestData.libraryId,
          libraryName: shareTestData.libraryName,
          wordsCount: shareTestData.words ? shareTestData.words.length : 0, // 只保存词汇数量
          createdBy: shareTestData.createdBy,
          creatorInfo: shareTestData.creatorInfo,
          createTime: shareTestData.createTime,
          // 不保存完整的words数组，以节省存储空间
          visitors: [],
          results: []
        };

        shareTests[shareId] = lightShareTestData;
        wx.setStorageSync('shareTests', shareTests);
        console.log('自定义分享测试已保存到本地:', shareId, '词汇数量:', lightShareTestData.wordsCount);
      } catch (error) {
        console.error('保存到本地存储失败:', error);
        console.warn('本地存储失败，但云端保存成功，分享功能仍可正常使用');
      }

      // 保存到云端（必须成功）
      try {
        // 自定义检测不能使用索引存储，因为词汇可能不在系统词库中
        // 用户可能上传系统词库中不存在的词汇，必须传递完整数据
        const useIndexBasedStorage = false;

        await wx.cloud.callFunction({
          name: 'createShareTest',
          data: {
            shareId: shareTestData.shareId,
            testType: shareTestData.testMode,
            // 超级优化：基于索引存储时只传递必要信息
            words: useIndexBasedStorage ?
              [{
                _id: shareTestData.words[0]._id,
                words: shareTestData.words[0].words,
                totalCount: shareTestData.words.length
              }] :
              shareTestData.words,
            libraryId: shareTestData.libraryId,
            libraryName: shareTestData.libraryName,
            // 自定义词库通常不需要乱序，但为了兼容性还是传递
            isRandomOrder: false,
            settings: {},
            expireDays: 7,
            wordsPerGroup: 20
          }
        });
        console.log('自定义分享测试已成功保存到云端和本地');
      } catch (err) {
        console.error('云端保存失败:', err);
        wx.hideLoading();
        wx.showModal({
          title: '保存失败',
          content: '分享测试保存到云端失败，可能是网络问题。请检查网络连接后重试。',
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }

      wx.hideLoading();
      
      // 显示分享选项
      wx.showActionSheet({
        itemList: ['复制分享链接', '分享到微信', '查看分享管理'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.copyShareLink(shareId);
          } else if (res.tapIndex === 1) {
            this.shareToWeChat(shareId, words.length);
          } else if (res.tapIndex === 2) {
            this.goToShareManagement();
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('创建分享测试失败:', error);
      wx.showToast({
        title: '创建失败',
        icon: 'error'
      });
    }
  },

  // 复制分享链接
  copyShareLink(shareId) {
    const shareUrl = `pages/task/custom/custom?shareId=${shareId}&shareMode=share`;
    
    wx.setClipboardData({
      data: shareUrl,
      success: () => {
        wx.showToast({ title: '链接已复制', icon: 'success' });
      },
      fail: () => {
        wx.showToast({ title: '复制失败', icon: 'error' });
      }
    });
  },

  // 分享到微信
  shareToWeChat(shareId, wordCount) {
    const shareTitle = `📝 自定义单词检测 - ${wordCount}个单词`;
    const sharePath = `pages/task/custom/custom?shareId=${shareId}&shareMode=share`;

    // 主动触发分享
    wx.shareAppMessage({
      title: shareTitle,
      path: sharePath,
      imageUrl: '/assets/icons/logo.png',
      success: () => {
        // 显示分享成功弹窗，提供返回和查看分享页选项
        this.showShareSuccessModal();
      },
      fail: () => {
        wx.showToast({ title: '分享取消', icon: 'none' });
      }
    });
  },

  /**
   * 显示分享成功弹窗
   */
  showShareSuccessModal() {
    wx.showModal({
      title: '分享成功',
      content: '自定义测试已成功分享给好友！',
      cancelText: '返回',
      confirmText: '查看分享页',
      success: (res) => {
        if (res.confirm) {
          // 用户点击"查看分享页"
          wx.navigateTo({
            url: '/pages/profile/share/share',
            success: () => {
              console.log('跳转到我的分享页面成功');
            },
            fail: (err) => {
              console.error('跳转失败:', err);
              wx.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
        } else if (res.cancel) {
          // 用户点击"返回"，不做任何操作，保持在当前页面
          console.log('用户选择返回当前页面');
        }
      }
    });
  },

  // 跳转到分享管理
  goToShareManagement() {
    wx.navigateTo({
      url: '/pages/profile/share/share',
      fail: (err) => {
        console.error('跳转到我的分享页面失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  }
}); 