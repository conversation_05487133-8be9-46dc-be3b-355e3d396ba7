.word-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.word-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.word-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
}

.word-phonetic {
  font-size: 28rpx;
  color: #888;
  margin-left: 16rpx;
}

.word-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
}

.word-content {
  margin-top: 24rpx;
}

.meaning-item {
  margin-bottom: 12rpx;
}

.part-of-speech {
  color: #4A90E2;
  margin-right: 8rpx;
}

.definition-item {
  margin-left: 16rpx;
  color: #333;
}

.example-text {
  display: block;
  color: #888;
  font-size: 26rpx;
  margin-top: 4rpx;
} 