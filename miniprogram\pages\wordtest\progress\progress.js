const app = getApp();

Page({
  data: {
    libraryId: '',
    libraryName: '',
    testMode: '', // en_to_cn, cn_to_en, elimination, fullbook
    modeText: '',
    
    // 进度数据
    totalWords: 0,
    learnedWords: 0,
    progressPercent: 0,
    lastStudyTime: '',
    
    // 学习统计
    correctCount: 0,
    wrongCount: 0,
    accuracy: 0,
    totalStudyTime: 0,
    
    // 状态
    hasProgress: false,
    loading: true,
    
    // 全书学习模式
    isFullBookMode: false,
    isRandom: false
  },

  onLoad: function(options) {
    const { libraryId, testMode, mode, isRandom } = options;
    
    if (!libraryId) {
      wx.showToast({ title: '参数错误', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 处理全书学习模式
    if (mode === 'fullbook') {
      const fullBookData = app.globalData.fullBookLearningData;
      if (!fullBookData) {
        wx.showToast({ title: '数据加载失败', icon: 'error' });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      this.setData({
        libraryId,
        testMode: 'fullbook',
        modeText: isRandom === 'true' ? '全书乱序学习' : '全书顺序学习',
        libraryName: fullBookData.libraryName || '词库',
        isFullBookMode: true,
        isRandom: isRandom === 'true'
      });

      this.loadFullBookProgress();
      return;
    }

    // 原有的测试模式逻辑
    if (!testMode) {
      wx.showToast({ title: '参数错误', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    const modeTextMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'elimination': '单词消消乐'
    };

    this.setData({
      libraryId,
      testMode,
      modeText: modeTextMap[testMode] || '未知模式',
      libraryName: app.globalData.learningData?.libraryName || '词库',
      isFullBookMode: false
    });

    this.loadProgress();
  },

  // 加载全书学习进度
  loadFullBookProgress: function() {
    const { libraryId } = this.data;
    const fullBookData = app.globalData.fullBookLearningData;
    
    try {
      // 从本地存储获取进度
      const progressKey = `fullbook_progress_${libraryId}`;
      const progress = wx.getStorageSync(progressKey);
      
      if (progress && progress.currentIndex > 0) {
        // 有学习进度
        this.setData({
          hasProgress: true,
          totalWords: fullBookData.words.length,
          learnedWords: progress.currentIndex,
          progressPercent: Math.round((progress.currentIndex / fullBookData.words.length) * 100),
          lastStudyTime: this.formatTime(progress.lastStudyTime),
          correctCount: progress.correctCount || 0,
          wrongCount: progress.wrongCount || 0,
          accuracy: progress.totalAnswered > 0 ? 
            Math.round((progress.correctCount / progress.totalAnswered) * 100) : 0,
          totalStudyTime: progress.totalStudyTime || 0,
          loading: false
        });
      } else {
        // 没有学习进度
        this.setData({
          hasProgress: false,
          totalWords: fullBookData.words.length,
          loading: false
        });
      }
    } catch (error) {
      console.error('加载全书学习进度失败:', error);
      this.setData({
        hasProgress: false,
        totalWords: fullBookData.words.length,
        loading: false
      });
    }
  },

  // 加载学习进度
  loadProgress: function() {
    const { libraryId, testMode } = this.data;
    const userInfo = app.globalData.userInfo;
    
    if (!userInfo || !userInfo.openid) {
      this.setData({ loading: false });
      return;
    }

    wx.cloud.callFunction({
      name: 'getLearningProgress',
      data: {
        libraryId,
        testMode,
        openid: userInfo.openid
      }
    }).then(result => {
      if (result.result.code === 200) {
        const progress = result.result.data;
        
        if (progress) {
          // 有学习进度
          this.setData({
            hasProgress: true,
            totalWords: progress.totalWords || 0,
            learnedWords: progress.learnedWords || 0,
            progressPercent: progress.totalWords > 0 ? 
              Math.round((progress.learnedWords / progress.totalWords) * 100) : 0,
            lastStudyTime: this.formatTime(progress.lastStudyTime),
            correctCount: progress.correctCount || 0,
            wrongCount: progress.wrongCount || 0,
            accuracy: progress.totalQuestions > 0 ? 
              Math.round((progress.correctCount / progress.totalQuestions) * 100) : 0,
            totalStudyTime: progress.totalStudyTime || 0,
            loading: false
          });
        } else {
          // 没有学习进度
          this.setData({
            hasProgress: false,
            loading: false
          });
        }
      } else {
        console.error('获取学习进度失败:', result.result);
        this.setData({
          hasProgress: false,
          loading: false
        });
      }
    }).catch(error => {
      console.error('获取学习进度失败:', error);
      this.setData({
        hasProgress: false,
        loading: false
      });
    });
  },

  // 继续上次进度
  continueProgress: function() {
    const { libraryId, testMode, isFullBookMode, isRandom } = this.data;
    
    wx.showLoading({ title: '加载中...' });
    
    if (isFullBookMode) {
      // 全书学习模式，直接跳转到学习页面
      const url = `/pages/learning/learning?mode=fullbook&libraryId=${libraryId}&isRandom=${isRandom}&continue=true`;
      console.log('继续全书学习，跳转URL:', url);
      
      wx.navigateTo({
        url: url,
        success: () => {
          wx.hideLoading();
          console.log('跳转到全书学习页面成功');
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('跳转到全书学习页面失败:', err);
          wx.showToast({ 
            title: '跳转失败，请重试', 
            icon: 'error',
            duration: 2000
          });
        }
      });
    } else {
      // 原有的测试模式逻辑
      wx.navigateTo({
        url: `/pages/wordbank/wordlist/wordlist?libraryId=${libraryId}&mode=test&testMode=${testMode}&continue=true`,
        success: () => {
          wx.hideLoading();
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('跳转失败:', err);
          wx.showToast({ 
            title: '跳转失败，请重试', 
            icon: 'error',
            duration: 2000
          });
        }
      });
    }
  },

  // 重新开始
  restartProgress: function() {
    const { isFullBookMode } = this.data;
    const content = isFullBookMode ? 
      '重新开始将清除当前的全书学习进度，是否确认？' : 
      '重新开始将清除当前的学习进度，是否确认？';
      
    wx.showModal({
      title: '确认重新开始',
      content: content,
      confirmText: '确认',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.clearProgress();
        }
      }
    });
  },

  // 清除进度
  clearProgress: function() {
    const { libraryId, testMode, isFullBookMode } = this.data;
    
    if (isFullBookMode) {
      // 清除全书学习进度
      try {
        const progressKey = `fullbook_progress_${libraryId}`;
        wx.removeStorageSync(progressKey);
        console.log('全书学习进度已清除');
      } catch (error) {
        console.error('清除全书学习进度失败:', error);
      }
      this.goToWordSelection();
      return;
    }

    // 原有的云端进度清除逻辑
    const userInfo = app.globalData.userInfo;
    
    if (!userInfo || !userInfo.openid) {
      this.goToWordSelection();
      return;
    }

    wx.showLoading({ title: '清除进度中...' });
    
    wx.cloud.callFunction({
      name: 'clearLearningProgress',
      data: {
        libraryId,
        testMode,
        openid: userInfo.openid
      }
    }).then(result => {
      wx.hideLoading();
      this.goToWordSelection();
    }).catch(error => {
      wx.hideLoading();
      console.error('清除进度失败:', error);
      this.goToWordSelection();
    });
  },

  // 跳转到词汇选择
  goToWordSelection: function() {
    const { libraryId, testMode, isFullBookMode, isRandom } = this.data;
    
    wx.showLoading({ title: '加载中...' });
    
    if (isFullBookMode) {
      // 全书学习模式，直接跳转到学习页面
      const url = `/pages/learning/learning?mode=fullbook&libraryId=${libraryId}&isRandom=${isRandom}`;
      console.log('开始全书学习，跳转URL:', url);
      
      wx.redirectTo({
        url: url,
        success: () => {
          wx.hideLoading();
          console.log('跳转到全书学习页面成功');
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('跳转到全书学习页面失败:', err);
          wx.showToast({ 
            title: '跳转失败，请重试', 
            icon: 'error',
            duration: 2000
          });
        }
      });
    } else {
      // 原有的测试模式逻辑
      wx.redirectTo({
        url: `/pages/wordbank/wordlist/wordlist?libraryId=${libraryId}&mode=test&testMode=${testMode}`,
        success: () => {
          wx.hideLoading();
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('跳转失败:', err);
          wx.showToast({ 
            title: '跳转失败，请重试', 
            icon: 'error',
            duration: 2000
          });
        }
      });
    }
  },

  // 查看详细统计
  viewDetailedStats: function() {
    const { libraryId, testMode } = this.data;
    wx.navigateTo({
      url: `/pages/profile/report/report?libraryId=${libraryId}&testMode=${testMode}`
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 格式化时间
  formatTime: function(timestamp) {
    if (!timestamp) return '暂无记录';
    
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) {
      return '刚刚';
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return time.toLocaleDateString();
    }
  },

  // 格式化学习时长
  formatStudyTime: function(seconds) {
    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}分钟`;
    } else {
      return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分钟`;
    }
  }
}); 