/* 关于页面样式 - 现代化精美设计 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 精美头部 */
.header {
  position: relative;
  padding: 60rpx 40rpx 50rpx;
  background: white;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 12rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  background-size: 200% 100%;
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.bg-wave {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.app-logo {
  margin-right: 32rpx;
}

.logo-circle {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-icon {
  font-size: 64rpx;
  z-index: 2;
  position: relative;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8rpx); }
}

.logo-ring {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 4rpx solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2) border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: exclude;
  animation: rotate 4s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.app-info {
  flex: 1;
  text-align: left;
}

.app-name {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.app-slogan {
  font-size: 26rpx;
  color: #4a5568;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.tagline {
  font-size: 22rpx;
  color: #718096;
  font-style: italic;
}

.version-badge {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
  overflow: hidden;
}

.version-text {
  font-size: 22rpx;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.badge-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
  animation: glow 2s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

/* 内容区域 */
.content {
  padding: 30rpx;
}

/* 卡片入场动画 */
.card-entrance {
  animation: slideUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(40rpx);
}

.card-entrance:nth-child(1) { animation-delay: 0.1s; }
.card-entrance:nth-child(2) { animation-delay: 0.2s; }
.card-entrance:nth-child(3) { animation-delay: 0.3s; }
.card-entrance:nth-child(4) { animation-delay: 0.4s; }
.card-entrance:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 通用卡片样式 */
.intro-card,
.features-card,
.modules-card,
.contact-card,
.settings-card,
.vision-card {
  background: white;
  border-radius: 28rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f1f5f9;
  position: relative;
  overflow: hidden;
}

/* 产品简介卡片 */
.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.intro-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #2d3748;
  margin-right: 16rpx;
}

.title-accent {
  flex: 1;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}

.intro-text {
  font-size: 30rpx;
  color: #4a5568;
  line-height: 1.8;
  margin-bottom: 40rpx;
}

.stats-container {
  position: relative;
}

.stats-row {
  display: flex;
  justify-content: space-around;
}

.animated-stat {
  text-align: center;
  position: relative;
  padding-bottom: 20rpx;
}

.stat-num {
  display: block;
  font-size: 44rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 8rpx;
}

.stat-desc {
  font-size: 24rpx;
  color: #718096;
  font-weight: 500;
}

.stat-progress {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 4rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
  animation: progressFill 2s ease-out 0.5s forwards;
  width: 0;
}

@keyframes progressFill {
  to { width: var(--progress-width, 100%); }
}

/* 卡片标题样式 */
.card-header {
  margin-bottom: 32rpx;
}

.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.gradient-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
}

.card-subtitle {
  font-size: 26rpx;
  color: #718096;
  font-style: italic;
}

/* 核心优势网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.feature-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 25rpx rgba(0, 0, 0, 0.1);
}

.feature-icon-bg {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.blue-bg { background: linear-gradient(135deg, #4A90E2 0%, #007AFF 100%); }
.green-bg { background: linear-gradient(135deg, #34C759 0%, #00C851 100%); }
.purple-bg { background: linear-gradient(135deg, #8E44AD 0%, #9B59B6 100%); }
.orange-bg { background: linear-gradient(135deg, #FF8C00 0%, #FF6347 100%); }

.feature-emoji {
  font-size: 32rpx;
  color: white;
}

.feature-content {
  flex: 1;
}

.feature-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 6rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #718096;
  line-height: 1.4;
}

/* 功能展示 */
.modules-showcase {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.module-category {
  text-align: center;
}

.category-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #4a5568;
  margin-bottom: 20rpx;
  position: relative;
}

.category-title::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 3rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}

.modules-row {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.module-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 28rpx 20rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.module-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
}

.blue-module { background: rgba(74, 144, 226, 0.1); }
.blue-module::before { background: #4A90E2; }

.green-module { background: rgba(52, 199, 89, 0.1); }
.green-module::before { background: #34C759; }

.purple-module { background: rgba(142, 68, 173, 0.1); }
.purple-module::before { background: #8E44AD; }

.teal-module { background: rgba(32, 178, 170, 0.1); }
.teal-module::before { background: #20B2AA; }

.red-module { background: rgba(255, 107, 107, 0.1); }
.red-module::before { background: #FF6B6B; }

.orange-module { background: rgba(255, 140, 0, 0.1); }
.orange-module::before { background: #FF8C00; }

.module-item:hover {
  transform: translateY(-6rpx);
  box-shadow: 0 12rpx 25rpx rgba(0, 0, 0, 0.1);
}

.module-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}

.module-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #2d3748;
}

/* 联系我们 */
.contact-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2rpx solid transparent;
}

.contact-item:active {
  transform: scale(0.98);
  background: #edf2f7;
  border-color: #667eea;
}

.contact-icon-wrapper {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.contact-icon {
  font-size: 28rpx;
  color: white;
}

.contact-info {
  flex: 1;
}

.contact-label {
  display: block;
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 4rpx;
}

.contact-text {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 500;
}

.copy-action {
  padding: 8rpx 16rpx;
  background: #667eea;
  color: white;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
}

/* 品牌理念卡片 */
.vision-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.vision-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

.vision-content {
  position: relative;
  z-index: 2;
}

.vision-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  animation: twinkle 2s ease-in-out infinite;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.8; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

.vision-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.vision-text {
  font-size: 28rpx;
  line-height: 1.8;
  opacity: 0.9;
}

/* 页脚 */
.footer {
  padding: 40rpx;
  background: white;
  margin-top: 20rpx;
}

.footer-content {
  text-align: center;
}

.copyright {
  display: block;
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 16rpx;
}

.footer-links {
  margin-bottom: 16rpx;
}

.footer-link {
  font-size: 22rpx;
  color: #4a5568;
  cursor: pointer;
}

.footer-link:hover {
  color: #667eea;
}

.divider {
  margin: 0 16rpx;
  color: #cbd5e0;
}

.footer-motto {
  font-size: 26rpx;
  color: #667eea;
  font-style: italic;
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .modules-row {
    flex-direction: column;
  }
  
  .module-item {
    margin-bottom: 16rpx;
  }
}

/* 应用设置卡片样式 */
.settings-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2rpx solid transparent;
}

.setting-item:active {
  transform: scale(0.98);
  background: #edf2f7;
  border-color: #667eea;
}

.update-item:active {
  border-color: #10b981;
}

.setting-icon-wrapper {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  animation: pulse 2s ease-in-out infinite;
}

.update-item .setting-icon-wrapper {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.9; }
}

.setting-icon {
  font-size: 28rpx;
  color: white;
}

.setting-info {
  flex: 1;
}

.setting-label {
  display: block;
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.setting-desc {
  font-size: 22rpx;
  color: #718096;
}

.setting-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  background: #10b981;
  color: white;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.action-arrow {
  font-size: 24rpx;
  color: #cbd5e0;
  font-weight: bold;
}

.version-status {
  padding: 6rpx 12rpx;
  background: #f0f9ff;
  color: #0284c7;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  border: 1rpx solid #e0f2fe;
}

.version-item {
  cursor: default;
}

.version-item:active {
  transform: none;
  background: #f8fafc;
  border-color: transparent;
} 