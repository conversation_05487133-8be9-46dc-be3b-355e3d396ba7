const ebbinghaus = require('../../utils/ebbinghaus');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    progressList: [],
    loading: true,
    empty: false,
    editMode: false,
    selectedCount: 0    // 选中的项目数量
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadLearningProgress();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 重新加载进度，以防有新的学习记录
    this.loadLearningProgress();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadLearningProgress();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '墨词自习室 - 我的学习进度',
      path: '/pages/index/index'
    }
  },

  // 切换编辑模式
  toggleEditMode() {
    const { progressList } = this.data;

    // 退出编辑模式时重置所有选中状态
    const updatedList = progressList.map(item => ({
      ...item,
      isSelected: false
    }));

    this.setData({
      editMode: !this.data.editMode,
      progressList: updatedList,
      selectedCount: 0
    });
  },

  // 继续新学
  continueNewStudy(e) {
    const item = e.currentTarget.dataset.item;
    this.continueStudy(e);
  },

  // 开始复习
  startReview(e) {
    const item = e.currentTarget.dataset.item;
    console.log('开始复习，点击的项目:', item);
    console.log('reviewInfo:', item.reviewInfo);
    console.log('totalReviewWords:', item.reviewInfo ? item.reviewInfo.totalReviewWords : 'reviewInfo不存在');

    // 检查是否有需要复习的词汇
    if (!item.reviewInfo || item.reviewInfo.totalReviewWords === 0) {
      console.log('没有需要复习的词汇，显示提示');
      wx.showToast({
        title: '暂无需要复习的词汇',
        icon: 'none'
      });
      return;
    }

    console.log('准备跳转到复习页面');
    // 跳转到复习页面
    wx.navigateTo({
      url: `/pages/review/review?libraryId=${item.libraryId}&mode=${item.mode}`
    });
  },



  // 全选/取消全选
  toggleSelectAll() {
    const { progressList } = this.data;
    const allSelected = progressList.every(item => item.isSelected);
    
    const updatedList = progressList.map(item => ({
      ...item,
      isSelected: !allSelected
    }));
    
    this.setData({
      progressList: updatedList
    });
  },

  // 切换单项选择
  toggleItemSelect(e) {
    const { key } = e.currentTarget.dataset;
    const { progressList } = this.data;
    
    const updatedList = progressList.map(item => {
      if (item.key === key) {
        return {
          ...item,
          isSelected: !item.isSelected
        };
      }
      return item;
    });
    
    this.setData({
      progressList: updatedList
    });
  },

  // 批量删除
  batchDelete() {
    const { progressList } = this.data;
    const selectedItems = progressList.filter(item => item.isSelected);
    const selectedKeys = selectedItems.map(item => item.key);
    
    if (selectedKeys.length === 0) {
      wx.showToast({
        title: '请选择要删除的进度',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedKeys.length} 个学习进度吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteProgressItems(selectedKeys);
        }
      }
    });
  },

  // 删除指定的进度项
  deleteProgressItems(keys) {
    wx.showModal({
      title: '提示',
      content: '云端学习进度数据无法直接删除，如需清除请在对应的学习页面中重置进度。',
      showCancel: false,
      confirmText: '知道了'
    });

    // 退出编辑模式
    this.setData({
      editMode: false,
      progressList: this.data.progressList.map(item => ({
        ...item,
        isSelected: false
      })),
      selectedCount: 0
    });
  },

  // 加载学习进度
  loadLearningProgress() {
    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      // 只从云端获取学习进度
      this.loadProgressFromCloud().then(cloudProgressList => {
        console.log('云端学习进度数据:', cloudProgressList);

        if (cloudProgressList.length === 0) {
          this.setData({
            progressList: [],
            loading: false,
            empty: true
          });
        } else {
          // 为每个项目添加选中状态和新的显示字段
          const processedData = cloudProgressList.map(item => {
            console.log('处理学习进度项目:', item.libraryName, item.mode);
            console.log('原始reviewInfo:', item.reviewInfo);
            const processed = {
              ...item,
              isSelected: false,
              groupProgress: this.calculateGroupProgress(item),
              newStudyProgress: this.calculateNewStudyProgress(item),
              reviewProgress: this.calculateReviewProgress(item)
            };
            console.log('处理后的reviewInfo:', processed.reviewInfo);
            return processed;
          });

          this.setData({
            progressList: processedData,
            loading: false,
            empty: false
          });
        }

        wx.hideLoading();
      }).catch(error => {
        console.error('从云端加载学习进度失败:', error);

        // 云端加载失败，显示空状态
        this.setData({
          progressList: [],
          loading: false,
          empty: true
        });

        wx.hideLoading();
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      });
    } catch (error) {
      console.error('加载学习进度失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 从云端加载学习进度
  loadProgressFromCloud() {
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'getLearningProgress',
        data: {
          includeReviewWords: true // 包含复习词汇信息
        }
      }).then(result => {
        if (result.result && result.result.success) {
          const cloudProgressList = result.result.data.map(progress => ({
            ...progress,
            key: `cloud_progress_${progress.libraryId}_${progress.mode}`,
            modeText: this.getModeText(progress.mode),
            lastStudyTimeText: this.formatTime(progress.updateTime),
            isDemo: false,
            isCloudData: true // 标记为云端数据
          }));

          console.log('从云端获取到学习进度:', cloudProgressList.length, '条');
          resolve(cloudProgressList);
        } else {
          reject(new Error('云端返回数据格式错误'));
        }
      }).catch(error => {
        reject(error);
      });
    });
  },







  // 获取模式文本
  getModeText(mode) {
    const modeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写',
      'elimination': '消消乐',
      'default': '默认模式'
    };
    return modeMap[mode] || mode;
  },

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '未知时间';
    
    const now = Date.now();
    const diff = now - timestamp;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days}天前`;
    } else if (hours > 0) {
      return `${hours}小时前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return '刚刚';
    }
  },

  // 继续学习
  continueStudy(e) {
    const { item } = e.currentTarget.dataset;

    // 如果是演示数据，提示用户开始学习
    if (item.isDemo) {
      wx.showModal({
        title: '开始学习',
        content: `您还没有开始学习「${item.libraryName}」的「${item.modeText}」模式，现在开始吗？`,
        confirmText: '开始学习',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: `/pages/wordbank/wordlist/wordlist?libraryId=${item.libraryId}`
            });
          }
        }
      });
      return;
    }

    // 检查是否有需要复习的词汇
    if (this.data.reviewMode && item.reviewInfo && item.reviewInfo.totalReviewWords > 0) {
      // 复习模式：优先复习需要复习的词汇
      this.startReviewMode(item);
    } else {
      // 普通模式：继续学习进度
      this.continueStudyWithCloudData(item);
    }
  },

  // 开始复习模式
  startReviewMode(item) {
    wx.showModal({
      title: '艾宾浩斯复习',
      content: `发现「${item.libraryName}」有 ${item.reviewInfo.totalReviewWords} 个词汇需要复习，是否开始复习？`,
      confirmText: '开始复习',
      cancelText: '继续学习',
      success: (res) => {
        if (res.confirm) {
          // 开始复习模式
          this.continueStudyWithCloudData(item, true);
        } else {
          // 继续普通学习
          this.continueStudyWithCloudData(item, false);
        }
      }
    });
  },

  // 从云端获取数据并继续学习
  continueStudyWithCloudData(item, isReviewMode = false) {
    wx.showLoading({ title: '加载进度中...' });

    // 调用云函数获取学习进度
    wx.cloud.callFunction({
      name: 'getLearningProgress',
      data: {
        libraryId: item.libraryId,
        mode: item.mode,
        includeReviewWords: true // 包含复习词汇信息
      }
    }).then(result => {
      wx.hideLoading();

      if (result.result && result.result.success && result.result.data.length > 0) {
        const cloudProgress = result.result.data[0];
        console.log('从云端获取到学习进度:', cloudProgress);

        // 使用云端数据继续学习
        this.continueStudyWithProgress(cloudProgress, isReviewMode);
      } else {
        console.log('云端没有找到学习进度，使用本地数据');
        // 如果云端没有数据，使用本地数据
        this.continueStudyWithProgress(item, isReviewMode);
      }
    }).catch(error => {
      wx.hideLoading();
      console.error('获取云端学习进度失败:', error);
      // 如果云端获取失败，使用本地数据
      this.continueStudyWithProgress(item, isReviewMode);
    });
  },

  // 使用进度数据继续学习
  continueStudyWithProgress(progressData, isReviewMode = false) {
    const mode = progressData.mode;
    let url = '';

    // 根据不同模式直接跳转到对应的测试页面
    if (mode === 'en_to_cn' || mode === 'cn_to_en') {
      // 英译汉/汉译英模式 - 直接跳转到测试页面
      url = `/pages/wordtest/test/test?testMode=${mode}&shareMode=continue&libraryId=${progressData.libraryId}`;

      // 如果是复习模式，添加复习标识
      if (isReviewMode) {
        url += `&reviewMode=true`;
      }

    } else if (mode === 'dictation') {
      // 听写模式 - 直接跳转到听写页面
      url = `/pages/spelling/practice/practice?mode=dictation&shareMode=continue&libraryId=${progressData.libraryId}`;

      // 如果是复习模式，添加复习标识
      if (isReviewMode) {
        url += `&reviewMode=true`;
      }

    } else if (mode === 'elimination') {
      // 消消乐模式 - 直接跳转到消消乐页面
      url = `/pages/task/puzzle/puzzle?mode=continue&libraryId=${progressData.libraryId}`;

      // 如果是复习模式，添加复习标识
      if (isReviewMode) {
        url += `&reviewMode=true`;
      }

    } else if (mode === 'phrase_en2zh' || mode === 'phrase_zh2en') {
      // 短语测试模式 - 跳转到测试页面，使用短语模式
      const actualMode = mode === 'phrase_en2zh' ? 'en_to_cn' : 'cn_to_en';
      url = `/pages/wordtest/test/test?testMode=${actualMode}&shareMode=continue&libraryId=${progressData.libraryId}&isPhrase=true`;

      // 如果是复习模式，添加复习标识
      if (isReviewMode) {
        url += `&reviewMode=true`;
      }

    } else {
      // 其他模式或未知模式，回退到词库选择页面
      url = `/pages/wordbank/wordlist/wordlist?libraryId=${progressData.libraryId}&mode=continue&targetMode=${mode}`;

      // 如果是复习模式，添加复习标识
      if (isReviewMode) {
        url += `&reviewMode=true`;
      }
    }

    // 如果有测试配置，传递测试配置参数
    if (progressData.testSettings) {
      const testSettings = progressData.testSettings;
      if (testSettings.perQuestionTime) {
        url += `&perQuestionTime=${testSettings.perQuestionTime}`;
      }
      if (testSettings.timeLimit) {
        url += `&timeLimit=${testSettings.timeLimit}`;
      }
      if (testSettings.wordsPerGroup) {
        url += `&wordsPerGroup=${testSettings.wordsPerGroup}`;
      }
    }

    // 如果是分组学习，传递分组信息
    if (progressData.isGrouped && progressData.groupInfo) {
      url += `&isGrouped=true&currentGroup=${progressData.groupInfo.currentGroup}&totalGroups=${progressData.groupInfo.totalGroups}&wordsPerGroup=${progressData.groupInfo.wordsPerGroup}`;
    }

    console.log('继续学习跳转URL:', url);
    wx.navigateTo({
      url: url
    });
  },

  // 清除单个进度
  clearProgress(e) {
    const { item } = e.currentTarget.dataset;

    wx.showModal({
      title: '确认清除',
      content: `确定要清除「${item.libraryName}」的「${item.modeText}」学习进度吗？此操作不可恢复。`,
      confirmText: '清除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteProgressItems([item.key]);
        }
      }
    });
  },

  // 查看词库
  viewLibrary(e) {
    const { item } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/wordbank/wordlist/wordlist?libraryId=${item.libraryId}`
    });
  },

  // 去学习页面
  goToWordBank() {
    wx.navigateTo({
      url: '/pages/wordbank/wordbank'
    });
  },

  // 长按进度卡片
  onProgressLongPress(e) {
    if (!this.data.editMode) {
      this.setData({
        editMode: true
      });
    }
  },

  // 切换选择进度项
  toggleSelectProgress(e) {
    const key = e.currentTarget.dataset.key;
    const progressList = this.data.progressList.map(item => {
      if (item.key === key) {
        item.isSelected = !item.isSelected;
      }
      return item;
    });

    const selectedCount = progressList.filter(item => item.isSelected).length;

    this.setData({
      progressList,
      selectedCount
    });
  },

  // 计算关卡进度
  calculateGroupProgress(item) {
    if (item.isGrouped && item.groupInfo) {
      return `${item.groupInfo.currentGroup}/${item.groupInfo.totalGroups}`;
    } else if (item.totalCount && item.currentIndex !== undefined) {
      // 对于非分组模式，计算当前词汇在总词汇中的位置
      const currentGroup = Math.ceil((item.currentIndex || 0) / 20); // 假设每组20个词
      const totalGroups = Math.ceil(item.totalCount / 20);
      return `${Math.max(1, currentGroup)}/${totalGroups}`;
    }
    return '1/1';
  },

  // 计算新学进度
  calculateNewStudyProgress(item) {
    if (item.isGrouped && item.groupInfo) {
      const remaining = item.groupInfo.totalGroups - item.groupInfo.currentGroup + 1;
      return `${remaining}关卡待学`;
    } else if (item.totalCount && item.currentIndex !== undefined) {
      const currentGroup = Math.ceil((item.currentIndex || 0) / 20);
      const totalGroups = Math.ceil(item.totalCount / 20);
      const remaining = totalGroups - currentGroup + 1;
      return `${Math.max(0, remaining)}关卡待学`;
    }
    return '0关卡待学';
  },

  // 计算复习进度
  calculateReviewProgress(item) {
    // 确保reviewInfo存在
    if (!item.reviewInfo) {
      return '0/0';
    }

    const reviewed = item.reviewInfo.reviewedWords || 0;
    const total = item.reviewInfo.totalReviewWords || 0;
    return `${reviewed}/${total}`;
  },


}); 