/* pages/competition/ranking/ranking.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 页面头部 */
.header {
  position: relative;
  padding: 60rpx 40rpx 40rpx 40rpx;
  color: white;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  backdrop-filter: blur(10rpx);
}

.header-content {
  position: relative;
  z-index: 2;
}

.competition-info {
  text-align: center;
  margin-bottom: 30rpx;
}

.competition-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.competition-mode {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

.participants-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  opacity: 0.8;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  color: white;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 排行榜内容 */
.ranking-content {
  padding: 0 20rpx 120rpx 20rpx;
}

/* 前三名特殊展示 */
.top-three {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  margin-bottom: 60rpx;
  padding: 0 20rpx;
  min-height: 300rpx;
}

.top-item {
  position: relative;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  margin: 0 10rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  text-align: center;
  min-width: 180rpx;
  animation: slideUp 0.6s ease-out;
}

.top-item.first {
  margin-top: -40rpx;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  transform: scale(1.1);
  z-index: 3;
}

.top-item.second {
  background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
  margin-top: -20rpx;
  z-index: 2;
}

.top-item.third {
  background: linear-gradient(135deg, #cd7f32 0%, #deb887 100%);
  z-index: 1;
}

.rank-crown {
  font-size: 48rpx;
  margin-bottom: 10rpx;
  animation: bounce 2s infinite;
}

.rank-number {
  font-size: 24rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 15rpx;
}

.top-item.first .rank-number {
  color: #b8860b;
}

.top-item.second .rank-number {
  color: #708090;
}

.top-item.third .rank-number {
  color: #8b4513;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* 前三名区域的用户信息容器居中 */
.top-item .user-info {
  align-items: center;
}

.user-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  max-width: 140rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 前三名区域的用户昵称居中 */
.top-item .user-info .user-name {
  text-align: center !important;
}

.user-score {
  font-size: 32rpx;
  font-weight: bold;
  color: #007aff;
}

.top-item.first .user-score {
  color: #b8860b;
}

.user-detail {
  font-size: 22rpx;
  color: #666;
}

.user-sub-detail {
  font-size: 20rpx;
  color: #999;
  margin-top: 4rpx;
}

.winner-effects {
  position: absolute;
  top: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10rpx;
}

.winner-effects .star {
  font-size: 24rpx;
  animation: twinkle 1.5s ease-in-out infinite;
}

.winner-effects .star:nth-child(2) {
  animation-delay: 0.3s;
}

.winner-effects .star:nth-child(3) {
  animation-delay: 0.6s;
}

/* 完整排行榜 */
.ranking-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.list-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx;
  text-align: center;
}

.list-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
  transition: background-color 0.3s ease;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-item.current-user {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1) 0%, rgba(0, 122, 255, 0.05) 100%);
  border: 2rpx solid rgba(0, 122, 255, 0.3);
}

.rank-section {
  width: 80rpx;
  text-align: center;
  margin-right: 30rpx;
}

.ranking-item .rank-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.user-section {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-main {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-main .user-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.submit-time {
  font-size: 22rpx;
  color: #999;
}

.user-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

.score {
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
}

.detail {
  font-size: 24rpx;
  color: #666;
}

.sub-detail {
  font-size: 22rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
  color: white;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 15rpx;
  display: block;
}

.empty-tip {
  font-size: 26rpx;
  opacity: 0.8;
  display: block;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx 40rpx;
  display: flex;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.action-btn::after {
  border: none;
}

.participate-btn {
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  color: white;
}

.share-btn {
  background: linear-gradient(135deg, #32d74b 0%, #30d158 100%);
  color: white;
}

.btn-text {
  color: inherit;
}

/* 动画效果 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .top-three {
    padding: 0 10rpx;
  }
  
  .top-item {
    min-width: 160rpx;
    margin: 0 5rpx;
  }
  
  .header {
    padding: 50rpx 30rpx 30rpx 30rpx;
  }
  
  .ranking-content {
    padding: 0 10rpx 120rpx 10rpx;
  }
  
  .bottom-actions {
    padding: 20rpx 30rpx;
  }
} 