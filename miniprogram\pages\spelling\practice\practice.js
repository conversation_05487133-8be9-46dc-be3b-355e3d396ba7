const app = getApp();
const examLock = require('../../../utils/exam-lock');

Page({
  data: {
    // 练习模式
    practiceMode: 'dictation', // dictation: 听写模式, spelling: 拼写模式
    
    // 练习数据
    words: [], // 单词列表
    currentIndex: 0, // 当前单词索引
    currentWord: null, // 当前单词数据
    total: 0, // 总单词数
    progress: 0, // 进度百分比

    // 听写模式新增数据
    wordsPerPage: 1, // 每页单词数改为1个
    currentPageIndex: 0,
    totalPages: 0,
    currentPageWords: [],
    currentlyPlayingIndex: 0,
    userInputs: [], // 用户输入的答案数组
    isPageCompleted: false, // 当前页面是否完成输入

    // 单词模式相关
    userInput: '', // 当前单词的用户输入
    showAnswer: false, // 是否显示答案
    inputFocused: false, // 输入框是否聚焦

    // 输入数据
    inputWord: '', // 用户输入的单词
    showResult: false, // 是否显示结果
    isCorrect: false, // 是否回答正确

    // 听写模式特有数据
    showMeaning: false, // 是否显示中文释义（听写模式下默认显示）
    autoPlaying: false,
    playingStage: 'waiting', // waiting, playing, pause, completed
    countdown: 0,
    dictationPracticeMode: 'practice', // practice: 练习模式, test: 测试模式
    dictationSettings: { // 听写设置
      playCount: 2, // 播放次数
      wordOrder: 'original' // 新增用户设置单词顺序的选项
    },
    
    // 音频播放数据
    audioContext: null, // 音频上下文
    isPlaying: false, // 是否正在播放
    audioProgress: 0, // 音频进度
    currentTime: '00:00', // 当前时间
    duration: '00:00', // 总时长
    playCount: 0,
    playTimes: 2, // 每个单词播放次数

    // 统计数据
    startTime: 0, // 开始时间
    totalTime: 0, // 总用时
    correctCount: 0, // 正确数量
    wrongCount: 0, // 错误数量
    mistakes: [], // 错题列表

    // 完成数据
    showCompletion: false, // 是否显示完成提示
    correctRate: 0, // 正确率
    
    // 结果页面数据
    showResultPage: false, // 是否显示结果页面
    resultDetails: [], // 详细结果列表
    selectedMistakes: [], // 选中的错题
    
    // 练习模式显示控制
    revealedWords: [], // 已显示的单词
    revealedPhonetics: [], // 已显示的音标
    revealedMeanings: [], // 已显示的中文含义
    revealedExamples: [], // 已显示的例句
    
    // 考试模式控制
    examMode: false, // 是否为考试模式
    
    // 测试结果相关
    showTestResult: false, // 是否显示测试结果页面
    testResults: [], // 全部测试结果
    selectedWrongWords: [], // 选中的错词
    overallStats: { // 整体统计
      totalWords: 0,
      correctCount: 0,
      wrongCount: 0,
      accuracyRate: 0,
      testTime: 0
    },
    allUserAnswers: {}, // 存储所有页面的用户答案
    inputRows: [], // 输入框行数据
    
    // 倒计时相关
    showCountdown: false, // 是否显示倒计时
    countdownSeconds: 10, // 倒计时秒数
    countdownTimer: null, // 倒计时定时器
    audioPlayCompleted: false, // 音频播放是否完成
    
    // 分享弹窗相关
    showShareModal: false, // 是否显示分享弹窗
    currentShareData: null, // 当前分享数据
    
    // 播放控制相关
    isPlayingStopped: false, // 播放是否已停止的标志
    currentPlayingPromise: null, // 当前播放的Promise
    
    // 后台播放控制（不受页面退出影响）
    backgroundPlaybackId: null, // 后台播放任务ID
    isBackgroundPlaybackActive: false, // 后台播放是否激活
    playbackStartTime: null, // 播放开始时间
    
    // 多关卡竞赛相关
    competitionMode: false, // 是否为竞赛模式
    competitionId: null, // 当前竞赛ID
    competitionName: '', // 竞赛名称
    masterCompetitionId: null, // 主竞赛ID（多关卡模式）
    hasNextLevel: false, // 是否有下一关
    nextLevelId: null, // 下一关ID
    nextLevelNumber: null, // 下一关编号
    competitionCompleted: false, // 当前关卡是否完成

    // 重定向控制
    isRedirecting: true // 初始为true，防止页面闪现
  },

    onLoad(options) {
    const { mode, library, practiceMode, shareMode, shareId, resumeTest, testStateId, competitionId, levelId } = options;

    console.log('📋 页面加载参数:', options);

    // 检测是否是从"查看分享页"按钮进入的
    const shareButtonClickTime = wx.getStorageSync('shareButtonClickTime');
    const currentShareId = wx.getStorageSync('currentShareId');

    if (shareButtonClickTime && shareId === currentShareId) {
      const now = Date.now();
      const timeDiff = now - shareButtonClickTime;

      // 如果时间差在合理范围内（1-30秒），认为是从"查看分享页"按钮进入的
      if (timeDiff > 1000 && timeDiff < 30000) {
        console.log('🔄 检测到从"查看分享页"按钮进入，重定向到我的分享页面');

        // 设置重定向标识，隐藏页面内容
        this.setData({
          isRedirecting: true
        });

        // 清除标识
        wx.removeStorageSync('shareButtonClickTime');
        wx.removeStorageSync('currentShareId');

        // 立即重定向
        wx.reLaunch({
          url: '/pages/profile/share/share?from=shareSuccess'
        });
        return;
      }
    }

    // 如果不需要重定向，显示页面内容
    this.setData({
      isRedirecting: false
    });
    console.log('🔍 关键参数检查:', {
      mode: options.mode,
      practiceMode: options.practiceMode,
      shareMode: options.shareMode,
      shareId: options.shareId
    });
    
    // 检查是否是测试恢复
    const isTestResume = resumeTest === 'true';
    let restoredState = null;
    
    if (isTestResume && testStateId) {
      console.log('🔄 检测到测试恢复，尝试恢复状态...');
      restoredState = examLock.restoreTestState(testStateId);
    }
    
    // 如果是竞赛模式，优先处理竞赛
    if (mode === 'competition' && competitionId) {
      this.loadCompetitionTest(competitionId, options);
      return;
    }
    
    // 如果是分享模式，优先处理分享测试
    if (shareMode === 'share' && shareId) {
      this.loadSharedDictationTest(shareId, options, levelId);
      return;
    }
    
    // 设置练习模式
    this.setData({
      practiceMode: mode === 'dictation' ? 'dictation' : 'spelling',
      showMeaning: mode === 'dictation', // 听写模式显示释义
      dictationPracticeMode: practiceMode || 'practice', // 听写的子模式：practice 或 test
      startTime: restoredState ? restoredState.lockStartTime : Date.now(), // 使用恢复的开始时间或当前时间
      isTestResume: isTestResume, // 标记是否为测试恢复
      examMode: isTestResume && restoredState ? true : false // 如果是测试恢复，直接设置为考试模式
    });
    
    // 如果是听写测试模式且不是测试恢复，启用锁定模式
    // 但要检查是否已经有测试结果，避免重复启用
    if (mode === 'dictation' && practiceMode === 'test' && !isTestResume && !this.data.showTestResult) {
      this.enableExamMode();
    } else if (isTestResume && restoredState) {
      // 测试恢复：先恢复状态，再启用锁定模式
      console.log('🔒 测试恢复：恢复状态并启用锁定模式');
      this.restoreTestState(restoredState);
    } else if (isTestResume && !restoredState) {
      // 没有恢复状态，显示错误信息
      console.log('⚠️ 没有找到恢复状态');
      wx.showModal({
        title: '恢复失败',
        content: '没有找到测试状态，将开始新的测试。',
        showCancel: false,
        success: () => {
          // 继续正常流程
        }
      });
    }
    
    // 初始化音频上下文
    this.initAudioContext();

    // 根据模式加载单词数据
    if (mode === 'custom') {
      // 自定义模式，从全局数据中获取
      const app = getApp();
      const customWords = app.globalData.customWords;
      
      if (!customWords || customWords.length === 0) {
        wx.showToast({
          title: '没有找到自定义单词',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      this.loadWords(customWords);
    } else if (library) {
      // 从词库加载单词
      this.loadWordsFromLibrary(library);
    } else {
      // 解析任务数据
      const taskData = options.data ? JSON.parse(options.data) : null;
      if (taskData && taskData.words) {
        this.loadWords(taskData.words);
      } else {
        // 从全局数据获取
        const app = getApp();
        const learningData = app.globalData.learningData;
        if (learningData && learningData.words) {
          // 获取听写设置
          if (learningData.dictationSettings) {
            console.log('获取到听写设置:', learningData.dictationSettings);
            this.setData({
              dictationSettings: learningData.dictationSettings
            });
          }
          
          this.loadWords(learningData.words);
        } else {
          wx.showToast({
            title: '没有找到单词数据',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      }
    }
  },

  onUnload() {
    // 销毁音频上下文
    if (this.data.audioContext && typeof this.data.audioContext.destroy === 'function') {
      try {
      this.data.audioContext.destroy();
      } catch (error) {
        console.error('销毁音频上下文失败:', error);
      }
    }
    // 清除倒计时定时器
    this.stopCountdown();
    // 清除定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
    // 清除播放定时器
    if (this.playTimer) {
      clearTimeout(this.playTimer);
      this.playTimer = null;
    }
  },



  // 从词库加载单词
  async loadWordsFromLibrary(libraryId) {
    try {
      wx.showLoading({ title: '加载词库中...' });

      // 调用云函数获取词汇
      const result = await wx.cloud.callFunction({
        name: 'getWords',
        data: {
          libraryId: libraryId,
          limit: 50 // 限制数量，避免一次加载太多
        }
      });

      wx.hideLoading();

      if (result.result.success) {
        // 设置词库名称
        const libraryName = this.getLibraryName(libraryId);
        this.setData({
          libraryName: libraryName
        });

        this.loadWords(result.result.data.words);
      } else {
        throw new Error(result.result.message || '获取词汇失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载词库失败:', error);
      wx.showToast({
        title: '加载词库失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 根据词库ID获取词库名称
  getLibraryName(libraryId) {
    const libraryMap = {
      'words_3500': '高考3500（顺序版）',
      'words_3500_luan': '高考3500（乱序版）',
      'words_cet4': '四级词汇',
      'words_cet6': '六级词汇',
      'words_bjwusan': '五三高频词（北京卷）',
      'words_toefl': '托福词汇',
      'words_ielts': '雅思词汇',
      'words_gre': 'GRE词汇',
      'words_kaoyan': '考研词汇'
    };

    return libraryMap[libraryId] || '词库';
  },

  // 从当前数据中获取词库名称
  getLibraryNameFromCurrentData() {
    // 尝试从全局数据获取
    const app = getApp();
    if (app.globalData.learningData && app.globalData.learningData.libraryName) {
      return app.globalData.learningData.libraryName;
    }

    // 尝试从竞赛数据获取
    if (this.data.competitionName) {
      return this.data.competitionName;
    }

    // 尝试从页面参数获取
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    if (currentPage && currentPage.options && currentPage.options.library) {
      return this.getLibraryName(currentPage.options.library);
    }

    return null;
  },

  // 初始化音频上下文
  initAudioContext() {
    // 🔧 修复：销毁之前的音频上下文，防止多个音频上下文同时存在
    if (this.data.audioContext) {
      try {
        this.data.audioContext.destroy();
        console.log('🗑️ 已销毁之前的音频上下文');
      } catch (error) {
        console.log('销毁音频上下文失败:', error);
      }
    }
    
    const audioContext = wx.createInnerAudioContext();
    
    // 设置音频属性
    audioContext.autoplay = false;
    audioContext.loop = false;
    audioContext.volume = 1.0;
    
    // 播放事件
    audioContext.onPlay(() => {
      console.log('音频开始播放');
      this.setData({ isPlaying: true });
    });
    
    // 暂停事件
    audioContext.onPause(() => {
      console.log('音频暂停');
      this.setData({ isPlaying: false });
    });
    
    // 停止事件
    audioContext.onStop(() => {
      console.log('音频停止');
      this.setData({ isPlaying: false });
    });
    
    // 播放结束事件
    audioContext.onEnded(() => {
      console.log('音频播放结束');
      this.setData({ 
        isPlaying: false,
        audioProgress: 0,
        currentTime: '00:00'
      });
    });
    
    // 错误事件
    audioContext.onError((error) => {
      console.error('音频播放错误:', error);
      this.setData({ isPlaying: false });
      // 显示错误提示
      wx.showToast({
        title: '音频播放失败',
        icon: 'none',
        duration: 1500
      });
    });
    
    // 时间更新事件
    audioContext.onTimeUpdate(() => {
      if (audioContext.duration > 0) {
      const progress = (audioContext.currentTime / audioContext.duration) * 100;
      this.setData({
        audioProgress: progress,
        currentTime: this.formatTime(audioContext.currentTime)
      });
      }
    });
    
    // 可以播放事件
    audioContext.onCanplay(() => {
      console.log('音频可以播放');
    });
    
    // 等待数据事件
    audioContext.onWaiting(() => {
      console.log('音频等待数据');
    });

    this.setData({ audioContext });
    console.log('音频上下文初始化完成');
  },

  // 加载竞赛测试数据
  async loadCompetitionTest(competitionId, options) {
    wx.showLoading({ title: '加载竞赛...' });
    
    try {
      // 调用云函数获取竞赛详情
      const result = await wx.cloud.callFunction({
        name: 'getCompetitionDetail',
        data: {
          competitionId: competitionId
        }
      });

      if (!result.result.success) {
        wx.hideLoading();
        wx.showModal({
          title: '加载失败',
          content: result.result.message || '竞赛不存在或已过期',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }

      const competitionData = result.result.data;
      
      // 设置竞赛模式参数
      this.setData({
        practiceMode: 'dictation',
        showMeaning: true,
        dictationPracticeMode: 'test', // 竞赛默认为测试模式
        startTime: Date.now(),
        competitionMode: true,
        competitionId: competitionId,
        competitionName: competitionData.name,
        masterCompetitionId: options.masterCompetitionId || null, // 主竞赛ID（多关卡模式）
        hasNextLevel: false, // 是否有下一关
        nextLevelId: null, // 下一关ID
        nextLevelNumber: null, // 下一关编号
        competitionCompleted: false, // 当前关卡是否完成
        // 设置听写配置
        dictationSettings: competitionData.dictationSettings || {
          playCount: 2,
          wordOrder: 'original'
        }
      });
      
      // 竞赛模式不启用锁定，允许中途退出
      // this.enableExamMode();
      
      // 初始化音频上下文
      this.initAudioContext();
      
      // 加载单词数据
      this.loadWords(competitionData.words);
      
      wx.hideLoading();
      
    } catch (error) {
      wx.hideLoading();
      console.error('加载竞赛失败:', error);
      wx.showToast({ title: '加载失败', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载分享听写测试数据
  async loadSharedDictationTest(shareId, options, levelId = null) {
    wx.showLoading({ title: '加载分享测试...' });
    
    try {
      let shareTestData = null;
      
      // 首先尝试从云端获取分享测试数据
      try {
        const result = await wx.cloud.callFunction({
          name: 'getShareTest',
          data: { shareId: shareId }
        });

        if (result.result.success) {
          shareTestData = result.result.data;
          console.log('从云端加载听写分享测试成功:', shareTestData);
        }
      } catch (cloudError) {
        console.error('从云端加载听写分享测试失败:', cloudError);
        // 不再尝试本地存储，因为分享测试必须在云端
      }

      if (!shareTestData || (!shareTestData.words && !shareTestData.wordSelection)) {
        wx.hideLoading();
        wx.showModal({
          title: '分享测试不存在',
          content: '该分享测试不存在或已过期。可能的原因：\n1. 分享链接错误\n2. 测试已过期（7天有效期）\n3. 创建者未成功上传到云端\n\n请联系分享者重新创建分享测试。',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
      // 记录被分享人信息
      this.recordSharedTestAccess(shareId, shareTestData);
      
      // 设置测试数据
      const { mode, practiceMode } = options;
      const actualMode = mode || 'dictation';
      // 分享的听写测试默认应该是test模式，以启用倒计时等测试功能
      const actualPracticeMode = practiceMode || shareTestData.practiceMode || 'test';

      // 获取分享测试的听写设置
      const shareSettings = shareTestData.settings || shareTestData.dictationSettings || {};
      console.log('分享测试的听写设置:', shareSettings);

      this.setData({
        practiceMode: actualMode === 'dictation' ? 'dictation' : 'spelling',
        showMeaning: actualMode === 'dictation',
        dictationPracticeMode: actualPracticeMode,
        startTime: Date.now(),
        shareMode: 'share',
        shareId: shareId,
        // 应用分享测试的听写设置
        dictationSettings: {
          playCount: shareSettings.playCount || shareSettings.timeLimit || 2, // 兼容旧的timeLimit字段
          wordOrder: shareSettings.wordOrder || 'original',
          timeLimit: shareSettings.timeLimit || shareSettings.perQuestionTime || 15 // 每题时间限制
        }
      });
      
      // 如果是听写测试模式，启用锁定模式
      if (actualMode === 'dictation' && actualPracticeMode === 'test') {
        this.enableExamMode();
      }
      
      // 初始化音频上下文
      this.initAudioContext();

      // 处理多关卡逻辑
      let wordsToLoad = shareTestData.words;
      const wordsPerGroup = shareSettings.wordsPerGroup || 20;
      const totalWords = shareTestData.allWords ? shareTestData.allWords.length : shareTestData.words.length;
      const totalLevels = Math.ceil(totalWords / wordsPerGroup);
      const isMultiLevel = totalLevels > 1;

      // 如果指定了levelId，加载指定关卡的词汇
      if (levelId && isMultiLevel && shareTestData.allWords) {
        const levelNumber = parseInt(levelId);
        const startIndex = (levelNumber - 1) * wordsPerGroup;
        const endIndex = Math.min(startIndex + wordsPerGroup, shareTestData.allWords.length);
        wordsToLoad = shareTestData.allWords.slice(startIndex, endIndex);

        console.log(`加载听写第${levelNumber}关词汇:`, {
          startIndex,
          endIndex,
          levelWords: wordsToLoad.length,
          totalWords: shareTestData.allWords.length
        });

        // 设置关卡信息
        this.setData({
          currentLevel: levelNumber,
          totalLevels: totalLevels,
          isMultiLevel: true,
          levelId: levelNumber
        });
      } else if (isMultiLevel && !levelId) {
        // 多关卡模式但没有指定关卡，跳转到关卡选择界面
        console.log('检测到多关卡听写分享测试，跳转到关卡选择界面');
        wx.hideLoading();

        wx.redirectTo({
          url: `/pages/competition/level-select/level-select?shareId=${shareId}&mode=share&testType=dictation&refresh=true`,
          fail: (err) => {
            console.error('跳转到关卡选择失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
            // 如果跳转失败，加载第一关
            this.loadWords(shareTestData.words);
          }
        });
        return;
      }

      // 加载单词数据
      this.loadWords(wordsToLoad);
      
      wx.hideLoading();
      
    } catch (error) {
      wx.hideLoading();
      console.error('加载分享听写测试失败:', error);
      wx.showToast({ title: '加载失败', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 记录分享测试访问
  recordSharedTestAccess(shareId, shareTestData) {
    try {
      const currentUser = wx.getStorageSync('userInfo') || {};
      const currentTime = Date.now();
      
      // 检查是否已记录过该用户的访问
      if (!shareTestData.visitors) {
        shareTestData.visitors = [];
      }
      
      const existingVisitor = shareTestData.visitors.find(visitor => 
        visitor.openid === currentUser.openid
      );
      
      if (!existingVisitor && currentUser.openid) {
        // 添加新访问者记录
        shareTestData.visitors.push({
          openid: currentUser.openid,
          nickName: currentUser.nickName || '匿名用户',
          avatar: currentUser.avatarUrl || '',
          firstVisitTime: currentTime,
          lastVisitTime: currentTime,
          visitCount: 1,
          testCount: 0,
          remark: ''
        });
        
        // 更新存储（只更新访问者信息，不保存完整词汇数据）
        try {
          const shareTests = wx.getStorageSync('shareTests') || {};
          if (shareTests[shareId]) {
            // 只更新访问者信息
            shareTests[shareId].visitors = shareTestData.visitors;
            wx.setStorageSync('shareTests', shareTests);
          }
        } catch (error) {
          console.error('更新本地存储访问者信息失败:', error);
        }
        
        console.log('记录新访问者:', currentUser.nickName);
      } else if (existingVisitor) {
        // 更新已有访问者的访问时间和次数
        existingVisitor.lastVisitTime = currentTime;
        existingVisitor.visitCount = (existingVisitor.visitCount || 1) + 1;
        
        // 更新存储（只更新访问者信息，不保存完整词汇数据）
        try {
          const shareTests = wx.getStorageSync('shareTests') || {};
          if (shareTests[shareId]) {
            // 只更新访问者信息
            shareTests[shareId].visitors = shareTestData.visitors;
            wx.setStorageSync('shareTests', shareTests);
          }
        } catch (error) {
          console.error('更新本地存储访问者信息失败:', error);
        }
        
        console.log('更新访问者记录:', existingVisitor.nickName);
      }
    } catch (error) {
      console.error('记录分享测试访问失败:', error);
    }
  },

  // 加载单词数据
  loadWords(words) {
    // 🔧 修复：根据用户设置和练习模式智能处理单词顺序
    const wordOrderSetting = this.data.dictationSettings?.wordOrder || 'original';
    
    if (wordOrderSetting === 'random' && this.data.dictationPracticeMode === 'practice') {
      // 练习模式且用户选择随机顺序时，打乱单词顺序
      console.log('📝 练习模式：按用户设置随机打乱单词顺序');
      this.shuffleArray(words);
    } else {
      // 测试模式或用户选择保持原顺序时，保持用户选择的顺序
      const modeText = this.data.dictationPracticeMode === 'test' ? '测试模式' : '练习模式';
      const orderText = wordOrderSetting === 'original' ? '用户选择保持原顺序' : '测试模式强制保持原顺序';
      console.log(`✏️ ${modeText}：${orderText}`);
    }
    
    // 确保每个单词都有正确的meaning字段
    const processedWords = words.map(word => ({
      ...word,
      meaning: word.meaning || (word.meanings && word.meanings[0] && word.meanings[0].definitions && word.meanings[0].definitions[0] && word.meanings[0].definitions[0].definition) || '暂无释义'
    }));
    
    const now = Date.now();
    this.setData({
      words: processedWords,
      total: processedWords.length,
      currentIndex: 0,
      currentWord: processedWords[0] || null,
      startTime: now,
      progress: 0,
      userInput: '',
      showAnswer: false,
      isCorrect: false,
      inputFocused: false
    });

    console.log('🚀 听写测试开始，记录开始时间:', new Date(now).toLocaleString());

    // 设置音频
    this.setAudio();

    // 检查是否需要恢复测试状态
    if (this.data.needResumeAfterLoad && this.data.restoredStateData) {
      console.log('🔄 单词加载完成，恢复测试状态');
      const restoredState = this.data.restoredStateData;

      // 恢复状态数据
      this.setData({
        startTime: restoredState.startTime,
        currentIndex: restoredState.currentIndex || 0,
        allUserAnswers: restoredState.allUserAnswers || {},
        countdownStartTime: restoredState.countdownStartTime,
        countdown: restoredState.countdown || (this.data.dictationSettings?.timeLimit || 15),
        playingStage: restoredState.playingStage || 'waiting',
        audioPlayCompleted: restoredState.audioPlayCompleted || false,
        needResumeAfterLoad: false,
        restoredStateData: null
      });

      // 设置当前单词
      if (restoredState.currentIndex < processedWords.length) {
        this.setData({
          currentWord: processedWords[restoredState.currentIndex]
        });
      }

      // 恢复考试模式
      this.resumeExamMode();
    } else if (this.data.practiceMode === 'dictation' && !this.data.isTestResume) {
      // 正常开始播放
      setTimeout(() => {
        this.startDictationFlow();
      }, 1000);
    } else if (this.data.practiceMode === 'dictation' && this.data.isTestResume) {
      // 测试恢复时，显示提示但不自动播放
      console.log('🔄 测试恢复模式：暂停自动播放');
      wx.showToast({
        title: '请手动开始播放 🎵',
        icon: 'none',
        duration: 3000
      });
    }
  },

  // 随机打乱数组
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  },

  // 设置音频
  setAudio() {
    if (!this.data.audioContext || !this.data.currentWord) return;

    // 如果有音频URL，使用音频文件
    if (this.data.currentWord.audioUrl) {
      this.data.audioContext.src = this.data.currentWord.audioUrl;
      this.data.audioContext.onCanplay(() => {
        this.setData({
          duration: this.formatTime(this.data.audioContext.duration)
        });
      });
    } else {
      // 没有音频URL时，使用TTS（文本转语音）
      this.setData({
        duration: '00:03', // 默认3秒
        audioProgress: 0
      });
    }
  },

  // 播放/暂停音频
  togglePlay() {
    if (!this.data.audioContext) return;

    if (this.data.isPlaying) {
      this.data.audioContext.pause();
    } else {
      // 如果有音频URL，播放音频文件
      if (this.data.currentWord.audioUrl) {
        this.data.audioContext.play();
      } else {
        // 使用TTS播放单词
        this.playTTS(this.data.currentWord.word);
      }
      
      this.setData({
        playCount: this.data.playCount + 1
      });
    }
  },

  // TTS播放单词
  playTTS(word) {
    this.setData({ isPlaying: true });
    
    // 使用微信TTS API
    wx.createSelectorQuery()
      .select('.')
      .boundingClientRect()
      .exec();
      
    // 模拟播放过程
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      this.setData({
        audioProgress: progress,
        currentTime: this.formatTime((progress / 100) * 3) // 3秒总时长
      });
      
      if (progress >= 100) {
        clearInterval(interval);
        this.setData({
          isPlaying: false,
          audioProgress: 0,
          currentTime: '00:00'
        });
      }
    }, 300);

    // 实际的TTS播放（需要调用微信TTS接口或第三方TTS服务）
    // 这里可以集成百度、腾讯等TTS服务
    this.speakWord(word);
  },

  // 使用云函数TTS朗读单词
  async speakWord(word) {
    try {
      console.log(`🎵 朗读单词: ${word}`);

      // 检查是否已有缓存的音频数据
      const cacheKey = `practice_audio_${word}`;
      let cachedAudio = this.data[cacheKey];

      if (!cachedAudio) {
        console.log(`获取音频数据: ${word}`);
        // 显示加载状态
        this.setData({ isPlaying: true });

        // 调用云函数实现TTS
        const result = await wx.cloud.callFunction({
          name: 'ttsSpeak',
          data: {
            text: word,
            lang: 'en',
            voice: 'en-US-AriaNeural', // 使用Azure语音名称
            mode: 'practice',
            playCount: 2, // 练习模式默认播放两遍
            wordType: 'translation' // 使用translation类型以支持特殊格式处理
          }
        });

        if (result.result && result.result.success) {
          cachedAudio = result.result.data;
          // 缓存音频数据
          this.setData({
            [cacheKey]: cachedAudio
          });
        }
      } else {
        console.log(`使用缓存音频: ${word}`);
        this.setData({ isPlaying: true });
      }

      if (cachedAudio) {
        const { playList, source } = cachedAudio;

        // 播放云端音频列表
        if (playList && playList.length > 0) {
          for (const playItem of playList) {
            for (let i = 0; i < playItem.playTimes; i++) {
              if (playItem.audioUrl) {
                await this.playAudioOnce(playItem.audioUrl);
              } else {
                console.error('音频URL为空:', playItem);
                throw new Error('音频URL无效');
              }

              // 播放间隔
              if (i < playItem.playTimes - 1) {
                await this.delay(500); // 500ms间隔
              }
            }
          }
        } else {
          console.error('无可用的播放列表');
          throw new Error('无可用音频');
        }

        console.log(`✅ 单词 ${word} 播放完成`);
        this.setData({ isPlaying: false });
      } else {
        console.error('TTS合成失败: 无音频数据');
        this.setData({ isPlaying: false });
        throw new Error('TTS合成失败');
      }

    } catch (error) {
      console.error('TTS播放失败:', error);
      this.setData({ isPlaying: false });
      wx.showToast({
        title: '发音失败',
        icon: 'none'
      });
    }
  },

  // 滑动条变化
  onSliderChange(e) {
    if (!this.data.audioContext) return;

    const value = e.detail.value;
    const time = (value / 100) * this.data.audioContext.duration;
    this.data.audioContext.seek(time);
  },

  // 输入变化
  onInputChange(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value;
    const userInputs = [...this.data.userInputs];
    userInputs[index] = value;
    
    this.setData({ userInputs });
    
    // 检查是否已经完成所有输入
    this.checkPageCompletion();
  },

  // 检查页面完成状态
  checkPageCompletion() {
    const { userInputs } = this.data;
    const isCompleted = userInputs.every(input => input.trim() !== '');
    
    this.setData({ isPageCompleted: isCompleted });
  },

  // 提交当前页面
  onSubmitPage() {
    if (!this.data.isPageCompleted) {
      wx.showToast({
        title: '请完成所有单词输入',
        icon: 'none'
      });
      return;
    }
    
    // 检查当前页面答案
    this.checkCurrentPageAnswers();
  },

  // 检查当前页面答案
  checkCurrentPageAnswers() {
    const { currentPageWords, userInputs } = this.data;
    const pageResults = [];
    
    currentPageWords.forEach((word, index) => {
      const userAnswer = userInputs[index].trim();
      const wordText = word.words || word.word;
      const isCorrect = userAnswer.toLowerCase() === wordText.toLowerCase();
      
      pageResults.push({
        word: wordText,
        meaning: word.meaning,
        userAnswer: userAnswer,
        isCorrect: isCorrect,
        isSelected: false
      });
      
      if (isCorrect) {
    this.setData({
          correctCount: this.data.correctCount + 1
        });
      } else {
        this.data.mistakes.push({
          word: wordText,
          meaning: word.meaning,
          userAnswer: userAnswer
        });
      }
    });
    
    // 保存结果
    this.data.resultDetails.push(...pageResults);
    
    // 检查是否还有下一页
    if (this.data.currentPageIndex + 1 < this.data.totalPages) {
      // 跳转到下一页
      this.nextPage();
    } else {
      // 所有页面完成，显示结果
      this.showFinalResults();
    }
  },

  // 下一页
  nextPage() {
    const nextPageIndex = this.data.currentPageIndex + 1;
    const startIndex = nextPageIndex * this.data.wordsPerPage;
    const endIndex = Math.min(startIndex + this.data.wordsPerPage, this.data.total);
    const nextPageWords = this.data.words.slice(startIndex, endIndex);
    
    this.setData({
      currentPageIndex: nextPageIndex,
      currentPageWords: nextPageWords,
      userInputs: new Array(nextPageWords.length).fill(''),
      currentlyPlayingIndex: 0,
      isPageCompleted: false,
      progress: Math.round((nextPageIndex / this.data.totalPages) * 100)
    });
    
    // 开始播放新页面的单词
    if (this.data.practiceMode === 'dictation') {
      setTimeout(() => {
        this.startPageDictation();
      }, 500);
    }
  },

  // 显示最终结果
  showFinalResults() {
    const correctRate = Math.round((this.data.correctCount / this.data.total) * 100);
    
    this.setData({
      showResultPage: true,
      correctRate: correctRate,
      totalTime: Date.now() - this.data.startTime
    });
  },

  // 重新播放当前页面
  onReplayPage() {
    if (this.data.practiceMode === 'dictation') {
      this.startPageDictation();
    }
  },

  // 检查答案
  onCheck() {
    const inputWord = this.data.inputWord.trim().toLowerCase();
    const correctWord = this.data.currentWord.word.toLowerCase();
    const isCorrect = inputWord === correctWord;
    
    // 更新统计
    if (isCorrect) {
      this.setData({
        correctCount: this.data.correctCount + 1
      });
    } else {
      // 添加到错题列表
      this.data.mistakes.push({
        ...this.data.currentWord,
        userAnswer: this.data.inputWord,
        timestamp: Date.now()
      });
    }
    
    // 添加到结果详情
    this.data.resultDetails.push({
      word: this.data.currentWord.word,
      meaning: this.data.currentWord.meaning,
      userAnswer: this.data.inputWord,
      isCorrect: isCorrect,
      isSelected: false, // 添加选择状态
      timestamp: Date.now()
    });
    
    this.setData({
      showResult: true,
      isCorrect: isCorrect,
      resultDetails: this.data.resultDetails
    });
    
    // 自动进入下一题（听写模式）
    if (this.data.practiceMode === 'dictation') {
      setTimeout(() => {
        this.onNextWord();
      }, 2000);
    }
  },

  // 下一个单词
  onNextWord() {
    const nextIndex = this.data.currentIndex + 1;
    const progress = Math.round((nextIndex / this.data.total) * 100);
    
    if (nextIndex >= this.data.total) {
      // 练习完成
      this.onComplete();
      return;
    }
    
    // 设置下一个单词
    this.setData({
      currentIndex: nextIndex,
      currentWord: this.data.words[nextIndex],
      inputWord: '',
      showResult: false,
      isCorrect: false,
      progress: progress,
      playCount: 0,
      audioProgress: 0,
      currentTime: '00:00',
      // 重置听写状态
      autoPlaying: false,
      playingStage: '',
      countdown: 0
    });
    
    // 设置音频
    this.setAudio();
    
    // 如果是听写模式，自动开始播放
    if (this.data.practiceMode === 'dictation') {
      console.log('进入下一个单词，准备开始听写');
      setTimeout(() => {
        this.startDictationFlow();
      }, 300); // 缩短等待时间
    }
  },

  // 完成练习
  onComplete() {
    console.log('🏁 练习完成，检查模式:', {
      practiceMode: this.data.practiceMode,
      dictationPracticeMode: this.data.dictationPracticeMode
    });

    // 🔧 修复：听写测试模式应该调用finishDictation来生成详细结果
    if (this.data.practiceMode === 'dictation' && this.data.dictationPracticeMode === 'test') {
      console.log('🔧 听写测试模式完成，调用finishDictation');
      this.finishDictation();
      return;
    }

    // 其他模式的原有逻辑
    const totalTime = Date.now() - this.data.startTime;
    const correctRate = Math.round((this.data.correctCount / this.data.total) * 100);

    // 格式化时间显示
    const minutes = Math.floor(totalTime / 60000);
    const seconds = Math.floor((totalTime % 60000) / 1000);
    const formattedTime = `${minutes}分${seconds}秒`;

    this.setData({
      showCompletion: this.data.practiceMode === 'spelling', // 拼写模式显示简单完成提示
      showResultPage: this.data.practiceMode === 'dictation', // 听写模式显示详细结果页面
      showTestResult: this.data.practiceMode === 'dictation', // 显示测试结果
      totalTime: totalTime,
      correctRate: correctRate,
      overallStats: {
        totalWords: this.data.total,
        correctCount: this.data.correctCount,
        wrongCount: this.data.wrongCount,
        accuracyRate: correctRate,
        formattedTime: formattedTime
      }
    });
  },

  // 重播当前单词
  onReplay() {
    if (this.data.practiceMode === 'dictation') {
      this.startDictationFlow();
    } else {
      this.togglePlay();
    }
  },

  // 开始听写流程（单个单词模式）
  async startDictationFlow() {
    if (!this.data.currentWord) return;

    const word = this.data.currentWord.words || this.data.currentWord.word;
    const playCount = this.data.dictationSettings?.playCount || 2;

    console.log(`开始播放单词: ${word}, 播放次数: ${playCount}`);

    // 先预加载音频数据
    this.setData({
      playingStage: 'loading',
      autoPlaying: true,
      inputFocused: true
    });

    try {
      // 检查缓存的音频数据
      const cacheKey = `dictation_audio_${word}`;
      let audioData = this.data[cacheKey];

      if (!audioData) {
        console.log(`🔄 第一次播放，预加载音频数据: ${word}`);
        // 预加载音频
        audioData = await this.preloadWordAudio(word);
        if (!audioData) {
          console.error('无法获取音频数据');
          wx.showToast({
            title: '音频加载失败',
            icon: 'none'
          });
          return;
        }

        // 缓存音频数据
        this.setData({
          [cacheKey]: audioData
        });
      } else {
        console.log(`✅ 使用缓存音频数据: ${word}`);
      }

      // 开始播放
      this.setData({
        playingStage: 'playing'
      });

      // 播放指定次数
      for (let i = 0; i < playCount; i++) {
        console.log(`🎵 播放第 ${i + 1}/${playCount} 遍: ${word}`);
        await this.playPreloadedAudio(word, audioData);

        // 如果不是最后一次播放，等待1秒（播放间隔）
        if (i < playCount - 1) {
          await this.waitForSeconds(1);
        }
      }

      this.setData({
        playingStage: 'completed',
        autoPlaying: false
      });

      // 在测试模式下启动答题倒计时
      if (this.data.dictationPracticeMode === 'test') {
        this.startCountdown();
      }

    } catch (error) {
      console.error('播放失败:', error);
      this.setData({
        playingStage: 'completed',
        autoPlaying: false
      });
    }
  },

  // 预加载单词音频数据
  async preloadWordAudio(word) {
    console.log(`🔄 预加载音频数据: ${word}`);

    try {
      const result = await wx.cloud.callFunction({
        name: 'ttsSpeak',
        data: {
          text: word,
          lang: 'en',
          voice: 'en-US-AriaNeural',
          mode: this.data.dictationPracticeMode,
          playCount: 1,
          wordType: 'dictation'
        }
      });

      if (result.result && result.result.success) {
        console.log(`✅ 音频数据预加载完成: ${word}`);
        return result.result.data;
      } else {
        console.error(`❌ 音频数据预加载失败: ${word}`, result.result?.message);
        return null;
      }
    } catch (error) {
      console.error(`❌ 预加载出错: ${word}`, error);
      return null;
    }
  },

  // 播放预加载的音频
  async playPreloadedAudio(word, audioData) {
    return new Promise(async (resolve) => {
      try {
        console.log(`🎵 播放预加载音频: ${word}`);

        if (!audioData) {
          console.error('无预加载数据，播放失败');
          throw new Error('音频数据加载失败');
        }

        const { playList, source } = audioData;

        // 播放云端音频
        if (playList && playList.length > 0) {
          for (const playItem of playList) {
            if (playItem.audioUrl) {
              await this.playAudioOnce(playItem.audioUrl);
            } else {
              console.error('音频URL为空:', playItem);
              throw new Error('音频URL无效');
            }
          }
        } else {
          console.error('无可用的播放列表');
          throw new Error('无可用音频');
        }

        console.log(`✅ 预加载音频播放完成: ${word}`);
        resolve();
      } catch (error) {
        console.error(`❌ 预加载音频播放失败: ${word}`, error);
        wx.showToast({
          title: '音频播放失败',
          icon: 'none'
        });
        reject(error);
      }
    });
  },

  // 播放单词音频
  async playWordAudio(word) {
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`🎵 开始播放单词: ${word}`);

        // 检查是否已有缓存的音频数据
        const cacheKey = `audio_${word}`;
        let cachedAudio = this.data[cacheKey];

        if (!cachedAudio) {
          console.log(`获取音频数据: ${word}`);
          // 调用云函数获取音频
          const result = await wx.cloud.callFunction({
            name: 'ttsSpeak',
            data: {
              text: word,
              lang: 'en',
              voice: 'en-US-AriaNeural',
              mode: this.data.dictationPracticeMode,
              playCount: 1,
              wordType: 'dictation'
            }
          });

          if (result.result && result.result.success) {
            cachedAudio = result.result.data;
            // 缓存音频数据
            this.setData({
              [cacheKey]: cachedAudio
            });
          }
        } else {
          console.log(`使用缓存音频: ${word}`);
        }

        if (cachedAudio) {
          const { playList, source } = cachedAudio;

          // 播放云端音频
          if (playList && playList.length > 0) {
            for (const playItem of playList) {
              if (playItem.audioUrl) {
                await this.playAudioOnce(playItem.audioUrl);
              } else {
                console.error('音频URL为空:', playItem);
                throw new Error('音频URL无效');
              }
            }
          } else {
            console.error('无可用的播放列表');
            throw new Error('无可用音频');
          }

          console.log(`✅ 单词 ${word} 播放完成`);
          resolve();
        } else {
          console.error(`单词 ${word} 获取音频失败`);
          wx.showToast({
            title: '音频获取失败',
            icon: 'none'
          });
          reject(new Error('音频获取失败'));
        }
      } catch (error) {
        console.error(`单词 ${word} 播放出错:`, error);
        wx.showToast({
          title: '播放失败',
          icon: 'none'
        });
        reject(error);
      }
    });
  },

  // 等待指定秒数（不显示倒计时，避免用户困惑）
  waitForSeconds(seconds) {
    return new Promise(resolve => {
      // 不显示等待时间的倒计时
      setTimeout(() => {
        resolve();
      }, seconds * 1000);
    });
  },

  // 启动倒计时（答题时间限制）
  startCountdown(remainingTime = null) {
    // 获取答题时间限制，默认15秒
    const timeLimit = this.data.dictationSettings?.timeLimit || 15;
    const initialTime = remainingTime !== null ? remainingTime : timeLimit;

    // 记录倒计时开始的绝对时间
    const countdownStartTime = Date.now() - (timeLimit - initialTime) * 1000;

    this.setData({
      countdown: initialTime,
      answerTimer: null,
      countdownStartTime: countdownStartTime // 保存倒计时开始的绝对时间
    });

    const timer = setInterval(() => {
      // 基于绝对时间计算剩余时间，确保后台时间继续
      const now = Date.now();
      const elapsedSeconds = Math.floor((now - this.data.countdownStartTime) / 1000);
      const timeLeft = Math.max(0, timeLimit - elapsedSeconds);

      this.setData({ countdown: timeLeft });

      if (timeLeft <= 0) {
        clearInterval(timer);
        this.setData({
          countdown: 0,
          answerTimer: null
        });

        // 时间到，自动提交答案
        this.handleDictationTimeout();
      }
    }, 1000);

    this.setData({ answerTimer: timer });
  },

  // 处理听写超时
  handleDictationTimeout() {
    if (this.data.showAnswer) return; // 如果已经显示答案，不处理超时

    // 清除倒计时定时器
    if (this.data.answerTimer) {
      clearInterval(this.data.answerTimer);
      this.setData({ answerTimer: null });
    }

    // 如果用户有输入内容，按照输入内容判断正误
    if (this.data.userInput && this.data.userInput.trim()) {
      this.onSubmitDictationAnswer();
    } else {
      // 没有输入，直接标记为错误
      this.setData({
        showAnswer: true,
        isCorrect: false,
        wrongCount: this.data.wrongCount + 1
      });

      // 自动进入下一个单词
      setTimeout(() => {
        this.nextWord();
      }, 2000);
    }
  },

  // 重新播放当前单词
  replayCurrentWord() {
    this.startDictationFlow();
  },

  // 处理听写输入
  onDictationInputChange(e) {
    this.setData({
      userInput: e.detail.value
    });
  },

  // 处理听写输入确认
  onDictationInputConfirm(e) {
    if (this.data.userInput.trim() && !this.data.showAnswer) {
      this.onSubmitDictationAnswer();
    }
  },

  // 验证答案是否正确（支持特殊格式）
  validateAnswer: function(userAnswer, correctAnswer) {
    if (!userAnswer || !correctAnswer) return false;

    const userInput = userAnswer.trim().toLowerCase();
    const correctText = correctAnswer.toLowerCase();

    // 1. 直接匹配
    if (userInput === correctText) return true;

    // 2. 处理特殊格式
    return this.checkSpecialFormats(userInput, correctText);
  },

  // 检查特殊格式
  checkSpecialFormats: function(userInput, correctText) {
    // 移除星号
    const cleanCorrect = correctText.replace(/\*/g, '');

    // 1. 处理逗号分隔格式 "am, is, are"
    if (cleanCorrect.includes(',')) {
      const options = cleanCorrect.split(',').map(s => s.trim());
      // 用户输入任何一个都算对
      if (options.includes(userInput)) return true;
      // 用户输入多个也算对
      const userOptions = userInput.split(/[,，]/).map(s => s.trim());
      if (userOptions.every(opt => options.includes(opt)) && userOptions.length > 0) return true;
    }

    // 2. 处理斜杠或等号分隔格式 "actor/actress", "kilo=kilogram"
    if (cleanCorrect.includes('/') || cleanCorrect.includes('=')) {
      const separators = /[\/=]/;
      const options = cleanCorrect.split(separators).map(s => s.trim());
      if (options.includes(userInput)) return true;
      // 用户输入多个也算对
      const userOptions = userInput.split(/[\/=,，]/).map(s => s.trim());
      if (userOptions.every(opt => options.includes(opt)) && userOptions.length > 0) return true;
    }

    // 3. 处理括号格式，忽略括号内容
    const withoutBrackets = cleanCorrect.replace(/\([^)]*\)/g, '').trim();
    if (userInput === withoutBrackets) return true;

    // 4. 处理带括号的可选内容 "according (to)", "(real) estate agent"
    const optionalPattern = /\(([^)]+)\)/g;
    let matches = [...cleanCorrect.matchAll(optionalPattern)];
    if (matches.length > 0) {
      // 生成所有可能的组合
      let variations = [cleanCorrect];
      matches.forEach(match => {
        const newVariations = [];
        variations.forEach(variation => {
          // 包含括号内容的版本
          newVariations.push(variation.replace(match[0], match[1]));
          // 不包含括号内容的版本
          newVariations.push(variation.replace(match[0], ''));
          // 保留括号的版本
          newVariations.push(variation);
        });
        variations = newVariations;
      });

      // 清理空格并检查匹配
      for (let variation of variations) {
        const cleaned = variation.replace(/\s+/g, ' ').trim().toLowerCase();
        if (userInput === cleaned) return true;
      }
    }

    // 5. 处理中英文标注格式 "centre（英）center（美）", "programme(美program)"
    const langPattern = /[（(][^)）]*[）)]/g;
    const withoutLangMarks = cleanCorrect.replace(langPattern, '').trim();
    if (userInput === withoutLangMarks) return true;

    // 提取所有可能的单词
    const allWords = cleanCorrect.match(/[a-zA-Z]+/g) || [];
    if (allWords.includes(userInput)) return true;

    // 6. 处理复杂等号格式 "drier"＝"dryer"
    if (cleanCorrect.includes('＝') || cleanCorrect.includes('=')) {
      const parts = cleanCorrect.split(/[＝=]/).map(s => s.replace(/["""]/g, '').trim());
      if (parts.includes(userInput)) return true;
    }

    return false;
  },

  // 提交听写答案
  onSubmitDictationAnswer() {
    if (this.data.showAnswer || !this.data.userInput.trim()) return;

    // 清除倒计时定时器
    if (this.data.answerTimer) {
      clearInterval(this.data.answerTimer);
      this.setData({ answerTimer: null });
    }

    const userAnswer = this.data.userInput.trim();
    const correctAnswer = this.data.currentWord.words || this.data.currentWord.word;
    const isCorrect = this.validateAnswer(userAnswer, correctAnswer);

    this.setData({
      showAnswer: true,
      isCorrect: isCorrect,
      countdown: 0
    });

    // 更新统计
    if (isCorrect) {
      this.setData({
        correctCount: this.data.correctCount + 1
      });
    } else {
      this.setData({
        wrongCount: this.data.wrongCount + 1
      });
    }

    // 🔧 修复：添加到resultDetails用于错词结果显示
    this.data.resultDetails.push({
      word: this.data.currentWord,
      userAnswer: userAnswer,
      isCorrect: isCorrect,
      isSelected: false
    });

    // 🔧 修复：手动提交后显示正确/错误信息，然后进入下一个单词
    console.log('✅ 用户手动提交答案，显示结果后进入下一个单词');

    // 显示结果2秒后自动进入下一个单词
    setTimeout(() => {
      this.nextWord();
    }, 2000);
  },

  // 下一个单词
  nextWord() {
    // 清除倒计时定时器
    if (this.data.answerTimer) {
      clearInterval(this.data.answerTimer);
    }

    const nextIndex = this.data.currentIndex + 1;

    if (nextIndex >= this.data.total) {
      // 练习完成
      this.onComplete();
      return;
    }

    // 设置下一个单词
    this.setData({
      currentIndex: nextIndex,
      currentWord: this.data.words[nextIndex],
      userInput: '',
      showAnswer: false,
      isCorrect: false,
      progress: Math.round((nextIndex / this.data.total) * 100),
      playingStage: 'waiting',
      countdown: 0,
      inputFocused: false,
      answerTimer: null
    });

    // 设置音频
    this.setAudio();

    // 自动开始下一个单词的听写
    setTimeout(() => {
      this.startDictationFlow();
    }, 500);
  },

  // 选择错题
  onSelectMistake(e) {
    const index = e.currentTarget.dataset.index;
    const mistake = this.data.resultDetails[index];
    
    if (!mistake.isCorrect) {
      // 切换选择状态
      const resultDetails = [...this.data.resultDetails];
      resultDetails[index].isSelected = !resultDetails[index].isSelected;
      
      // 更新选中的错题列表
      const selectedMistakes = resultDetails.filter(item => !item.isCorrect && item.isSelected);
      
      this.setData({
        resultDetails: resultDetails,
        selectedMistakes: selectedMistakes
      });
    }
  },

  // 选择所有错题
  onSelectAllMistakes() {
    const resultDetails = [...this.data.resultDetails];
    const allMistakes = [];
    
    // 将所有错题设置为选中状态
    resultDetails.forEach(item => {
      if (!item.isCorrect) {
        item.isSelected = true;
        allMistakes.push(item);
      }
    });
    
    this.setData({
      resultDetails: resultDetails,
      selectedMistakes: allMistakes
    });
  },

  // 重新听写选中的错题
  onRetrySelected() {
    if (this.data.selectedMistakes.length === 0) {
      wx.showToast({
        title: '请先选择要重新听写的单词',
        icon: 'none'
      });
      return;
    }
    
    // 将选中的错题转换为单词格式
    const wordsToRetry = this.data.selectedMistakes.map(mistake => ({
      words: mistake.word, // 使用 words 字段名
      word: mistake.word,  // 保持兼容性
      meaning: mistake.meaning,
      phonetic: this.data.words.find(w => (w.words || w.word) === mistake.word)?.phonetic,
      meanings: this.data.words.find(w => (w.words || w.word) === mistake.word)?.meanings
    }));
    
    // 重新开始听写
    const totalPages = Math.ceil(wordsToRetry.length / this.data.wordsPerPage);
    const currentPageWords = wordsToRetry.slice(0, Math.min(this.data.wordsPerPage, wordsToRetry.length));
    
    this.setData({
      words: wordsToRetry,
      total: wordsToRetry.length,
      totalPages: totalPages,
      currentPageWords: currentPageWords,
      currentPageIndex: 0,
      currentlyPlayingIndex: 0,
      userInputs: new Array(Math.min(this.data.wordsPerPage, wordsToRetry.length)).fill(''),
      showResult: false,
      showResultPage: false,
      correctCount: 0,
      mistakes: [],
      resultDetails: [],
      selectedMistakes: [],
      progress: 0,
      startTime: Date.now(),
      isPageCompleted: false
    });
    
    this.initAudioContext();
    
    if (this.data.practiceMode === 'dictation') {
      setTimeout(() => {
        this.startPageDictation();
      }, 1000);
    }
  },

  // 开始页面听写流程
  startPageDictation() {
    console.log('开始页面听写');
    
    // 如果不是测试恢复模式，清空之前的输入
    if (!this.data.isTestResume || !this.data.examMode) {
      this.setData({
        userInputs: new Array(this.data.currentPageWords.length).fill(''),
        isPageCompleted: false,
        focusedInput: -1
      });
    } else {
      console.log('🔄 测试恢复模式：保留用户之前的输入');
      // 测试恢复时只重置焦点，保留用户输入
      this.setData({
        focusedInput: -1
      });
    }

    // 开始播放
    this.playCurrentPageWords();
  },

  // 启动后台播放任务
  startBackgroundPlayback() {
    console.log('🎵 启动后台播放任务');
    
    // 生成唯一的播放任务ID
    const playbackId = 'playback_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    const startTime = Date.now();
    
    // 保存播放状态到全局存储，以便页面切换时保持
    const playbackState = {
      id: playbackId,
      startTime: startTime,
      currentPageIndex: this.data.currentPageIndex,
      currentPageWords: this.data.currentPageWords,
      dictationPracticeMode: this.data.dictationPracticeMode,
      dictationSettings: this.data.dictationSettings,
      userInputs: this.data.userInputs,
      allUserAnswers: this.data.allUserAnswers,
      // 播放状态
      isActive: true,
      stage: 'playing',
      currentWordIndex: 0,
      audioCompleted: false
    };
    
    try {
      wx.setStorageSync('background_playback_state', playbackState);
      console.log('💾 后台播放状态已保存:', playbackState);
    } catch (error) {
      console.error('❌ 保存后台播放状态失败:', error);
    }
    
    this.setData({
      backgroundPlaybackId: playbackId,
      isBackgroundPlaybackActive: true,
      playbackStartTime: startTime
    });
    
    // 🔧 关键修复：启动真正的后台播放任务
    this.startRealBackgroundPlayback(playbackState);
    
    return playbackId;
  },

  // 启动真正的后台播放任务（独立于页面状态）
  async startRealBackgroundPlayback(playbackState) {
    console.log('🎵 启动真正的后台播放任务');
    
    const { currentPageWords, dictationSettings, dictationPracticeMode, id } = playbackState;
    
    // 设置后台播放任务正在运行
    this.setData({
      backgroundPlaybackTaskRunning: true,
      backgroundPlaybackId: id
    });
    
    try {
      // 真正播放音频
      for (let i = 0; i < currentPageWords.length; i++) {
        // 检查是否被中断
        if (!this.data.backgroundPlaybackTaskRunning || this.data.backgroundPlaybackId !== id) {
          console.log('🛑 后台播放任务被中断');
          return;
        }
        
        console.log(`🎵 [后台播放] 第 ${i + 1} 个单词: ${currentPageWords[i].word || currentPageWords[i].words}`);
        
        // 更新播放进度（存储 + UI）
        this.updateBackgroundPlaybackProgress(i, `playing_word_${i + 1}`);
        this.updateBackgroundPlaybackUI(i, `playing_word_${i + 1}`);
        
        // 播放当前单词
        const playTimes = dictationPracticeMode === 'practice' ? 2 : (dictationSettings?.playCount || 2);
        await this.playWordMultipleTimes(currentPageWords[i], playTimes);
      }
      
             // 播放完成
       console.log('🎵 [后台播放] 播放任务完成');
       this.markBackgroundPlaybackCompleted(id);
       this.onBackgroundPlaybackCompleted(); // 触发UI更新
       
     } catch (error) {
       console.error('🎵 [后台播放] 播放出错:', error);
       this.markBackgroundPlaybackCompleted(id);
       this.onBackgroundPlaybackCompleted(); // 出错时也触发UI更新
     } finally {
       this.setData({ backgroundPlaybackTaskRunning: false });
     }
  },

  // 标记后台播放已完成
  markBackgroundPlaybackCompleted(playbackId) {
    try {
      const savedState = wx.getStorageSync('background_playback_state');
      if (savedState && savedState.id === playbackId && savedState.isActive) {
        console.log('✅ 标记后台播放任务完成');
        
        const completedState = {
          ...savedState,
          isActive: false,
          audioCompleted: true,
          stage: 'completed',
          completedTime: Date.now()
        };
        
        wx.setStorageSync('background_playback_state', completedState);
        console.log('💾 后台播放完成状态已保存');
      }
    } catch (error) {
      console.error('标记后台播放完成失败:', error);
    }
  },

  // 更新后台播放进度（仅存储）
  updateBackgroundPlaybackProgress(currentWordIndex, stage, audioCompleted = false) {
    if (!this.data.isBackgroundPlaybackActive) {
      return;
    }
    
    try {
      const currentState = wx.getStorageSync('background_playback_state');
      if (currentState && currentState.id === this.data.backgroundPlaybackId) {
        const updatedState = {
          ...currentState,
          currentWordIndex: currentWordIndex,
          stage: stage,
          audioCompleted: audioCompleted,
          lastUpdateTime: Date.now(),
          // 更新用户输入
          userInputs: this.data.userInputs,
          allUserAnswers: this.data.allUserAnswers
        };
        
        wx.setStorageSync('background_playback_state', updatedState);
        console.log('🔄 后台播放进度已更新:', { currentWordIndex, stage, audioCompleted });
      }
    } catch (error) {
      console.error('❌ 更新后台播放进度失败:', error);
    }
  },

  // 更新后台播放UI
  updateBackgroundPlaybackUI(currentWordIndex, stage) {
    if (this.data.backgroundPlaybackTaskRunning) {
      this.setData({
        currentlyPlayingIndex: currentWordIndex,
        playingStage: stage
      });
      console.log(`📱 UI已更新: 第 ${currentWordIndex + 1} 个单词 - ${stage}`);
    }
  },

  // 检查并恢复后台播放状态
  async checkAndResumeBackgroundPlayback() {
    console.log('🔍 检查后台播放状态');
    
    try {
      const savedState = wx.getStorageSync('background_playback_state');
      if (!savedState || !savedState.isActive) {
        console.log('没有活跃的后台播放任务');
        return false;
      }
      
      console.log('📋 发现后台播放状态:', savedState);
      
      // 🔧 修复：检查当前是否已有播放任务在进行
      if (this.data.isBackgroundPlaybackActive && this.data.backgroundPlaybackId !== savedState.id) {
        console.log('🚫 检测到不同的播放任务ID，停止当前任务');
        this.stopCurrentPlayback();
      }
      
      // 计算播放经过的时间
      const elapsedTime = Date.now() - savedState.startTime;
      console.log(`⏱️ 播放已进行了 ${Math.round(elapsedTime / 1000)} 秒`);
      
      // 恢复页面状态
      this.setData({
        backgroundPlaybackId: savedState.id,
        isBackgroundPlaybackActive: true,
        playbackStartTime: savedState.startTime,
        userInputs: savedState.userInputs || this.data.userInputs,
        allUserAnswers: savedState.allUserAnswers || this.data.allUserAnswers,
        currentPageWords: savedState.currentPageWords || this.data.currentPageWords,
        dictationSettings: savedState.dictationSettings || this.data.dictationSettings
      });
      
      // 首先尝试获取真实的播放进度
      const realProgress = this.getRealBackgroundPlaybackProgress();
      
      // 如果真实进度显示已完成，或者没有真实进度时估算已完成
      const estimatedProgress = this.estimatePlaybackProgress(savedState, elapsedTime);
      
      if (realProgress.stage === 'completed' || (realProgress.currentWordIndex === 0 && estimatedProgress.audioCompleted)) {
        console.log('🎯 音频播放应该已完成，启动倒计时');
        this.setData({
          audioPlayCompleted: true,
          playingStage: 'completed',
          autoPlaying: false,
          currentlyPlayingIndex: 0
        });
        
        // 更新后台状态为已完成
        this.updateBackgroundPlaybackProgress(0, 'completed', true);
        
        if (this.data.dictationPracticeMode === 'test') {
          setTimeout(() => {
            this.startSubmitCountdown();
          }, 1000);
        }
        
        return true;
      } else {
        console.log('🎵 后台播放任务还在进行中，同步UI状态');
        console.log('📱 用户返回时，后台播放继续，只需同步UI并等待完成');
        
        // 使用真实的播放进度（已经获取过了）
        
        this.setData({
          audioPlayCompleted: false,
          playingStage: realProgress.stage,
          autoPlaying: true,
          currentlyPlayingIndex: realProgress.currentWordIndex,
          backgroundPlaybackTaskRunning: true // 标记后台任务正在运行
        });
        
        console.log(`📱 UI已同步到真实后台播放进度: 第 ${realProgress.currentWordIndex + 1} 个单词`);
        
        // 启动检查器，监听后台播放完成
        this.startPlaybackCompletionChecker();
        
        return true;
      }
      
    } catch (error) {
      console.error('❌ 恢复后台播放状态失败:', error);
      return false;
    }
  },

  // 获取真实的后台播放进度
  getRealBackgroundPlaybackProgress() {
    try {
      const savedState = wx.getStorageSync('background_playback_state');
      if (savedState && savedState.isActive) {
        return {
          stage: savedState.stage || 'playing_word_1',
          currentWordIndex: savedState.currentWordIndex || 0
        };
      }
    } catch (error) {
      console.error('获取真实后台播放进度失败:', error);
    }
    
    // 默认返回第一个单词
    return {
      stage: 'playing_word_1',
      currentWordIndex: 0
    };
  },

  // 估算播放进度
  estimatePlaybackProgress(savedState, elapsedTime) {
    const words = savedState.currentPageWords || [];
    if (words.length === 0) {
      return { audioCompleted: true, stage: 'completed', currentWordIndex: 0 };
    }
    
    // 估算播放时间
    const playTimes = savedState.dictationPracticeMode === 'practice' ? 2 : (savedState.dictationSettings?.playCount || 2);

    // 更保守的时间估算：每次单词播放约2.5秒
    const estimatedWordPlayDuration = playTimes * 2500; // 每次播放约2.5秒（保守估计）

    // 计算总播放时间：所有单词播放时间（现在是一页一个单词，不需要停顿）
    const totalPlayTime = words.length * estimatedWordPlayDuration;
    const totalEstimatedTime = totalPlayTime;
    
    console.log(`📊 播放时间估算详情:
      - 单词数: ${words.length}
      - 每个单词播放时间: ${estimatedWordPlayDuration}ms (播放${playTimes}次)
      - 总播放时长: ${Math.round(totalEstimatedTime / 1000)} 秒
      - 已过时间: ${Math.round(elapsedTime / 1000)} 秒`);
    
    // 如果总时间已过，说明播放完成
    if (elapsedTime >= totalEstimatedTime) {
      console.log('🎯 根据时间估算，播放应该已完成');
      return { audioCompleted: true, stage: 'completed', currentWordIndex: 0 };
    }
    
    // 估算当前应该播放到第几个单词
    let remainingTime = elapsedTime;
    let currentWordIndex = 0;
    
    for (let i = 0; i < words.length; i++) {
      const wordTotalTime = estimatedWordPlayDuration;

      if (remainingTime <= wordTotalTime) {
        currentWordIndex = i;
        break;
      }
      
      remainingTime -= wordTotalTime;
      
      if (i === words.length - 1) {
        // 如果循环完了还有剩余时间，说明应该播放完了
        console.log('🎯 循环完成，播放应该已完成');
        return { audioCompleted: true, stage: 'completed', currentWordIndex: 0 };
      }
    }
    
    console.log(`📍 估算结果: 应该播放第 ${currentWordIndex + 1} 个单词`);
    
    return {
      audioCompleted: false,
      stage: `playing_word_${currentWordIndex + 1}`,
      currentWordIndex: currentWordIndex
    };
  },

  // 从指定索引继续播放剩余内容
  async continuePlaybackFromIndex(startIndex) {
    console.log(`📍 从索引 ${startIndex} 继续播放剩余内容`);
    
    // 检查播放状态
    if (this.data.isPlayingStopped) {
      console.log('🛑 播放已停止，无法继续播放');
      return;
    }
    
    this.setData({
      autoPlaying: true,
      isPlayingStopped: false
    });

    // 从指定位置开始播放剩余单词
    for (let i = startIndex; i < this.data.currentPageWords.length; i++) {
      // 检查是否需要停止播放
      if (this.data.isPlayingStopped) {
        console.log('🛑 播放被中断，停止继续播放');
        return;
      }
      
      this.setData({
        currentlyPlayingIndex: i,
        playingStage: `playing_word_${i + 1}`
      });

      // 更新后台播放进度
      if (this.data.isBackgroundPlaybackActive) {
        this.updateBackgroundPlaybackProgress(i, `playing_word_${i + 1}`);
      }

      // 播放当前单词（指定次数）
      const playTimes = this.data.dictationPracticeMode === 'practice' ? 2 : (this.data.dictationSettings.playCount || 2);
      await this.playWordMultipleTimes(this.data.currentPageWords[i], playTimes);
      
      // 再次检查是否需要停止播放
      if (this.data.isPlayingStopped) {
        console.log('🛑 播放被中断，停止继续播放');
        return;
      }
      
      // 现在是一页一个单词，不需要单词间停顿
    }

    // 检查是否因为中断而退出
    if (this.data.isPlayingStopped) {
      console.log('🛑 继续播放任务被中断');
      return;
    }

    console.log('🎉 继续播放完成，启动倒计时');
    this.setData({
      autoPlaying: false,
      playingStage: 'completed',
      currentlyPlayingIndex: 0,
      audioPlayCompleted: true
    });
    
    // 更新后台播放状态为已完成
    if (this.data.isBackgroundPlaybackActive) {
      this.updateBackgroundPlaybackProgress(0, 'completed', true);
    }
    
    // 如果是测试模式，开始10秒倒计时
    if (this.data.dictationPracticeMode === 'test') {
      this.startSubmitCountdown();
    }
  },

  // 停止后台播放
  stopBackgroundPlayback() {
    console.log('🛑 停止后台播放任务');
    
    this.setData({
      backgroundPlaybackId: null,
      isBackgroundPlaybackActive: false,
      playbackStartTime: null
    });
    
    try {
      wx.removeStorageSync('background_playback_state');
      console.log('🗑️ 后台播放状态已清理');
    } catch (error) {
      console.error('❌ 清理后台播放状态失败:', error);
    }
  },

  // 启动播放完成检查器
  startPlaybackCompletionChecker() {
    console.log('🔍 启动播放完成检查器');
    
    // 清除之前的检查器
    if (this.playbackChecker) {
      clearInterval(this.playbackChecker);
    }
    
    this.playbackChecker = setInterval(() => {
      try {
        const savedState = wx.getStorageSync('background_playback_state');
        if (!savedState || !savedState.isActive) {
          console.log('🎯 检测到后台播放已停止');
          this.onBackgroundPlaybackCompleted();
          return;
        }
        
        // 检查是否应该完成了
        const elapsedTime = Date.now() - savedState.startTime;
        const estimatedProgress = this.estimatePlaybackProgress(savedState, elapsedTime);
        
        if (estimatedProgress.audioCompleted) {
          console.log('🎯 检测到后台播放应该已完成');
          this.onBackgroundPlaybackCompleted();
        }
      } catch (error) {
        console.error('播放完成检查失败:', error);
      }
    }, 2000); // 每2秒检查一次
  },

  // 后台播放完成处理
  onBackgroundPlaybackCompleted() {
    console.log('🎉 后台播放已完成');
    
    // 清除检查器
    if (this.playbackChecker) {
      clearInterval(this.playbackChecker);
      this.playbackChecker = null;
    }
    
    // 更新UI状态
    this.setData({
      audioPlayCompleted: true,
      playingStage: 'completed',
      autoPlaying: false,
      currentlyPlayingIndex: 0,
      backgroundPlaybackTaskRunning: false
    });
    
    // 清理后台播放状态
    this.stopBackgroundPlayback();
    
    // 如果是测试模式，启动倒计时
    if (this.data.dictationPracticeMode === 'test') {
      setTimeout(() => {
        this.startSubmitCountdown();
      }, 1000);
    }
  },

  // 强制停止所有播放任务（用于修复双重播放）
  forceStopAllPlayback() {
    console.log('🛑 [FORCE] 强制停止所有播放任务');
    
    // 设置全局停止标志
    this.setData({
      isPlayingStopped: true,
      autoPlaying: false,
      isPlaying: false
    });
    
    // 设置全局停止标记
    try {
      wx.setStorageSync('audio_playback_stopped', Date.now());
    } catch (error) {
      console.log('设置停止标记失败:', error);
    }
    
    // 停止并销毁主音频上下文
    if (this.data.audioContext) {
      try {
        this.data.audioContext.stop && this.data.audioContext.stop();
        this.data.audioContext.pause && this.data.audioContext.pause();
        this.data.audioContext.destroy && this.data.audioContext.destroy();
        console.log('🗑️ [FORCE] 已销毁主音频上下文');
      } catch (error) {
        console.log('强制停止主音频上下文失败:', error);
      }
    }
    
    // 停止后台播放任务
    this.stopBackgroundPlayback();
    
    // 等待一段时间确保所有播放都停止
    return new Promise(resolve => {
      setTimeout(() => {
        console.log('✅ [FORCE] 强制停止完成');
        resolve();
      }, 500);
    });
  },

  // 停止当前播放（彻底停止）
  stopCurrentPlayback() {
    console.log('🛑 停止当前播放任务');
    
    // 设置停止标志
    this.setData({
      isPlayingStopped: true,
      autoPlaying: false,
      isPlaying: false
    });
    
    // 停止后台播放任务
    this.stopBackgroundPlayback();
    
    // 停止并销毁主音频上下文
    if (this.data.audioContext) {
      try {
        this.data.audioContext.stop && this.data.audioContext.stop();
        this.data.audioContext.pause && this.data.audioContext.pause();
        // 清除事件监听器
        this.data.audioContext.offPlay && this.data.audioContext.offPlay();
        this.data.audioContext.offEnded && this.data.audioContext.offEnded();
        this.data.audioContext.offError && this.data.audioContext.offError();
        // 销毁音频上下文
        this.data.audioContext.destroy && this.data.audioContext.destroy();
        console.log('🗑️ 已销毁主音频上下文');
      } catch (error) {
        console.log('停止主音频上下文时出错:', error);
      }
      
      // 清除引用
      this.setData({ audioContext: null });
    }
    
    // 停止所有可能的InnerAudioContext实例
    // 由于无法直接访问所有创建的innerAudioContext，我们通过全局标记来阻止新的播放
    wx.setStorageSync('audio_playback_stopped', Date.now());
    
    // 如果有正在进行的播放Promise，等待其完成或中断
    if (this.data.currentPlayingPromise) {
      this.data.currentPlayingPromise = null;
    }
    
    console.log('✅ 播放任务已停止');
  },

  // 播放当前页面的所有单词
  async playCurrentPageWords() {
    console.log('🎵 [MAIN] 开始连续播放当前页面所有单词');
    
    // 🔧 修复：如果是测试恢复模式，检查并恢复后台播放状态
    if (this.data.isTestResume) {
      console.log('🔄 [MAIN] 测试恢复模式：检查后台播放状态');
      const resumed = await this.checkAndResumeBackgroundPlayback();
      if (resumed) {
        console.log('🔄 [MAIN] 后台播放状态恢复成功');
        return;
      } else {
        console.log('🔄 [MAIN] 后台播放状态恢复失败，继续正常播放流程');
        // 清除恢复标记，继续正常播放
        this.setData({ isTestResume: false });
      }
    }
    
    // 如果已经有后台播放在进行，且不是第一次启动，跳过重复播放
    if (this.data.isBackgroundPlaybackActive && !this.data.isTestResume) {
      console.log('🔄 [MAIN] 后台播放任务已在进行中，跳过重新播放');
      return;
    }
    
    // 启动后台播放任务（测试模式才启用）
    if (this.data.dictationPracticeMode === 'test') {
      console.log('🎵 [MAIN] 测试模式：只启动后台播放任务，不进行前台播放');
      this.startBackgroundPlayback();
      
      // 测试模式：设置播放状态但不实际播放音频
      this.setData({
        isPlayingStopped: false,
        autoPlaying: true,
        playingStage: 'playing',
        currentlyPlayingIndex: 0
      });
      
      return; // 测试模式不进行前台播放，只依赖后台播放
    }
    
    // 练习模式：重置停止标志，进行正常的前台播放
    console.log('🎵 [MAIN] 练习模式：进行正常的前台播放');
    this.setData({
      isPlayingStopped: false,
      autoPlaying: true,
      playingStage: 'playing'
    });

    for (let i = 0; i < this.data.currentPageWords.length; i++) {
      // 检查是否需要停止播放
      if (this.data.isPlayingStopped) {
        console.log('🛑 播放已被停止，退出播放循环');
        return;
      }
      
      this.setData({
        currentlyPlayingIndex: i,
        playingStage: `playing_word_${i + 1}`
      });

      // 更新后台播放进度
      if (this.data.isBackgroundPlaybackActive) {
        this.updateBackgroundPlaybackProgress(i, `playing_word_${i + 1}`);
      }

      // 播放当前单词（指定次数）
      const playTimes = this.data.dictationPracticeMode === 'practice' ? 2 : (this.data.dictationSettings.playCount || 2);
      await this.playWordMultipleTimes(this.data.currentPageWords[i], playTimes);
      
      // 再次检查是否需要停止播放
      if (this.data.isPlayingStopped) {
        console.log('🛑 播放已被停止，退出播放循环');
        return;
      }
      
      // 现在是一页一个单词，不需要单词间停顿
    }

    // 检查是否因为停止而退出
    if (this.data.isPlayingStopped) {
      console.log('🛑 播放任务已被中断');
      return;
    }

    console.log('页面所有单词播放完成');
    this.setData({
      autoPlaying: false,
      playingStage: 'completed',
      currentlyPlayingIndex: 0,
      audioPlayCompleted: true
    });
    
    // 更新后台播放状态为已完成
    if (this.data.isBackgroundPlaybackActive) {
      this.updateBackgroundPlaybackProgress(0, 'completed', true);
    }
    
    // 如果是测试模式，开始10秒倒计时
    if (this.data.dictationPracticeMode === 'test') {
      this.startSubmitCountdown();
    }
  },

  // 播放单词多次（遍数间固定1秒停顿）
  async playWordMultipleTimes(word, times) {
    return new Promise((resolve) => {
      // 检查播放是否已停止
      if (this.data.isPlayingStopped) {
        console.log('🛑 播放已停止，跳过单词播放');
        resolve();
        return;
      }
      
      const wordText = word.words || word.word;
      if (!wordText) {
        console.error('单词文本为空');
        resolve();
        return;
      }
      
      // 调用TTS云函数获取播放列表
      this.getWordPlayList(wordText, times).then((playList) => {
        // 再次检查播放是否已停止
        if (this.data.isPlayingStopped) {
          console.log('🛑 播放已停止，跳过音频序列播放');
          resolve();
          return;
        }
        
        if (!playList || playList.length === 0) {
          console.error('无法获取单词播放列表');
          resolve();
          return;
        }
        
        // 播放列表中的所有音频
        this.playAudioSequence(playList, resolve);
      }).catch((error) => {
        console.error('获取播放列表失败:', error);
        resolve();
      });
    });
  },

  // 获取单词播放列表
  async getWordPlayList(wordText, times) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'ttsSpeak',
        data: {
          text: wordText,
          mode: this.data.dictationPracticeMode, // practice 或 test
          playCount: times,
          wordType: 'dictation'
        }
      });
      
      console.log('TTS云函数返回结果:', result);

      if (result.result && result.result.success) {
        const { playList, source } = result.result.data;
        
        // 如果是降级模式，使用备用播放逻辑
        if (source === 'fallback' || source === 'fallback_error') {
          console.log('使用降级播放模式');
          return this.createFallbackPlayList(wordText, this.data.dictationPracticeMode, times);
        }
        
        return playList;
      } else {
        console.error('TTS云函数调用失败:', result.result);
        // 使用降级播放逻辑
        return this.createFallbackPlayList(wordText, this.data.dictationPracticeMode, times);
      }
    } catch (error) {
      console.error('调用TTS云函数失败:', error);
      // 使用降级播放逻辑
      return this.createFallbackPlayList(wordText, this.data.dictationPracticeMode, times);
    }
  },

  // 创建降级播放列表
  createFallbackPlayList(word, mode, playCount) {
    console.log(`创建降级播放列表: ${word}`);
    
    // 解析特殊格式（简化版本）
    const playList = [];
    
    // 双拼写格式：petrol（美）gas（英）
    const doubleSpellingMatch = word.match(/^(.+?)（[^）]*）(.+?)（[^）]*）$/);
    if (doubleSpellingMatch) {
      const word1 = doubleSpellingMatch[1].trim();
      const word2 = doubleSpellingMatch[2].trim();
      
      if (mode === 'practice') {
        playList.push({ word: word1, type: 'american', playTimes: 1, fallback: true });
        playList.push({ word: word2, type: 'british', playTimes: 1, fallback: true });
      } else {
        // 测试模式
        if (playCount === 1) {
          playList.push({ word: word2, type: 'british', playTimes: 1, fallback: true });
        } else if (playCount === 2) {
          playList.push({ word: word1, type: 'american', playTimes: 1, fallback: true });
          playList.push({ word: word2, type: 'british', playTimes: 1, fallback: true });
        } else {
          playList.push({ word: word1, type: 'american', playTimes: 1, fallback: true });
          playList.push({ word: word2, type: 'british', playTimes: 1, fallback: true });
          playList.push({ word: word2, type: 'british', playTimes: 1, fallback: true });
        }
      }
      return playList;
    }
    
    // 动词变形格式：pay(paid,paid)
    const verbFormsMatch = word.match(/^(.+?)\(([^,]+),([^)]+)\)$/);
    if (verbFormsMatch) {
      const baseForm = verbFormsMatch[1].trim();
      const pastForm = verbFormsMatch[2].trim();
      const pastParticipleForm = verbFormsMatch[3].trim();
      
      if (mode === 'practice') {
        playList.push({ word: baseForm, type: 'base', playTimes: 2, fallback: true });
      } else {
        // 测试模式只读原形
        playList.push({ word: baseForm, type: 'base', playTimes: playCount, fallback: true });
      }
      return playList;
    }
    
    // 斜杠分隔格式：a.m./A.M.
    const slashFormatMatch = word.match(/^(.+?)\/(.+?)$/);
    if (slashFormatMatch) {
      const leftPart = slashFormatMatch[1].trim();
      const rightPart = slashFormatMatch[2].trim();
      
      console.log(`检测到斜杠格式: ${leftPart}/${rightPart}, 只播放左边部分: ${leftPart}`);
      
      // 只对左边的单词进行发音，处理逻辑与普通单词相同
      const times = mode === 'practice' ? 2 : playCount;
      playList.push({ word: leftPart, type: 'slash_format', playTimes: times, fallback: true });
      
      return playList;
    }
    
    // 普通单词
    const times = mode === 'practice' ? 2 : playCount;
    playList.push({ word: word, type: 'normal', playTimes: times, fallback: true });
    
    return playList;
  },

  // 播放音频序列
  async playAudioSequence(playList, callback) {
    if (!playList || playList.length === 0) {
      console.error('播放列表为空');
      callback && callback();
      return;
    }

    console.log('开始播放音频序列:', playList);

    for (const playItem of playList) {
      // 检查播放是否已停止
      if (this.data.isPlayingStopped) {
        console.log('🛑 播放已停止，中断音频序列播放');
        callback && callback();
        return;
      }
      
      const { word, playTimes, audioUrl, fallback } = playItem;

      for (let i = 0; i < playTimes; i++) {
        // 再次检查播放是否已停止
        if (this.data.isPlayingStopped) {
          console.log('🛑 播放已停止，中断单词重复播放');
          callback && callback();
          return;
        }
        
        try {
          if (fallback || !audioUrl) {
            // 降级模式：无可用音频
            console.error(`无可用音频: ${word} (第${i + 1}/${playTimes}遍)`);
            throw new Error('无可用音频源');
          } else {
            // 正常模式：使用云端音频
            console.log(`播放音频: ${word} (第${i + 1}/${playTimes}遍) - ${audioUrl}`);
            await this.playAudioOnce(audioUrl);
          }

          // 播放间隔
          if (i < playTimes - 1) {
            await this.delay(300); // 300ms间隔
          }
        } catch (error) {
          console.error(`播放失败: ${word}`, error);
          // 播放失败，不再降级
          wx.showToast({
            title: '音频播放失败',
            icon: 'none'
          });
        }
      }

      // 每个单词之间的间隔
      if (playList.indexOf(playItem) < playList.length - 1) {
        await this.delay(500); // 500ms间隔
      }
    }

    console.log('音频序列播放完成');
    callback && callback();
  },

  // 延时函数
  delay(ms) {
    return new Promise(resolve => {
      const checkInterval = 50; // 每50ms检查一次
      let elapsed = 0;
      
      const timer = setInterval(() => {
        // 检查播放是否已停止
        if (this.data.isPlayingStopped) {
          console.log('🛑 播放已停止，中断延时');
          clearInterval(timer);
          resolve();
          return;
        }
        
        elapsed += checkInterval;
        if (elapsed >= ms) {
          clearInterval(timer);
          resolve();
        }
      }, checkInterval);
    });
  },



  // 播放单个音频一次
  async playAudioOnce(audioUrl) {
    return new Promise((resolve) => {
      console.log(`🎵 [AUDIO] 开始播放音频: ${audioUrl?.substring(0, 50)}...`);

      // 检查播放停止标记
      if (this.data.isPlayingStopped) {
        console.log('🛑 [AUDIO] 播放已停止，跳过音频播放');
        resolve();
        return;
      }

      // 检查全局存储的停止标记
      try {
        const stopTime = wx.getStorageSync('audio_playback_stopped');
        if (stopTime && (Date.now() - stopTime) < 5000) { // 5秒内的停止标记有效
          console.log('🛑 [AUDIO] 检测到全局停止标记，跳过音频播放');
          resolve();
          return;
        }
      } catch (error) {
        console.log('检查停止标记失败:', error);
      }

      // 创建新的音频上下文，避免复用导致的问题
      const audioContext = wx.createInnerAudioContext();
      audioContext.autoplay = false;
      audioContext.loop = false;
      audioContext.volume = 1.0;

      // 设置事件监听器
      audioContext.onEnded(() => {
        console.log(`✅ [AUDIO] 音频播放完成`);
        this.setData({ isPlaying: false });
        clearTimeout(timeoutId); // 清除超时
        if (!isDestroyed && typeof audioContext.destroy === 'function') {
          audioContext.destroy(); // 播放完成后立即销毁
          isDestroyed = true;
        }
        resolve();
      });

      audioContext.onError((error) => {
        console.error('❌ [AUDIO] 音频播放错误:', error);
        this.setData({ isPlaying: false });
        clearTimeout(timeoutId); // 清除超时
        if (!isDestroyed && typeof audioContext.destroy === 'function') {
          audioContext.destroy(); // 出错时也要销毁
          isDestroyed = true;
        }
        resolve();
      });

      // 设置音频源并播放
      audioContext.src = audioUrl;
      this.setData({ isPlaying: true });

      try {
        console.log(`🎵 [AUDIO] 开始播放音频上下文`);
        const playResult = audioContext.play();
        if (playResult && typeof playResult.then === 'function') {
          playResult.catch((error) => {
            console.error('❌ [AUDIO] 音频播放启动失败:', error);
            this.setData({ isPlaying: false });
            audioContext.destroy(); // 启动失败时也要销毁
            resolve();
          });
        }
      } catch (error) {
        console.error('❌ [AUDIO] 音频播放启动异常:', error);
        this.setData({ isPlaying: false });
        audioContext.destroy(); // 异常时也要销毁
        resolve();
      }

      // 设置超时保护，防止音频卡住
      let isDestroyed = false;
      const timeoutId = setTimeout(() => {
        try {
          if (audioContext && !isDestroyed) {
            console.log('⏰ [AUDIO] 音频播放超时，强制结束');
            this.setData({ isPlaying: false });
            if (typeof audioContext.destroy === 'function') {
              audioContext.destroy();
              isDestroyed = true;
            }
            resolve();
          }
        } catch (error) {
          console.error('超时处理错误:', error);
          resolve();
        }
      }, 10000); // 10秒超时
    });
  },

  // 格式化时间
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  },

  // 加入错题本
  async onAddToMistakes() {
    const app = getApp();
    const db = wx.cloud.database();
    const mistakes = this.data.mistakes;

    // 使用更可靠的登录状态检查
    if (!app.canCollectMistakes()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    const userId = app.getUserOpenId();

    try {
      // 批量添加错题
      for (const mistake of mistakes) {
        await db.collection('mistake_spelling').add({
          data: {
            ...mistake,
            createTime: db.serverDate(),
            openid: userId
          }
        });
      }

      wx.showToast({
        title: '已加入错题本',
        icon: 'success'
      });
    } catch (error) {
      console.error('添加错题失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    }
  },

  // 错题重练
  onRetryMistakes() {
    if (this.data.mistakes.length === 0) return;

    // 构建任务数据
    const taskData = {
      words: this.data.mistakes
    };

    // 跳转到练习页面
    wx.redirectTo({
      url: `/pages/spelling/practice/practice?data=${JSON.stringify(taskData)}`
    });
  },

  // 显示答案（已废弃 - 听写模式不应显示答案）
  onShowAnswer() {
    // 听写模式下不允许显示答案，这违反了听写的基本原则
    console.log('听写模式不允许显示答案');
    wx.showToast({
      title: '听写模式需要靠听力完成',
      icon: 'none',
      duration: 2000
    });
    return;
  },

  // 返回首页
  onBackToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 测试TTS功能
  async testTTSFunction() {
    console.log('=== 开始测试TTS功能 ===');
    
    try {
      wx.showLoading({ title: '测试中...' });
      
      // 测试原始ttsSpeak云函数
      console.log('测试ttsSpeak云函数...');
      const ttsResult = await wx.cloud.callFunction({
        name: 'ttsSpeak',
        data: {
          text: 'hello',
          lang: 'en'
        }
      });
      
      console.log('ttsSpeak结果:', ttsResult.result);
      
      // 测试Free Dictionary API连通性
      console.log('Free Dictionary API测试完成，查看上面的日志');
      
      wx.hideLoading();
      
      // 显示结果
      const resultText = `
ttsSpeak云函数:
- 成功: ${ttsResult.result?.success}
- 音频源: ${ttsResult.result?.data?.source || 'unknown'}
- 音频URL: ${ttsResult.result?.data?.audioUrl?.substring(0, 50)}...

请查看控制台获取详细日志`;
      
      wx.showModal({
        title: 'TTS测试结果',
        content: resultText,
        showCancel: false
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('TTS测试失败:', error);
      wx.showModal({
        title: 'TTS测试失败',
        content: error.message,
        showCancel: false
      });
    }
  },

  // 测试TTS功能
  async refreshTTSCache() {
    console.log('=== 测试TTS功能 ===');

    if (!this.data.currentPageWords || this.data.currentPageWords.length === 0) {
      wx.showToast({
        title: '没有可测试的单词',
        icon: 'none'
      });
      return;
    }

    const firstWord = this.data.currentPageWords[0];
    const wordText = (firstWord.words || firstWord.word || '').trim();

    if (!wordText) {
      wx.showToast({
        title: '单词数据错误',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({ title: '测试中...' });

      const result = await wx.cloud.callFunction({
        name: 'ttsSpeak',
        data: {
          text: wordText,
          lang: 'en'
        }
      });

      wx.hideLoading();

      console.log('TTS测试结果:', result.result);

      if (result.result && result.result.success) {
        const source = result.result.data?.source || 'unknown';
        wx.showModal({
          title: 'TTS测试成功',
          content: `单词: ${wordText}\n音频源: ${source}\n状态: 正常工作`,
          showCancel: false
        });
      } else {
        wx.showModal({
          title: 'TTS测试失败',
          content: result.result?.message || '未知错误',
          showCancel: false
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('TTS测试失败:', error);
      wx.showToast({
        title: '测试失败',
        icon: 'none'
      });
    }
  },

  // 测试特殊格式单词播放功能
  async testSpecialFormatPlayback() {
    console.log('=== 测试特殊格式单词播放 ===');
    
    const testWords = [
      'petrol（美）gas（英）',  // 双拼写格式
      'pay(paid,paid)',         // 动词变形格式（相同过去式和过去分词）
      'write(wrote,written)',   // 动词变形格式（不同过去式和过去分词）
      'hello'                   // 普通单词
    ];
    
    try {
      wx.showLoading({ title: '测试中...' });
      
      for (const testWord of testWords) {
        console.log(`测试单词: ${testWord}`);
        
        // 测试听写练习模式
        const dictationResult = await wx.cloud.callFunction({
          name: 'ttsSpeak',
          data: {
            text: testWord,
            lang: 'en',
            mode: 'practice',
            playCount: 2,
            wordType: 'dictation'
          }
        });
        
        console.log(`听写练习模式结果:`, dictationResult.result);
        
        // 测试英译汉模式
        const translationResult = await wx.cloud.callFunction({
          name: 'ttsSpeak',
          data: {
            text: testWord,
            lang: 'en',
            mode: 'practice',
            playCount: 2,
            wordType: 'translation'
          }
        });
        
        console.log(`英译汉模式结果:`, translationResult.result);
      }
      
      wx.hideLoading();
      
      wx.showModal({
        title: '测试完成',
        content: '特殊格式单词播放测试完成，请查看控制台日志获取详细结果',
        showCancel: false
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('测试失败:', error);
      wx.showModal({
        title: '测试失败',
        content: error.message,
        showCancel: false
      });
    }
  },

  // 倒计时函数（单词间停顿时不显示倒计时）
  countdown(seconds) {
    return new Promise((resolve) => {
      let remainingSeconds = seconds;
      // 不显示单词间停顿的倒计时，避免用户困惑
      // this.setData({ countdown: remainingSeconds });

      const timer = setInterval(() => {
        // 检查播放是否已停止
        if (this.data.isPlayingStopped) {
          console.log('🛑 播放已停止，中断倒计时');
          clearInterval(timer);
          resolve();
          return;
        }

        remainingSeconds--;
        // 不显示单词间停顿的倒计时
        // this.setData({ countdown: remainingSeconds });

        if (remainingSeconds <= 0) {
          clearInterval(timer);
          resolve();
        }
      }, 1000);
    });
  },

  // 输入确认处理
  onInputConfirm(e) {
    const { index } = e.currentTarget.dataset;
    // 自动聚焦到下一个输入框
    if (index < this.data.currentPageWords.length - 1) {
      this.setData({
        focusedInput: index + 1
      });
    }
  },

  // 检查页面完成状态
  checkPageCompletion() {
    const { userInputs, currentPageWords } = this.data;
    const completedCount = userInputs.filter(input => input && input.trim()).length;
    const isCompleted = completedCount === currentPageWords.length;
    
    this.setData({
      isPageCompleted: isCompleted
    });
    
    return isCompleted;
  },

  // 检查当前页面答案
  checkCurrentPageAnswers() {
    const { currentPageWords, userInputs } = this.data;
    let correctCount = 0;
    let results = [];

    for (let i = 0; i < currentPageWords.length; i++) {
      const word = currentPageWords[i];
      const userInput = (userInputs[i] || '').trim().toLowerCase();
      const correctAnswer = (word.words || word.word || '').trim().toLowerCase();
      
      const isCorrect = userInput === correctAnswer;
      if (isCorrect) {
        correctCount++;
      }

      results.push({
        word: word,
        userInput: userInputs[i] || '',
        correctAnswer: word.words || word.word || '',
        isCorrect: isCorrect
      });
    }

    // 显示结果
    this.showPageResults(results, correctCount);
  },

  // 显示页面结果
  showPageResults(results, correctCount) {
    const totalCount = results.length;
    const wrongCount = totalCount - correctCount;
    
    let resultText = `本页结果：${correctCount}/${totalCount} 正确\n\n`;
    
    results.forEach((result, index) => {
      const status = result.isCorrect ? '✓' : '✗';
      resultText += `${index + 1}. ${status} ${result.correctAnswer}\n`;
      if (!result.isCorrect && result.userInput) {
        resultText += `   您的答案：${result.userInput}\n`;
      }
    });

    wx.showModal({
      title: '听写结果',
      content: resultText,
      showCancel: false,
      confirmText: '确定',
      success: () => {
        // 记录错误单词
        this.recordMistakes(results.filter(r => !r.isCorrect));
      }
    });
  },

  // 记录错误单词
  recordMistakes(mistakes) {
    if (mistakes.length === 0) return;

    mistakes.forEach(mistake => {
      // 构建错题数据
      const mistakeRecord = {
        word: mistake.word.words || mistake.word.word,
        meaning: mistake.word.meaning || mistake.word.chinese,
        phonetic: mistake.word.phonetic || '',
        example: mistake.word.example || '',
        userAnswer: mistake.userInput,
        correctAnswer: mistake.correctAnswer,
        mistakeType: 'dictation', // 标识来源为听写
        practiceMode: this.data.dictationPracticeMode, // 练习模式或测试模式
        libraryId: this.data.libraryId,
        libraryName: this.data.libraryName,
        createTime: new Date()
      };

      // 测试模式下自动添加错题到错题本
      if (this.data.dictationPracticeMode === 'test') {
        const app = getApp();
        
        // 使用更可靠的登录状态检查
        if (app.canCollectMistakes()) {
          const userId = app.getUserOpenId();
          
          wx.cloud.callFunction({
            name: 'addMistake',
            data: {
              userId: userId,
              wordId: mistakeRecord.word,
              type: 'word', // 统一使用word类型，通过testMode区分
              extra: mistakeRecord
            }
          }).then(result => {
            console.log('错题已自动添加到错题本:', result);
          }).catch(error => {
            console.error('自动添加错题失败:', error);
          });
        } else {
          console.log('用户未登录或openid不可用，跳过错题收集');
        }
      }
    });
  },

  // 下一页
  nextPage() {
    // 保存当前页面的答案
    if (this.data.dictationPracticeMode === 'test') {
      const allUserAnswers = { ...this.data.allUserAnswers };
      allUserAnswers[this.data.currentPageIndex] = [...this.data.userInputs];
      this.setData({ allUserAnswers });
    }
    
    const nextPageIndex = this.data.currentPageIndex + 1;
    
    if (nextPageIndex < this.data.totalPages) {
      this.loadPage(nextPageIndex);
    }
  },

  // 完成听写
  finishDictation() {
    // 🔧 调试：检查模式设置
    console.log('🔍 finishDictation 调用，检查模式:', {
      dictationPracticeMode: this.data.dictationPracticeMode,
      competitionMode: this.data.competitionMode,
      examMode: this.data.examMode
    });

    // 🔧 检查是否有分组测试
    const app = getApp();
    const groupingData = app.globalData.testGroupingData;
    const learningData = app.globalData.learningData;
    
    if (this.data.dictationPracticeMode === 'test' && groupingData && groupingData.testMode === 'dictation') {
      // 有分组且不是最后一组
      if (groupingData.currentGroup < groupingData.totalGroups) {
        // 先解除考试锁定模式
        if (this.data.examMode) {
          this.disableExamMode();
        }
        
        // 显示当前组完成提示，询问是否继续下一组
        wx.showModal({
          title: '当前组完成！',
          content: `第${groupingData.currentGroup}组听写完成！\n\n还有${groupingData.totalGroups - groupingData.currentGroup}组待完成，是否继续下一组？`,
          showCancel: true,
          cancelText: '查看结果',
          confirmText: '下一组',
          success: (res) => {
            if (res.confirm) {
              this.goToNextGroup();
            } else {
              this.generateTestResults();
            }
          }
        });
        return;
      }
    }
    
    // 如果是测试模式（包括竞赛模式），先解除锁定，然后生成详细结果
    if (this.data.dictationPracticeMode === 'test' || this.data.competitionMode) {
      // 先解除考试锁定模式
      if (this.data.examMode) {
        this.disableExamMode();
      }
      this.generateTestResults();
    } else {
      // 练习模式直接返回
      wx.showModal({
        title: '听写完成',
        content: '恭喜你完成了所有单词的听写！',
        showCancel: false,
        confirmText: '返回',
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 🔧 新增：进入下一组听写
  goToNextGroup() {
    const app = getApp();
    const groupingData = app.globalData.testGroupingData;
    
    if (!groupingData) {
      wx.showToast({ title: '分组数据错误', icon: 'error' });
      return;
    }
    
    const nextGroup = groupingData.currentGroup + 1;
    const startIndex = (nextGroup - 1) * groupingData.wordsPerGroup;
    const endIndex = Math.min(startIndex + groupingData.wordsPerGroup, groupingData.allWords.length);
    const nextGroupWords = groupingData.allWords.slice(startIndex, endIndex);
    
    // 更新分组数据
    groupingData.currentGroup = nextGroup;
    app.globalData.testGroupingData = groupingData;
    
    // 更新学习数据
    app.globalData.learningData = {
      ...app.globalData.learningData,
      words: nextGroupWords,
      currentGroup: nextGroup,
      totalGroups: groupingData.totalGroups,
      isGrouped: true
    };
    
    console.log(`听写进入第${nextGroup}组，词汇数量:`, nextGroupWords.length);
    
    // 跳转到听写页面
    wx.redirectTo({
      url: `/pages/spelling/practice/practice?mode=dictation&practiceMode=test`,
      success: () => {
        console.log('成功跳转到下一组听写');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({ title: '跳转失败', icon: 'error' });
      }
    });
  },

  // 页面卸载时清理
  onUnload() {
    // 测试模式下不停止播放，让它在后台继续
    if (this.data.dictationPracticeMode === 'test' && this.data.examMode) {
      console.log('🎵 测试模式页面卸载，播放任务继续在后台运行');
      // 不调用 stopCurrentPlayback，让播放任务继续
      
      // 清理检查器但不停止后台播放
      if (this.playbackChecker) {
        clearInterval(this.playbackChecker);
        this.playbackChecker = null;
      }
    } else {
      // 练习模式停止播放
      this.stopCurrentPlayback();
    }
    
    // 如果是测试模式，恢复正常模式
    if (this.data.dictationPracticeMode === 'test') {
      this.disableExamMode();
    }
    
    // 清理音频上下文（如果不是测试模式）
    if (!this.data.examMode && this.data.audioContext) {
      try {
        if (this.data.audioContext.destroy) {
          this.data.audioContext.destroy();
          console.log('🗑️ 页面卸载时已销毁音频上下文');
        }
      } catch (error) {
        console.error('音频上下文销毁失败:', error);
      }
    }
    
    // 清理定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  },

  // 启用考试模式（锁定功能）
  enableExamMode() {
    console.log('启用听写测试考试模式');
    
    // 显示考试模式提示
    wx.showModal({
      title: '🔒 听写测试模式',
      content: '即将进入测试模式：\n\n• 全屏专注模式\n• 无法中途退出\n• 屏蔽返回按键\n• 保持屏幕常亮\n\n确定要开始测试吗？',
      showCancel: true,
      cancelText: '取消',
      confirmText: '开始测试',
      confirmColor: '#007aff',
      success: (res) => {
        if (res.confirm) {
          // 用户确认，启用完整防作弊锁定
          this.lockExamMode();
        } else {
          // 用户取消，返回上一页
          wx.navigateBack();
        }
      }
    });
  },

  // 恢复测试状态
  restoreTestState(restoredState) {
    console.log('🔄 恢复听写测试状态:', restoredState);

    // 检查是否有有效的测试数据
    const hasValidTestData = restoredState.startTime &&
                            restoredState.currentIndex !== undefined &&
                            restoredState.words &&
                            restoredState.words.length > 0;

    if (hasValidTestData) {
      // 恢复测试数据
      this.setData({
        startTime: restoredState.startTime,
        currentIndex: restoredState.currentIndex || 0,
        total: restoredState.total || restoredState.words.length,
        words: restoredState.words,
        allUserAnswers: restoredState.allUserAnswers || {},
        countdownStartTime: restoredState.countdownStartTime,
        countdown: restoredState.countdown || (this.data.dictationSettings?.timeLimit || 15),
        examMode: true,
        playingStage: restoredState.playingStage || 'waiting',
        audioPlayCompleted: restoredState.audioPlayCompleted || false,
        dictationSettings: restoredState.dictationSettings || this.data.dictationSettings
      });

      // 设置当前单词
      const currentIndex = restoredState.currentIndex || 0;
      if (currentIndex < restoredState.words.length) {
        this.setData({
          currentWord: restoredState.words[currentIndex]
        });
      }

      console.log('🔄 听写测试已开始，直接恢复测试状态');
      console.log('🔄 恢复的数据:', {
        startTime: restoredState.startTime,
        currentIndex: currentIndex,
        totalWords: restoredState.words.length,
        currentWord: restoredState.words[currentIndex]?.words
      });

      // 设置音频
      this.setAudio();

      // 直接恢复测试
      this.resumeExamMode();
    } else {
      console.log('🔄 没有有效的测试数据，等待单词数据加载完成后恢复测试');
      // 标记需要恢复，等待单词加载完成
      this.setData({
        needResumeAfterLoad: true,
        restoredStateData: restoredState
      });
    }
  },

  // 恢复考试模式（测试中断后恢复）
  resumeExamMode() {
    console.log('🔄 恢复听写测试考试模式');

    // 直接启用完整防作弊锁定，不显示确认对话框
    this.lockExamMode();

    // 恢复测试状态，计算已经过去的时间
    this.resumeTestWithTimeCalculation();
  },

  // 恢复测试并计算时间
  resumeTestWithTimeCalculation() {
    const now = Date.now();
    const { startTime, dictationSettings, currentIndex, words } = this.data;
    const timeLimit = dictationSettings?.timeLimit || 15;
    const playTime = (dictationSettings?.playCount || 2) * 2; // 播放时间估算
    const totalTimePerWord = playTime + timeLimit; // 每个单词总时间

    if (startTime) {
      // 计算测试总共已经过去的时间
      const totalElapsedSeconds = Math.floor((now - startTime) / 1000);
      const totalTestTime = words.length * totalTimePerWord;

      console.log(`🕐 恢复听写测试时间计算:`, {
        totalElapsedSeconds,
        totalTestTime,
        currentIndex,
        totalWords: words.length,
        timeLimit,
        totalTimePerWord
      });

      // 检查测试是否已经结束
      if (totalElapsedSeconds >= totalTestTime) {
        // 测试时间已到，直接结束测试
        wx.showModal({
          title: '听写测试已结束',
          content: '测试时间已到，系统已自动提交您的答案。',
          showCancel: false,
          confirmText: '查看结果',
          success: () => {
            this.finishDictation();
          }
        });
        return;
      }

      // 计算应该在第几个单词
      const expectedWordIndex = Math.floor(totalElapsedSeconds / totalTimePerWord);
      const actualWordIndex = Math.min(expectedWordIndex, words.length - 1);

      // 计算当前单词内的时间进度
      const wordElapsedSeconds = totalElapsedSeconds % totalTimePerWord;

      console.log(`📊 听写题目跳转计算:`, {
        expectedWordIndex,
        actualWordIndex,
        currentIndex,
        wordElapsedSeconds,
        playTime
      });

      // 如果需要跳转单词
      if (actualWordIndex > currentIndex) {
        // 标记跳过的单词为错误
        for (let i = currentIndex; i < actualWordIndex; i++) {
          if (!this.data.allUserAnswers[i]) {
            this.data.allUserAnswers[i] = {
              userAnswer: '',
              correctAnswer: words[i].words || words[i].word,
              isCorrect: false,
              isTimeout: true,
              isSkipped: true // 标记为因退出而跳过
            };
          }
        }

        // 跳转到正确的单词
        this.setData({
          currentIndex: actualWordIndex,
          currentWord: words[actualWordIndex],
          allUserAnswers: this.data.allUserAnswers
        });

        wx.showToast({
          title: `已跳转到第${actualWordIndex + 1}个单词`,
          icon: 'none',
          duration: 2000
        });
      }

      // 判断当前单词的状态
      if (wordElapsedSeconds < playTime) {
        // 还在播放阶段，设置为播放完成并开始倒计时
        this.setData({
          playingStage: 'completed',
          audioPlayCompleted: true,
          autoPlaying: false,
          isPlayingStopped: false
        });

        const remainingAnswerTime = totalTimePerWord - wordElapsedSeconds;
        this.startCountdown(Math.max(1, remainingAnswerTime));

        wx.showToast({
          title: `测试已恢复，本题剩余${Math.max(1, remainingAnswerTime)}秒`,
          icon: 'success',
          duration: 2000
        });
      } else {
        // 答题时间已过，自动跳转到下一题
        if (actualWordIndex < words.length - 1) {
          wx.showToast({
            title: '当前题目时间已到，自动跳转',
            icon: 'none',
            duration: 1500
          });

          setTimeout(() => {
            this.handleDictationTimeout();
          }, 1500);
        } else {
          // 所有单词都已完成，结束测试
          this.finishDictation();
        }
      }
    } else {
      // 没有保存的时间信息，检查播放状态
      if (this.data.dictationPracticeMode === 'test') {
        if (this.checkAndResumeBackgroundPlayback()) {
          console.log('🎯 成功恢复后台播放状态');
        } else {
          // 设置为播放完成状态，启动倒计时
          this.setData({
            playingStage: 'completed',
            audioPlayCompleted: true,
            autoPlaying: false,
            isPlayingStopped: false
          });
          this.startCountdown();
        }
      } else {
        // 练习模式直接设置为完成状态
        this.setData({
          playingStage: 'completed',
          audioPlayCompleted: true,
          autoPlaying: false,
          isPlayingStopped: false
        });
      }

      wx.showToast({
        title: '测试已恢复',
        icon: 'success',
        duration: 2000
      });
    }

    console.log('✅ 听写测试恢复完成');
  },

  // 锁定考试模式
  lockExamMode() {
    // 启用完整的防作弊锁定功能
    examLock.enable({
      examName: '听写测试',
      onBackAttempt: (attemptCount) => {
        // 用户尝试退出时的回调
        console.log(`用户第${attemptCount}次尝试退出听写测试`);

        // 不暂停计时器，让测试继续进行
        console.log('⏰ 听写计时器继续运行，测试正常进行');
      },
      onExitConfirm: () => {
        // 用户确认退出时的回调
        console.log('用户确认强制退出听写测试，测试将继续在后台进行');

        // 记录测试被中断
        this.recordTestInterruption();

        // 显示退出提示，说明测试继续进行
        wx.showToast({
          title: '测试继续后台进行，将自动提交成绩',
          icon: 'none',
          duration: 3000
        });

        // 延迟执行退出逻辑
        setTimeout(() => {
          wx.navigateBack({
            delta: 1,
            fail: () => {
              wx.reLaunch({
                url: '/pages/spelling/mode-select/mode-select',
                fail: () => {
                  wx.switchTab({
                    url: '/pages/index/index'
                  });
                }
              });
            }
          });
        }, 300);
      }
    });
    
    // 设置考试状态
    this.setData({
      examMode: true
    });
    
    console.log('听写测试锁定模式已启用');
  },

  // 禁用考试模式（恢复正常功能）
  disableExamMode() {
    console.log('禁用听写测试考试模式');
    
    // 禁用防作弊锁定功能
    examLock.disable();
    
    // 清除考试状态
    this.setData({
      examMode: false
    });
    
    console.log('听写测试锁定模式已禁用');
  },

  // 记录测试中断
  recordTestInterruption(skipExamLockSave = false) {
    const now = Date.now();

    // 准备详细的音频播放状态
    const audioPlaybackState = {
      // 基本测试状态
      startTime: this.data.startTime, // 添加测试开始时间
      currentIndex: this.data.currentIndex,
      total: this.data.total,
      words: this.data.words,
      // 音频播放状态
      currentlyPlayingIndex: this.data.currentlyPlayingIndex,
      playingStage: this.data.playingStage,
      autoPlaying: this.data.autoPlaying,
      audioPlayCompleted: this.data.audioPlayCompleted,
      userInputs: this.data.userInputs,
      allUserAnswers: this.data.allUserAnswers,
      currentPageIndex: this.data.currentPageIndex,
      totalPages: this.data.totalPages,
      wordsPerPage: this.data.wordsPerPage,
      currentPageWords: this.data.currentPageWords,
      dictationPracticeMode: this.data.dictationPracticeMode,
      dictationSettings: this.data.dictationSettings,
      // 后台播放相关状态
      backgroundPlaybackId: this.data.backgroundPlaybackId,
      isBackgroundPlaybackActive: this.data.isBackgroundPlaybackActive,
      playbackStartTime: this.data.playbackStartTime,
      // 时间相关状态，用于恢复时计算
      countdownStartTime: this.data.countdownStartTime,
      countdown: this.data.countdown
    };

    const interruptionData = {
      timestamp: now,
      testType: 'dictation',
      stage: '测试进行中',
      currentPage: this.data.currentPageIndex + 1,
      totalPages: this.data.totalPages,
      completedQuestions: this.data.currentPageIndex * this.data.wordsPerPage + this.data.userInputs.filter(input => input.trim()).length,
      totalQuestions: this.data.words.length,
      // 添加音频播放状态
      audioPlaybackState: audioPlaybackState
    };

    console.log('记录听写测试中断:', interruptionData);

    // 只有在不是被examLock调用时才保存到examLock（避免重复保存）
    if (!skipExamLockSave && examLock && typeof examLock.saveCurrentTestState === 'function') {
      // 合并基本中断数据和音频播放状态
      const completeState = {
        ...interruptionData,
        ...audioPlaybackState
      };
      examLock.saveCurrentTestState(completeState);
    }

    // 这里可以将中断数据保存到本地或云端
    try {
      const interruptions = wx.getStorageSync('test_interruptions') || [];
      interruptions.push(interruptionData);
      wx.setStorageSync('test_interruptions', interruptions);
    } catch (error) {
      console.error('保存中断记录失败:', error);
    }

    return audioPlaybackState;
  },

  // ================ 练习模式相关方法 ================
  
  // 切换单词显示
  toggleWordReveal(e) {
    const index = e.currentTarget.dataset.index;
    const revealedWords = [...this.data.revealedWords];
    revealedWords[index] = !revealedWords[index];
    this.setData({ revealedWords });
  },

  // 切换音标显示
  togglePhoneticReveal(e) {
    const index = e.currentTarget.dataset.index;
    const revealedPhonetics = [...this.data.revealedPhonetics];
    revealedPhonetics[index] = !revealedPhonetics[index];
    this.setData({ revealedPhonetics });
  },

  // 切换中文含义显示
  toggleMeaningReveal(e) {
    const index = e.currentTarget.dataset.index;
    const revealedMeanings = [...this.data.revealedMeanings];
    revealedMeanings[index] = !revealedMeanings[index];
    this.setData({ revealedMeanings });
  },

  // 切换例句显示
  toggleExampleReveal(e) {
    const index = e.currentTarget.dataset.index;
    const revealedExamples = [...this.data.revealedExamples];
    revealedExamples[index] = !revealedExamples[index];
    this.setData({ revealedExamples });
  },

  // 显示全部信息
  revealAllWords() {
    const length = this.data.currentPageWords.length;
    this.setData({
      revealedWords: new Array(length).fill(true),
      revealedPhonetics: new Array(length).fill(true),
      revealedMeanings: new Array(length).fill(true),
      revealedExamples: new Array(length).fill(true)
    });
  },

  // 隐藏全部信息
  hideAllWords() {
    const length = this.data.currentPageWords.length;
    this.setData({
      revealedWords: new Array(length).fill(false),
      revealedPhonetics: new Array(length).fill(false),
      revealedMeanings: new Array(length).fill(false),
      revealedExamples: new Array(length).fill(false)
    });
  },

  // 完成练习（练习模式）
  finishPractice() {
    wx.showModal({
      title: '练习完成',
      content: '恭喜您完成了本次听写练习！您已熟悉了所有单词的发音和含义。',
      showCancel: false,
      confirmText: '返回',
      success: () => {
        wx.navigateBack();
      }
    });
  },

  // 生成测试结果
  generateTestResults() {
    // 🔧 修复：测试结束时立即停止所有定时器和音频
    console.log('🛑 测试结束，停止所有定时器和音频');

    // 停止所有定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
    if (this.submitTimer) {
      clearTimeout(this.submitTimer);
      this.submitTimer = null;
    }
    if (this.answerTimer) {
      clearTimeout(this.data.answerTimer);
      this.setData({ answerTimer: null });
    }

    // 停止所有音频播放
    this.stopCurrentPlayback();
    this.stopBackgroundPlayback();

    // 停止音频上下文
    if (this.data.audioContext) {
      try {
        this.data.audioContext.stop();
        this.data.audioContext.destroy();
      } catch (error) {
        console.log('音频上下文停止时出错:', error);
      }
    }

    const results = [];
    let correctCount = 0;
    let allUserInputs = [];

    // 🔧 修复：听写模式使用resultDetails数据
    if (this.data.practiceMode === 'dictation' && this.data.resultDetails && this.data.resultDetails.length > 0) {

      // 直接使用resultDetails中的数据
      this.data.resultDetails.forEach((detail, index) => {
        const userInput = detail.userAnswer || '';
        const correctAnswer = detail.word.word || detail.word.words || '';
        const isCorrect = detail.isCorrect;

        if (isCorrect) {
          correctCount++;
        }

        // 🔧 判断是否为短语（长度超过15个字符或包含空格）
        const isPhrase = correctAnswer.length > 15 || correctAnswer.includes(' ') || correctAnswer.includes('(') || correctAnswer.includes('.');

        results.push({
          word: detail.word,
          userInput: userInput,
          userAnswer: userInput,
          correctAnswer: correctAnswer,
          isCorrect: isCorrect,
          isPhrase: isPhrase, // 添加短语标识
          pageIndex: 0,
          wordIndex: index
        });
      });
    } else if (this.data.totalPages && this.data.totalPages > 0) {
      // 原有的分页模式数据收集

      // 收集所有页面的答案
      for (let pageIndex = 0; pageIndex < this.data.totalPages; pageIndex++) {
        const startIndex = pageIndex * this.data.wordsPerPage;
        const endIndex = Math.min(startIndex + this.data.wordsPerPage, this.data.words.length);
        const pageWords = this.data.words.slice(startIndex, endIndex);

        // 获取该页的用户输入（如果是当前页面，使用当前的userInputs）
        let pageUserInputs;
        if (pageIndex === this.data.currentPageIndex) {
          pageUserInputs = this.data.userInputs;
        } else {
          // 从存储中获取或默认为空
          pageUserInputs = this.data.allUserAnswers?.[pageIndex] || new Array(pageWords.length).fill('');
        }

        // 🔧 调试：检查每页数据
        console.log(`🔍 页面 ${pageIndex} 数据:`, {
          pageWords: pageWords.length,
          pageUserInputs: pageUserInputs,
          isCurrentPage: pageIndex === this.data.currentPageIndex
        });

        // 批改该页答案
        for (let i = 0; i < pageWords.length; i++) {
          const word = pageWords[i];
          const userInput = (pageUserInputs[i] || '').trim();
          const correctAnswer = (word.words || word.word || '').trim();

          // 🔧 使用validateAnswer函数进行答案验证，支持特殊格式
          const isCorrect = this.validateAnswer(userInput, correctAnswer);

          // 调试日志
          if (i === 0 && pageIndex === 0) {
            console.log('听写结果生成调试:', {
              word: word,
              wordKeys: Object.keys(word),
              correctAnswer: correctAnswer,
              userInput: userInput,
              isCorrect: isCorrect,
              simpleComparison: userInput.toLowerCase() === correctAnswer.toLowerCase(),
              validateAnswerResult: this.validateAnswer(userInput, correctAnswer)
            });
          }

          if (isCorrect) {
            correctCount++;
          }

          // 🔧 判断是否为短语（长度超过15个字符或包含空格）
          const isPhrase = correctAnswer.length > 15 || correctAnswer.includes(' ') || correctAnswer.includes('(') || correctAnswer.includes('.');

          results.push({
            word: word,
            userInput: userInput,      // 保留原字段用于内部逻辑
            userAnswer: userInput,     // 🔧 添加统一字段用于WXML显示
            correctAnswer: correctAnswer,
            isCorrect: isCorrect,
            isPhrase: isPhrase,        // 添加短语标识
            pageIndex: pageIndex,
            wordIndex: i
          });
        }
      }
    } else {
      // 🔧 兜底逻辑：如果没有收集到任何数据，尝试从其他地方获取

      // 如果有单词数据但没有结果，标记为全部错误
      if (this.data.words && this.data.words.length > 0) {
        this.data.words.forEach((word, index) => {
          const correctAnswer = word.words || word.word || '';
          // 🔧 判断是否为短语（长度超过15个字符或包含空格）
          const isPhrase = correctAnswer.length > 15 || correctAnswer.includes(' ') || correctAnswer.includes('(') || correctAnswer.includes('.');

          results.push({
            word: word,
            userInput: '',
            userAnswer: '',
            correctAnswer: correctAnswer,
            isCorrect: false,
            isPhrase: isPhrase, // 添加短语标识
            pageIndex: 0,
            wordIndex: index
          });
        });
      }
    }

    // 为错误单词添加错误索引
    let wrongIndex = 0;
    results.forEach(result => {
      if (!result.isCorrect) {
        result.wrongIndex = wrongIndex++;
      }
    });


    
    // 计算统计数据
    const totalWords = this.data.words.length;
    const wrongCount = totalWords - correctCount;
    const accuracyRate = Math.round((correctCount / totalWords) * 100);
    const testTime = Date.now() - this.data.startTime;
    
    // 🔧 检查分组信息
    const app = getApp();
    const groupingData = app.globalData.testGroupingData;
    let groupInfo = null;
    
    if (groupingData && groupingData.testMode === 'dictation') {
      const hasNextGroup = groupingData.currentGroup < groupingData.totalGroups;
      groupInfo = {
        hasNextGroup: hasNextGroup,
        currentGroup: groupingData.currentGroup,
        totalGroups: groupingData.totalGroups,
        remainingGroups: groupingData.totalGroups - groupingData.currentGroup
      };
    }
    
    // 设置结果数据

    this.setData({
      testResults: results,
      overallStats: {
        totalWords: totalWords,
        correctCount: correctCount,
        wrongCount: wrongCount,
        accuracyRate: accuracyRate,
        testTime: testTime,
        formattedTime: this.formatTestTime(testTime)
      },
      selectedWrongWords: new Array(wrongCount).fill(false),
      showTestResult: true,
      groupInfo: groupInfo // 🔧 添加分组信息
    });

    // 🔧 强制测试：如果没有错词，创建一个测试错词
    if (wrongCount === 0) {
      console.log('🔧 强制添加测试错词用于调试');
      const testWrongResult = {
        word: { word: 'test', meaning: '测试' },
        userInput: 'tset',
        userAnswer: 'tset',
        correctAnswer: 'test',
        isCorrect: false,
        wrongIndex: 0,
        pageIndex: 0,
        wordIndex: 0
      };

      this.setData({
        testResults: [testWrongResult],
        overallStats: {
          ...this.data.overallStats,
          wrongCount: 1
        }
      });

      console.log('🔧 强制设置测试数据完成');
    }
    
    // 保存测试结果（仅测试模式）
    if (this.data.practiceMode === 'dictation') {
      this.saveDictationTestResult(results, {
        totalWords,
        correctCount,
        wrongCount,
        accuracyRate,
        testTime
      });
    }
    
    // 恢复正常功能
    this.disableExamMode();
  },

  // 保存听写测试结果
  saveDictationTestResult(detailResults, stats) {
    try {
      // 收集错词数据
      const mistakes = detailResults
        .filter(result => !result.isCorrect)
        .map(result => ({
          word: result.correctAnswer,
          meaning: result.word.meaning || '',
          userAnswer: result.userInput,
          correctAnswer: result.correctAnswer,
          phonetic: result.word.phonetic || '',
          example: result.word.example || ''
        }));

      // 构建测试结果对象
      // 获取关卡信息
      const { levelId, currentLevel, currentGroup = 1, isMultiLevel, totalLevels } = this.data;
      const actualLevelId = levelId || currentLevel || currentGroup;

      const testResult = {
        testMode: 'dictation',
        correctCount: stats.correctCount,
        wrongCount: stats.wrongCount,
        totalQuestions: stats.totalWords,
        score: Math.round((stats.correctCount / stats.totalWords) * 100),
        accuracy: stats.accuracyRate.toString(),
        duration: stats.testTime,
        mistakes: mistakes,
        libraryId: this.data.libraryId || 'custom',
        libraryName: this.data.libraryName || '自定义听写',
        shareMode: this.data.shareMode || 'self',
        shareId: this.data.shareId || '',
        // 添加关卡信息
        levelId: actualLevelId,
        level: actualLevelId,
        levelNumber: actualLevelId,
        groupIndex: actualLevelId - 1, // 从0开始的索引
        isMultiLevel: isMultiLevel || false,
        totalLevels: totalLevels || 1,
        timestamp: Date.now()
      };

      // 根据是否为分享测试或竞赛决定保存方式
      if (this.data.competitionMode && this.data.competitionId) {
        // 竞赛模式，提交结果到云端
        this.submitCompetitionResult(testResult);
      } else if (this.data.shareMode === 'share' && this.data.shareId) {
        this.saveDictationSharedTestResult(testResult);
      } else {
        // 自己的测试，保存到本地（听写目前没有云端保存）
        this.saveToLocalTestResults(testResult);

        // 更新学习进度（仅自己的测试）
        this.updateDictationLearningProgress();
      }

      console.log('听写测试结果已保存:', testResult);
    } catch (error) {
      console.error('保存听写测试结果失败:', error);
    }
  },

  // 更新听写学习进度
  updateDictationLearningProgress: function() {
    const {
      libraryId,
      libraryName,
      isGrouped,
      currentGroup,
      totalGroups,
      wordsPerGroup,
      overallStats,
      dictationSettings
    } = this.data;

    if (!libraryId) {
      return;
    }

    // 构建测试配置
    const testSettings = {
      timeLimit: dictationSettings?.timeLimit || 15,
      playCount: dictationSettings?.playCount || 2,
      wordsPerGroup: wordsPerGroup,
      dictationMode: dictationSettings?.mode || 'test'
    };

    try {
      const testMode = 'dictation';

      if (isGrouped && currentGroup && totalGroups && wordsPerGroup) {
        // 分组学习进度更新
        const progressKey = `progress_${libraryId}_${testMode}`;
        let progressData = wx.getStorageSync(progressKey) || {};

        // 初始化或更新分组进度数据
        if (!progressData.isGrouped) {
          progressData = {
            libraryId: libraryId,
            libraryName: libraryName,
            mode: testMode,
            isGrouped: true,
            groupInfo: {
              currentGroup: 1,
              totalGroups: totalGroups,
              wordsPerGroup: wordsPerGroup,
              completedGroups: []
            }
          };
        }

        // 检查当前组是否通过（听写可以设置更高的通过标准）
        const accuracy = overallStats.accuracyRate || 0;
        const isPassed = accuracy >= 70; // 70%通过率

        if (isPassed && !progressData.groupInfo.completedGroups.includes(currentGroup)) {
          // 标记当前组为已完成
          progressData.groupInfo.completedGroups.push(currentGroup);
          progressData.groupInfo.completedGroups.sort((a, b) => a - b);
        }

        // 更新当前组和进度信息
        progressData.groupInfo.currentGroup = Math.max(currentGroup, progressData.groupInfo.currentGroup);
        progressData.lastStudyTime = Date.now();
        progressData.percentage = Math.round((progressData.groupInfo.completedGroups.length / totalGroups) * 100);
        progressData.currentIndex = progressData.groupInfo.completedGroups.length * wordsPerGroup;
        progressData.totalCount = totalGroups * wordsPerGroup;
        progressData.progressText = `第${currentGroup}/${totalGroups}组`;
        progressData.detailText = `已完成${progressData.groupInfo.completedGroups.length}组，共${totalGroups}组`;

        // 添加测试配置
        progressData.testSettings = testSettings;

        // 保存到本地存储
        wx.setStorageSync(progressKey, progressData);
        console.log('听写分组学习进度已更新到本地:', progressData);

        // 同时保存到云端
        this.saveProgressToCloud(progressData, testSettings);

      } else {
        // 普通学习进度更新（非分组）
        const progressKey = `progress_${libraryId}_${testMode}`;
        let progressData = wx.getStorageSync(progressKey) || {};

        // 更新基本进度信息
        progressData = {
          ...progressData,
          libraryId: libraryId,
          libraryName: libraryName,
          mode: testMode,
          lastStudyTime: Date.now(),
          isGrouped: false,
          testSettings: testSettings
        };

        // 保存到本地存储
        wx.setStorageSync(progressKey, progressData);
        console.log('听写学习进度已更新到本地:', progressData);

        // 同时保存到云端
        this.saveProgressToCloud(progressData, testSettings);
      }

    } catch (error) {
      console.error('更新听写学习进度失败:', error);
    }
  },

  // 保存学习进度到云端
  saveProgressToCloud: function(progressData, testSettings) {
    try {
      wx.cloud.callFunction({
        name: 'saveLearningProgress',
        data: {
          libraryId: progressData.libraryId,
          libraryName: progressData.libraryName,
          mode: progressData.mode,
          progressData: progressData,
          testSettings: testSettings
        }
      }).then(result => {
        if (result.result && result.result.success) {
          console.log('听写学习进度已保存到云端:', result.result);
        } else {
          console.error('保存听写学习进度到云端失败:', result.result);
        }
      }).catch(error => {
        console.error('调用云函数保存听写学习进度失败:', error);
      });
    } catch (error) {
      console.error('保存听写学习进度到云端异常:', error);
    }
  },

  // 提交竞赛结果
  async submitCompetitionResult(testResult) {
    try {
      wx.showLoading({
        title: '提交结果...',
        mask: true
      });

      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo') || {};
      
      // 调用云函数提交竞赛结果
      const result = await wx.cloud.callFunction({
        name: 'submitCompetitionResult',
        data: {
          competitionId: this.data.competitionId,
          mode: 'dictation',
          score: testResult.score,
          duration: Math.round(testResult.duration / 1000), // 转换为秒
          accuracy: testResult.accuracy,
          correctCount: testResult.correctCount,
          totalCount: testResult.totalQuestions,
          userName: userInfo.nickName || userInfo.username || '匿名用户'
        }
      });

      wx.hideLoading();

      if (result.result.success) {
        // 显示提交成功的提示
        if (result.result.isNewRecord) {
          wx.showToast({
            title: '新纪录！成绩已更新',
            icon: 'success',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: result.result.message || '成绩已提交',
            icon: 'success',
            duration: 2000
          });
        }
        
        // 设置竞赛完成状态和检查是否有下一关
        // 优先使用云函数返回的completed状态，其次使用本地计算的结果
        const isCompleted = result.result.completed !== undefined ? 
          result.result.completed : 
          (testResult.accuracy >= 80);
        
        this.setData({
          competitionCompleted: isCompleted
        });
        
        // 如果是多关卡竞赛且通过了当前关卡，检查是否有下一关
        if (isCompleted && this.data.masterCompetitionId) {
          this.checkNextLevel();
          // 通知关卡选择页面刷新状态（如果存在）
          this.notifyLevelSelectRefresh();
        }
      } else {
        throw new Error(result.result.message || '提交失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('提交竞赛结果失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 保存听写分享测试结果
  async saveDictationSharedTestResult(testResult) {
    try {
      wx.showLoading({
        title: '提交结果...',
        mask: true
      });

      const currentUser = wx.getStorageSync('userInfo') || {};
      const { shareId, currentGroup = 1, levelId, currentLevel } = this.data;
      // 使用正确的关卡ID，优先级：levelId > currentLevel > currentGroup
      const actualLevelId = levelId || currentLevel || currentGroup;

      // 获取用户昵称，尝试多种可能的字段
      let nickName = '匿名用户';
      let avatarUrl = '';

      if (currentUser.nickName) {
        nickName = currentUser.nickName;
      } else if (currentUser.wechatInfo?.nickName) {
        nickName = currentUser.wechatInfo.nickName;
      } else if (currentUser.username) {
        nickName = currentUser.username;
      } else if (currentUser.phone) {
        nickName = `用户${currentUser.phone.slice(-4)}`;
      }

      if (currentUser.avatarUrl) {
        avatarUrl = currentUser.avatarUrl;
      } else if (currentUser.wechatInfo?.avatarUrl) {
        avatarUrl = currentUser.wechatInfo.avatarUrl;
      }

      console.log('听写用户信息调试:', {
        currentUser: currentUser,
        extractedNickName: nickName,
        extractedAvatarUrl: avatarUrl
      });

      // 构建测试结果数据
      const submitData = {
        shareId: shareId,
        levelId: actualLevelId,
        testResult: {
          score: testResult.score,
          accuracy: parseFloat(testResult.accuracy),
          correctCount: testResult.correctCount,
          wrongCount: testResult.wrongCount,
          mistakes: testResult.mistakes || [],
          timeSpent: Math.round(testResult.duration / 1000), // 转换为秒
          totalQuestions: testResult.totalQuestions
        },
        userInfo: {
          nickName: nickName,
          avatarUrl: avatarUrl
        }
      };

      console.log('提交听写分享测试结果:', submitData);

      // 优先提交到云端
      let cloudSubmitted = false;

      try {
        const result = await wx.cloud.callFunction({
          name: 'submitShareTestResult',
          data: submitData
        });

        if (result.result.success) {
          cloudSubmitted = true;
          console.log('云端提交成功:', result.result.data);
        }
      } catch (cloudError) {
        console.log('云端提交失败，使用本地存储:', cloudError);
      }

      // 同时保存到本地存储作为备份
      const shareTests = wx.getStorageSync('shareTests') || {};
      const shareTestData = shareTests[testResult.shareId];

      if (shareTestData) {
        // 添加参与者信息
        const result = {
          ...testResult,
          participantInfo: currentUser,
          participantOpenid: currentUser.openid,
          creatorOpenid: shareTestData.creatorInfo?.openid
        };

        shareTestData.results.push(result);

        // 更新被分享人的测试次数
        if (shareTestData.visitors && currentUser.openid) {
          const visitor = shareTestData.visitors.find(v => v.openid === currentUser.openid);
          if (visitor) {
            visitor.testCount = (visitor.testCount || 0) + 1;
            visitor.lastTestTime = Date.now();
            visitor.bestScore = Math.max(visitor.bestScore || 0, testResult.score);
            visitor.totalTests = (visitor.totalTests || 0) + 1;
            visitor.totalScore = (visitor.totalScore || 0) + testResult.score;
            visitor.averageScore = Math.round(visitor.totalScore / visitor.totalTests);
          }
        }

        shareTests[testResult.shareId] = shareTestData;
        wx.setStorageSync('shareTests', shareTests);

        // 2. 保存到测试结果统一存储
        this.saveToLocalTestResults(result);

        console.log('听写分享测试结果已保存:', result);
      }

      wx.hideLoading();

      // 显示提交状态
      if (cloudSubmitted) {
        wx.showToast({
          title: '结果已提交',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '已保存到本地',
          icon: 'none'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('保存听写分享测试结果失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      });
    }
  },

  // 保存到本地测试结果存储
  saveToLocalTestResults(testResult) {
    try {
      const allTestResults = wx.getStorageSync('testResults') || [];
      allTestResults.push(testResult);
      wx.setStorageSync('testResults', allTestResults);
      console.log('测试结果已保存到本地存储');
    } catch (error) {
      console.error('保存到本地测试结果失败:', error);
    }
  },

   // 切换错词选择
   toggleWrongWordSelection(e) {
     const index = e.currentTarget.dataset.index;
     const selectedWrongWords = [...this.data.selectedWrongWords];
     selectedWrongWords[index] = !selectedWrongWords[index];
     this.setData({ selectedWrongWords });
   },

   // 全选错词
   selectAllWrongWords() {
     const wrongWords = this.data.testResults.filter(r => !r.isCorrect);
     const currentSelected = this.data.selectedWrongWords;
     const allSelected = currentSelected.every(selected => selected);

     // 如果已经全选，则取消全选；否则全选
     this.setData({
       selectedWrongWords: new Array(wrongWords.length).fill(!allSelected)
     });
   },

   // 重新听写选中的错词
   retrySelectedWords() {
     const selectedWords = this.getSelectedWords();
     if (selectedWords.length === 0) {
       wx.showToast({
         title: '请选择要重新听写的单词',
         icon: 'none'
       });
       return;
     }
     
     this.startRetryMode('dictation', selectedWords, '重新听写');
   },

   // 英译汉/汉译英模式
   // 英译汉重试
   retryWithEnToCn() {
     const selectedWords = this.getSelectedWords();
     if (selectedWords.length === 0) {
       wx.showToast({
         title: '请选择要练习的单词',
         icon: 'none'
       });
       return;
     }
     
     this.startRetryMode('en2cn', selectedWords, '英译汉');
   },
   
   // 汉译英重试
   retryWithCnToEn() {
     const selectedWords = this.getSelectedWords();
     if (selectedWords.length === 0) {
       wx.showToast({
         title: '请选择要练习的单词',
         icon: 'none'
       });
       return;
     }
     
     this.startRetryMode('cn2en', selectedWords, '汉译英');
   },

   // 消消乐模式
   retryWithGame() {
     const selectedWords = this.getSelectedWords();
     if (selectedWords.length === 0) {
       wx.showToast({
         title: '请选择要游戏的单词',
         icon: 'none'
       });
       return;
     }
     
     this.startRetryMode('game', selectedWords, '消消乐');
   },

   // 分享给他人测试
   shareSelectedWords() {
     const selectedWords = this.getSelectedWords();
     if (selectedWords.length === 0) {
       wx.showToast({
         title: '请选择要分享的单词',
         icon: 'none'
       });
       return;
     }

     wx.showActionSheet({
       itemList: ['听写模式', '英译汉', '汉译英', '消消乐'],
       success: (res) => {
         const modes = ['dictation', 'en2cn', 'cn2en', 'game'];
         const modeNames = ['听写', '英译汉', '汉译英', '消消乐'];
         const selectedMode = modes[res.tapIndex];
         const modeName = modeNames[res.tapIndex];
         
         this.createShareLink(selectedWords, selectedMode, modeName);
       }
     });
   },

   // 获取选中的单词
   getSelectedWords() {
     const wrongWords = this.data.testResults.filter(r => !r.isCorrect);
     const selectedWords = [];
     
     wrongWords.forEach((wrongWord) => {
       if (this.data.selectedWrongWords[wrongWord.wrongIndex]) {
         selectedWords.push(wrongWord.word);
       }
     });
     
     return selectedWords;
   },

   // 开始重试模式
   startRetryMode(mode, words, modeName) {
     wx.showModal({
       title: `${modeName}练习`,
       content: `您选择了 ${words.length} 个单词进行${modeName}练习，确定开始吗？`,
       confirmText: '开始练习',
       cancelText: '取消',
       success: (res) => {
         if (res.confirm) {
           const app = getApp();
           
           if (mode === 'game') {
             // 消消乐模式需要特殊的数据结构
             app.globalData.customWords = words;
             
             // 转换单词数据格式为消消乐需要的格式
             const gameWords = words.map(word => ({
               english: word.words || word.word,
               chinese: word.meaning,
               phonetic: word.phonetic || '',
               example: word.example || ''
             }));
             
             app.globalData.eliminationGameData = {
               words: gameWords,
               mode: 'custom',
               totalGroups: 1,
               currentGroup: 0,
               wordsPerGroup: gameWords.length,
               fromCustom: true,
               libraryName: `错词${modeName}`
             };
           } else {
             // 其他模式使用learningData
             app.globalData.learningData = {
               words: words,
               mode: mode,
               practiceMode: 'test',
               libraryId: 'retry',
               libraryName: `错词${modeName}`,
               dictationSettings: this.data.dictationSettings
             };
           }
           
           // 根据模式跳转到不同页面
           let url = '';
           switch (mode) {
             case 'dictation':
               url = '/pages/spelling/practice/practice?mode=dictation&practiceMode=test';
               break;
             case 'en2cn':
               url = '/pages/wordtest/test/test?mode=custom&type=en2cn';
               break;
             case 'cn2en':
               url = '/pages/wordtest/test/test?mode=custom&type=cn2en';
               break;
             case 'game':
               url = '/pages/task/puzzle/puzzle?mode=custom';
               break;
           }
           
           if (url) {
             wx.redirectTo({ url });
           }
         }
       }
     });
   },

   // 创建分享链接
   async createShareLink(words, mode, modeName) {
     wx.showLoading({ title: '创建分享链接...' });

     try {
       const shareId = 'share_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
       const currentUser = wx.getStorageSync('userInfo') || {};

       // 创建分享测试数据
       const shareTestData = {
         shareId: shareId,
         testMode: mode,
         libraryId: 'dictation_practice',
         libraryName: '听写练习',
         words: words,
         dictationSettings: {
           playCount: 2,
           wordOrder: 'original'
         },
         createdBy: currentUser.nickName || '匿名用户',
         creatorInfo: {
           openid: currentUser.openid,
           nickName: currentUser.nickName,
           avatarUrl: currentUser.avatarUrl
         },
         createTime: Date.now(),
         visitors: [],
         results: []
       };

       // 保存到本地存储（只保存必要的元数据）
       try {
         const shareTests = wx.getStorageSync('shareTests') || {};

         // 为了避免本地存储大小限制，只保存必要的元数据
         const lightShareTestData = {
           shareId: shareTestData.shareId,
           testMode: shareTestData.testMode,
           libraryId: shareTestData.libraryId,
           libraryName: shareTestData.libraryName,
           wordsCount: shareTestData.words ? shareTestData.words.length : 0, // 只保存词汇数量
           dictationSettings: shareTestData.dictationSettings,
           createdBy: shareTestData.createdBy,
           creatorInfo: shareTestData.creatorInfo,
           createTime: shareTestData.createTime,
           // 不保存完整的words数组，以节省存储空间
           visitors: [],
           results: []
         };

         shareTests[shareId] = lightShareTestData;
         wx.setStorageSync('shareTests', shareTests);
         console.log('听写分享测试已保存到本地:', shareId, '词汇数量:', lightShareTestData.wordsCount);
       } catch (error) {
         console.error('保存到本地存储失败:', error);
         console.warn('本地存储失败，但云端保存成功，分享功能仍可正常使用');
       }

       // 保存到云端（必须成功）
       try {
         // 超级优化：判断是否使用基于索引的存储策略
         const isSystemLibrary = shareTestData.libraryId &&
                                 !shareTestData.libraryId.includes('custom') &&
                                 !shareTestData.libraryId.includes('mistake') &&
                                 shareTestData.libraryId !== 'dictation_practice';
         const isLargeLibrary = shareTestData.words && shareTestData.words.length >= 100;
         const useIndexBasedStorage = isSystemLibrary && isLargeLibrary;

         await wx.cloud.callFunction({
           name: 'createShareTest',
           data: {
             shareId: shareTestData.shareId,
             testType: shareTestData.testMode,
             // 超级优化：基于索引存储时只传递必要信息
             words: useIndexBasedStorage ?
               [{
                 _id: shareTestData.words[0]._id,
                 words: shareTestData.words[0].words,
                 totalCount: shareTestData.words.length
               }] :
               shareTestData.words,
             libraryId: shareTestData.libraryId,
             libraryName: shareTestData.libraryName,
             // 听写练习通常不需要乱序，但为了兼容性还是传递
             isRandomOrder: false,
             settings: shareTestData.dictationSettings,
             expireDays: 7,
             wordsPerGroup: 20
           }
         });
         console.log('听写分享测试已成功保存到云端和本地');
       } catch (err) {
         console.error('云端保存失败:', err);
         wx.hideLoading();
         wx.showModal({
           title: '保存失败',
           content: '分享测试保存到云端失败，可能是网络问题。请检查网络连接后重试。',
           showCancel: false,
           confirmText: '知道了'
         });
         return;
       }

       wx.hideLoading();
       
       // 显示分享选项
       this.showShareOptions({
         title: `${modeName}分享测试已创建`,
         content: `分享ID: ${shareId}\n模式: ${modeName}\n单词数: ${words.length}个`,
         shareId: shareId,
         shareData: {
           testMode: mode,
           testName: `${modeName}测试 - ${words.length}个单词`,
           shareId: shareId
         }
       });
       
     } catch (error) {
       wx.hideLoading();
       console.error('创建分享链接失败:', error);
       wx.showToast({ title: '创建失败', icon: 'error' });
     }
   },

   // 显示分享选项
   showShareOptions(options) {
     const { title, content, shareId, shareData } = options;
     
     wx.showActionSheet({
       itemList: ['复制链接', '分享到微信', '查看管理'],
       success: (res) => {
         switch (res.tapIndex) {
           case 0:
             // 复制链接
             this.copyShareLink(shareData);
             break;
           case 1:
             // 分享到微信
             this.shareToWeChat(shareData);
             break;
           case 2:
             // 查看管理
             this.goToShareManagement(shareId);
             break;
         }
       }
     });
   },

   // 复制分享链接
   copyShareLink(shareData) {
     const shareUrl = this.getSharePath(shareData.testMode, shareData.shareId);
     wx.setClipboardData({
       data: shareUrl,
       success: () => {
         wx.showToast({ title: '链接已复制', icon: 'success' });
       },
       fail: () => {
         wx.showToast({ title: '复制失败', icon: 'error' });
       }
     });
   },

   // 分享到微信
   shareToWeChat(shareData) {
     const { testMode, testName, shareId } = shareData;
     
     // 获取测试模式的emoji图标
     const modeEmojis = {
       'dictation': '🎧',
       'en2cn': '🇨🇳',
       'cn2en': '🇺🇸',
       'game': '🎮'
     };
     
     const emoji = modeEmojis[testMode] || '📝';
     
     // 设置当前分享数据
     this.setData({
       currentShareData: {
         title: `${emoji} ${testName}`,
         path: this.getSharePath(testMode, shareId),
         imageUrl: '/assets/icons/logo.png'
       }
     });

     // 主动触发分享
     wx.shareAppMessage({
       title: `${emoji} ${testName}`,
       path: this.getSharePath(testMode, shareId),
       imageUrl: '/assets/icons/logo.png',
       success: () => {
         // 显示分享成功弹窗，提供返回和查看分享页选项
         this.showShareSuccessModal();
       },
       fail: () => {
         wx.showToast({ title: '分享取消', icon: 'none' });
       }
     });
   },

   // 获取分享路径
   getSharePath(testMode, shareId) {
     if (testMode === 'dictation') {
       return `pages/spelling/practice/practice?shareId=${shareId}&shareMode=share`;
     } else if (testMode === 'game') {
       // 消消乐需要传递gameMode参数，避免显示模式选择界面
       const gameMode = 'time'; // 默认值，实际应该从分享数据中获取
       return `pages/task/puzzle/puzzle?shareId=${shareId}&isShared=true&mode=custom&gameMode=${gameMode}`;
     } else if (testMode === 'phrase_en2zh' || testMode === 'phrase_zh2en') {
       // 短语测试需要传递isPhrase参数
       const actualTestMode = testMode === 'phrase_en2zh' ? 'en_to_cn' : 'cn_to_en';
       return `pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${actualTestMode}&isPhrase=true`;
     } else {
       return `pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${testMode}`;
     }
   },

   // 跳转到分享管理页面
   goToShareManagement(shareId) {
     wx.navigateTo({
       url: '/pages/profile/share/share',
       success: () => {
         console.log('跳转到我的分享页面成功');
       },
       fail: (err) => {
         console.error('跳转失败:', err);
         wx.showToast({ 
           title: '跳转失败', 
           icon: 'none'
         });
       }
     });
   },

   // 分享测试结果
   shareTestResult() {
     const options = ['保存成绩图片到相册', '分享给微信好友', '分享到朋友圈'];

     wx.showActionSheet({
       itemList: options,
       success: (res) => {
         switch(res.tapIndex) {
           case 0:
             this.saveResultImageToAlbum();
             break;
           case 1:
             this.shareDictationToWeChat();
             break;
           case 2:
             this.shareToTimeline();
             break;
         }
       }
     });
   },

   // 生成听写测试结果图片
   generateDictationResultImage() {
     const { overallStats } = this.data;
     
     // 创建画布
     const ctx = wx.createCanvasContext('dictationResultCanvas', this);
     
     // 设置背景
     const gradient = ctx.createLinearGradient(0, 0, 375, 600);
     gradient.addColorStop(0, '#667eea');
     gradient.addColorStop(1, '#764ba2');
     ctx.fillStyle = gradient;
     ctx.fillRect(0, 0, 375, 600);
     
     // 设置字体样式
     ctx.setFillStyle('#ffffff');
     ctx.setFontSize(28);
     ctx.setTextAlign('center');
     
     // 标题
     ctx.fillText('墨词自习室 - 听写成绩', 187.5, 60);
     
     // 测试信息
     ctx.setFontSize(24);
     ctx.fillText(`听写测试`, 187.5, 100);
     
     // 成绩卡片背景
     ctx.setFillStyle('rgba(255, 255, 255, 0.95)');
     ctx.fillRect(30, 130, 315, 340);
     
     // 成绩信息
     ctx.setFillStyle('#333333');
     ctx.setFontSize(36);
     ctx.setTextAlign('center');
     ctx.fillText(`${overallStats.accuracyRate}%`, 187.5, 200);
     
     ctx.setFontSize(20);
     ctx.fillText(`准确率`, 187.5, 230);
     
     // 详细统计
     ctx.setFontSize(18);
     ctx.setTextAlign('left');
     
     ctx.fillText(`总词数：${overallStats.totalWords}`, 60, 280);
     ctx.fillText(`正确数：${overallStats.correctCount}`, 60, 310);
     ctx.fillText(`错误数：${overallStats.wrongCount}`, 60, 340);
     ctx.fillText(`用时：${overallStats.formattedTime}`, 60, 370);
     
     // 时间信息
     const testTime = new Date().toLocaleString('zh-CN');
     ctx.setFontSize(14);
     ctx.setTextAlign('center');
     ctx.fillText(`测试时间：${testTime}`, 187.5, 420);
     
     // 二维码占位
     ctx.setFillStyle('#f0f0f0');
     ctx.fillRect(300, 480, 60, 60);
     ctx.setFillStyle('#666666');
     ctx.setFontSize(12);
     ctx.fillText('扫码体验', 330, 570);
     
     ctx.draw(false, () => {
       // 保存图片到相册
       wx.canvasToTempFilePath({
         canvasId: 'dictationResultCanvas',
         success: (res) => {
           // 直接尝试保存，不预先检查权限
           wx.saveImageToPhotosAlbum({
             filePath: res.tempFilePath,
             success: () => {
               wx.showToast({
                 title: '已保存到相册',
                 icon: 'success'
               });
             },
             fail: (err) => {
               console.error('直接保存失败:', err);
               // 如果直接保存失败，尝试权限处理流程
               this.handleSaveFailure(err, res.tempFilePath);
             }
           });
         },
         fail: (error) => {
           console.error('生成图片失败:', error);
           wx.showToast({
             title: '生成图片失败',
             icon: 'none'
           });
         }
       }, this);
     });
   },

   // 分享听写结果到微信
   shareDictationToWeChat() {
     const { overallStats } = this.data;
     
     // 设置分享数据并显示弹窗
     this.setData({
       currentShareData: {
         title: `我在墨词自习室听写测试中获得了${overallStats.accuracyRate}%的准确率！`,
         path: `/pages/spelling/practice/practice?from=share`,
         imageUrl: '/assets/icons/logo.png'
       },
       showShareModal: true
     });
   },

   // 复制听写结果链接
   copyDictationResultLink() {
     const { overallStats } = this.data;
     
     const shareText = `🎯 墨词自习室听写成绩分享\n\n📚 听写测试\n📊 准确率：${overallStats.accuracyRate}%\n✅ 正确：${overallStats.correctCount}/${overallStats.totalWords}\n⏱️ 用时：${overallStats.formattedTime}\n\n快来挑战吧！`;
     
     wx.setClipboardData({
       data: shareText,
       success: () => {
         wx.showToast({
           title: '已复制到剪贴板',
           icon: 'success'
         });
       }
     });
   },

   // 关闭分享弹窗
   closeShareModal() {
     this.setData({
       showShareModal: false
     });
   },

   // 分享按钮点击
   onShareButtonTap() {
     console.log('分享按钮被点击');
     // 关闭分享弹窗
     this.setData({
       showShareModal: false
     });
   },

   // 保存听写成绩图片到相册（替换复制分享信息功能）
   saveDictationResultToAlbum() {
     // 关闭分享弹窗
     this.setData({
       showShareModal: false
     });
     
     // 调用生成图片功能
     this.generateDictationResultImage();
   },

   // 关闭测试结果页面 - 模仿英译汉的返回逻辑
   closeTestResult() {
     // 先确保考试锁定模式已解除
     if (this.data.examMode) {
       console.log('🔓 关闭测试结果时解除考试锁定模式');
       this.disableExamMode();
     }

     this.setData({
       showTestResult: false,
       examMode: false // 确保考试模式状态被重置
     });

     // 模仿英译汉的返回逻辑
     this.backToHome();
   },

   // 返回主页 - 与英译汉测试保持一致的逻辑
   backToHome() {
     // 检查页面栈，如果只有一个页面或者前一个页面是主页，则使用switchTab
     const pages = getCurrentPages();
     if (pages.length <= 1) {
       wx.switchTab({
         url: '/pages/index/index'
       });
     } else {
       const prevPage = pages[pages.length - 2];
       if (prevPage && prevPage.route === 'pages/index/index') {
         wx.switchTab({
           url: '/pages/index/index'
         });
       } else {
         wx.navigateBack();
       }
     }
   },

   // 格式化测试时间
   formatTestTime(milliseconds) {
     const totalSeconds = Math.floor(milliseconds / 1000);
     const minutes = Math.floor(totalSeconds / 60);
     const seconds = totalSeconds % 60;
     
     if (minutes > 0) {
       return `${minutes}分${seconds}秒`;
     } else {
       return `${seconds}秒`;
     }
   },

  // 加载指定页面
  loadPage(pageIndex) {
    const startIndex = pageIndex * this.data.wordsPerPage;
    const endIndex = Math.min(startIndex + this.data.wordsPerPage, this.data.words.length);
    const currentPageWords = this.data.words.slice(startIndex, endIndex);
    
    // 生成输入行数据（一行两个）
    const inputRows = [];
    for (let i = 0; i < currentPageWords.length; i += 2) {
      const row = [];
      row.push({
        word: currentPageWords[i],
        globalIndex: startIndex + i
      });
      if (i + 1 < currentPageWords.length) {
        row.push({
          word: currentPageWords[i + 1],
          globalIndex: startIndex + i + 1
        });
      }
      inputRows.push(row);
    }
    
    // 如果是测试恢复模式，尝试恢复该页的用户输入
    let userInputsForPage = new Array(currentPageWords.length).fill('');
    if (this.data.isTestResume && this.data.examMode && this.data.allUserAnswers && this.data.allUserAnswers[pageIndex]) {
      userInputsForPage = this.data.allUserAnswers[pageIndex];
      console.log(`🔄 恢复页面${pageIndex + 1}的用户输入:`, userInputsForPage);
    }
    
    this.setData({
      currentPageIndex: pageIndex,
      currentPageWords: currentPageWords,
      currentlyPlayingIndex: 0,
      userInputs: userInputsForPage,
      isPageCompleted: false,
      inputRows: inputRows, // 添加输入行数据
      // 重置练习模式的显示控制数组
      revealedWords: new Array(currentPageWords.length).fill(false),
      revealedPhonetics: new Array(currentPageWords.length).fill(false),
      revealedMeanings: new Array(currentPageWords.length).fill(false),
      revealedExamples: new Array(currentPageWords.length).fill(false),
      // 重置播放状态为等待状态
      playingStage: 'waiting',
      audioPlayCompleted: false,
      autoPlaying: false
    });
    
    console.log(`📄 加载页面${pageIndex + 1}`);
  },

  // 开始提交倒计时
  startSubmitCountdown() {
    // 检查是否已有倒计时在运行，避免重复启动
    if (this.countdownTimer) {
      console.log('⚠️ 倒计时已在运行中，跳过重复启动');
      return;
    }
    
    console.log('🕒 启动10秒提交倒计时');
    
    this.setData({
      showCountdown: true,
      countdownSeconds: 10
    });
    
    wx.showToast({
      title: '音频播放完成，10秒后自动提交',
      icon: 'none',
      duration: 2000
    });
    
    this.countdownTimer = setInterval(() => {
      const newSeconds = this.data.countdownSeconds - 1;
      this.setData({ countdownSeconds: newSeconds });
      
      if (newSeconds <= 0) {
        this.stopCountdown();
        this.submitAllAnswers();
      }
    }, 1000);
  },
  
  // 停止倒计时
  stopCountdown() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
    this.setData({
      showCountdown: false,
      countdownSeconds: 10
    });
  },

  // 提交所有答案
  submitAllAnswers() {
    // 停止倒计时
    this.stopCountdown();
    
    // 保存当前页面的答案
    const allUserAnswers = { ...this.data.allUserAnswers };
    allUserAnswers[this.data.currentPageIndex] = [...this.data.userInputs];
    this.setData({ allUserAnswers });
    
    // 生成测试结果
    this.generateTestResults();
  },

  // 页面分享
  onShareAppMessage() {
    if (this.data.currentShareData) {
      return this.data.currentShareData;
    }

    return {
      title: '墨词自习室 - 师生互动学习空间',
      path: '/pages/index/index',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 朋友圈分享配置
  onShareTimeline() {
    // 优先使用专门为朋友圈设置的分享数据
    const timelineShareData = this.data.currentTimelineShareData;
    if (timelineShareData) {
      return timelineShareData;
    }

    // 其次使用普通分享数据
    const shareData = this.data.currentShareData;
    if (shareData) {
      return {
        title: shareData.title + ' 快来挑战吧！',
        query: 'from=timeline',
        imageUrl: shareData.imageUrl
      };
    }

    // 默认朋友圈分享数据
    const { score, accuracyRate } = this.data;

    return {
      title: `我在墨词自习室听写测试中获得了${score}分！快来挑战吧！`,
      query: 'from=timeline',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 检查是否有下一关可用（听写竞赛模式）
  async checkNextLevel() {
    try {
      if (!this.data.masterCompetitionId) {
        return;
      }

      // 获取分组竞赛详情，检查下一关状态
      const result = await wx.cloud.callFunction({
        name: 'getCompetitions',
        data: {
          mode: 'getGroupedDetail',
          masterCompetitionId: this.data.masterCompetitionId
        }
      });

      if (result.result.success) {
        const data = result.result.data;
        const levels = data.levels || [];
        
        // 找到当前关卡在列表中的位置
        const currentLevelIndex = levels.findIndex(level => level.id === this.data.competitionId);
        const hasNext = currentLevelIndex >= 0 && currentLevelIndex < levels.length - 1;
        
        console.log('听写检查下一关:', {
          currentLevelIndex,
          totalLevels: levels.length,
          hasNext,
          nextLevelId: hasNext ? levels[currentLevelIndex + 1].id : null
        });
        
        this.setData({
          hasNextLevel: hasNext,
          nextLevelId: hasNext ? levels[currentLevelIndex + 1].id : null,
          nextLevelNumber: hasNext ? levels[currentLevelIndex + 1].levelNumber : null
        });
      }
    } catch (error) {
      console.error('检查下一关失败:', error);
    }
  },

  // 进入下一关竞赛
  async goToNextCompetitionLevel() {
    if (!this.data.nextLevelId) {
      wx.showToast({
        title: '没有下一关了',
        icon: 'none'
      });
      return;
    }

    // 检查当前关卡的正确率是否达到80%
    // 优先使用overallStats中的accuracyRate，其次使用根级别的accuracyRate
    let currentAccuracy = 0;
    if (this.data.overallStats && this.data.overallStats.accuracyRate !== undefined) {
      currentAccuracy = this.data.overallStats.accuracyRate;
    } else if (this.data.accuracyRate !== undefined) {
      currentAccuracy = this.data.accuracyRate;
    } else if (this.data.correctRate !== undefined) {
      currentAccuracy = this.data.correctRate;
    }

    if (currentAccuracy < 80) {
      wx.showModal({
        title: '正确率不达标',
        content: `当前正确率为${currentAccuracy}%，需要达到80%以上才能进入下一关。\n\n是否重新挑战本关？`,
        confirmText: '重新挑战',
        cancelText: '返回列表',
        success: (res) => {
          if (res.confirm) {
            // 重新开始当前关卡
            this.restartTest();
          } else {
            // 返回关卡列表
            this.backToLevelSelect();
          }
        }
      });
      return;
    }

    wx.showLoading({
      title: '加载下一关...',
      mask: true
    });

    try {
      // 跳转到下一关听写
      wx.redirectTo({
        url: `/pages/spelling/practice/practice?competitionId=${this.data.nextLevelId}&mode=competition&practiceMode=test&masterCompetitionId=${this.data.masterCompetitionId}`
      });
    } catch (error) {
      wx.hideLoading();
      console.error('进入下一关失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 查看竞赛排行榜
  viewCompetitionRanking() {
    const { competitionId, masterCompetitionId } = this.data;

    if (!competitionId) {
      wx.showToast({
        title: '竞赛信息不完整',
        icon: 'none'
      });
      return;
    }

    // 如果是多关卡竞赛，直接查看整体排行榜（不是单个关卡的排行榜）
    if (masterCompetitionId) {
      wx.navigateTo({
        url: `/pages/competition/ranking/ranking?competitionId=${masterCompetitionId}&mode=dictation&type=master`,
        fail: (err) => {
          console.error('跳转排行榜失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 单一竞赛，直接查看排行榜
      wx.navigateTo({
        url: `/pages/competition/ranking/ranking?competitionId=${competitionId}&mode=dictation`,
        fail: (err) => {
          console.error('跳转排行榜失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 重新开始测试
  restartTest() {
    // 重置测试状态
    this.setData({
      currentIndex: 0,
      correctCount: 0,
      wrongCount: 0,
      score: 0,
      correctRate: 0,
      accuracyRate: 0,
      showResultPage: false,
      showCompletion: false,
      startTime: Date.now(),
      totalTime: 0,
      userAnswers: {},
      allUserAnswers: {},
      overallStats: {
        totalWords: 0,
        correctCount: 0,
        wrongCount: 0,
        accuracyRate: 0,
        testTime: 0
      }
    });

    // 重新开始听写
    this.startDictation();
  },

  // 返回关卡选择页面
  backToLevelSelect() {
    // 设置全局标记，通知关卡选择页面需要刷新
    const app = getApp();
    app.globalData.needRefreshLevelSelect = true;

    if (this.data.masterCompetitionId) {
      wx.navigateTo({
        url: `/pages/competition/level-select/level-select?masterCompetitionId=${this.data.masterCompetitionId}&mode=dictation`
      });
    } else {
      wx.navigateBack({
        fail: () => {
          // 如果返回失败，说明没有上一页，则重新导航到关卡选择页面
          const { shareId } = this.data;
          if (shareId) {
            wx.redirectTo({
              url: `/pages/competition/level-select/level-select?shareId=${shareId}&mode=share&testType=dictation&refresh=true`
            });
          }
        }
      });
    }
  },

  // 通知关卡选择页面刷新状态
  notifyLevelSelectRefresh() {
    // 通过全局事件总线通知关卡选择页面刷新
    const app = getApp();
    if (app.globalData) {
      app.globalData.needRefreshLevelSelect = true;
      app.globalData.refreshTimestamp = Date.now();
    }
    
    // 同时通过页面栈查找关卡选择页面并触发刷新
    const pages = getCurrentPages();
    for (let i = pages.length - 1; i >= 0; i--) {
      const page = pages[i];
      if (page.route.includes('level-select') && typeof page.refreshLevelStatus === 'function') {
        console.log('找到关卡选择页面，触发刷新');
        page.refreshLevelStatus();
        break;
      }
    }
  },

  // 保存成绩图片到相册
  saveResultImageToAlbum() {
    // 检查微信版本和环境
    const systemInfo = wx.getSystemInfoSync();
    console.log('保存图片 - 系统信息:', {
      platform: systemInfo.platform,
      version: systemInfo.version,
      SDKVersion: systemInfo.SDKVersion
    });

    // 先尝试直接保存，如果失败再处理权限
    this.attemptDirectSave();
  },

  // 尝试直接保存（绕过权限检查）
  attemptDirectSave() {
    wx.showLoading({
      title: '生成图片中...',
      mask: true
    });

    // 生成成绩图片并保存到相册
    this.generateDictationResultImageForSave().then(() => {
      wx.canvasToTempFilePath({
        canvasId: 'dictationResultCanvas',
        success: (res) => {
          // 直接尝试保存，不预先检查权限
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading();
              wx.showToast({
                title: '已保存到相册',
                icon: 'success'
              });
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('直接保存失败:', err);
              // 如果直接保存失败，尝试权限处理流程
              this.handleSaveFailure(err, res.tempFilePath);
            }
          });
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('生成图片失败:', err);
          this.showFallbackOptions();
        }
      }, this);
    }).catch((err) => {
      wx.hideLoading();
      console.error('生成图片失败:', err);
      this.showFallbackOptions();
    });
  },

  // 处理保存失败的情况
  handleSaveFailure(err, tempFilePath) {
    console.error('保存图片失败详情:', err);

    if (err.errMsg.includes('privacy api banned') || err.errMsg.includes('banned')) {
      // API被禁用，提供替代方案
      this.showPrivacyApiBannedDialog();
    } else if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
      // 权限问题，尝试权限流程
      this.handlePermissionFlow(tempFilePath);
    } else {
      // 其他错误，显示通用错误处理
      this.showFallbackOptions();
    }
  },

  // 显示隐私API被禁用的对话框
  showPrivacyApiBannedDialog() {
    wx.showModal({
      title: '保存功能暂不可用',
      content: '由于微信政策限制，当前版本暂时无法直接保存图片到相册。\n\n建议您：\n1. 截屏保存当前页面\n2. 等待后续版本更新\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 提示用户截屏
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },

  // 处理权限流程
  handlePermissionFlow(tempFilePath) {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启相册权限',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      // 用户开启了权限，重新尝试保存
                      this.retrySaveImage(tempFilePath);
                    }
                  }
                });
              }
            }
          });
        } else {
          // 尝试请求权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.retrySaveImage(tempFilePath);
            },
            fail: () => {
              this.showFallbackOptions();
            }
          });
        }
      },
      fail: () => {
        this.showFallbackOptions();
      }
    });
  },

  // 重新尝试保存图片
  retrySaveImage(tempFilePath) {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: () => {
        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('重试保存失败:', err);
        this.showFallbackOptions();
      }
    });
  },

  // 显示备选方案
  showFallbackOptions() {
    wx.showModal({
      title: '保存失败',
      content: '图片保存失败，您可以：\n\n1. 截屏保存当前页面\n2. 稍后重试\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '稍后重试',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },



  // 生成听写结果图片用于保存
  generateDictationResultImageForSave() {
    return new Promise((resolve, reject) => {
      // 优先使用overallStats数据，如果没有则使用其他字段
      const { overallStats, score, correctCount, wrongCount, totalWords, accuracyRate, libraryName, total } = this.data;

      // 确定要使用的数据
      let finalScore, finalCorrectCount, finalWrongCount, finalTotalWords, finalAccuracyRate, finalLibraryName, finalFormattedTime;

      if (overallStats && overallStats.totalWords > 0) {
        // 使用overallStats数据
        finalScore = Math.round(overallStats.accuracyRate) || 0;
        finalCorrectCount = overallStats.correctCount || 0;
        finalWrongCount = overallStats.wrongCount || 0;
        finalTotalWords = overallStats.totalWords || 0;
        finalAccuracyRate = Math.round(overallStats.accuracyRate) || 0;
        finalFormattedTime = overallStats.formattedTime || '未知';
      } else {
        // 使用其他字段数据
        finalScore = score || Math.round(accuracyRate || 0);
        finalCorrectCount = correctCount || 0;
        finalWrongCount = wrongCount || 0;
        finalTotalWords = totalWords || total || 0;
        finalAccuracyRate = Math.round(accuracyRate || 0);
        finalFormattedTime = this.formatTestTime(this.data.totalTime || 0);
      }

      // 优化词库名称获取逻辑
      finalLibraryName = libraryName || this.getLibraryNameFromCurrentData() || '词库';

      console.log('📚 分享图片词库信息:', {
        libraryName: libraryName,
        currentLibraryName: this.getLibraryNameFromCurrentData(),
        finalLibraryName: finalLibraryName
      });

      // 创建画布
      const ctx = wx.createCanvasContext('dictationResultCanvas', this);

      // 设置背景
      const gradient = ctx.createLinearGradient(0, 0, 375, 600);
      gradient.addColorStop(0, '#667eea');
      gradient.addColorStop(1, '#764ba2');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, 375, 600);

      // 设置字体样式
      ctx.setFillStyle('#ffffff');
      ctx.setFontSize(28);
      ctx.setTextAlign('center');

      // 标题
      ctx.fillText('墨词自习室 - 听写成绩', 187.5, 60);

      // 测试信息
      ctx.setFontSize(24);
      ctx.fillText('听写测试', 187.5, 100);
      ctx.setFontSize(18);
      ctx.fillText(`词库：${finalLibraryName}`, 187.5, 130);

      // 成绩卡片背景
      ctx.setFillStyle('rgba(255, 255, 255, 0.95)');
      ctx.fillRect(30, 150, 315, 300);

      // 成绩信息
      ctx.setFillStyle('#333333');
      ctx.setFontSize(36);
      ctx.setTextAlign('center');
      ctx.fillText(`${finalScore}分`, 187.5, 220);

      ctx.setFontSize(20);
      ctx.fillText(`最终得分`, 187.5, 250);

      // 详细统计
      ctx.setFontSize(18);
      ctx.setTextAlign('left');

      ctx.fillText(`正确：${finalCorrectCount}个`, 60, 300);
      ctx.fillText(`错误：${finalWrongCount}个`, 200, 300);
      ctx.fillText(`总词数：${finalTotalWords}个`, 60, 330);
      ctx.fillText(`正确率：${finalAccuracyRate}%`, 200, 330);
      ctx.fillText(`用时：${finalFormattedTime}`, 60, 360);

      // 时间信息
      const testTime = new Date().toLocaleString('zh-CN');
      ctx.setFontSize(14);
      ctx.setTextAlign('center');
      ctx.fillText(`测试时间：${testTime}`, 187.5, 400);

      ctx.draw(false, () => {
        resolve();
      });
    });
  },

  // 分享到朋友圈
  shareToTimeline() {
    const { score, correctCount, totalWords, accuracyRate, libraryName } = this.data;

    // 设置朋友圈分享数据
    this.setData({
      currentTimelineShareData: {
        title: `我在墨词自习室听写测试中得了${score}分！正确率${accuracyRate}%，快来挑战吧！`,
        query: 'from=timeline',
        imageUrl: '/assets/icons/logo.png'
      }
    });

    // 更新分享菜单，启用朋友圈分享
    wx.updateShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        // 直接提示用户朋友圈分享已准备好
        wx.showToast({
          title: '朋友圈分享已准备好',
          icon: 'success',
          duration: 1500
        });

        // 延迟提示用户使用右上角分享
        setTimeout(() => {
          wx.showModal({
            title: '分享到朋友圈',
            content: '请点击右上角"..."按钮，选择"分享到朋友圈"来分享您的听写成绩！',
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1600);
      },
      fail: () => {
        wx.showToast({
          title: '分享功能暂不可用',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 显示分享成功弹窗
   */
  showShareSuccessModal() {
    wx.showModal({
      title: '分享成功',
      content: '听写测试已成功分享给好友！',
      cancelText: '返回',
      confirmText: '查看分享页',
      success: (res) => {
        if (res.confirm) {
          // 用户点击"查看分享页"
          wx.navigateTo({
            url: '/pages/profile/share/share',
            success: () => {
              console.log('跳转到我的分享页面成功');
            },
            fail: (err) => {
              console.error('跳转失败:', err);
              wx.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
        } else if (res.cancel) {
          // 用户点击"返回"，不做任何操作，保持在当前页面
          console.log('用户选择返回当前页面');
        }
      }
    });
  },
});