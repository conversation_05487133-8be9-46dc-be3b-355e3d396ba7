/**
 * 测试防作弊锁定工具
 * 提供全屏模式、屏蔽返回按键等功能
 */

function ExamLock() {
  this.isLocked = false;
  this.originalBackHandler = null;
  this.originalNavigationBarStyle = null;
  this.originalNavigateBack = null;
  this.originalReLaunch = null;
  this.originalRedirectTo = null;
  this.originalSwitchTab = null;
  this.originalOnUnload = null;
  this.originalOnHide = null;
  this.onAppHideHandler = null;
  this.onAppShowHandler = null;
  this.allowNavigation = false; // 控制是否允许导航
  this.lockStartTime = null;
  this.preventBackCount = 0;
  this.maxPreventCount = 3; // 最大阻止次数
  this.isHandlingAttempt = false; // 防止重复处理
  this.currentTestStateId = null; // 当前测试状态ID
}

/**
 * 启用测试锁定模式
 */
ExamLock.prototype.enable = function(examName, onBackAttempt, onExitConfirm) {
  if (this.isLocked) {
    console.log('测试锁定模式已启用');
    return;
  }

  console.log('启用测试防作弊锁定模式');

  this.isLocked = true;
  this.lockStartTime = Date.now();
  this.preventBackCount = 0;
  this.isHandlingAttempt = false;

  // 1. 启用沉浸式状态栏（全屏模式）
  this.enableFullScreen();

  // 2. 屏蔽物理返回按键
  this.disableBackButton(onBackAttempt, onExitConfirm, examName);

  // 3. 屏蔽上拉刷新和下拉刷新
  this.disablePullRefresh();

  // 4. 禁止长按保存图片等操作
  this.disableLongPress();

  // 5. 添加页面生命周期监听
  this.addLifecycleListeners();

  console.log('测试锁定模式启用完成');
};

/**
 * 禁用测试锁定模式
 */
ExamLock.prototype.disable = function() {
  if (!this.isLocked) {
    console.log('测试锁定模式未启用');
    return;
  }

  console.log('禁用测试防作弊锁定模式');

  this.isLocked = false;

  // 1. 恢复普通状态栏
  this.disableFullScreen();

  // 2. 恢复返回按键功能
  this.enableBackButton();

  // 3. 恢复上拉下拉刷新
  this.enablePullRefresh();

  // 4. 恢复长按操作
  this.enableLongPress();

  // 5. 重置所有状态
  this.preventBackCount = 0;
  this.isHandlingAttempt = false;
  this.lockStartTime = null;

  console.log('测试锁定模式禁用完成');
};

/**
 * 启用全屏模式
 */
ExamLock.prototype.enableFullScreen = function() {
  try {
    // 设置沉浸式状态栏
    wx.setNavigationBarColor && wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#000000',
      animation: {
        duration: 400,
        timingFunc: 'easeIn'
      }
    });

    console.log('全屏模式启用成功');
  } catch (error) {
    console.error('启用全屏模式失败:', error);
  }
};

/**
 * 禁用全屏模式
 */
ExamLock.prototype.disableFullScreen = function() {
  try {
    // 恢复导航栏
    wx.setNavigationBarColor && wx.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff',
      animation: {
        duration: 400,
        timingFunc: 'easeIn'
      }
    });

    console.log('全屏模式禁用成功');
  } catch (error) {
    console.error('禁用全屏模式失败:', error);
  }
};

/**
 * 屏蔽返回按键
 */
ExamLock.prototype.disableBackButton = function(onBackAttempt, onExitConfirm, examName) {
  var self = this;
  var pages = getCurrentPages();
  if (pages.length > 0) {
    var currentPage = pages[pages.length - 1];
    
    // 保存原始方法
    this.originalOnHide = currentPage.onHide;
    this.originalOnUnload = currentPage.onUnload;
    this.originalNavigateBack = wx.navigateBack;
    
    // 1. 重写全局导航方法（拦截代码调用的导航）
    wx.navigateBack = function(options) {
      if (self.isLocked && !self.allowNavigation) {
        console.log('🚫 拦截 wx.navigateBack 调用');
        self.handleBackAttemptDirectly(examName, onBackAttempt, onExitConfirm);
        return Promise.reject(new Error('测试模式下无法退出'));
      }
      return self.originalNavigateBack(options);
    };
    
    // 同时拦截其他可能的退出方式
    this.originalReLaunch = wx.reLaunch;
    this.originalRedirectTo = wx.redirectTo;
    this.originalSwitchTab = wx.switchTab;
    
    wx.reLaunch = function(options) {
      if (self.isLocked && !self.allowNavigation) {
        console.log('🚫 拦截 wx.reLaunch 调用');
        self.handleBackAttemptDirectly(examName, onBackAttempt, onExitConfirm);
        return Promise.reject(new Error('测试模式下无法退出'));
      }
      return self.originalReLaunch(options);
    };
    
    wx.redirectTo = function(options) {
      if (self.isLocked && !self.allowNavigation) {
        console.log('🚫 拦截 wx.redirectTo 调用');
        self.handleBackAttemptDirectly(examName, onBackAttempt, onExitConfirm);
        return Promise.reject(new Error('测试模式下无法退出'));
      }
      return self.originalRedirectTo(options);
    };
    
    wx.switchTab = function(options) {
      if (self.isLocked && !self.allowNavigation) {
        console.log('🚫 拦截 wx.switchTab 调用');
        self.handleBackAttemptDirectly(examName, onBackAttempt, onExitConfirm);
        return Promise.reject(new Error('测试模式下无法退出'));
      }
      return self.originalSwitchTab(options);
    };
    
    // 2. 重写页面的 onHide 方法（监听页面隐藏）
    currentPage.onHide = function() {
      if (self.isLocked && !self.allowNavigation) {
        console.log('🚫 检测到页面隐藏，可能是用户尝试退出');
        
        // 保存当前页面信息
        var currentRoute = currentPage.route;
        var currentOptions = currentPage.options || {};
        
        // 立即重新导航回测试页面
        setTimeout(function() {
          self.redirectToTestPageAndShowDialog(examName, onBackAttempt, onExitConfirm, {
            route: currentRoute,
            options: currentOptions
          });
        }, 100);
      }
      
      // 执行原始的onHide
      if (self.originalOnHide) {
        self.originalOnHide.call(currentPage);
      }
    };

    // 3. 重写页面的 onUnload 方法（拦截系统返回）
    currentPage.onUnload = function(e) {
      if (self.isLocked && !self.allowNavigation) {
        console.log('🚫 检测到页面尝试卸载，但测试锁定模式已启用');

        // 在页面卸载前立即保存状态
        console.log('💾 页面卸载前保存状态');
        var testState = null;
        if (typeof currentPage.recordTestInterruption === 'function') {
          console.log('📋 调用页面方法保存状态');
          testState = currentPage.recordTestInterruption(true);
        }

        // 保存当前页面信息
        var currentRoute = currentPage.route;
        var currentOptions = currentPage.options || {};

        // 立即重新导航回测试页面（无延迟）
        self.redirectToTestPageAndShowDialog(examName, onBackAttempt, onExitConfirm, {
          route: currentRoute,
          options: currentOptions
        }, testState);

        return false;
      }
      
      // 如果锁定已解除或允许导航，执行原始的onUnload
      if (self.originalOnUnload) {
        return self.originalOnUnload.call(currentPage, e);
      }
    };
  }
};

/**
 * 直接处理退出尝试（不卸载页面）
 */
ExamLock.prototype.handleBackAttemptDirectly = function(examName, onBackAttempt, onExitConfirm) {
  var self = this;

  // 防止重复弹窗
  if (this.isHandlingAttempt) {
    console.log('正在处理退出尝试，忽略重复调用');
    return;
  }

  this.isHandlingAttempt = true;
  this.preventBackCount++;

  console.log('🚫 用户第' + this.preventBackCount + '次尝试退出');

  // 触发回调（但不暂停计时器）
  if (typeof onBackAttempt === 'function') {
    onBackAttempt(this.preventBackCount);
  }

  if (this.preventBackCount >= this.maxPreventCount) {
    // 超过最大阻止次数，显示最终确认对话框
    wx.showModal({
      title: '🚫 强制退出确认',
      content: '您已' + this.maxPreventCount + '次尝试退出' + examName + '！\n\n⚠️ 确定要放弃当前测试吗？\n\n📝 提醒：退出后测试将继续在后台进行并自动提交成绩！',
      confirmText: '确定退出',
      cancelText: '继续测试',
      confirmColor: '#ff4444',
      success: function(res) {
        self.isHandlingAttempt = false;
        
        if (res.confirm) {
          // 用户确认退出
          console.log('✅ 用户最终确认退出测试');
          
          wx.showToast({
            title: '测试继续后台进行',
            icon: 'none',
            duration: 2000
          });
          
          // 临时允许导航操作
          self.allowNavigation = true;
          self.disable();
          
          setTimeout(function() {
            onExitConfirm();
          }, 100);
        } else {
          // 用户取消退出，重置计数器
          self.preventBackCount = Math.max(0, self.preventBackCount - 2);
          wx.showToast({
            title: '继续测试中...',
            icon: 'success',
            duration: 1500
          });
        }
      },
      fail: function() {
        self.isHandlingAttempt = false;
      }
    });
  } else {
    // 显示警告提示
    var remainingAttempts = this.maxPreventCount - this.preventBackCount;
    var encourageMessages = [
      '🔒 测试模式已锁定，请专心答题',
      '💪 坚持就是胜利，继续努力',
      '🎯 专注测试，马上就要完成了'
    ];
    var randomMessage = encourageMessages[Math.floor(Math.random() * encourageMessages.length)];
    
    wx.showModal({
      title: '🚨 测试进行中',
      content: examName + '正在进行中，暂时无法退出。\n\n💡 ' + randomMessage + '\n\n如确需退出，请再尝试 ' + remainingAttempts + ' 次。',
      showCancel: false,
      confirmText: '继续测试',
      confirmColor: '#007aff',
      success: function() {
        self.isHandlingAttempt = false;
        
        // 随机显示鼓励提示
        var toastMessages = [
          '请专心测试 📚',
          '加油，你可以的！ 💪',
          '专注是成功的关键 🎯',
          '坚持到底 🏆'
        ];
        var randomToast = toastMessages[Math.floor(Math.random() * toastMessages.length)];
        
        wx.showToast({
          title: randomToast,
          icon: 'none',
          duration: 2000
        });
      },
      fail: function() {
        self.isHandlingAttempt = false;
      }
    });
  }
};

/**
 * 重新导航到测试页面并显示防作弊弹窗
 */
ExamLock.prototype.redirectToTestPageAndShowDialog = function(examName, onBackAttempt, onExitConfirm, pageInfo, preloadedTestState) {
  var self = this;

  // 防止重复处理
  if (this.isHandlingAttempt) {
    console.log('正在处理退出尝试，忽略重复调用');
    return;
  }

  this.isHandlingAttempt = true;
  this.preventBackCount++;

  console.log('🚫 用户第' + this.preventBackCount + '次尝试退出');

  // 使用预加载的状态或保存当前状态
  var testState;
  if (preloadedTestState) {
    console.log('📋 使用预加载的测试状态');
    testState = this.savePreloadedTestState(preloadedTestState);
  } else {
    console.log('📋 保存当前测试状态');
    testState = this.saveCurrentTestState();
  }
  this.currentTestStateId = testState.id;

  // 构建重新导航的URL
  var queryString = '';
  var options = pageInfo.options || {};

  // 手动构建查询字符串
  var params = [];
  for (var key in options) {
    if (options.hasOwnProperty(key)) {
      params.push(key + '=' + encodeURIComponent(options[key]));
    }
  }
  params.push('resumeTest=true');
  params.push('testStateId=' + this.currentTestStateId);

  if (params.length > 0) {
    queryString = '?' + params.join('&');
  }

  var fullUrl = '/' + pageInfo.route + queryString;

  console.log('🔄 重新导航到测试页面:', fullUrl);

  // 临时允许导航，避免被自己的拦截器阻止
  this.allowNavigation = true;

  // 直接使用 reLaunch 重新导航（可能更快）
  wx.reLaunch({
    url: fullUrl,
    success: function() {
      console.log('✅ reLaunch 导航成功');

      // 重新启用导航拦截
      self.allowNavigation = false;

      // 页面会自动检测恢复参数并恢复测试状态，无需显示弹窗
      console.log('🔄 页面将自动恢复测试状态');
    },
    fail: function(error) {
      console.error('❌ reLaunch 失败，尝试 redirectTo:', error);

      // 如果 reLaunch 失败，尝试 redirectTo
      wx.redirectTo({
        url: fullUrl,
        success: function() {
          console.log('✅ redirectTo 成功');

          // 重新启用导航拦截
          self.allowNavigation = false;

          // 页面会自动检测恢复参数并恢复测试状态，无需显示弹窗
          console.log('🔄 页面将自动恢复测试状态');
        },
        fail: function(fallbackError) {
          console.error('❌ redirectTo 也失败:', fallbackError);

          // 重新启用导航拦截
          self.allowNavigation = false;
          self.isHandlingAttempt = false;
        }
      });
    }
  });
};

/**
 * 显示防作弊弹窗
 */
ExamLock.prototype.showAntiCheatDialog = function(examName, onBackAttempt, onExitConfirm) {
  var self = this;

  // 触发回调（如果存在）
  if (typeof onBackAttempt === 'function') {
    onBackAttempt(this.preventBackCount);
  }

  if (this.preventBackCount >= this.maxPreventCount) {
    // 超过最大阻止次数，显示最终确认对话框
    wx.showModal({
      title: '🚫 强制退出确认',
      content: '您已' + this.maxPreventCount + '次尝试退出' + examName + '！\n\n⚠️ 确定要放弃当前测试吗？\n\n📝 提醒：退出后测试将继续在后台进行并自动提交成绩！',
      confirmText: '确定退出',
      cancelText: '继续测试',
      confirmColor: '#ff4444',
      success: function(res) {
        self.isHandlingAttempt = false;

        if (res.confirm) {
          // 用户确认退出
          console.log('✅ 用户最终确认退出测试');

          wx.showToast({
            title: '测试继续后台进行',
            icon: 'none',
            duration: 2000
          });

          // 临时允许导航操作
          self.allowNavigation = true;
          self.disable();

          setTimeout(function() {
            onExitConfirm();
          }, 100);
        } else {
          // 用户取消退出，重置计数器
          self.preventBackCount = Math.max(0, self.preventBackCount - 2);
          wx.showToast({
            title: '继续测试中...',
            icon: 'success',
            duration: 1500
          });
        }
      },
      fail: function() {
        self.isHandlingAttempt = false;
      }
    });
  } else {
    // 显示警告提示
    var remainingAttempts = this.maxPreventCount - this.preventBackCount;
    var encourageMessages = [
      '🔒 测试模式已锁定，请专心答题',
      '💪 坚持就是胜利，继续努力',
      '🎯 专注测试，马上就要完成了'
    ];
    var randomMessage = encourageMessages[Math.floor(Math.random() * encourageMessages.length)];

    wx.showModal({
      title: '🚨 测试进行中',
      content: examName + '正在进行中，暂时无法退出。\n\n💡 ' + randomMessage + '\n\n如确需退出，请再尝试 ' + remainingAttempts + ' 次。',
      showCancel: false,
      confirmText: '继续测试',
      confirmColor: '#007aff',
      success: function() {
        self.isHandlingAttempt = false;

        // 随机显示鼓励提示
        var toastMessages = [
          '请专心测试 📚',
          '加油，你可以的！ 💪',
          '专注是成功的关键 🎯',
          '坚持到底 🏆'
        ];
        var randomToast = toastMessages[Math.floor(Math.random() * toastMessages.length)];

        wx.showToast({
          title: randomToast,
          icon: 'none',
          duration: 2000
        });
      },
      fail: function() {
        self.isHandlingAttempt = false;
      }
    });
  }
};

/**
 * 保存当前测试状态
 */
ExamLock.prototype.saveCurrentTestState = function(additionalState) {
  additionalState = additionalState || {};
  var testStateId = 'test_state_' + Date.now();

  // 尝试从当前页面获取测试状态
  var pageTestState = {};
  try {
    var pages = getCurrentPages();
    var currentPage = pages[pages.length - 1];

    if (currentPage && currentPage.data) {
      console.log('📋 直接获取页面数据保存状态');
      console.log('📊 页面数据预览:', {
        testStarted: currentPage.data.testStarted,
        startTime: currentPage.data.startTime,
        currentIndex: currentPage.data.currentIndex,
        totalQuestions: currentPage.data.totalQuestions,
        wordsLength: currentPage.data.words?.length || 0,
        testMode: currentPage.data.testMode
      });

      // 直接获取页面的测试状态数据
        pageTestState = {
          // 基本测试状态
          testStarted: currentPage.data.testStarted,
          startTime: currentPage.data.startTime,
          currentIndex: currentPage.data.currentIndex,
          currentQuestion: (currentPage.data.currentIndex || 0) + 1, // 添加currentQuestion字段
          totalQuestions: currentPage.data.totalQuestions || currentPage.data.total,
          correctCount: currentPage.data.correctCount,
          wrongCount: currentPage.data.wrongCount,
          // 测试数据
          words: currentPage.data.words,
          currentWord: currentPage.data.currentWord,
          testMode: currentPage.data.testMode,
          isTestMode: currentPage.data.isTestMode,
          shareMode: currentPage.data.shareMode,
          perQuestionTime: currentPage.data.perQuestionTime,
          mistakes: currentPage.data.mistakes,
          userAnswers: currentPage.data.userAnswers,
          // 时间相关状态
          questionStartTime: currentPage.data.questionStartTime,
          currentQuestionTimeLeft: currentPage.data.currentQuestionTimeLeft,
          countdownStartTime: currentPage.data.countdownStartTime,
          // 答题状态
          isAnswered: currentPage.data.isAnswered,
          selectedOption: currentPage.data.selectedOption,
          correctOption: currentPage.data.correctOption,
          options: currentPage.data.options,
          userInput: currentPage.data.userInput,
          // 游戏状态（消消乐）
          gameStarted: currentPage.data.gameStarted,
          gameMode: currentPage.data.gameMode,
          score: currentPage.data.score,
          // 音频播放状态
          playingStage: currentPage.data.playingStage,
          audioPlayCompleted: currentPage.data.audioPlayCompleted,
          dictationSettings: currentPage.data.dictationSettings
        };

        console.log('📊 获取到页面测试状态:', pageTestState);
    } else {
      console.log('⚠️ 当前页面或页面数据不可用');
    }
  } catch (error) {
    console.error('❌ 获取页面测试状态失败:', error);
  }

  var testState = {
    id: testStateId,
    timestamp: Date.now(),
    isLocked: this.isLocked,
    preventBackCount: this.preventBackCount,
    lockStartTime: this.lockStartTime,
    examMode: true,
    resumeFromInterruption: true
  };

  // 合并页面测试状态
  for (var key in pageTestState) {
    if (pageTestState.hasOwnProperty(key)) {
      testState[key] = pageTestState[key];
    }
  }

  // 合并额外的状态数据
  for (var key in additionalState) {
    if (additionalState.hasOwnProperty(key)) {
      testState[key] = additionalState[key];
    }
  }

  try {
    wx.setStorageSync(testStateId, testState);
    console.log('💾 已保存完整测试状态:', testState);
  } catch (error) {
    console.error('❌ 保存测试状态失败:', error);
  }

  return testState;
};

/**
 * 保存预加载的测试状态
 */
ExamLock.prototype.savePreloadedTestState = function(preloadedState) {
  var testStateId = 'test_state_' + Date.now();

  var testState = {
    id: testStateId,
    timestamp: Date.now(),
    isLocked: this.isLocked,
    preventBackCount: this.preventBackCount,
    lockStartTime: this.lockStartTime,
    examMode: true,
    resumeFromInterruption: true
  };

  // 合并预加载的状态数据
  for (var key in preloadedState) {
    if (preloadedState.hasOwnProperty(key)) {
      testState[key] = preloadedState[key];
    }
  }

  try {
    wx.setStorageSync(testStateId, testState);
    console.log('💾 已保存预加载测试状态:', testState);
  } catch (error) {
    console.error('❌ 保存预加载测试状态失败:', error);
  }

  return testState;
};

/**
 * 恢复测试状态
 */
ExamLock.prototype.restoreTestState = function(testStateId) {
  if (!testStateId) {
    return null;
  }

  try {
    var testState = wx.getStorageSync(testStateId);
    if (testState) {
      console.log('📋 恢复测试状态:', testState);
      return testState;
    } else {
      console.log('⚠️ 未找到测试状态:', testStateId);
      return null;
    }
  } catch (error) {
    console.error('❌ 恢复测试状态失败:', error);
    return null;
  }
};

/**
 * 其他必要的方法
 */
ExamLock.prototype.disablePullRefresh = function() {
  try {
    wx.stopPullDownRefresh && wx.stopPullDownRefresh();
    console.log('已禁用下拉刷新');
  } catch (error) {
    console.error('禁用下拉刷新失败:', error);
  }
};

ExamLock.prototype.enablePullRefresh = function() {
  console.log('已恢复下拉刷新');
};

ExamLock.prototype.disableLongPress = function() {
  console.log('已禁用长按操作');
};

ExamLock.prototype.enableLongPress = function() {
  console.log('已恢复长按操作');
};

ExamLock.prototype.addLifecycleListeners = function() {
  console.log('已添加生命周期监听器');
};

ExamLock.prototype.enableBackButton = function() {
  var self = this;

  // 恢复所有导航方法
  if (this.originalNavigateBack) {
    wx.navigateBack = this.originalNavigateBack;
    this.originalNavigateBack = null;
  }

  if (this.originalReLaunch) {
    wx.reLaunch = this.originalReLaunch;
    this.originalReLaunch = null;
  }

  if (this.originalRedirectTo) {
    wx.redirectTo = this.originalRedirectTo;
    this.originalRedirectTo = null;
  }

  if (this.originalSwitchTab) {
    wx.switchTab = this.originalSwitchTab;
    this.originalSwitchTab = null;
  }

  // 恢复页面的onHide和onUnload方法
  var pages = getCurrentPages();
  if (pages.length > 0) {
    var currentPage = pages[pages.length - 1];

    if (this.originalOnHide) {
      currentPage.onHide = this.originalOnHide;
      this.originalOnHide = null;
    }

    if (this.originalOnUnload) {
      currentPage.onUnload = this.originalOnUnload;
      this.originalOnUnload = null;
    }
  }

  // 移除应用生命周期监听器
  if (this.onAppHideHandler) {
    wx.offAppHide && wx.offAppHide(this.onAppHideHandler);
    this.onAppHideHandler = null;
  }

  if (this.onAppShowHandler) {
    wx.offAppShow && wx.offAppShow(this.onAppShowHandler);
    this.onAppShowHandler = null;
  }

  // 重置导航控制标志
  this.allowNavigation = false;

  console.log('✅ 已恢复所有导航功能和监听器');
};

// 创建单例实例
var examLock = new ExamLock();

module.exports = examLock;
