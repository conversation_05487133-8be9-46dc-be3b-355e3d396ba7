const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()
const mistakesCollection = db.collection('mistakes')

exports.main = async (event, context) => {
  const { mistakeId, updateData } = event
  try {
    await mistakesCollection.doc(mistakeId).update({ data: updateData })
    return { code: 200, message: '更新成功' }
  } catch (e) {
    return { code: 500, message: '更新失败', error: e }
  }
} 