/* 容器样式 */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* 顶部统计样式 */
.stats-bar {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮样式 */
.action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.action-button {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #4CAF50;
  border: none;
}

/* 错题列表样式 */
.mistake-list {
  margin-bottom: 120rpx;
}

.mistake-item {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.word-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.word {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-right: 20rpx;
}

.phonetic {
  font-size: 28rpx;
  color: #666;
}

.word-meaning {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.word-status {
  font-size: 24rpx;
  color: #f44336;
  padding: 6rpx 20rpx;
  background-color: #ffebee;
  border-radius: 20rpx;
  display: inline-block;
}

.word-status.reviewed {
  color: #4CAF50;
  background-color: #e8f5e9;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
}

/* 单词详情弹窗样式 */
.word-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}

.close-button {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.modal-body {
  margin-bottom: 30rpx;
}

.word-info {
  text-align: center;
  margin-bottom: 30rpx;
}

.word {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.phonetic {
  font-size: 32rpx;
  color: #666;
}

.meaning {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 30rpx;
  line-height: 1.5;
}

.example {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
}

.example-text {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.example-translation {
  font-size: 28rpx;
  color: #666;
  display: block;
}

/* 音频播放器样式 */
.audio-player {
  margin: 30rpx 0;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 15rpx;
}

.audio-header {
  margin-bottom: 20rpx;
}

.audio-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.audio-subtitle {
  font-size: 24rpx;
  color: #999;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.play-button {
  width: 80rpx;
  height: 80rpx;
  background-color: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-icon {
  width: 40rpx;
  height: 40rpx;
}

.audio-slider {
  flex: 1;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
}

.modal-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
  color: #fff;
  border: none;
}

.modal-button.review {
  background-color: #2196F3;
}

.modal-button.remove {
  background-color: #f44336;
} 