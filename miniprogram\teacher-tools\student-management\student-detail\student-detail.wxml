<view class="container">
  <!-- 学生基本信息 -->
  <view class="student-info-card" wx:if="{{student}}">
    <view class="student-header">
      <view class="student-avatar">{{student.name.charAt(0)}}</view>
      <view class="student-basic">
        <view class="student-name">{{student.name}}</view>
        <view class="student-details">
          <text class="detail-item" wx:if="{{student.grade || student.class}}">
            班级: {{student.grade}}{{student.class}}
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 统计数据卡片 -->
  <view class="stats-grid" wx:if="{{student}}">
    <view class="stat-card">
      <view class="stat-number">{{student.stats.totalExams}}</view>
      <view class="stat-label">考试次数</view>
    </view>
    <view class="stat-card">
      <view class="stat-number">{{student.stats.avgScore}}</view>
      <view class="stat-label">平均分</view>
    </view>
    <view class="stat-card">
      <view class="stat-number">{{student.stats.bestScore}}</view>
      <view class="stat-label">最高分</view>
    </view>
    <view class="stat-card">
      <view class="stat-number {{student.stats.improvement >= 0 ? 'positive' : 'negative'}}">
        {{student.stats.improvement >= 0 ? '+' : ''}}{{student.stats.improvement}}
      </view>
      <view class="stat-label">总进步</view>
    </view>
  </view>

  <!-- 分数曲线图 -->
  <view class="chart-card" wx:if="{{student && student.scores.length > 0}}">
    <view class="card-header">
      <view class="card-title">分数趋势图</view>
      <view class="trend-indicator">
        <text class="trend-icon {{student.stats.trend}}">
          {{student.stats.trend === 'up' ? '📈' : student.stats.trend === 'down' ? '📉' : '➖'}}
        </text>
        <text class="trend-text">
          {{student.stats.trend === 'up' ? '上升趋势' : student.stats.trend === 'down' ? '下降趋势' : '稳定'}}
        </text>
      </view>
    </view>
    <view class="chart-container">
      <canvas 
        canvas-id="scoreChart" 
        id="scoreChart"
        class="score-chart"
        disable-scroll="true"
      ></canvas>
    </view>
  </view>

  <!-- 成绩列表 -->
  <view class="scores-card" wx:if="{{student}}">
    <view class="card-header">
      <view class="card-title">考试成绩</view>
      <button class="add-score-btn" bindtap="showAddScoreDialog">+ 添加</button>
    </view>
    
    <view class="scores-list" wx:if="{{student.scores.length > 0}}">
      <view class="score-item" wx:for="{{student.scores}}" wx:key="_id">
        <view class="score-header">
          <view class="score-info">
            <view class="exam-name">
              <text wx:if="{{item.grade && item.semester}}">{{item.grade}}{{item.semester}}</text>{{item.examName}}
            </view>
            <view class="exam-details">
              <text class="exam-date">{{item.examDate}}</text>
              <text class="score-ratio">{{item.score}}/{{item.totalScore}}</text>
            </view>
          </view>
          <view class="score-display">
            <text class="score-value">{{item.score}}</text>
          </view>
        </view>
        <view class="score-actions">
          <button
            class="action-btn edit"
            data-score="{{item}}"
            bindtap="showEditScoreDialog"
          >编辑</button>
          <button
            class="action-btn delete"
            data-score="{{item}}"
            bindtap="deleteScore"
          >删除</button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-scores" wx:if="{{student.scores.length === 0}}">
      <view class="empty-icon">📊</view>
      <view class="empty-text">暂无考试成绩</view>
      <view class="empty-desc">点击"添加"按钮录入第一次考试成绩</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>
</view>

<!-- 成绩对话框 -->
<view class="modal-overlay" wx:if="{{showScoreDialog}}" bindtap="onScoreModalOverlayClick">
  <view class="modal-content" catchtap="onScoreModalContentClick">
    <view class="modal-header">
      <view class="modal-title">
        {{editingScore ? '编辑成绩' : '添加成绩'}}
      </view>
      <view class="modal-close" catchtap="hideScoreDialog">×</view>
    </view>

    <view class="modal-body">
      <view class="form-group">
        <view class="form-label">考试名称 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            placeholder="如：期中考试、月考"
            value="{{editingScore ? editScore.examName : newScore.examName}}"
            data-field="examName"
            bindinput="onScoreInput"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">得分 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            type="digit"
            placeholder="请输入得分"
            value="{{editingScore ? editScore.score : newScore.score}}"
            data-field="score"
            bindinput="onScoreInput"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">总分 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            type="digit"
            placeholder="请输入总分"
            value="{{editingScore ? editScore.totalScore : newScore.totalScore}}"
            data-field="totalScore"
            bindinput="onScoreInput"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">年级 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            placeholder="如：高一、高二、高三"
            value="{{editingScore ? editScore.grade : newScore.grade}}"
            data-field="grade"
            bindinput="onScoreInput"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">学期 *</view>
        <view class="input-container" catchtap="">
          <input
            class="form-input"
            placeholder="如：上学期、下学期"
            value="{{editingScore ? editScore.semester : newScore.semester}}"
            data-field="semester"
            bindinput="onScoreInput"
          />
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">考试日期 *</view>
        <view class="picker-container" catchtap="">
          <picker
            mode="date"
            value="{{editingScore ? editScore.examDate : newScore.examDate}}"
            data-field="examDate"
            bindchange="onScoreInput"
          >
            <view class="form-input picker">
              {{editingScore ? editScore.examDate : newScore.examDate || '选择日期'}}
            </view>
          </picker>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <button class="modal-btn cancel" catchtap="hideScoreDialog">取消</button>
      <button class="modal-btn confirm" catchtap="saveScore">保存</button>
    </view>
  </view>
</view>
