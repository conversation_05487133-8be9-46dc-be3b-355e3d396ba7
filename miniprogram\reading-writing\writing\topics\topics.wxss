/* pages/writing/topics/topics.wxss */
.container {
  padding: 0 24rpx 24rpx;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

.page-header {
  padding: 32rpx 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-content {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1e293b;
  display: block;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 26rpx;
  color: #64748b;
  display: block;
}

.header-decoration {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}

.decoration-icon {
  font-size: 32rpx;
}

.topics-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.topic-card {
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow:
    0 2rpx 12rpx rgba(0, 0, 0, 0.08),
    0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
  min-height: 80rpx;
}

.topic-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  pointer-events: none;
}

.topic-card:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow:
    0 8rpx 30rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.15);
}

.topic-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.topic-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.icon-text {
  font-size: 28rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.2));
}

.topic-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.topic-title {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  display: block;
  margin-bottom: 4rpx;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.4);
  line-height: 1.2;
}

.topic-subtitle {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  line-height: 1.3;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
}

.topic-arrow {
  position: relative;
  z-index: 1;
  width: 36rpx;
  height: 36rpx;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.arrow-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.bottom-notice {
  margin-top: 40rpx;
  padding: 24rpx;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 193, 7, 0.2);
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.notice-icon {
  font-size: 28rpx;
  margin-top: 2rpx;
}

.notice-text {
  font-size: 24rpx;
  color: #8B5A00;
  line-height: 1.5;
  flex: 1;
}
