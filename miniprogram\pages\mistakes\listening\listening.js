const app = getApp();

Page({
  data: {
    filterType: 'time', // 筛选类型：time-时间，source-来源
    sortOrder: 'desc', // 排序方式：desc-降序，asc-升序
    mistakes: [], // 错题列表
    loading: false,
    selectedMistakes: [], // 选中的错题ID列表
    editMode: false, // 是否为编辑模式
    showBatchActions: false, // 是否显示批量操作
    showAddDialog: false, // 是否显示添加错题对话框
    newMistakeForm: { // 新错题表单
      word: '',
      meaning: '',
      phonetic: '',
      audioUrl: '',
      example: ''
    }
  },

  onLoad() {
    this.loadMistakes();
  },

  onShow() {
    // 每次显示页面时刷新列表
    this.loadMistakes();
  },

  // 加载错题列表
  async loadMistakes() {
    this.setData({ loading: true });
    const db = wx.cloud.database();
    const _ = db.command;
    const openid = wx.getStorageSync('openid');

    try {
      let query = db.collection('mistake_listening')
        .where({ openid });

      // 根据筛选类型和排序方式查询
      if (this.data.filterType === 'time') {
        query = query.orderBy('createTime', this.data.sortOrder);
      } else {
        query = query.orderBy('sourceName', this.data.sortOrder);
      }

      const res = await query.get();
      
      // 格式化时间并添加选中状态
      const mistakes = res.data.map(item => ({
        ...item,
        selected: false,
        createTime: this.formatTime(item.createTime)
      }));

      this.setData({
        mistakes,
        loading: false
      });
    } catch (error) {
      console.error('加载错题失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  },

  // 切换筛选类型
  onFilterTap(e) {
    const type = e.currentTarget.dataset.type;
    if (type === this.data.filterType) {
      // 切换排序方式
      this.setData({
        sortOrder: this.data.sortOrder === 'desc' ? 'asc' : 'desc'
      });
    } else {
      // 切换筛选类型
      this.setData({
        filterType: type,
        sortOrder: 'desc'
      });
    }
    this.loadMistakes();
  },

  // 切换编辑模式
  toggleEditMode() {
    const editMode = !this.data.editMode;
    this.setData({
      editMode: editMode,
      selectedMistakes: editMode ? [] : this.data.selectedMistakes,
      showBatchActions: false
    });
    
    // 重置所有错题的选中状态
    if (!editMode) {
      const mistakes = this.data.mistakes.map(mistake => ({
        ...mistake,
        selected: false
      }));
      this.setData({ mistakes });
    }
  },

  // 选择错题
  onMistakeSelect(e) {
    if (!this.data.editMode) return;
    
    const index = e.currentTarget.dataset.index;
    const mistakes = [...this.data.mistakes];
    mistakes[index].selected = !mistakes[index].selected;
    
    const selectedMistakes = mistakes.filter(mistake => mistake.selected).map(mistake => mistake._id);
    
    this.setData({
      mistakes: mistakes,
      selectedMistakes: selectedMistakes,
      showBatchActions: selectedMistakes.length > 0
    });
  },

  // 全选/取消全选
  toggleSelectAll() {
    const allSelected = this.data.selectedMistakes.length === this.data.mistakes.length;
    const mistakes = this.data.mistakes.map(mistake => ({
      ...mistake,
      selected: !allSelected
    }));
    
    const selectedMistakes = allSelected ? [] : mistakes.map(mistake => mistake._id);
    
    this.setData({
      mistakes: mistakes,
      selectedMistakes: selectedMistakes,
      showBatchActions: selectedMistakes.length > 0
    });
  },

  // 批量删除
  onBatchDelete() {
    if (this.data.selectedMistakes.length === 0) return;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${this.data.selectedMistakes.length} 个错题吗？`,
      success: (res) => {
        if (res.confirm) {
          this.deleteSelectedMistakes();
        }
      }
    });
  },

  // 删除选中的错题
  deleteSelectedMistakes() {
    const db = wx.cloud.database();
    const deletePromises = this.data.selectedMistakes.map(mistakeId => {
      return db.collection('mistake_listening').doc(mistakeId).remove();
    });
    
    Promise.all(deletePromises).then(() => {
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
      this.setData({
        selectedMistakes: [],
        showBatchActions: false,
        editMode: false
      });
      this.loadMistakes();
    }).catch(error => {
      console.error('批量删除失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    });
  },

  // 显示添加错题对话框
  showAddMistakeDialog() {
    this.setData({
      showAddDialog: true,
      newMistakeForm: {
        word: '',
        meaning: '',
        phonetic: '',
        audioUrl: '',
        example: ''
      }
    });
  },

  // 隐藏添加错题对话框
  hideAddMistakeDialog() {
    this.setData({
      showAddDialog: false
    });
  },

  // 表单输入
  onFormInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`newMistakeForm.${field}`]: value
    });
  },

  // 手动添加错题
  onAddMistake() {
    const { word, meaning, phonetic, audioUrl, example } = this.data.newMistakeForm;
    
    if (!word.trim()) {
      wx.showToast({
        title: '请输入单词',
        icon: 'none'
      });
      return;
    }
    
    if (!meaning.trim()) {
      wx.showToast({
        title: '请输入中文含义',
        icon: 'none'
      });
      return;
    }
    
    // 调用云函数添加错题
    wx.cloud.callFunction({
      name: 'addMistake',
      data: {
        userId: app.globalData.openid,
        wordId: word.trim(),
        type: 'listening',
        extra: {
          word: word.trim(),
          meaning: meaning.trim(),
          phonetic: phonetic.trim(),
          audioUrl: audioUrl.trim(),
          example: example.trim(),
          mistakeType: 'manual', // 标识为手动添加
          source: '手动添加',
          createTime: new Date()
        }
      }
    }).then(result => {
      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });
      this.hideAddMistakeDialog();
      this.loadMistakes();
    }).catch(error => {
      console.error('添加错题失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    });
  },

  // 开始复习
  onReviewTap() {
    if (this.data.mistakes.length === 0) {
      wx.showToast({
        title: '暂无错题',
        icon: 'none'
      });
      return;
    }

    // 将错词数据转换为听写练习需要的格式
    const words = this.data.mistakes.map(mistake => ({
      _id: mistake._id,
      words: mistake.word,
      phonetic: mistake.phonetic || '',
      meaning: mistake.meaning || '',
      example: mistake.example || ''
    }));

    // 将错词数据存储到全局，供听写练习页面使用
    const app = getApp();
    app.globalData.learningData = {
      words: words,
      testMode: 'dictation',
      libraryName: '听写错词本',
      isFromMistakes: true
    };

    // 跳转到听写练习模式选择页面
    wx.navigateTo({
      url: `/pages/wordtest/mode-select/mode-select?testMode=dictation&total=${words.length}&fromMistakes=true`
    });
  },

  // 标记为已掌握
  async onMasteredTap(e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '提示',
      content: '确定要将该错题标记为已掌握吗？',
      success: async (res) => {
        if (res.confirm) {
          const db = wx.cloud.database();
          try {
            await db.collection('mistake_listening').doc(id).remove();
            wx.showToast({
              title: '已标记为掌握',
              icon: 'success'
            });
            this.loadMistakes();
          } catch (error) {
            console.error('标记失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 复习选中的错题
  onReviewSelectedTap() {
    const selectedMistakes = this.data.selectedMistakes;

    if (selectedMistakes.length === 0) {
      wx.showToast({
        title: '请先选择要复习的错题',
        icon: 'none'
      });
      return;
    }

    // 获取选中错题的完整数据
    const allMistakes = this.data.mistakes;
    const selectedMistakesData = allMistakes.filter(mistake =>
      selectedMistakes.includes(mistake._id)
    );

    if (selectedMistakesData.length === 0) {
      wx.showToast({
        title: '选中的错题数据异常',
        icon: 'none'
      });
      return;
    }

    // 将错题数据转换为听写练习需要的格式
    const words = selectedMistakesData.map(mistake => ({
      _id: mistake._id,
      words: mistake.word,
      phonetic: mistake.phonetic || '',
      meaning: mistake.meaning || '',
      example: mistake.example || ''
    }));

    // 将错题数据存储到全局，供听写练习页面使用
    const app = getApp();
    app.globalData.learningData = {
      words: words,
      testMode: 'dictation',
      libraryName: '听写错词本',
      isFromMistakes: true
    };

    // 跳转到听写练习模式选择页面
    wx.navigateTo({
      url: `/pages/wordtest/mode-select/mode-select?testMode=dictation&total=${words.length}&fromMistakes=true`
    });
  },

  // 点击错题项
  onMistakeTap(e) {
    const id = e.currentTarget.dataset.id;
    // 跳转到错题详情页面
    wx.navigateTo({
      url: `/pages/mistakes/listening/detail/detail?id=${id}`
    });
  }
}); 