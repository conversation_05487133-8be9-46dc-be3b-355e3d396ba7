const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()
const mistakesCollection = db.collection('mistakes')

exports.main = async (event, context) => {
  const { userId, wordId, type, extra } = event
  try {
    const now = new Date()
    await mistakesCollection.add({
      data: {
        _openid: userId, // 统一使用_openid字段
        wordId,
        type,
        createTime: now,
        reviewed: false,
        ...extra
      }
    })
    return { code: 200, message: '添加成功' }
  } catch (e) {
    return { code: 500, message: '添加失败', error: e }
  }
}