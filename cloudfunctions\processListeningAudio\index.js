const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const { audioFileID, wordFileID, questionType, parameters } = event;

  try {
    console.log('开始处理听口音频:', { audioFileID, wordFileID, questionType });

    // 1. 下载音频文件
    const audioResult = await cloud.downloadFile({
      fileID: audioFileID
    });

    // 2. 下载Word文档
    const wordResult = await cloud.downloadFile({
      fileID: wordFileID
    });

    // 3. 模拟音频分割处理（实际项目中需要使用音频处理库）
    const audioSegments = await simulateAudioSegmentation(audioResult.buffer, questionType, parameters);

    // 4. 模拟Word文档解析（实际项目中需要使用文档解析库）
    const questionData = await simulateWordDocumentParsing(wordResult.buffer, questionType);

    // 5. 上传分割后的音频片段到云存储
    const uploadedSegments = await uploadAudioSegments(audioSegments, openid);

    // 6. 清理临时文件
    try {
      await cloud.deleteFile({
        fileList: [audioFileID, wordFileID]
      });
    } catch (cleanupError) {
      console.warn('清理临时文件失败:', cleanupError);
    }

    return {
      success: true,
      data: {
        audioSegments: uploadedSegments,
        questionData: questionData
      },
      message: '音频处理完成'
    };

  } catch (error) {
    console.error('处理听口音频失败:', error);
    return {
      success: false,
      message: '音频处理失败: ' + error.message,
      error: error.message
    };
  }
};

// 模拟音频分割（实际项目中需要使用专业音频处理库）
async function simulateAudioSegmentation(audioBuffer, questionType, parameters) {
  console.log('模拟音频分割，参数:', parameters);
  
  // 根据题型确定分割数量
  let segmentCount = 1;
  switch (questionType) {
    case 'listen_choose':
      segmentCount = 14; // 听后选择14道小题
      break;
    case 'listen_record':
      segmentCount = 3; // 听后记录3遍播放
      break;
    case 'read_answer':
      segmentCount = 3; // 朗读文本 + 2个问题
      break;
  }

  const segments = [];
  const totalDuration = 300; // 假设总时长5分钟
  const segmentDuration = Math.floor(totalDuration / segmentCount);

  for (let i = 0; i < segmentCount; i++) {
    segments.push({
      index: i,
      startTime: i * segmentDuration,
      endTime: (i + 1) * segmentDuration,
      duration: segmentDuration,
      size: Math.floor(audioBuffer.length / segmentCount), // 模拟文件大小
      buffer: audioBuffer.slice(
        Math.floor(i * audioBuffer.length / segmentCount),
        Math.floor((i + 1) * audioBuffer.length / segmentCount)
      )
    });
  }

  return segments;
}

// 模拟Word文档解析（实际项目中需要使用文档解析库如mammoth）
async function simulateWordDocumentParsing(wordBuffer, questionType) {
  console.log('模拟Word文档解析，题型:', questionType);
  
  const questions = [];
  
  switch (questionType) {
    case 'listen_choose':
      // 生成14道听后选择题
      for (let i = 1; i <= 14; i++) {
        questions.push({
          id: i,
          type: 'listen_choose',
          question: `听力选择题第${i}题`,
          options: [
            { key: 'A', text: `选项A${i}` },
            { key: 'B', text: `选项B${i}` },
            { key: 'C', text: `选项C${i}` }
          ],
          correctAnswer: ['A', 'B', 'C'][Math.floor(Math.random() * 3)],
          audioSegmentIndex: i - 1
        });
      }
      break;
      
    case 'listen_record':
      questions.push({
        id: 1,
        type: 'listen_record',
        title: '听后记录与转述',
        content: '关于笔记记录的重要性...',
        blanks: [
          { index: 1, answer: 'lecture' },
          { index: 2, answer: 'organized' },
          { index: 3, answer: 'abbreviations' },
          { index: 4, answer: 'system' }
        ],
        audioSegmentIndex: 0
      });
      break;
      
    case 'read_answer':
      questions.push({
        id: 1,
        type: 'read_answer',
        title: '朗读短文并回答问题',
        passage: '这是一篇需要朗读的英语短文...',
        questions: [
          {
            id: 1,
            question: '根据短文内容，回答问题1',
            audioSegmentIndex: 1
          },
          {
            id: 2,
            question: '根据短文内容，回答问题2',
            audioSegmentIndex: 2
          }
        ],
        audioSegmentIndex: 0 // 朗读部分
      });
      break;
  }

  return questions;
}

// 上传音频片段到云存储
async function uploadAudioSegments(segments, openid) {
  const uploadedSegments = [];
  
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];
    
    try {
      // 生成唯一文件名，使用公有读目录
      const fileName = `public/listening_audio/${openid}/${Date.now()}_segment_${i}.mp3`;

      // 上传音频片段（这里需要将buffer转换为可上传的格式）
      const uploadResult = await cloud.uploadFile({
        cloudPath: fileName,
        fileContent: segment.buffer
      });

      // 获取永久有效的下载链接（公有读文件不会过期）
      const urlResult = await cloud.getTempFileURL({
        fileList: [uploadResult.fileID] // 公有读文件不需要maxAge参数
      });
      
      uploadedSegments.push({
        index: segment.index,
        fileID: uploadResult.fileID,
        audioUrl: urlResult.fileList[0].tempFileURL,
        startTime: segment.startTime,
        endTime: segment.endTime,
        duration: segment.duration,
        size: segment.size,
        sizeText: (segment.size / 1024).toFixed(1) + 'KB'
      });
      
    } catch (uploadError) {
      console.error(`上传音频片段${i}失败:`, uploadError);
      // 继续处理其他片段
    }
  }
  
  return uploadedSegments;
}
