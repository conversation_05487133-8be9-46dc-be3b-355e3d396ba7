<view class="container">
  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-header">
      <text class="title">输入文本</text>
      <text class="subtitle">请输入需要检测的英文文本</text>
    </view>
    <textarea 
      class="input-area" 
      placeholder="请输入英文文本..." 
      bindinput="onInputChange"
      value="{{inputText}}"
      maxlength="1000"
    />
    <view class="word-count">{{inputText.length}}/1000</view>
  </view>

  <!-- 检测结果 -->
  <view class="result-section" wx:if="{{showResult}}">
    <view class="result-header">
      <text class="title">检测结果</text>
      <text class="subtitle">共发现 {{words.length}} 个单词</text>
    </view>
    
    <!-- 单词列表 -->
    <view class="word-list">
      <view class="word-item" wx:for="{{words}}" wx:key="index">
        <view class="word-info">
          <text class="word">{{item.word}}</text>
          <text class="phonetic" wx:if="{{item.phonetic}}">[{{item.phonetic}}]</text>
        </view>
        <view class="word-detail">
          <text class="definition">{{item.definition}}</text>
          <view class="example" wx:if="{{item.example}}">
            <text class="example-text">{{item.example}}</text>
          </view>
        </view>
        <view class="word-actions">
          <view class="action-btn" bindtap="playAudio" data-word="{{item}}">
            <image class="action-icon" src="/images/audio.png" />
          </view>
          <view class="action-btn" bindtap="showMnemonic" data-word="{{item}}">
            <image class="action-icon" src="/images/mnemonic.png" />
          </view>
          <view class="action-btn" bindtap="addToLibrary" data-word="{{item}}">
            <image class="action-icon" src="/images/add.png" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-buttons">
    <button 
      class="primary-btn" 
      bindtap="onStartDetection"
      disabled="{{!inputText}}"
    >开始检测</button>
    <button 
      class="secondary-btn" 
      bindtap="onStartPractice"
      disabled="{{!showResult || words.length === 0}}"
    >开始练习</button>
  </view>

  <!-- 记忆提示弹窗 -->
  <view class="mnemonic-modal" wx:if="{{showMnemonicModal}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">记忆提示</text>
        <view class="close-btn" bindtap="hideMnemonic">×</view>
      </view>
      <view class="modal-body">
        <text class="mnemonic-text">{{currentWord.mnemonic}}</text>
      </view>
    </view>
  </view>
</view> 