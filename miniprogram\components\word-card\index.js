const app = getApp()

Component({
  properties: {
    word: {
      type: Object,
      value: null
    },
    showPhonetic: {
      type: Boolean,
      value: true
    },
    showExample: {
      type: Boolean,
      value: true
    },
    showActions: {
      type: Boolean,
      value: true
    }
  },

  data: {
    isPlaying: false
  },

  methods: {
    // 播放发音
    playAudio() {
      if (!this.data.word || !this.data.word.audioUrl) return;
      if (this.data.isPlaying) return;
      this.setData({ isPlaying: true });
      const innerAudioContext = wx.createInnerAudioContext();
      innerAudioContext.src = this.data.word.audioUrl;
      innerAudioContext.onEnded(() => {
        this.setData({ isPlaying: false });
        innerAudioContext.destroy();
      });
      innerAudioContext.onError(() => {
        this.setData({ isPlaying: false });
        wx.showToast({ title: '播放失败', icon: 'none' });
        innerAudioContext.destroy();
      });
      innerAudioContext.play();
    },

    // 添加到错题本
    addToMistakes() {
      this.triggerEvent('addmistake', { word: this.data.word });
    }
  }
}) 