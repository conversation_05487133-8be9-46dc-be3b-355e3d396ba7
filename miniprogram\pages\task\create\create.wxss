.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 任务名称输入框 */
.task-name-input {
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

/* 模式选择 */
.mode-list {
  display: flex;
  gap: 20rpx;
}

.mode-item {
  flex: 1;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
}

.mode-item.active {
  background-color: #4A90E2;
}

.mode-item.active .mode-name,
.mode-item.active .mode-desc {
  color: #fff;
}

.mode-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.mode-desc {
  font-size: 24rpx;
  color: #666;
}

/* 测试类型 */
.test-type-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.test-type-item {
  flex: 1;
  min-width: 200rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
}

.test-type-item.active {
  background-color: #4A90E2;
}

.test-type-item.active .test-type-name {
  color: #fff;
}

.test-type-name {
  font-size: 28rpx;
  color: #333;
}

/* 挑战设置 */
.challenge-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.challenge-item {
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
}

.challenge-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.challenge-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.challenge-option {
  flex: 1;
  min-width: 150rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
}

.challenge-option.active {
  background-color: #4A90E2;
  color: #fff;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  color: #fff;
  border: none;
}

.btn-primary {
  background-color: #4A90E2;
}

.btn-share {
  background-color: #50E3C2;
} 