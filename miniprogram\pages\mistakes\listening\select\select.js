Page({
  data: {
    mistakes: [], // 错题列表
    selected: [], // 选中的错题id数组
    selectedCount: 0, // 选中的错题数量
    allSelected: false, // 是否全选
    loading: false
  },

  onLoad() {
    this.loadMistakes();
  },

  // 加载错题列表
  async loadMistakes() {
    this.setData({ loading: true });
    const db = wx.cloud.database();
    const openid = wx.getStorageSync('openid');

    try {
      const res = await db.collection('mistake_listening')
        .where({ openid })
        .orderBy('createTime', 'desc')
        .get();

      // 格式化时间并添加选中状态
      const mistakes = res.data.map(item => ({
        ...item,
        checked: false,
        createTime: this.formatTime(item.createTime)
      }));

      this.setData({
        mistakes,
        loading: false
      });
    } catch (error) {
      console.error('加载错题失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 格式化时间
  formatTime(date) {
    date = new Date(date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}-${month}-${day}`;
  },

  // 全选
  onSelectAll() {
    const { mistakes, allSelected } = this.data;
    if (allSelected) {
      // 取消全选
      mistakes.forEach(item => item.checked = false);
      this.setData({
        mistakes,
        selected: [],
        selectedCount: 0,
        allSelected: false
      });
    } else {
      // 全选
      const selected = mistakes.map(item => item._id);
      mistakes.forEach(item => item.checked = true);
      this.setData({
        mistakes,
        selected,
        selectedCount: selected.length,
        allSelected: true
      });
    }
  },

  // 反选
  onInvertSelection() {
    const { mistakes } = this.data;
    const selected = [];
    mistakes.forEach(item => {
      item.checked = !item.checked;
      if (item.checked) {
        selected.push(item._id);
      }
    });
    this.setData({
      mistakes,
      selected,
      selectedCount: selected.length,
      allSelected: selected.length === mistakes.length && mistakes.length > 0
    });
  },

  // 点击错题项
  onMistakeTap(e) {
    const { index } = e.currentTarget.dataset;
    const { mistakes, selected } = this.data;
    const mistake = mistakes[index];
    
    mistake.checked = !mistake.checked;
    let newSelected = [];
    if (mistake.checked) {
      newSelected = [...selected, mistake._id];
    } else {
      newSelected = selected.filter(id => id !== mistake._id);
    }

    this.setData({
      mistakes,
      selected: newSelected,
      selectedCount: newSelected.length,
      allSelected: newSelected.length === mistakes.length && mistakes.length > 0
    });
  },

  // 开始复习
  onReviewTap() {
    const { selected } = this.data;
    if (selected.length === 0) {
      wx.showToast({
        title: '请先选择要复习的错题',
        icon: 'none'
      });
      return;
    }

    // 跳转到听口复习页面
    wx.navigateTo({
      url: `/pages/review/listening/listening?ids=${selected.join(',')}`
    });
  }
}); 