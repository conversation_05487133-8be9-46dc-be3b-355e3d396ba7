/* 首页样式 - 扁平风格设计 */
.luxury-container {
  height: 100vh;
  background: #f8fafc;
  padding-bottom: calc(60rpx + env(safe-area-inset-bottom));
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部Banner - 高档立体设计 */
.luxury-header {
  position: relative;
  margin: 28rpx 32rpx 0;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #4f46e5 100%);
  flex: 0 0 300rpx;
  display: flex;
  align-items: center;
  overflow: hidden;
  box-shadow:
    0 16rpx 48rpx rgba(102, 126, 234, 0.25),
    0 8rpx 24rpx rgba(118, 75, 162, 0.2),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.2),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.1);
}

.luxury-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 60%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 60%),
    linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.05) 50%, transparent 60%);
  pointer-events: none;
}

.luxury-header::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.08) 50%, transparent 70%);
  transform: rotate(45deg);
  animation: headerShine 6s ease-in-out infinite;
  pointer-events: none;
}

@keyframes headerShine {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

.header-content {
  width: 100%;
  padding: 0 40rpx;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.brand-section {
  display: flex;
  align-items: center;
  width: 100%;
}

.brand-logo {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  padding: 20rpx;
  border-radius: 16rpx;
  margin-right: 28rpx;
  box-shadow:
    0 8rpx 24rpx rgba(0, 0, 0, 0.12),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.25),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.logo-image {
  width: 64rpx;
  height: 64rpx;
  border-radius: 12rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.2));
}

.brand-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.app-title {
  font-size: 44rpx;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 1rpx;
  margin-bottom: 4rpx;
}

.app-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.85);
  font-weight: 400;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}



@keyframes headerShine {
  0%, 100% { transform: rotate(45deg) translateX(-150%); opacity: 0; }
  50% { transform: rotate(45deg) translateX(150%); opacity: 1; }
}

.header-glow {
  display: none;
}

.header-content {
  padding: 0 40rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
}

.brand-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.brand-logo {
  position: relative;
  margin-right: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 18rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(15rpx);
  box-shadow: 
    0 8rpx 24rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.brand-logo::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, transparent 60%);
  border-radius: 18rpx;
  pointer-events: none;
}

.logo-image {
  width: 68rpx;
  height: 68rpx;
  border-radius: 12rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.2));
  position: relative;
  z-index: 2;
}

.logo-shine {
  display: none;
}

.brand-info {
  display: flex;
  flex-direction: column;
}

.app-title {
  color: white;
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  letter-spacing: 2rpx;
  text-shadow: 
    0 2rpx 4rpx rgba(0, 0, 0, 0.3),
    0 4rpx 8rpx rgba(0, 0, 0, 0.2),
    0 0 20rpx rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 2;
}

.app-title::after {
  content: '';
  position: absolute;
  bottom: -6rpx;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 20%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0.4) 80%, transparent 100%);
  border-radius: 1rpx;
  box-shadow: 0 0 8rpx rgba(255, 255, 255, 0.3);
}

.app-subtitle {
  color: rgba(255, 255, 255, 0.95);
  font-size: 28rpx;
  font-weight: 500;
  text-shadow: 
    0 2rpx 4rpx rgba(0, 0, 0, 0.25),
    0 0 15rpx rgba(255, 255, 255, 0.08);
  letter-spacing: 1rpx;
  position: relative;
  z-index: 2;
}

/* 功能区域 - 扁平风格 */
.luxury-functions {
  flex: 0 0 680rpx;
  padding: 32rpx 32rpx 0;
  display: flex;
  flex-direction: column;
}

.functions-title {
  text-align: left;
  margin-bottom: 32rpx;
  position: relative;
}

.title-text {
  color: #1e293b;
  font-size: 40rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.title-underline {
  position: absolute;
  bottom: -12rpx;
  left: 0;
  width: 80rpx;
  height: 4rpx;
  background: #4f46e5;
  border-radius: 2rpx;
}

.function-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(2, 200rpx);
  gap: 24rpx;
  flex: 1;
}

/* 美化卡片设计 */
.luxury-card {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2rpx solid transparent;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.luxury-card:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.luxury-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

/* 卡片背景装饰 */
.card-bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* 卡片光效 */
.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: rotate(45deg);
  animation: cardGlow 4s ease-in-out infinite;
  pointer-events: none;
}

/* 为不同卡片添加延迟，创造错落效果 */
.card-gradient-blue .card-glow { animation-delay: 0s; }
.card-gradient-green .card-glow { animation-delay: 0.5s; }
.card-gradient-purple .card-glow { animation-delay: 1s; }
.card-gradient-red .card-glow { animation-delay: 1.5s; }
.card-gradient-teal .card-glow { animation-delay: 2s; }
.card-gradient-orange .card-glow { animation-delay: 2.5s; }

.card-gradient-blue .card-icon { animation-delay: 0s; }
.card-gradient-green .card-icon { animation-delay: 0.3s; }
.card-gradient-purple .card-icon { animation-delay: 0.6s; }
.card-gradient-red .card-icon { animation-delay: 0.9s; }
.card-gradient-teal .card-icon { animation-delay: 1.2s; }
.card-gradient-orange .card-icon { animation-delay: 1.5s; }

@keyframes cardGlow {
  0%, 100% { transform: rotate(45deg) translateX(-120%); opacity: 0; }
  50% { transform: rotate(45deg) translateX(120%); opacity: 0.8; }
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-3rpx) scale(1.08); }
}

.card-sparkle {
  display: none;
}

.icon-shine {
  display: block;
}

/* 卡片内容 - 横向布局 */
.card-inner {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  z-index: 2;
  padding: 28rpx 32rpx;
  min-height: 200rpx;
  box-sizing: border-box;
}

.card-icon-container {
  position: relative;
  margin-right: 24rpx;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 12rpx;
  padding: 20rpx;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  box-shadow: 
    0 4rpx 12rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  overflow: hidden;
}

.card-icon-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.card-icon {
  font-size: 48rpx;
  display: block;
  line-height: 1;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
  animation: iconFloat 3.5s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-2rpx) scale(1.05); }
}

.card-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex: 1;
  height: 100%;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
  line-height: 1.3;
  letter-spacing: 0.5rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.card-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  font-weight: 400;
  letter-spacing: 0.3rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 美化卡片配色 - 渐变背景 */
.card-gradient-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.card-gradient-green {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-gradient-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.card-gradient-red {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.card-gradient-teal {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.card-gradient-orange {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}















/* 隐藏旧的框内励志语句样式 */
.motto-section-inside {
  display: none;
}

/* 隐藏复杂的统计信息和头像相关样式 */
.user-details,
.user-name,
.user-badges,
.stats-container,
.motto-icon,
.user-avatar-section,
.avatar-container,
.user-avatar,
.avatar-ring,
.avatar-glow {
  display: none;
}

.motto-decoration {
  display: none;
}

/* 去除所有动画 */
@keyframes gradientShift,
@keyframes glowRotate,
@keyframes shine,
@keyframes ringRotate,
@keyframes pulse,
@keyframes iconShine,
@keyframes sparkle,
@keyframes panelShimmer,
@keyframes twinkle,
@keyframes cardFloat {
  to { opacity: 1; transform: none; }
}

.luxury-card {
  opacity: 1;
  transform: none;
  animation: none;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .luxury-header {
    margin: 24rpx 28rpx 0;
    flex: 0 0 260rpx;
    border-radius: 18rpx;
    box-shadow:
      0 12rpx 36rpx rgba(102, 126, 234, 0.2),
      0 6rpx 18rpx rgba(118, 75, 162, 0.15),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.15),
      inset 0 -1rpx 0 rgba(0, 0, 0, 0.08);
  }

  
  .header-content {
    padding: 0 32rpx;
  }
  
  .brand-logo {
    padding: 20rpx;
    margin-right: 28rpx;
    border-radius: 16rpx;
    box-shadow: 
      0 6rpx 18rpx rgba(0, 0, 0, 0.12),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.25),
      inset 0 -1rpx 0 rgba(0, 0, 0, 0.08);
  }
  
  .logo-image {
    width: 58rpx;
    height: 58rpx;
    border-radius: 10rpx;
    filter: drop-shadow(0 3rpx 6rpx rgba(0, 0, 0, 0.18));
  }
  
  .app-title {
    font-size: 40rpx;
    margin-bottom: 10rpx;
  }
  
  .app-subtitle {
    font-size: 24rpx;
  }
  
  .luxury-functions {
    flex: 0 0 620rpx;
    padding: 28rpx 28rpx 0;
  }
  
  .functions-title {
    margin-bottom: 28rpx;
  }
  
  .title-text {
    font-size: 34rpx;
  }
  
  .function-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: repeat(2, 185rpx);
    gap: 20rpx;
  }
  
  .card-inner {
    padding: 24rpx 28rpx;
    min-height: 185rpx;
  }
  
    .card-icon-container {
    margin-right: 20rpx;
    padding: 18rpx;
    width: 72rpx;
    height: 72rpx;
    border-radius: 10rpx;
  }

  .card-icon {
    font-size: 42rpx;
  }

  @keyframes cardGlow {
    0%, 100% { transform: rotate(45deg) translateX(-100%); opacity: 0; }
    50% { transform: rotate(45deg) translateX(80%); opacity: 0.8; }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-2rpx) scale(1.05); }
  }
  
  .card-title {
    font-size: 28rpx;
    margin-bottom: 6rpx;
  }
  
  .card-subtitle {
    font-size: 21rpx;
  }
  


  .notice-text {
    font-size: 22rpx;
  }

  .section-divider {
    margin: 32rpx 40rpx;
  }

  .shortcut-section {
    padding: 18rpx 28rpx 28rpx;
  }
}

@media (max-height: 800px) {
  .luxury-header {
    margin: 20rpx 28rpx 0;
    flex: 0 0 240rpx;
    border-radius: 16rpx;
    box-shadow:
      0 10rpx 30rpx rgba(102, 126, 234, 0.18),
      0 5rpx 15rpx rgba(118, 75, 162, 0.12),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.12),
      inset 0 -1rpx 0 rgba(0, 0, 0, 0.06);
  }

  
  .header-content {
    padding: 0 32rpx;
  }
  
  .brand-logo {
    padding: 18rpx;
    margin-right: 24rpx;
    border-radius: 14rpx;
    box-shadow: 
      0 5rpx 15rpx rgba(0, 0, 0, 0.1),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.2),
      inset 0 -1rpx 0 rgba(0, 0, 0, 0.06);
  }
  
  .logo-image {
    width: 56rpx;
    height: 56rpx;
    border-radius: 10rpx;
    filter: drop-shadow(0 2rpx 5rpx rgba(0, 0, 0, 0.15));
  }
  
  .app-title {
    font-size: 38rpx;
    margin-bottom: 8rpx;
  }
  
  .app-subtitle {
    font-size: 22rpx;
  }
  
  .luxury-functions {
    flex: 0 0 580rpx;
    padding: 24rpx 28rpx 0;
  }
  
  .functions-title {
    margin-bottom: 24rpx;
  }
  
  .title-text {
    font-size: 32rpx;
  }
  
  .function-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: repeat(2, 175rpx);
    gap: 18rpx;
  }
  
  .card-inner {
    padding: 20rpx 24rpx;
    min-height: 175rpx;
  }
  
  .card-icon-container {
    margin-right: 18rpx;
    padding: 16rpx;
    width: 68rpx;
    height: 68rpx;
    border-radius: 10rpx;
  }
  
    .card-icon {
    font-size: 40rpx;
  }

  @keyframes cardGlow {
    0%, 100% { transform: rotate(45deg) translateX(-100%); opacity: 0; }
    50% { transform: rotate(45deg) translateX(70%); opacity: 0.7; }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-2rpx) scale(1.04); }
  }

  .card-title {
    font-size: 26rpx;
    margin-bottom: 6rpx;
  }
  
  .card-subtitle {
    font-size: 19rpx;
  }
  


  .section-divider {
    margin: 28rpx 36rpx;
  }

  .shortcut-section {
    padding: 16rpx 24rpx 24rpx;
  }
}

/* ========== 分割线样式 ========== */
.section-divider {
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(79, 172, 254, 0.3) 20%, rgba(79, 172, 254, 0.6) 50%, rgba(79, 172, 254, 0.3) 80%, transparent 100%);
  margin: 40rpx 48rpx;
  border-radius: 1rpx;
  box-shadow: 0 1rpx 3rpx rgba(79, 172, 254, 0.2);
}

/* ========== 快捷方式区域样式 ========== */
.shortcut-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 28rpx 32rpx;
  flex: 1;
  border-radius: 24rpx 24rpx 0 0;
  margin-top: 8rpx;
}

/* ========== 学习进度卡片样式（完全照搬学习进度页面）========== */
.learning-progress-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  margin-bottom: 20rpx;
}

/* 进度信息（照搬学习进度页面）*/
.learning-progress-card .task-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.learning-progress-card .task-header {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.learning-progress-card .title-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.switch-task-btn {
  background: rgba(255, 255, 255, 0.2);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
  transition: all 0.3s ease;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.switch-task-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.switch-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  line-height: 1;
}

.learning-progress-card .task-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #ffffff;
  flex: 1;
  line-height: 1.3;
  word-break: break-all;
}

.learning-progress-card .task-type {
  background: rgba(255, 255, 255, 0.2);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.learning-progress-card .mode-text {
  line-height: 1;
}

.learning-progress-card .last-study-time {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.learning-progress-card .progress-stats {
  display: flex;
  gap: 24rpx;
}

.learning-progress-card .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.learning-progress-card .stat-number {
  font-size: 24rpx;
  font-weight: bold;
  color: #ffffff;
}

.learning-progress-card .stat-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.learning-progress-card .progress-bar-container {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.learning-progress-card .progress-bar {
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.learning-progress-card .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.learning-progress-card .progress-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* ========== 学习进度操作卡片样式（完全照搬学习进度页面）========== */
.learning-progress-card .action-cards {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  min-width: 200rpx;
}

.learning-progress-card .action-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  cursor: pointer;
}

.learning-progress-card .action-card.primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4rpx 12rpx rgba(79, 172, 254, 0.3);
}

.learning-progress-card .action-card.review {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.3);
}

.learning-progress-card .action-card.review.disabled,
.learning-progress-card .action-card.disabled {
  background: rgba(255, 255, 255, 0.1);
  box-shadow: none;
  opacity: 0.6;
  cursor: not-allowed;
}

.learning-progress-card .action-card.disabled .card-title,
.learning-progress-card .action-card.disabled .card-progress,
.learning-progress-card .action-card.disabled .card-desc {
  color: rgba(255, 255, 255, 0.5);
}

.learning-progress-card .action-card:active:not(.disabled) {
  transform: scale(0.98);
}

.learning-progress-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.learning-progress-card .card-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #ffffff;
}

.learning-progress-card .card-progress {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.learning-progress-card .card-desc {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 设为默认显示按钮 */
.set-default-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 10rpx;
  padding: 6rpx 12rpx;
  margin: 10rpx 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  align-self: flex-start;
  display: flex;
  align-items: center;
  justify-content: center;
}

.set-default-btn:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.96);
}

.set-default-text {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  line-height: 1;
}







/* ========== 内容来源选择样式 ========== */
.source-selector {
  display: flex;
  padding: 12rpx 0 16rpx;
  gap: 8rpx;
  margin-bottom: 20rpx;
}

.source-option {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 12rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 18rpx;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  height: 32rpx;
}

.source-option.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-color: rgba(79, 172, 254, 0.5);
  box-shadow: 0 4rpx 12rpx rgba(79, 172, 254, 0.4);
  transform: translateY(-2rpx);
}

.source-text {
  font-size: 20rpx;
  font-weight: 600;
  letter-spacing: 0.2rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1;
}

.source-option.active .source-text {
  color: #ffffff;
  font-weight: 700;
}

/* ========== 任务列表样式 ========== */
.shortcut-tasks {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 16rpx;
  border: 2rpx dashed rgba(79, 70, 229, 0.2);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  opacity: 0.6;
  background: linear-gradient(135deg, #4f46e5 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-text {
  font-size: 30rpx;
  color: #4a5568;
  margin-bottom: 8rpx;
  font-weight: 600;
}

.empty-hint {
  font-size: 26rpx;
  color: #a0aec0;
  text-align: center;
  line-height: 1.4;
}

/* ========== 设置弹窗样式 ========== */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.settings-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4rpx);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 70vh;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 -8rpx 40rpx rgba(0, 0, 0, 0.15);
}

.settings-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 32rpx;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border-bottom: 1rpx solid rgba(79, 70, 229, 0.1);
}

.modal-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3748;
  letter-spacing: 0.5rpx;
}

.modal-close {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 50%;
  font-size: 32rpx;
  color: #4f46e5;
  font-weight: bold;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(79, 70, 229, 0.2);
}

.modal-close:active {
  background: rgba(79, 70, 229, 0.2);
  transform: scale(0.95);
}

.modal-body {
  padding: 0 40rpx 40rpx;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 28rpx;
  padding: 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid rgba(79, 70, 229, 0.1);
}

.filter-item:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-size: 30rpx;
  color: #2d3748;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}

.filter-selector {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: white;
  border-radius: 12rpx;
  min-width: 240rpx;
  justify-content: space-between;
  border: 1rpx solid rgba(79, 70, 229, 0.2);
  transition: all 0.3s ease;
}

.filter-selector:active {
  background: rgba(79, 70, 229, 0.05);
  border-color: #4f46e5;
}

.selector-text {
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
}

.selector-arrow {
  font-size: 24rpx;
  color: #4f46e5;
  margin-left: 16rpx;
  font-weight: bold;
}

.modal-actions {
  display: flex;
  gap: 24rpx;
  padding-top: 16rpx;
}

.action-btn {
  flex: 1;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20rpx;
  font-size: 30rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  letter-spacing: 0.5rpx;
}

.action-btn.secondary {
  background: rgba(248, 250, 252, 0.8);
  color: #4a5568;
  border: 1rpx solid rgba(79, 70, 229, 0.2);
}

.action-btn.secondary:active {
  background: rgba(79, 70, 229, 0.1);
  transform: scale(0.98);
}

.action-btn.primary {
  background: linear-gradient(135deg, #4f46e5 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(79, 70, 229, 0.3);
}

.action-btn.primary:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(79, 70, 229, 0.4);
}

/* ========== 选择器弹窗样式 ========== */
.selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.selector-modal.show {
  opacity: 1;
  visibility: visible;
}

.selector-modal .modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4rpx);
}

.selector-modal .modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 70vh;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 -8rpx 40rpx rgba(0, 0, 0, 0.15);
}

.selector-modal.show .modal-content {
  transform: translateY(0);
}

.selector-modal .modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 32rpx;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border-bottom: 1rpx solid rgba(79, 70, 229, 0.1);
}

.selector-modal .modal-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3748;
  letter-spacing: 0.5rpx;
}

.selector-modal .modal-close {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 50%;
  font-size: 32rpx;
  color: #4f46e5;
  font-weight: bold;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(79, 70, 229, 0.2);
}

.selector-modal .modal-close:active {
  background: rgba(79, 70, 229, 0.2);
  transform: scale(0.95);
}

.selector-modal .modal-body {
  max-height: 500rpx;
  overflow-y: auto;
  padding: 0 40rpx 40rpx;
}

.mode-option,
.library-option,
.sharer-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 24rpx;
  margin-bottom: 12rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  transition: all 0.3s ease;
  border: 1rpx solid transparent;
}

.mode-option:last-child,
.library-option:last-child,
.sharer-option:last-child {
  margin-bottom: 0;
}

.mode-option.selected,
.library-option.selected,
.sharer-option.selected {
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-color: rgba(79, 70, 229, 0.3);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(79, 70, 229, 0.15);
}

.mode-option:active,
.library-option:active,
.sharer-option:active {
  transform: scale(0.98);
}

.option-text {
  font-size: 30rpx;
  color: #2d3748;
  font-weight: 500;
  letter-spacing: 0.3rpx;
}

.mode-option.selected .option-text,
.library-option.selected .option-text,
.sharer-option.selected .option-text {
  color: #4f46e5;
  font-weight: 600;
}

.option-check {
  font-size: 28rpx;
  color: #4f46e5;
  font-weight: bold;
  background: rgba(79, 70, 229, 0.1);
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid rgba(79, 70, 229, 0.3);
}

/* ========== 收到分享详情弹窗样式 ========== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 3000;
  padding: 80rpx 20rpx 120rpx;
}

.detail-modal-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 680rpx;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx 40rpx 40rpx;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  width: 100%;
  box-sizing: border-box;
}

.detail-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  gap: 20rpx;
  min-height: 60rpx;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
  width: 160rpx;
  line-height: 1.4;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  text-align: right;
  flex: 1;
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
  min-width: 0;
}

.level-detail {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.level-score-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  width: 100%;
  box-sizing: border-box;
}

.level-score-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.level-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.level-stats {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.level-score {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 600;
}

.level-accuracy {
  font-size: 24rpx;
  color: #34C759;
  font-weight: 600;
}