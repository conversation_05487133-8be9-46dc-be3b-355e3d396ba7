<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">{{testMode === 'elimination' ? '🎮 消消乐练习' : (testMode === 'en_to_cn' ? '🇬🇧 英译汉' : (testMode === 'cn_to_en' ? '🇨🇳 汉译英' : (testMode === 'phrase_en2zh' ? '🔤 短语英译汉' : (testMode === 'phrase_zh2en' ? '🈳 短语汉译英' : '测试模式'))))}}</text>
      <text class="page-subtitle">{{selectedWords.length}}个单词 · 选择学习模式</text>
      <!-- 分组信息显示 -->
      <view wx:if="{{showGroupInfo}}" class="group-info">
        <text class="group-text">📚 {{groupInfo}}</text>
      </view>
    </view>
  </view>

  <!-- 模式选择卡片 -->
  <view class="mode-cards">
    <!-- 练习模式 -->
    <view class="mode-card {{selectedMode === 'practice' ? 'selected' : ''}}" 
          bindtap="onModeSelect" 
          data-mode="practice">
      <view class="card-header">
        <view class="mode-icon">{{testMode === 'elimination' ? eliminationPracticeMode.icon : practiceMode.icon}}</view>
        <view class="mode-info">
          <text class="mode-title">{{testMode === 'elimination' ? eliminationPracticeMode.title : practiceMode.title}}</text>
          <text class="mode-desc">{{testMode === 'elimination' ? eliminationPracticeMode.desc : practiceMode.desc}}</text>
        </view>
      </view>
      <view class="mode-features">
        <view class="feature-item" wx:for="{{testMode === 'elimination' ? eliminationPracticeMode.features : practiceMode.features}}" wx:key="*this">
          <text class="feature-icon">✓</text>
          <text class="feature-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 测试模式 -->
    <view class="mode-card {{selectedMode === 'test' ? 'selected' : ''}}" 
          bindtap="onModeSelect" 
          data-mode="test">
      <view class="card-header">
        <view class="mode-icon">{{testMode === 'elimination' ? eliminationTestMode.icon : testModeConfig.icon}}</view>
        <view class="mode-info">
          <text class="mode-title">{{testMode === 'elimination' ? eliminationTestMode.title : testModeConfig.title}}</text>
          <text class="mode-desc">{{testMode === 'elimination' ? eliminationTestMode.desc : testModeConfig.desc}}</text>
        </view>
      </view>
      <view class="mode-features">
        <view class="feature-item" wx:for="{{testMode === 'elimination' ? eliminationTestMode.features : testModeConfig.features}}" wx:key="*this">
          <text class="feature-icon">✓</text>
          <text class="feature-text">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 测试模式配置 -->
  <view class="test-config" wx:if="{{selectedMode === 'test'}}">
    <!-- 英译汉/汉译英/短语测试的分组设置 -->
    <view class="config-section" wx:if="{{testMode === 'en_to_cn' || testMode === 'cn_to_en' || testMode === 'phrase_en2zh' || testMode === 'phrase_zh2en'}}">
      <view class="section-title">
        <text class="title">每组词汇数量</text>
        <text class="subtitle">选择每组测试包含的词汇数量</text>
      </view>
              <view class="time-options">
          <view class="time-option {{wordsPerGroup === item.value ? 'selected' : ''}}"
                wx:for="{{wordsPerGroupOptions}}"
                wx:key="value"
                bindtap="onWordsPerGroupSelect"
                data-value="{{item.value}}">
            <text class="option-label">{{item.label}}</text>
            <text class="option-desc">{{item.desc}}</text>
          </view>
        </view>
    </view>

    <!-- 时间限制设置（英译汉/汉译英/短语测试） -->
    <view class="config-section" wx:if="{{testMode === 'en_to_cn' || testMode === 'cn_to_en' || testMode === 'phrase_en2zh' || testMode === 'phrase_zh2en'}}">
      <view class="section-title">
        <text class="title">时间限制</text>
        <text class="subtitle">每道题的答题时间限制</text>
      </view>
      <view class="time-options">
        <view class="time-option {{timeLimit === item.value ? 'selected' : ''}}"
              wx:for="{{timeLimitOptions}}"
              wx:key="value"
              bindtap="onTimeLimitSelect"
              data-value="{{item.value}}">
          <text class="option-label">{{item.label}}</text>
          <text class="option-desc">{{item.desc}}</text>
        </view>
      </view>
    </view>

    <!-- 游戏模式设置（消消乐） -->
    <view class="config-section" wx:if="{{testMode === 'elimination'}}">
      <view class="section-title">
        <text class="title">游戏模式</text>
        <text class="subtitle">选择消消乐的游戏时长</text>
      </view>
      <view class="time-options">
        <view class="time-option {{eliminationGameMode === item.value ? 'selected' : ''}}"
              wx:for="{{eliminationGameModes}}"
              wx:key="value"
              bindtap="onEliminationModeSelect"
              data-value="{{item.value}}">
          <text class="option-label">{{item.label}}</text>
          <text class="option-desc">{{item.desc}}</text>
        </view>
      </view>
    </view>

    <!-- 测试对象选择 -->
    <view class="config-section">
      <view class="section-title">
        <text class="title">测试对象</text>
        <text class="subtitle">选择谁来完成这个测试</text>
      </view>
      <view class="share-options">
        <view class="share-option {{shareOption === 'self' ? 'selected' : ''}}"
              bindtap="onShareOptionSelect"
              data-option="self">
          <view class="option-icon">👤</view>
          <view class="option-info">
            <text class="option-title">自己测试</text>
            <text class="option-desc">立即开始测试，错题自动记录</text>
          </view>
        </view>
        <view class="share-option {{shareOption === 'others' ? 'selected' : ''}}"
              bindtap="onShareOptionSelect"
              data-option="others">
          <view class="option-icon">👥</view>
          <view class="option-info">
            <text class="option-title">分享给他人</text>
            <text class="option-desc">创建分享链接，查看他人测试结果</text>
          </view>
        </view>
        <view class="share-option {{shareOption === 'competition' ? 'selected' : ''}}"
              bindtap="onShareOptionSelect"
              data-option="competition">
          <view class="option-icon">🏆</view>
          <view class="option-info">
            <text class="option-title">创建单词竞赛</text>
            <text class="option-desc">创建公开竞赛，与他人一起挑战</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 有效期设置（仅在分享给他人时显示） -->
    <view class="config-section" wx:if="{{shareOption === 'others'}}">
      <view class="section-title">
        <text class="title">分享有效期</text>
        <text class="subtitle">选择分享链接的有效时间</text>
      </view>
      <view class="time-options">
        <view class="time-option {{expireDays === item.value ? 'selected' : ''}}"
              wx:for="{{expireDaysOptions}}"
              wx:key="value"
              bindtap="onExpireDaysSelect"
              data-value="{{item.value}}">
          <text class="option-label">{{item.label}}</text>
          <text class="option-desc">{{item.desc}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="onBack">返回</button>
    <button class="action-btn primary {{selectedMode ? '' : 'disabled'}}" 
            bindtap="{{selectedMode === 'test' ? 'startTest' : 'startPractice'}}"
            disabled="{{!selectedMode}}">
      {{selectedMode === 'test' ? (shareOption === 'self' ? '开始测试' : shareOption === 'others' ? '创建分享' : '创建竞赛') : '开始练习'}}
    </button>
  </view>

  <!-- 自定义竞赛名称输入组件 -->
  <competition-input id="competitionInput" bind:confirm="onCompetitionInputConfirm" bind:cancel="onCompetitionInputCancel" />

  <!-- 分享弹窗 -->
  <view class="share-modal" wx:if="{{showShareModal}}" bindtap="closeShareModal">
    <view class="share-content" catchtap="">
      <view class="share-header">
        <text class="share-title">分享测试</text>
        <text class="close-btn" bindtap="closeShareModal">✕</text>
      </view>
      
      <view class="share-info">
        <text class="share-desc">选择分享方式，将测试分享给朋友</text>
        <text class="share-note">朋友可以通过分享链接参与测试</text>
      </view>

      <view class="share-buttons">
        <button class="share-btn copy-btn" bindtap="onCopyTestInfo">
          <text class="share-btn-icon">📋</text>
          <text class="share-btn-text">复制测试信息</text>
        </button>

        <button
          class="share-btn wechat-share-btn"
          open-type="share"
          bindtap="onShareToWeChatFriends"
        >
          <text class="share-btn-icon">📤</text>
          <text class="share-btn-text">分享给微信好友</text>
        </button>



        <button class="share-btn timeline-btn" bindtap="onShareToTimeline">
          <text class="share-btn-icon">🌟</text>
          <text class="share-btn-text">分享到朋友圈</text>
        </button>
      </view>
    </view>
  </view>
</view> 