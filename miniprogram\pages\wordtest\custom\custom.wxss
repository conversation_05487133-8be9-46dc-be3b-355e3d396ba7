/* 自定义检测页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
  padding-bottom: 100rpx;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  text-align: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
  margin-bottom: 12rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #7f8c8d;
  display: block;
}

/* 区域样式 */
.input-section, .words-section, .mode-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

/* 区域头部 */
.input-header, .section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
  margin-bottom: 8rpx;
}

.section-desc {
  font-size: 26rpx;
  color: #7f8c8d;
  display: block;
}

.section-actions {
  margin-top: 16rpx;
}

.action-link {
  font-size: 24rpx;
  color: #e74c3c;
  padding: 8rpx 16rpx;
  background: rgba(231, 76, 60, 0.1);
  border-radius: 16rpx;
  display: inline-block;
}

/* 输入区域 */
.input-area {
  margin-bottom: 24rpx;
}

.input-text {
  width: 100%;
  min-height: 300rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 20rpx;
  font-size: 28rpx;
  line-height: 1.6;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.input-text:focus {
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(52, 152, 219, 0.1);
}

/* 工具按钮 */
.input-tools {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  margin-top: 20rpx;
}

.tool-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 12rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.tool-btn:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.tool-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tool-text {
  font-size: 22rpx;
  color: #6c757d;
  text-align: center;
}

/* AI处理按钮 */
.ai-process-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24rpx 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.ai-process-btn:active {
  transform: scale(0.98);
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
}

.ai-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.ai-text {
  font-size: 28rpx;
  font-weight: bold;
}

/* 单词列表 */
.words-list {
  background: #f8f9fa;
  border-radius: 20rpx;
  overflow: hidden;
}

.word-item {
  background: white;
  border-bottom: 2rpx solid #f8f9fa;
  transition: all 0.3s ease;
}

.word-item:last-child {
  border-bottom: none;
}

.word-item:active {
  background: #f1f3f4;
}

.word-content {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
}

.word-main {
  flex: 1;
  margin-right: 16rpx;
}

.word-header {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
  gap: 12rpx;
}

.word-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.word-phonetic {
  font-size: 24rpx;
  color: #3498db;
  font-style: italic;
}

.word-definition {
  font-size: 28rpx;
  color: #34495e;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.word-example {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.4;
  font-style: italic;
}

.word-actions {
  display: flex;
  gap: 12rpx;
  align-items: flex-start;
}

.action-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn.edit {
  background: #e3f2fd;
  color: #2196f3;
}

.action-btn.delete {
  background: #ffebee;
  color: #f44336;
}

.action-btn:active {
  transform: scale(0.9);
}

.action-icon {
  font-size: 24rpx;
}

/* 模式选择 */
.mode-list {
  background: #f8f9fa;
  border-radius: 20rpx;
  overflow: hidden;
}

.mode-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: white;
  border-bottom: 2rpx solid #f8f9fa;
  transition: all 0.3s ease;
}

.mode-item:last-child {
  border-bottom: none;
}

.mode-item:active {
  background: #f1f3f4;
}

.mode-item-selected {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-left: 6rpx solid #3498db;
  box-shadow: 0 4rpx 16rpx rgba(52, 152, 219, 0.2);
}

.mode-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  min-width: 56rpx;
  text-align: center;
}

.mode-text {
  flex: 1;
}

.mode-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
  margin-bottom: 8rpx;
}

.mode-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  display: block;
}

.mode-check-container {
  position: relative;
  width: 48rpx;
  height: 48rpx;
}

.mode-check-normal {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mode-check-selected {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #3498db;
  border: 2rpx solid #3498db;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(52, 152, 219, 0.3);
  transform: scale(1.1);
}

.mode-check-normal {
  background: white;
  border: 2rpx solid #e0e0e0;
}

.check-text {
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.mode-check-normal .check-text {
  color: transparent;
}



/* 开始练习按钮 */
.start-section {
  text-align: center;
  padding: 20rpx;
}

.start-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
  color: white;
  padding: 28rpx 48rpx;
  border-radius: 50rpx;
  box-shadow: 0 8rpx 24rpx rgba(33, 150, 243, 0.3);
  transition: all 0.3s ease;
  margin-bottom: 16rpx;
}

.start-btn.disabled {
  background: #bdc3c7;
  box-shadow: none;
  pointer-events: none;
}

.start-btn:active {
  transform: scale(0.98);
  box-shadow: 0 6rpx 20rpx rgba(33, 150, 243, 0.4);
}

.start-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 12rpx;
}

.start-icon {
  font-size: 28rpx;
}

.start-tip {
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 使用说明 */
.help-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.help-content {
  text-align: center;
}

.help-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 24rpx;
}

.help-list {
  margin-bottom: 32rpx;
}

.help-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  text-align: left;
}

.help-number {
  width: 48rpx;
  height: 48rpx;
  background: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.help-text {
  font-size: 26rpx;
  color: #34495e;
  line-height: 1.5;
  flex: 1;
  padding-top: 8rpx;
}

/* 格式示例 */
.format-examples {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: left;
}

.format-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 16rpx;
}

.format-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.format-name {
  font-size: 24rpx;
  color: #7f8c8d;
  min-width: 120rpx;
}

.format-example {
  font-size: 24rpx;
  color: #3498db;
  font-weight: 500;
}

/* 单词输入弹窗 */
.word-input-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: calc(100% - 40rpx);
  max-width: 560rpx;
  max-height: 90vh;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 30rpx 30rpx 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.modal-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.modal-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  color: #666;
}

.modal-body {
  padding: 30rpx;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.input-group {
  margin-bottom: 25rpx;
}

.input-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.input-field {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.textarea-field {
  width: 100%;
  height: 120rpx;
  background: #f8f8f8;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 15rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.4;
}

.input-field:focus,
.textarea-field:focus {
  border-color: #007aff;
  background: white;
}

.modal-footer {
  padding: 20rpx 30rpx 30rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
  flex-shrink: 0;
  background: white;
}

.modal-btn {
  flex: 1;
  padding: 18rpx;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  box-sizing: border-box;
}

/* 取消按钮样式 */
.modal-btn.cancel {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #dee2e6;
}

/* 确认按钮样式 */
.modal-btn.confirm {
  background: #007aff;
  color: white;
  border: 2rpx solid #007aff;
}

.modal-btn:active {
  transform: scale(0.98);
}

.modal-btn.cancel:active {
  background: #e9ecef;
  border-color: #ced4da;
}

.modal-btn.confirm:active {
  background: #0056cc;
  border-color: #0056cc;
}

/* 响应式适配已移除，使用固定样式 */

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.word-item {
  animation: fadeIn 0.3s ease-out;
}

.mode-item {
  animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式在小程序中不支持，已移除 */ 