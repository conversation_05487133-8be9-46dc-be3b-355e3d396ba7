/* 竞赛页面样式 */
.container {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 页面头部 */
.page-header {
  margin-bottom: 30rpx;
  text-align: center;
}

.header-content {
  padding: 32rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  box-shadow: 0 6rpx 24rpx rgba(102, 126, 234, 0.3);
  color: white;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.page-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
}

/* 模式切换 */
.mode-tabs {
  margin-bottom: 30rpx;
}

.tab-list {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 创建按钮 */
.create-btn {
  display: flex;
  align-items: center;
  background-color: #4a90e2;
  color: white;
  border-radius: 30rpx;
  padding: 12rpx 25rpx;
  font-size: 28rpx;
  border: none;
  margin: 0;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.btn-text {
  font-weight: 500;
}

.sort-dropdown {
  position: relative;
  min-width: 180rpx;
}

.sort-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
  min-height: 40rpx;
}

.sort-trigger:active {
  transform: scale(0.98);
  border-color: #667eea;
}

.sort-current {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.sort-arrow {
  font-size: 20rpx;
  color: #999;
  transition: transform 0.3s ease;
  margin-left: 8rpx;
}

.sort-arrow.active {
  transform: rotate(180deg);
  color: #667eea;
}

.sort-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  border: 2rpx solid #f0f0f0;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
  margin-top: 8rpx;
  overflow: hidden;
}

.sort-options.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.sort-option {
  padding: 20rpx 24rpx;
  transition: all 0.2s ease;
  border-bottom: 1rpx solid #f5f5f5;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option:hover {
  background: #f8f9ff;
}

.sort-option.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.sort-option:active {
  transform: scale(0.98);
}

.sort-option .sort-text {
  font-size: 24rpx;
  font-weight: 500;
  color: inherit;
}

.sort-option.active .sort-text {
  color: white;
  font-weight: 600;
}

/* 竞赛列表 */
.competition-list {
  margin-bottom: 30rpx;
}

.competition-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.competition-item:active {
  transform: scale(0.98);
}

.competition-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.competition-info {
  flex: 1;
}

/* 竞赛标题行 */
.competition-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.competition-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}

/* 竞赛类型徽章 */
.competition-type-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
  flex-shrink: 0;
}

/* 创建中状态徽章 */
.creating-badge {
  background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 167, 38, 0.3);
  flex-shrink: 0;
  margin-right: 8rpx;
  animation: creatingPulse 1.5s ease-in-out infinite;
}

@keyframes creatingPulse {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
  }
  50% { 
    opacity: 0.7;
    transform: scale(1.02);
  }
}

.pin-badge {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.4);
  flex-shrink: 0;
  animation: pinGlow 2s ease-in-out infinite;
}

@keyframes pinGlow {
  0%, 100% { 
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.4);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.6);
    transform: scale(1.05);
  }
}

.badge-text {
  font-weight: 500;
  letter-spacing: 1rpx;
}

.competition-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.competition-time {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.competition-creator {
  font-size: 22rpx;
  color: #999;
  padding: 4rpx 8rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
}

.competition-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #666;
  background: #f8f9fa;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

.stat-item.highlight-red {
  color: #ff4757;
  background: #ffe8e8;
  font-weight: 600;
}

/* 竞赛详情 */
.competition-details {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  width: 80rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.best-score {
  color: #ff6b6b;
  font-weight: 600;
}

.competition-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 20rpx;
  flex-wrap: wrap;
}

/* 4按钮布局 */
.competition-actions.has-delete {
  display: flex;
  gap: 12rpx;
}

/* 3按钮布局 */
.competition-actions.no-delete {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  height: 80rpx;
  width: 80rpx;
  border: none;
  flex-shrink: 0;
  font-size: 24rpx;
}

.join-btn {
  flex: 1;
  max-width: 200rpx;
}

.action-btn:active {
  transform: scale(0.95);
}

.join-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4rpx 8rpx rgba(102, 126, 234, 0.2);
}

.ranking-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  box-shadow: 0 4rpx 8rpx rgba(40, 167, 69, 0.2);
}

.share-btn {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: white;
  box-shadow: 0 4rpx 8rpx rgba(255, 193, 7, 0.2);
}

.pin-btn {
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
  color: white;
  box-shadow: 0 4rpx 8rpx rgba(255, 193, 7, 0.2);
}

.delete-btn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  box-shadow: 0 4rpx 8rpx rgba(220, 53, 69, 0.2);
}

.action-text {
  font-size: 18rpx;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  color: white;
  padding: 0 8rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 20rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}

.empty-tip {
  font-size: 24rpx;
  color: #999;
  display: block;
}

/* 说明区域 */
.info-section {
  margin-top: 20rpx;
}

.info-content {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 分享弹窗样式 */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.share-content {
  background: white;
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  overflow: hidden;
  animation: shareModalShow 0.3s ease-out;
}

@keyframes shareModalShow {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.share-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 36rpx;
  color: #999;
  padding: 10rpx;
  line-height: 1;
}

.share-info {
  padding: 30rpx 40rpx;
  text-align: center;
}

.share-desc {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.share-note {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.share-buttons {
  padding: 20rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  gap: 8rpx;
  height: 60rpx;
}

.share-btn::after {
  border: none;
}

.share-btn-icon {
  font-size: 24rpx;
}

.share-btn-text {
  color: inherit;
}

.copy-btn {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #e9ecef;
}

.copy-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.wechat-share-btn {
  background: linear-gradient(135deg, #07c160 0%, #00ae42 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(7, 193, 96, 0.3);
}

.wechat-share-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.4);
}

.enterprise-share-btn {
  background: linear-gradient(135deg, #576b95 0%, #4a5a7a 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(87, 107, 149, 0.3);
}

.enterprise-share-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(87, 107, 149, 0.4);
}

.timeline-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 107, 0.3);
}

.timeline-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.4);
}