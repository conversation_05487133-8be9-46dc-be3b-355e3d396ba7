<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <input class="search-input" 
           placeholder="搜索单词" 
           value="{{searchKeyword}}"
           bindinput="onSearchInput"
           bindconfirm="onSearchConfirm" />
    <button class="search-btn" bindtap="onSearchConfirm">搜索</button>
  </view>

  <!-- 学习模式按钮 -->
  <view class="mode-buttons">
    <button class="mode-btn primary" bindtap="studyInOrder">
      📖 按顺序学习全部
    </button>
    <button class="mode-btn secondary" bindtap="studyRandom">
      🎲 乱序学习全部
    </button>
  </view>

  <!-- 操作栏 -->
  <view class="action-bar">
    <view class="selected-count">已选择: {{selectedWords.length}}个</view>
    <view class="action-buttons">
      <button class="action-btn" bindtap="selectFreeWords" size="mini">全选</button>
      <button class="action-btn" bindtap="clearSelection" size="mini">清空</button>
    </view>
  </view>

  <!-- 消消乐配置 -->
  <view class="puzzle-config" wx:if="{{presetTestMode === 'elimination'}}">
    <!-- 每组词汇数量选择 -->
    <view class="config-section">
      <view class="config-title">
        <text class="config-label">每组词汇数量</text>
        <text class="config-desc">选择每组消消乐包含的词汇数量</text>
      </view>
      <view class="quantity-options">
        <view class="quantity-option {{wordsPerGroup === item ? 'selected' : ''}}"
              wx:for="{{wordsPerGroupOptions}}"
              wx:key="*this"
              bindtap="onWordsPerGroupSelect"
              data-value="{{item}}">
          <text class="option-value">{{item}}</text>
          <text class="option-unit">个</text>
        </view>
      </view>
    </view>

    <!-- 消消乐提示 -->
    <view class="puzzle-notice" wx:if="{{selectedWords.length > 0}}">
      <view class="notice-content">
        <text class="notice-icon">🎮</text>
        <text class="notice-text">
          {{selectedWords.length <= wordsPerGroup ? 
            '当前选择可在一组内完成（' + selectedWords.length + '/' + wordsPerGroup + '个）' : 
            '当前选择将分为' + Math.ceil(selectedWords.length / wordsPerGroup) + '组进行，每组' + wordsPerGroup + '个词汇'
          }}
        </text>
      </view>
    </view>
  </view>

  <!-- VIP限制提示 -->
  <view class="vip-notice" wx:if="{{showVipLimit}}">
    <view class="notice-content">
      <text class="notice-icon">💎</text>
      <text class="notice-text">免费用户只能选择前100个词汇，开通会员解锁全部词汇</text>
    </view>
  </view>

  <!-- 词汇列表 -->
  <view class="words-list">
    <block wx:for="{{words}}" wx:key="_id">
      <view class="word-item {{item.selected ? 'selected' : ''}} {{!checkWordSelectable(index) ? 'disabled' : ''}}"
            bindtap="onWordSelect"
            data-index="{{index}}">
        
        <!-- 选择状态 -->
        <view class="select-status">
          <view class="checkbox {{item.selected ? 'checked' : ''}}">
            <text class="checkmark" wx:if="{{item.selected}}">✓</text>
          </view>
        </view>

        <!-- 单词信息 -->
        <view class="word-content">
          <view class="word-header">
            <text class="word-text">{{item.words || item.word}}</text>
            <text class="phonetic" wx:if="{{item.phonetic}}">{{item.phonetic}}</text>
          </view>
          
          <view class="meaning-text">
            <text class="definition">{{item.meaning || (item.meanings && item.meanings[0] && item.meanings[0].definitions && item.meanings[0].definitions[0].definition)}}</text>
          </view>



          <!-- 显示标签（如果有） -->
          <view class="word-tags" wx:if="{{item.tags}}">
            <block wx:for="{{item.tags}}" wx:for-item="tag" wx:key="*this">
              <text class="tag">{{tag}}</text>
            </block>
          </view>
        </view>

        <!-- VIP标识 - 根据配置决定是否显示 -->
        <view class="vip-mark" wx:if="{{!checkWordSelectable(index) && showVipFeatures}}">VIP</view>
      </view>
    </block>

    <!-- 加载提示 -->
    <view class="loading-tip" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && words.length === 0}}">
      <text class="empty-icon">📚</text>
      <text class="empty-text">暂无词汇数据</text>
    </view>
  </view>

  <!-- 翻页控件 -->
  <view class="pagination" wx:if="{{!loading && totalPages > 1}}">
    <!-- 分页信息 -->
    <view class="page-info">
      <text class="page-text">第 {{currentPage}} / {{totalPages}} 页</text>
      <text class="total-text">共 {{totalWords}} 个词汇</text>
    </view>

    <!-- 分页按钮 -->
    <view class="page-buttons">
      <button class="page-btn prev {{!hasPrev ? 'disabled' : ''}}" 
              bindtap="onPrevPage" 
              disabled="{{!hasPrev}}">
        <text class="btn-icon">‹</text>
        <text class="btn-text">上一页</text>
      </button>

      <button class="page-btn goto" bindtap="onGoToPage">
        <text class="btn-text">跳转</text>
      </button>

      <button class="page-btn next {{!hasMore ? 'disabled' : ''}}" 
              bindtap="onNextPage" 
              disabled="{{!hasMore}}">
        <text class="btn-text">下一页</text>
        <text class="btn-icon">›</text>
      </button>
    </view>

    <!-- 快速跳转页码 -->
    <view class="quick-pages" wx:if="{{totalPages <= 10}}">
      <block wx:for="{{totalPages}}" wx:key="*this">
        <view class="quick-page {{currentPage === (index + 1) ? 'active' : ''}}"
              bindtap="onQuickPage"
              data-page="{{index + 1}}">
          {{index + 1}}
        </view>
      </block>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-bar" wx:if="{{selectedWords.length > 0}}">
    <button class="next-btn" bindtap="studyCustom">
      {{mode === 'test' ? '开始学习' : '下一步'}} ({{selectedWords.length}})
    </button>
  </view>
</view> 