/* pages/profile/share/user-report/user-report.wxss */
.report-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

/* 报告内容 */
.report-content {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 用户信息卡片 */
.user-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.user-avatar {
  margin-right: 24rpx;
}

.avatar-img {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.user-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

.export-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.export-btn::after {
  border: none;
}

/* 统计数据概览 */
.stats-overview {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
}

.stats-value {
  font-size: 42rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 22rpx;
  color: #666666;
  text-align: center;
  line-height: 1.2;
}

/* 章节标题 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}

/* 所有测试记录 */
.all-tests {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.test-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.test-item {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.test-item:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #e9ecef, #f8f9fa);
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.test-mode-badge {
  font-size: 24rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  color: #ffffff;
}

.test-mode-badge.mode-en_to_cn {
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
}

.test-mode-badge.mode-cn_to_en {
  background: linear-gradient(135deg, #4ecdc4, #26a69a);
}

.test-mode-badge.mode-dictation {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.test-mode-badge.mode-elimination {
  background: linear-gradient(135deg, #ff9f43, #ff6348);
}

.test-time {
  font-size: 22rpx;
  color: #999999;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.test-library {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

.test-stats {
  display: flex;
  gap: 20rpx;
  font-size: 22rpx;
}

.test-score {
  font-weight: bold;
}

.test-score.score-high {
  color: #51cf66;
}

.test-score.score-medium {
  color: #ff9f43;
}

.test-score.score-low {
  color: #ff6b6b;
}

.test-accuracy {
  color: #51cf66;
  font-weight: bold;
}

.test-duration {
  color: #339af0;
  font-weight: bold;
}

/* 测试项底部 */
.test-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.test-mistakes {
  font-size: 22rpx;
  color: #ff6b6b;
  font-weight: 500;
}

.test-detail-hint {
  font-size: 20rpx;
  color: #999999;
}

/* 空状态 */
.empty-state {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15rpx;
}

.empty-desc {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

/* 分析建议 */
.analysis-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.analysis-item {
  display: flex;
  align-items: flex-start;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 16rpx;
  padding: 20rpx;
  border: 2rpx solid #f0f0f0;
}

.analysis-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.analysis-text {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
  line-height: 1.5;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.detail-modal, .retest-modal {
  background: #ffffff;
  border-radius: 20rpx;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.close-btn {
  font-size: 40rpx;
  color: #999999;
  line-height: 1;
  padding: 10rpx;
  cursor: pointer;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 30rpx;
}

/* 测试基本信息 */
.test-basic-info {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
}

.info-row {
  display: flex;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 24rpx;
  color: #666666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  flex: 1;
}

.info-value.score-high {
  color: #51cf66;
  font-weight: bold;
}

.info-value.score-medium {
  color: #ff9f43;
  font-weight: bold;
}

.info-value.score-low {
  color: #ff6b6b;
  font-weight: bold;
}

/* 错词列表 */
.mistakes-section {
  margin-bottom: 30rpx;
}

.mistakes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.mistakes-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.mistakes-actions {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  background: #667eea;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
}

.action-btn::after {
  border: none;
}

.mistakes-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.mistake-item {
  display: flex;
  align-items: flex-start;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.mistake-item.selected {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.mistake-checkbox {
  width: 32rpx;
  height: 32rpx;
  border-radius: 6rpx;
  border: 2rpx solid #d0d0d0;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.mistake-item.selected .mistake-checkbox {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-icon {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
}

.mistake-content {
  flex: 1;
}

.mistake-word {
  font-size: 26rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.mistake-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.user-answer, .correct-answer {
  font-size: 22rpx;
  line-height: 1.3;
}

.user-answer {
  color: #ff6b6b;
}

.correct-answer {
  color: #51cf66;
}

/* 重新测试按钮 */
.retest-section {
  margin-top: 30rpx;
  text-align: center;
}

.retest-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
  font-weight: bold;
  width: 100%;
}

.retest-btn::after {
  border: none;
}

/* 无错词提示 */
.no-mistakes {
  text-align: center;
  padding: 60rpx 40rpx;
}

.no-mistakes-text {
  font-size: 28rpx;
  color: #51cf66;
  font-weight: bold;
}

/* 重新测试配置 */
.config-section {
  margin-bottom: 40rpx;
}

.config-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.mode-options, .practice-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.share-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.mode-option, .practice-option {
  flex: 1;
  min-width: 160rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.share-option {
  background: rgba(255, 255, 255, 0.95);
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  padding: 18rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.mode-option.selected, .practice-option.selected {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.share-option.selected {
  background: #e3f2fd;
  border-color: #2196f3;
  box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.2);
}

.mode-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  display: block;
}

.mode-text, .practice-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #333333;
  display: block;
}

.share-text {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.practice-desc {
  font-size: 20rpx;
  color: #666666;
  margin-top: 4rpx;
  display: block;
}

.share-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.3;
}

/* 分享选项内容区域 */
.share-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.share-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  color: #2196f3;
}

.share-info {
  flex: 1;
}

.share-indicator {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.share-option.selected .share-indicator {
  border-color: #2196f3;
  background: #2196f3;
}

.radio-icon {
  font-size: 20rpx;
  color: #fff;
  font-weight: bold;
}

.config-summary {
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.summary-text {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 500;
}

.confirm-btn {
  background: linear-gradient(135deg, #51cf66, #40c057);
  color: #ffffff;
  border: none;
  border-radius: 25rpx;
  padding: 24rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  width: 100%;
}

.confirm-btn::after {
  border: none;
} 