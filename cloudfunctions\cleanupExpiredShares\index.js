const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  try {
    console.log('开始清理过期分享测试...');
    
    // 获取当前时间
    const now = db.serverDate();
    
    // 查询所有过期的分享测试
    const expiredResult = await db.collection('shareTests')
      .where({
        expireTime: db.command.lt(now)
      })
      .get();
    
    console.log(`发现 ${expiredResult.data.length} 个过期分享`);
    
    if (expiredResult.data.length === 0) {
      return {
        success: true,
        message: '没有需要清理的过期分享',
        cleanedCount: 0
      };
    }
    
    // 批量删除过期记录
    const deletePromises = expiredResult.data.map(share => {
      return db.collection('shareTests')
        .doc(share._id)
        .remove();
    });
    
    await Promise.all(deletePromises);
    
    console.log(`成功清理 ${expiredResult.data.length} 个过期分享`);
    
    return {
      success: true,
      message: `成功清理 ${expiredResult.data.length} 个过期分享`,
      cleanedCount: expiredResult.data.length,
      cleanedShares: expiredResult.data.map(share => ({
        shareId: share.shareId,
        createdBy: share.createdBy,
        createTime: share.createTime,
        expireTime: share.expireTime
      }))
    };
    
  } catch (error) {
    console.error('清理过期分享失败:', error);
    return {
      success: false,
      message: '清理过期分享失败',
      error: error.message
    };
  }
}; 