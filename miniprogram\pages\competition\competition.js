Page({
  data: {
    currentMode: 'elimination', // 当前选中的模式
    currentModeName: '消消乐', // 当前模式名称
    modes: [
      { id: 'elimination', name: '消消乐', icon: '🧩' },
      { id: 'en2zh', name: '英译汉', icon: '🈯' },
      { id: 'zh2en', name: '汉译英', icon: '🈲' },
      { id: 'dictation', name: '听写', icon: '🎧' }
    ],
    competitions: {
      elimination: [],
      en2zh: [],
      zh2en: [],
      dictation: []
    },
    sortBy: 'latest', // latest: 最新创建, participants: 参与人数, wordCount: 词汇数量
    showSortDropdown: false, // 是否显示排序下拉菜单
    currentSortText: '按创建时间', // 当前排序方式的显示文本
    loading: false,
    currentUserOpenId: '', // 当前用户openid
    isAdmin: false, // 是否为管理员

    // 分享相关数据
    showShareModal: false,
    currentShareData: null,
    currentTimelineShareData: null
  },

  async onLoad(options) {
    const { mode, highlightId } = options;

    // 启用分享功能，包括朋友圈分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    // 如果指定了模式，切换到对应的模式
    if (mode && this.data.modes.find(m => m.id === mode)) {
      const modeName = this.data.modes.find(m => m.id === mode)?.name || '未知模式';
      this.setData({
        currentMode: mode,
        currentModeName: modeName
      });
    }

    await this.getCurrentUser();
    this.loadCompetitions();

    // 如果有高亮ID，显示提示（后续可以实现高亮效果）
    if (highlightId) {
      console.log('需要高亮的竞赛ID:', highlightId);
    }
  },
  
  // 初始化竞赛集合
  async initCompetitionsCollection() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'initCompetitions',
        data: {}
      });
      
      if (result.result.success) {
        console.log('竞赛集合初始化结果:', result.result.message);
      }
    } catch (error) {
      console.error('初始化竞赛集合失败，尝试直接创建测试数据...');
      
      // 如果云函数失败，尝试直接创建
      try {
        const db = wx.cloud.database();
        await db.collection('competitions').add({
          data: {
            name: '系统测试竞赛',
            mode: 'en2zh',
            words: [],
            status: 'active',
            createTime: new Date(),
            creatorOpenId: 'system',
            creatorName: '系统',
            participants: 0,
            results: []
          }
        });
        console.log('直接创建测试竞赛成功');
      } catch (dbError) {
        console.error('直接创建测试竞赛失败:', dbError);
      }
    }
  },

  onShow() {
    // 页面显示时刷新竞赛列表
    this.loadCompetitions();
    
    // 如果有highlightId，说明是刚创建的竞赛，需要高亮显示
    const app = getApp();
    if (app.globalData.highlightCompetitionId) {
      // 轮询检查新创建的竞赛是否出现
      this.checkNewCompetition(app.globalData.highlightCompetitionId);
      app.globalData.highlightCompetitionId = null; // 清除标记
    }
  },

  // 轮询检查新创建的竞赛
  checkNewCompetition(competitionId, retryCount = 0) {
    const maxRetries = 10; // 最多重试10次
    const retryInterval = 1000; // 每次间隔1秒
    
    // 检查竞赛是否已经出现在列表中
    const competitions = this.data.competitions[this.data.currentMode] || [];
    const competitionExists = competitions.find(comp => comp.id === competitionId);
    
    if (competitionExists) {
      // 竞赛已存在，高亮显示
      this.highlightNewCompetition(competitionId);
      return;
    }
    
    if (retryCount < maxRetries) {
      // 竞赛还未出现，重新加载并重试
      console.log(`检查新竞赛第${retryCount + 1}次，竞赛ID: ${competitionId}`);
      
      setTimeout(() => {
        this.loadCompetitions().then(() => {
          this.checkNewCompetition(competitionId, retryCount + 1);
        });
      }, retryInterval);
    } else {
      // 超过重试次数，显示提示但不影响主要功能
      console.log('新竞赛检查超时，但竞赛可能已创建成功');
      wx.showToast({
        title: '竞赛创建成功',
        icon: 'success'
      });
    }
  },

  // 高亮新创建的竞赛
  highlightNewCompetition(competitionId) {
    // 找到对应的竞赛并滚动到可见位置
    const competitions = this.data.competitions[this.data.currentMode] || [];
    const competitionIndex = competitions.findIndex(comp => comp.id === competitionId);
    
    if (competitionIndex >= 0) {
      // 滚动到该竞赛位置
      wx.pageScrollTo({
        selector: `#competition-${competitionId}`,
        duration: 300
      });
      
      wx.showToast({
        title: '竞赛创建成功',
        icon: 'success'
      });
    }
  },

  // 获取当前用户信息
  async getCurrentUser() {
    try {
      const app = getApp();
      const userInfo = await app.getUserInfo();
      
      if (userInfo) {
        // 管理员账号识别逻辑
        const ADMIN_OPENID_PART = '5938DE76'; // 微信登录账号ID部分
        const ADMIN_PHONE = '15547663399'; // 用户名密码登录账号
        
        let isAdmin = false;
        
        // 检查微信登录账号
        if (userInfo.openid && userInfo.openid.includes(ADMIN_OPENID_PART)) {
          isAdmin = true;
        }
        
        // 检查账号密码登录账号
        if (userInfo.phone === ADMIN_PHONE || userInfo.username === ADMIN_PHONE) {
          isAdmin = true;
        }
        
        // 检查其他可能的标识字段
        if (userInfo._id && userInfo._id.includes(ADMIN_OPENID_PART)) {
          isAdmin = true;
        }
        
        console.log('用户信息检查:', {
          openid: userInfo.openid,
          phone: userInfo.phone,
          username: userInfo.username,
          _id: userInfo._id,
          isAdmin: isAdmin
        });
        
        this.setData({
          currentUserOpenId: userInfo.openid || userInfo._id,
          isAdmin: isAdmin
        });
        
        // 如果是管理员，自动初始化竞赛集合
        if (isAdmin) {
          this.initCompetitionsCollection();
        }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },

  // 切换模式
  onModeChange(e) {
    const mode = e.currentTarget.dataset.mode;
    const modeName = this.data.modes.find(m => m.id === mode)?.name || '未知模式';
    this.setData({
      currentMode: mode,
      currentModeName: modeName
    });
    this.loadCompetitions();
  },

  // 加载竞赛列表
  async loadCompetitions() {
    this.setData({ loading: true });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'getCompetitions',
        data: {
          mode: this.data.currentMode,
          sortBy: this.data.sortBy
        }
      });

      if (result.result.success) {
        const competitions = { ...this.data.competitions };
        const competitionList = result.result.data || [];
        
        console.log(`=== 获取${this.data.currentMode}模式竞赛 ===`);
        console.log('竞赛数量:', competitionList.length);
        console.log('竞赛列表:', competitionList);
        
        // 为每个竞赛添加canDelete标志和处理创建者昵称
        const formattedCompetitions = competitionList.map(comp => {
          // 处理创建者昵称显示
          let displayCreatorName = comp.creatorName || '匿名用户';
          
          // 如果昵称是openid格式（以wx开头），显示为"匿名用户"
          if (displayCreatorName.startsWith('wx') && displayCreatorName.length > 20) {
            displayCreatorName = '匿名用户';
          }
          
          return {
            ...comp,
            creatorName: displayCreatorName,
            canDelete: this.canDeleteCompetition(comp),
            pinned: comp.pinned || false
          };
        });
        
        // 对竞赛进行排序：置顶的在前，然后按创建时间或其他条件排序
        formattedCompetitions.sort((a, b) => {
          // 首先按置顶状态排序
          if (a.pinned && !b.pinned) return -1;
          if (!a.pinned && b.pinned) return 1;
          
          // 置顶状态相同时，按原有逻辑排序
          if (this.data.sortBy === 'participants') {
            return (b.participants || 0) - (a.participants || 0);
          } else if (this.data.sortBy === 'wordCount') {
            return (b.wordCount || 0) - (a.wordCount || 0);
          } else {
            // 默认按创建时间排序（新的在前）
            return new Date(b.createTime) - new Date(a.createTime);
          }
        });
        
        competitions[this.data.currentMode] = formattedCompetitions;
        this.setData({ competitions });
        
        return Promise.resolve(); // 返回成功的Promise
      } else {
        return Promise.reject(new Error('获取竞赛列表失败'));
      }
    } catch (error) {
      console.error('加载竞赛列表失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
      return Promise.reject(error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 创建竞赛
  onCreateCompetition() {
    // 直接跳转到单词检测页面
    wx.navigateTo({
      url: '/pages/wordtest/wordtest'
    });
  },

  // 切换置顶状态
  async onTogglePin(e) {
    const competitionId = e.currentTarget.dataset.id;
    const isPinned = e.currentTarget.dataset.pinned;
    
    if (!this.data.isAdmin) {
      wx.showToast({
        title: '无权限操作',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: isPinned ? '取消置顶中...' : '置顶中...',
      mask: true
    });

    try {
      const result = await wx.cloud.callFunction({
        name: 'getCompetitions',
        data: {
          mode: 'togglePin',
          competitionId: competitionId,
          pinned: !isPinned
        }
      });

      wx.hideLoading();

      if (result.result.success) {
        wx.showToast({
          title: isPinned ? '已取消置顶' : '已置顶',
          icon: 'success'
        });
        
        // 重新加载竞赛列表
        this.loadCompetitions();
      } else {
        wx.showModal({
          title: '操作失败',
          content: result.result.message || '置顶操作失败',
          showCancel: false
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('置顶操作失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 清理系统测试竞赛
  onCleanSystemTest() {
    wx.showModal({
      title: '清理确认',
      content: '确定要清理所有系统测试竞赛吗？此操作不可撤销。',
      confirmText: '清理',
      confirmColor: '#ff9800',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '清理中...',
            mask: true
          });

          try {
            const result = await wx.cloud.callFunction({
              name: 'clearSystemTestCompetitions',
              data: {}
            });

            wx.hideLoading();

            if (result.result.success) {
              wx.showToast({
                title: `清理成功: ${result.result.deleted}个`,
                icon: 'success'
              });
              
              // 重新加载竞赛列表
              this.loadCompetitions();
            } else {
              throw new Error(result.result.message || '清理失败');
            }
          } catch (error) {
            wx.hideLoading();
            console.error('清理系统测试竞赛失败:', error);
            wx.showToast({
              title: error.message || '清理失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },



  // 参与竞赛
  async onJoinCompetition(e) {
    const competitionId = e.currentTarget.dataset.id;
    const competitionType = e.currentTarget.dataset.type;
    const competition = this.data.competitions[this.data.currentMode].find(c => c.id === competitionId);
    
    console.log('点击参与竞赛:', {
      competitionId,
      competitionType,
      competition: competition ? {
        id: competition.id,
        name: competition.name,
        type: competition.type
      } : null
    });
    
    if (!competition) {
      wx.showToast({
        title: '竞赛不存在',
        icon: 'none'
      });
      return;
    }

    // 检查用户登录状态
    const app = getApp();
    if (!app.isLoggedIn()) {
      wx.showModal({
        title: '需要登录',
        content: '参与竞赛需要登录，是否立即登录？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 如果竞赛的子关卡还在创建中，提示用户等待
    if (competition.levelCreationStatus === 'creating') {
      wx.showModal({
        title: '关卡创建中',
        content: `竞赛"${competition.name}"正在创建关卡中（${competition.levelCreationProgress || 0}%），请稍后再试。`,
        showCancel: false
      });
      return;
    }

    // 如果是分组竞赛，跳转到关卡选择页面
    // 检查多个条件来确定是否为多关卡竞赛
    const isGroupedCompetition = competition.type === 'master' || 
                                competitionType === 'master' ||
                                competition.totalLevels > 1 ||
                                competition.levelCompetitions;
    
    if (isGroupedCompetition) {
      console.log('检测到多关卡竞赛，跳转到关卡选择页面');
      wx.navigateTo({
        url: `/pages/competition/level-select/level-select?masterCompetitionId=${competitionId}&mode=${this.data.currentMode}&competitionName=${encodeURIComponent(competition.name)}`
      });
      return;
    }

    // 单一竞赛的处理逻辑（原有逻辑）
    wx.showLoading({
      title: '加载竞赛...',
      mask: true
    });

    try {
      // 获取竞赛详情和词汇数据
      const result = await wx.cloud.callFunction({
        name: 'getCompetitionDetail',
        data: {
          competitionId: competitionId
        }
      });

      wx.hideLoading();

      if (result.result.success) {
        const competitionData = result.result.data;
        
        // 根据不同模式跳转到不同页面
        if (this.data.currentMode === 'elimination') {
          // 消消乐模式 - 需要先设置全局数据
          const app = getApp();
          // 转换词汇格式为消消乐需要的格式
          const gameWords = competitionData.words.map(word => ({
            english: word.words || word.word || word.english,
            chinese: word.meaning || word.chinese || '无释义'
          }));
          
          app.globalData.eliminationGameData = {
            words: gameWords,
            currentGroup: 1,
            totalGroups: 1,
            libraryId: competitionData.libraryId,
            libraryName: competitionData.libraryName,
            competitionMode: true,
            competitionId: competitionId
          };
          
          wx.navigateTo({
            url: `/pages/task/puzzle/puzzle?competitionId=${competitionId}&mode=custom&gameMode=${competitionData.gameMode || '60'}`
          });
        } else if (this.data.currentMode === 'dictation') {
          // 听写模式
          wx.navigateTo({
            url: `/pages/spelling/practice/practice?competitionId=${competitionId}&mode=competition&practiceMode=test`
          });
        } else {
          // 英译汉/汉译英模式 - 需要先设置学习数据
          const app = getApp();
          app.globalData.learningData = {
            words: competitionData.words,
            libraryId: competitionData.libraryId,
            libraryName: competitionData.libraryName
          };
          
          wx.navigateTo({
            url: `/pages/wordtest/test/test?competitionId=${competitionId}&mode=${this.data.currentMode}&shareMode=competition`
          });
        }
      } else {
        throw new Error(result.result.message || '获取竞赛详情失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载竞赛失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 查看排行榜
  onViewRanking(e) {
    const competitionId = e.currentTarget.dataset.id;
    
    // 跳转到独立的排行榜页面
    wx.navigateTo({
      url: `/pages/competition/ranking/ranking?competitionId=${competitionId}&mode=${this.data.currentMode}`
    });
  },

  // 分享竞赛
  onShareCompetition(e) {
    const competitionId = e.currentTarget.dataset.id;
    const competition = this.data.competitions[this.data.currentMode].find(c => c.id === competitionId);

    if (!competition) {
      wx.showToast({
        title: '竞赛不存在',
        icon: 'none'
      });
      return;
    }

    // 设置当前分享的竞赛数据
    const modeText = this.data.modes.find(m => m.id === this.data.currentMode)?.name || '单词';
    this.setData({
      currentShareCompetition: {
        id: competition.id,
        name: competition.name,
        title: `🏆 ${competition.name} - ${modeText}竞赛`,
        path: `/pages/competition/competition?mode=${this.data.currentMode}&highlightId=${competition.id}`,
        imageUrl: '/assets/icons/logo.png'
      }
    });

    // 显示分享弹窗，使用button组件实现直接分享
    this.setData({
      showShareModal: true
    });
  },

  // 复制竞赛ID
  copyCompetitionId(competition) {
    const competitionId = competition.id;
    const modeText = this.data.modes.find(m => m.id === this.data.currentMode)?.name || '单词';

    const shareText = `🏆 墨词自习室${modeText}竞赛邀请\n\n📝 ${competition.name}\n🆔 竞赛ID: ${competitionId}\n\n📲 参与方式：\n1. 打开"墨词自习室"小程序\n2. 进入"竞赛"页面\n3. 点击对应竞赛参与\n\n快来挑战吧！`;

    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '竞赛信息已复制',
          icon: 'success'
        });
        setTimeout(() => {
          wx.showModal({
            title: '竞赛信息已复制',
            content: '完整的竞赛信息已复制到剪贴板，可以发送给朋友邀请参与！',
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1500);
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        });
      }
    });
  },

  // 分享给微信好友
  shareToWeChatFriends(competition) {
    const modeText = this.data.modes.find(m => m.id === this.data.currentMode)?.name || '单词';

    // 设置分享数据，然后提示用户使用右上角分享
    this.setData({
      currentShareData: {
        title: `🏆 ${competition.name} - ${modeText}竞赛`,
        path: `/pages/competition/competition?mode=${this.data.currentMode}&highlightId=${competition.id}`,
        imageUrl: '/assets/icons/logo.png'
      }
    });

    // 提示用户使用右上角分享按钮
    wx.showModal({
      title: '分享给微信好友',
      content: '请点击右上角"..."按钮，选择"转发"来分享给朋友，朋友点击卡片即可直接参与竞赛！',
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 更新分享菜单，确保右上角分享可用
        wx.updateShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage']
        });
      }
    });
  },

  // 分享到朋友圈
  shareToWeChatMoments(competition) {
    const modeText = this.data.modes.find(m => m.id === this.data.currentMode)?.name || '单词';

    // 设置朋友圈分享数据
    this.setData({
      currentTimelineShareData: {
        title: `🏆 ${competition.name} - ${modeText}竞赛挑战`,
        query: `mode=${this.data.currentMode}&highlightId=${competition.id}`,
        imageUrl: '/assets/icons/logo.png'
      }
    });

    // 提示用户使用右上角分享到朋友圈
    wx.showModal({
      title: '分享到朋友圈',
      content: '请点击右上角"..."按钮，选择"分享到朋友圈"来分享竞赛，朋友看到后可以直接参与挑战！',
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 更新分享菜单，确保朋友圈分享可用
        wx.updateShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline']
        });
      }
    });
  },

  // 关闭分享弹窗
  closeShareModal() {
    this.setData({
      showShareModal: false
    });
  },

  // 复制竞赛信息
  onCopyCompetitionInfo() {
    const { currentShareCompetition } = this.data;
    if (currentShareCompetition) {
      this.copyCompetitionId({ id: currentShareCompetition.id, name: currentShareCompetition.name });
    }
    this.closeShareModal();
  },

  // 分享给微信好友按钮点击
  onShareToWeChatFriends() {
    // 关闭分享弹窗，让 open-type="share" 触发系统分享
    this.closeShareModal();
    // 分享数据通过 onShareAppMessage 回调提供
  },



  // 分享到朋友圈按钮点击
  onShareToTimeline() {
    // 设置朋友圈分享数据
    const { currentShareCompetition } = this.data;
    if (currentShareCompetition) {
      this.setData({
        currentTimelineShareData: {
          title: `${currentShareCompetition.title}挑战`,
          query: `mode=${this.data.currentMode}&highlightId=${currentShareCompetition.id}`,
          imageUrl: '/assets/icons/logo.png'
        }
      });
    }
    this.closeShareModal();

    // 更新分享菜单，启用朋友圈分享
    wx.updateShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        // 提示用户点击右上角分享到朋友圈
        wx.showModal({
          title: '分享到朋友圈',
          content: '请点击右上角"..."按钮，选择"分享到朋友圈"来分享竞赛！',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    });
  },

  // 保留原有方法以兼容其他调用
  copyCompetitionLink(competition) {
    const link = `pages/competition/join/join?competitionId=${competition.id}`;
    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        });
      }
    });
  },

  shareToWechat(competition) {
    this.directShareToWeChat(competition);
  },

  shareToMoments(competition) {
    this.directShareToTimeline(competition);
  },

  // 切换排序下拉菜单
  toggleSortDropdown() {
    this.setData({
      showSortDropdown: !this.data.showSortDropdown
    });
  },

  // 选择排序方式
  onSortSelect(e) {
    const sortBy = e.currentTarget.dataset.sort;
    
    // 更新排序方式和显示文本
    const sortTextMap = {
      'latest': '按创建时间',
      'participants': '按参与人数',
      'wordCount': '按词汇数量'
    };
    
    this.setData({
      sortBy: sortBy,
      currentSortText: sortTextMap[sortBy] || '按创建顺序',
      showSortDropdown: false // 选择后关闭下拉菜单
    });
    
    this.loadCompetitions();
  },

  // 检查是否可以删除竞赛
  canDeleteCompetition(competition) {
    if (!this.data.currentUserOpenId) {
      return false;
    }
    
    // 创建者或管理员可以删除
    const isCreator = competition.creatorOpenId === this.data.currentUserOpenId;
    const isAdmin = this.data.isAdmin;
    
    return isCreator || isAdmin;
  },

  // 删除竞赛
  async onDeleteCompetition(e) {
    const competitionId = e.currentTarget.dataset.id;
    const competition = this.data.competitions[this.data.currentMode].find(c => c.id === competitionId);
    
    if (!competition) {
      wx.showToast({
        title: '竞赛不存在',
        icon: 'none'
      });
      return;
    }

    // 确认删除
    const typeText = competition.type === 'master' ? '多关卡竞赛' : '竞赛';
    const statusText = competition.levelCreationStatus === 'creating' ? '（关卡创建中）' : '';
    wx.showModal({
      title: '确认删除',
      content: `确定要删除${typeText}"${competition.name}"${statusText}吗？删除后无法恢复。`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
            mask: true
          });

          try {
            const result = await wx.cloud.callFunction({
              name: 'deleteCompetition',
              data: {
                competitionId: competitionId
              }
            });

            wx.hideLoading();

            if (result.result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              
              // 重新加载竞赛列表
              this.loadCompetitions();
            } else {
              throw new Error(result.result.message || '删除失败');
            }
          } catch (error) {
            wx.hideLoading();
            console.error('删除竞赛失败:', error);
            wx.showToast({
              title: error.message || '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 获取模式文本
  getModeText(mode) {
    const modeMap = {
      'en2zh': '英译汉',
      'zh2en': '汉译英', 
      'dictation': '听写',
      'elimination': '消消乐'
    };
    return modeMap[mode] || '未知模式';
  },

  // 格式化时长
  formatDuration(seconds) {
    if (!seconds) return '00:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      return '今天';
    } else if (days === 1) {
      return '昨天';
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  // 页面分享
  onShareAppMessage() {
    const { currentShareCompetition, currentShareData } = this.data;

    // 优先使用竞赛分享数据
    if (currentShareCompetition) {
      console.log('使用竞赛分享数据:', currentShareCompetition);

      return {
        title: currentShareCompetition.title,
        path: currentShareCompetition.path,
        imageUrl: currentShareCompetition.imageUrl,
        success: (res) => {
          console.log('竞赛分享成功', res);
          wx.showToast({ title: '分享成功', icon: 'success' });

          setTimeout(() => {
            wx.showModal({
              title: '分享成功',
              content: '竞赛已成功分享给好友，朋友点击卡片即可直接参与竞赛！',
              showCancel: false,
              confirmText: '知道了'
            });
          }, 500);
        },
        fail: (err) => {
          console.error('竞赛分享失败', err);
          wx.showToast({ title: '分享失败', icon: 'error' });
        }
      };
    }

    // 使用通用分享数据
    if (currentShareData) {
      return {
        title: currentShareData.title,
        path: currentShareData.path,
        imageUrl: currentShareData.imageUrl
      };
    }

    return {
      title: '墨词自习室 - 单词竞赛',
      path: '/pages/competition/competition',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 朋友圈分享
  onShareTimeline() {
    const { currentTimelineShareData } = this.data;

    if (currentTimelineShareData) {
      console.log('使用朋友圈分享数据:', currentTimelineShareData);
      return {
        title: currentTimelineShareData.title,
        query: currentTimelineShareData.query,
        imageUrl: currentTimelineShareData.imageUrl,
        success: (res) => {
          console.log('朋友圈分享成功', res);
          wx.showToast({ title: '分享到朋友圈成功', icon: 'success' });

          setTimeout(() => {
            wx.showModal({
              title: '朋友圈分享成功',
              content: '竞赛已成功分享到朋友圈，朋友看到后可以直接参与挑战！',
              showCancel: false,
              confirmText: '知道了'
            });
          }, 500);
        },
        fail: (err) => {
          console.error('朋友圈分享失败', err);
          wx.showToast({ title: '朋友圈分享失败', icon: 'error' });
        }
      };
    }

    return {
      title: '墨词自习室 - 单词竞赛挑战',
      query: '',
      imageUrl: '/assets/icons/logo.png'
    };
  }
});