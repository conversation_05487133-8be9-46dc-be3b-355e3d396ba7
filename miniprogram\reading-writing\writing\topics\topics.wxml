<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">话题素材</text>
      <text class="header-subtitle">丰富写作素材库</text>
    </view>
    <view class="header-decoration">
      <text class="decoration-icon">📝</text>
    </view>
  </view>

  <view class="topics-list">
    <view class="topic-card"
          wx:for="{{topics}}"
          wx:key="id"
          bindtap="onTopicTap"
          data-id="{{item.id}}"
          style="background: {{item.gradient}}">
      <view class="topic-left">
        <view class="topic-icon">
          <text class="icon-text">{{item.icon}}</text>
        </view>
        <view class="topic-content">
          <text class="topic-title">{{item.title}}</text>
          <text class="topic-subtitle">{{item.subtitle}}</text>
        </view>
      </view>
      <view class="topic-arrow">
        <text class="arrow-text">→</text>
      </view>
    </view>
  </view>

  <!-- 底部提示 -->
  <view class="bottom-notice">
    <view class="notice-icon">⚠️</view>
    <text class="notice-text">注意：这只是示例内容，不是正式内容，具体内容正在完善中</text>
  </view>
</view>