/* 教师工具箱页面样式 */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  box-sizing: border-box;
}

/* 页面头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}



/* 工具容器 */
.tools-container {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-left: 12rpx;
  border-left: 8rpx solid #4F46E5;
}

.tools-grid {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.tool-card {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.tool-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.tool-card.disabled {
  opacity: 0.6;
}

.tool-card.disabled:active {
  transform: none;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 32rpx 24rpx 16rpx;
  background: linear-gradient(135deg, #4F46E5 0%, #3B82F6 100%);
  color: white;
  display: flex;
  align-items: center;
  position: relative;
  min-height: 120rpx;
}

/* 蓝色主题 */
.card-header.blue {
  background: linear-gradient(135deg, #4F46E5 0%, #3B82F6 100%);
}

/* 绿色主题 */
.card-header.green {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

/* 紫色主题 */
.card-header.purple {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}

.tool-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  line-height: 1;
}

.tool-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tool-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  line-height: 1.2;
  display: block;
}

.tool-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.3;
  display: block;
}

.developing-badge {
  background: rgba(255, 193, 7, 0.9);
  color: #856404;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-weight: bold;
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 1;
}

.card-content {
  padding: 24rpx;
  background: white;
}

.tool-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 使用贴士 */
.tips-section {
  margin-bottom: 40rpx;
}

.tips-container {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 12rpx 0;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-dot {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 2rpx;
  line-height: 1;
  flex-shrink: 0;
}

.tip-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
} 