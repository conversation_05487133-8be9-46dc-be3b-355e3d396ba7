<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">自定义检测</text>
      <text class="page-subtitle">输入或上传您要练习的单词</text>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-header">
      <text class="section-title">内容输入</text>
      <text class="section-desc">支持多种格式，程序会智能识别</text>
    </view>
    
    <view class="input-area">
      <textarea 
        class="input-text"
        placeholder="支持多种格式：简单格式(apple 苹果)、带音标(apple /ˈæpl/ 苹果)、带例句(apple /ˈæpl/ 苹果 I like apple.)或直接输入单词列表，程序会智能处理"
        value="{{inputText}}"
        bindinput="onInputChange"
        maxlength="5000"
        show-confirm-bar="{{false}}"
      />
      <view class="input-tools">
        <view class="tool-btn" bindtap="onChooseImage">
          <text class="tool-icon">📷</text>
          <text class="tool-text">拍照识别</text>
        </view>
        <view class="tool-btn" bindtap="onManualAdd">
          <text class="tool-icon">➕</text>
          <text class="tool-text">单个添加</text>
        </view>
        <view class="tool-btn" bindtap="onBatchAdd">
          <text class="tool-icon">📝</text>
          <text class="tool-text">批量添加</text>
        </view>
        <view class="tool-btn" bindtap="onImportExample">
          <text class="tool-icon">💡</text>
          <text class="tool-text">导入示例</text>
        </view>
      </view>
    </view>
    
    <view class="ai-process-btn" bindtap="onAIProcess" disabled="{{isProcessing}}">
      <text class="ai-icon">🤖</text>
      <text class="ai-text">{{isProcessing ? '智能处理中...' : '智能识别单词'}}</text>
    </view>
  </view>

  <!-- 单词列表 -->
  <view class="words-section" wx:if="{{words.length > 0}}">
    <view class="section-header">
      <text class="section-title">识别的单词 ({{words.length}}个)</text>
      <text class="section-desc">可编辑、删除或添加更多单词</text>
      <view class="section-actions">
        <view class="action-link" bindtap="onClearAll">清空全部</view>
      </view>
    </view>
    
    <view class="words-list">
      <view 
        class="word-item" 
        wx:for="{{words}}" 
        wx:key="index"
      >
        <view class="word-content">
          <view class="word-main">
            <view class="word-header">
              <text class="word-text">{{item.word}}</text>
              <text class="word-phonetic" wx:if="{{item.phonetic}}">{{item.phonetic}}</text>
            </view>
            <text class="word-definition">{{item.definition || '暂无释义'}}</text>
            <text class="word-example" wx:if="{{item.example}}">例句：{{item.example}}</text>
          </view>
          <view class="word-actions">
            <view class="action-btn edit" bindtap="onEditWord" data-index="{{index}}">
              <text class="action-icon">✏️</text>
            </view>
            <view class="action-btn delete" bindtap="onDeleteWord" data-index="{{index}}">
              <text class="action-icon">🗑️</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 模式选择 -->
  <view class="mode-section" wx:if="{{words.length > 0}}">
    <view class="section-header">
      <text class="section-title">选择练习模式</text>
      <text class="section-desc">请选择一种练习模式</text>

    </view>
    
    <view class="mode-list">
      <view 
        class="mode-item {{item.selected ? 'selected' : ''}}" 
        wx:for="{{modeItems}}" 
        wx:key="id"
        bindtap="onModeToggle"
        data-id="{{item.id}}"
      >
        <view class="mode-icon">{{item.icon}}</view>
        <view class="mode-text">
          <text class="mode-name">{{item.name}}</text>
          <text class="mode-desc">{{item.desc}}</text>
        </view>
        <view class="mode-check-container">
          <view wx:if="{{item.selected}}" 
                class="mode-check-selected">
            <text class="check-text">✓</text>
          </view>
          <view wx:else 
                class="mode-check-normal">
            <text class="check-text">○</text>
          </view>

        </view>
      </view>
    </view>
  </view>

  <!-- 开始练习按钮 -->
  <view class="start-section" wx:if="{{words.length > 0}}">
    <view class="start-btn {{selectedModes.length === 0 ? 'disabled' : ''}}" bindtap="onStartPractice">
      <text class="start-text">开始练习</text>
      <text class="start-icon">🚀</text>
    </view>
    <view class="start-tip" wx:if="{{selectedModes.length === 0}}">请先选择练习模式</view>
  </view>

  <!-- 使用说明 -->
  <view class="help-section" wx:if="{{words.length === 0}}">
    <view class="help-content">
      <view class="help-title">📝 使用说明</view>
      <view class="help-list">
        <view class="help-item">
          <text class="help-number">1</text>
          <text class="help-text">在上方输入框输入单词，支持多种格式</text>
        </view>
        <view class="help-item">
          <text class="help-number">2</text>
          <text class="help-text">可以拍照识别图片中的单词</text>
        </view>
        <view class="help-item">
          <text class="help-number">3</text>
          <text class="help-text">点击"智能识别单词"自动整理</text>
        </view>
        <view class="help-item">
          <text class="help-number">4</text>
          <text class="help-text">选择喜欢的练习模式，开始学习</text>
        </view>
      </view>
      
      <view class="format-examples">
        <view class="format-title">💡 支持的输入格式</view>
        <view class="format-item">
          <text class="format-name">简单格式：</text>
          <text class="format-example">apple 苹果</text>
        </view>
        <view class="format-item">
          <text class="format-name">带音标：</text>
          <text class="format-example">apple /ˈæpl/ 苹果</text>
        </view>
        <view class="format-item">
          <text class="format-name">带例句：</text>
          <text class="format-example">apple /ˈæpl/ 苹果 I like apples.</text>
        </view>
        <view class="format-item">
          <text class="format-name">纯单词：</text>
          <text class="format-example">apple, banana, orange</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 单词输入弹窗 -->
  <view class="word-input-modal" wx:if="{{showWordInputModal}}">
    <view class="modal-mask" bindtap="onCloseWordInput"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">{{editingIndex !== undefined ? '编辑单词' : '添加单词'}}</text>
        <view class="modal-close" bindtap="onCloseWordInput">✕</view>
      </view>
      
      <view class="modal-body">
        <view class="input-group">
          <text class="input-label">英文单词 *</text>
          <input 
            class="input-field" 
            placeholder="请输入英文单词" 
            value="{{tempWord.word}}"
            bindinput="onWordInput"
            maxlength="50"
          />
        </view>
        
        <view class="input-group">
          <text class="input-label">中文释义 *</text>
          <input 
            class="input-field" 
            placeholder="请输入中文释义" 
            value="{{tempWord.definition}}"
            bindinput="onDefinitionInput"
            maxlength="100"
          />
        </view>
        
        <view class="input-group">
          <text class="input-label">音标（可选）</text>
          <input 
            class="input-field" 
            placeholder="请输入音标，如：/ˈæpl/" 
            value="{{tempWord.phonetic}}"
            bindinput="onPhoneticInput"
            maxlength="30"
          />
        </view>
        
        <view class="input-group">
          <text class="input-label">例句（可选）</text>
          <textarea 
            class="textarea-field" 
            placeholder="请输入例句" 
            value="{{tempWord.example}}"
            bindinput="onExampleInput"
            maxlength="200"
            auto-height="{{false}}"
            show-confirm-bar="{{false}}"
          />
        </view>
      </view>
      
      <view class="modal-footer">
        <view class="modal-btn cancel" bindtap="onCloseWordInput">取消</view>
        <view class="modal-btn confirm" bindtap="{{editingIndex !== undefined ? 'onConfirmEditWord' : 'onConfirmAddWord'}}">
          {{editingIndex !== undefined ? '保存' : '添加'}}
        </view>
      </view>
    </view>
  </view>
</view> 