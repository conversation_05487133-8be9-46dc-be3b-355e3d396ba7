<!--pages/profile/share/share.wxml-->
<view class="share-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">我的分享</text>
    <text class="page-subtitle">管理你创建的分享测试</text>
    <view class="header-actions" wx:if="{{!isEmpty && !loading}}">
      <button 
        class="edit-btn {{isEditMode ? 'active' : ''}}" 
        bindtap="toggleEditMode"
      >
        {{isEditMode ? '完成' : '管理'}}
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{isEmpty}}">
    <view class="empty-icon">📝</view>
    <text class="empty-title">暂无分享测试</text>
    <text class="empty-desc">创建分享测试后，测试信息将显示在这里</text>
    <button class="empty-btn" bindtap="goToCreateShare">创建分享测试</button>
  </view>

  <!-- 编辑模式工具栏 -->
  <view class="edit-toolbar" wx:if="{{isEditMode && !isEmpty}}">
    <view class="toolbar-info">
      <text class="selected-count">已选择 {{selectedTests.length}} 项</text>
    </view>
    <view class="toolbar-actions">
      <button 
        class="batch-btn danger" 
        bindtap="batchDeleteTests"
        disabled="{{selectedTests.length === 0}}"
      >
        批量删除
      </button>
    </view>
  </view>



  <!-- 分享测试按模式分组列表 -->
  <view class="mode-groups" wx:if="{{!isEmpty && !loading}}">
    <view 
      class="mode-group" 
      wx:for="{{modeGroups}}" 
      wx:key="mode"
      wx:for-item="modeGroup"
    >
      <!-- 模式标题栏 -->
      <view 
        class="mode-header"
        bindtap="toggleModeExpand"
        data-mode="{{modeGroup.mode}}"
      >
        <view class="mode-info">
          <text class="mode-icon">{{modeGroup.icon}}</text>
          <text class="mode-title">{{modeGroup.name}}</text>
          <text class="mode-count">({{modeGroup.stats.totalTests}})</text>
        </view>
        <view class="mode-stats" wx:if="{{modeGroup.expanded}}">
          <text class="stat">{{modeGroup.stats.totalVisitors}}访问</text>
          <text class="stat">{{modeGroup.stats.totalAttempts}}测试</text>
        </view>
        <view class="mode-actions">
          <button 
            class="clear-btn" 
            catchtap="clearModeTests"
            data-mode="{{modeGroup.mode}}"
            wx:if="{{modeGroup.expanded}}"
          >
            清空
          </button>
          <text class="expand-icon {{modeGroup.expanded ? 'expanded' : ''}}">▼</text>
        </view>
      </view>

      <!-- 测试列表 -->
      <view
        class="test-list {{modeGroup.expanded ? 'expanded' : ''}}"
        wx:if="{{modeGroup.expanded && modeGroup.tests.length > 0}}"
      >
        <view 
          class="test-card {{isEditMode ? 'edit-mode' : ''}} {{isEditMode && test.isSelected ? 'selected' : ''}}"
          wx:for="{{modeGroup.tests}}" 
          wx:key="shareId"
          wx:for-item="test"
          bindlongpress="onShareTestLongPress"
          data-share-id="{{test.shareId}}"
          data-test-mode="{{test.testMode}}"
        >
          <!-- 选择框 (编辑模式) -->
          <view 
            wx:if="{{isEditMode}}" 
            class="test-selector"
            catchtap="toggleSelectTest" 
            data-share-id="{{test.shareId}}"
          >
            <view class="selector-circle {{test.isSelected ? 'selected' : ''}}">
              <text wx:if="{{test.isSelected}}" class="selector-check">✓</text>
            </view>
          </view>

          <!-- 测试信息 -->
          <view class="test-info">
            <view class="test-header">
              <view class="title-row">
                <text class="test-title">测试 {{test.shareId.split('_')[test.shareId.split('_').length-1]}}</text>
                <view class="expire-warning" wx:if="{{test.soonExpire}}">
                  <text class="expire-text">{{test.expireDays}}天后过期</text>
                </view>
              </view>
              <text class="create-time">{{formatTime(test.createTime)}}</text>
            </view>
            
            <view class="test-stats">
              <view class="stat-item">
                <text class="stat-number">{{test.wordCount}}</text>
                <text class="stat-label">单词</text>
              </view>
              <view class="stat-item" wx:if="{{test.isMultiLevel || test.totalGroups > 1}}">
                <text class="stat-number">{{test.totalGroups}}</text>
                <text class="stat-label">关卡</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{test.visitorCount}}</text>
                <text class="stat-label">访问</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{test.testCount}}</text>
                <text class="stat-label">测试</text>
              </view>
              <view class="stat-item" wx:if="{{test.averageScore > 0}}">
                <text class="stat-number">{{test.averageScore}}</text>
                <text class="stat-label">平均分</text>
              </view>
            </view>
          </view>

          <!-- 操作按钮 (非编辑模式) -->
          <view class="test-actions" wx:if="{{!isEditMode}}">
            <button
              class="action-btn secondary"
              data-share-id="{{test.shareId}}"
              bindtap="viewVisitors"
            >
              进度
            </button>
            <button
              class="action-btn primary share-btn"
              data-share-id="{{test.shareId}}"
              data-test-mode="{{test.testMode}}"
              data-word-count="{{test.wordCount}}"
              bindtap="showShareOptions"
            >
              分享
            </button>
            <button
              class="action-btn danger"
              data-share-id="{{test.shareId}}"
              data-test-mode="{{test.testMode}}"
              bindtap="deleteShareTest"
            >
              删除
            </button>
          </view>
        </view>
      </view>

      <!-- 模式分页控件 -->
      <view
        class="mode-pagination"
        wx:if="{{modeGroup.expanded && modePagination[modeGroup.mode] && modePagination[modeGroup.mode].totalPages > 1}}"
      >
        <view class="mode-pagination-controls">
          <button
            class="mode-page-btn"
            bindtap="modePrevPage"
            data-mode="{{modeGroup.mode}}"
            disabled="{{modePagination[modeGroup.mode].currentPage <= 1}}"
          >
            上一页
          </button>

          <view class="mode-page-info">
            <text class="mode-current-page">{{modePagination[modeGroup.mode].currentPage}}</text>
            <text class="mode-page-separator">/</text>
            <text class="mode-total-pages">{{modePagination[modeGroup.mode].totalPages}}</text>
          </view>

          <button
            class="mode-page-btn"
            bindtap="modeNextPage"
            data-mode="{{modeGroup.mode}}"
            disabled="{{modePagination[modeGroup.mode].currentPage >= modePagination[modeGroup.mode].totalPages}}"
          >
            下一页
          </button>
        </view>

        <!-- 跳转到指定页 -->
        <view class="mode-jump-to-page">
          <text class="mode-jump-label">跳转到第</text>
          <input
            class="mode-page-input"
            type="number"
            placeholder="{{modePagination[modeGroup.mode].totalPages}}"
            bindconfirm="modeJumpToPage"
            bindinput="onModePageInputChange"
            data-mode="{{modeGroup.mode}}"
            maxlength="2"
            value="{{modePagination[modeGroup.mode].inputPage || ''}}"
          />
          <text class="mode-jump-label">页</text>
          <button
            class="mode-jump-btn"
            bindtap="modeJumpToPage"
            data-mode="{{modeGroup.mode}}"
          >
            跳转
          </button>
        </view>
      </view>
    </view>
  </view>



  <!-- 使用说明 -->
  <view class="help-section" wx:if="{{!isEmpty && !loading && !isEditMode}}">
    <view class="help-title">💡 使用说明</view>
    <view class="help-content">
      <text class="help-item">• 点击模式标题展开/折叠测试列表</text>
      <text class="help-item">• 每个模式都有独立的分页功能</text>
      <text class="help-item">• 点击"查看进度"管理访问者和查看结果</text>
      <text class="help-item">• 点击"清空"删除该模式下所有测试</text>
      <text class="help-item">• 长按测试卡片显示更多操作</text>
    </view>
  </view>

  <!-- 浮动创建按钮 -->
  <view class="floating-create-btn" wx:if="{{!loading && !isEditMode}}" bindtap="goToCreateShare">
    <view class="fab-icon">+</view>
  </view>
</view>

<!-- 分享选项弹窗 -->
<view class="share-modal" wx:if="{{showShareModal}}" bindtap="closeShareModal">
  <view class="share-content" catchtap="">
    <view class="share-header">
      <text class="share-title">分享测试</text>
      <view class="close-btn" bindtap="closeShareModal">×</view>
    </view>

    <view class="share-info">
      <text class="share-desc">选择分享方式，将测试分享给朋友</text>
      <text class="share-note">朋友可以通过分享链接参与测试</text>
    </view>

    <view class="share-buttons">
      <button class="share-btn copy-btn" bindtap="copyTestInfo">
        <view class="share-btn-icon">📋</view>
        <text class="share-btn-text">复制测试信息</text>
      </button>

      <button
        class="share-btn wechat-share-btn"
        open-type="share"
        bindtap="onShareToWeChatFriends"
      >
        <view class="share-btn-icon">📤</view>
        <text class="share-btn-text">分享给微信好友</text>
      </button>

      <button class="share-btn timeline-btn" bindtap="shareToMoments">
        <view class="share-btn-icon">⭐</view>
        <text class="share-btn-text">分享到朋友圈</text>
      </button>
    </view>
  </view>
</view>