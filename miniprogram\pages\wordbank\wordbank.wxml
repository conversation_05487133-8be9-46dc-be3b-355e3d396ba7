<view class="container">
  <!-- 自定义返回按钮 -->
  <view class="custom-nav" wx:if="{{returnTo}}" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="onBack">
      <text class="back-icon">‹</text>
      <text class="back-text">返回</text>
    </view>
  </view>

  <!-- 自定义词库 -->
  <view class="section">
    <view class="section-title">✏️ 自定义词库</view>
    
    <!-- 创建新词库按钮 -->
    <view class="create-wordbank-card" bindtap="showCreateWordbankModal">
      <view class="create-icon">➕</view>
      <view class="create-text">
        <text class="create-title">创建词库</text>
        <text class="create-desc">添加您的自定义词汇集</text>
      </view>
    </view>

    <!-- 批量操作工具栏 -->
    <view class="batch-toolbar" wx:if="{{customWordbanks.length > 0}}">
      <view wx:if="{{!batchDeleteMode}}" class="toolbar-normal">
        <button class="batch-btn" bindtap="enterBatchDeleteMode">批量删除</button>
      </view>
      <view wx:else class="toolbar-batch">
        <button class="batch-btn cancel" bindtap="exitBatchDeleteMode">取消</button>
        <button class="batch-btn select-all" bindtap="toggleSelectAll">全选</button>
        <button class="batch-btn delete" bindtap="batchDeleteWordbanks">删除选中</button>
      </view>
    </view>

    <!-- 用户创建的词库列表 -->
    <view class="custom-wordbank-list" wx:if="{{customWordbanks.length > 0}}">
      <block wx:for="{{customWordbanks}}" wx:for-item="wordbank" wx:key="id">
        <view class="custom-wordbank-item {{wordbank.selected ? 'selected' : ''}}" 
              bindtap="{{batchDeleteMode ? 'toggleWordbankSelection' : 'onCustomWordbankTap'}}" 
              data-id="{{wordbank.id}}">
          
          <!-- 批量删除模式的选择框 -->
          <view class="selection-checkbox" wx:if="{{batchDeleteMode}}">
            <icon type="{{wordbank.selected ? 'success' : 'circle'}}" size="20"/>
          </view>
          
          <!-- 词库名称 -->
          <view class="custom-name">{{wordbank.name}}</view>
          <text class="custom-count">{{wordbank.wordCount}}词</text>
          
          <!-- 操作按钮（仅非批量模式显示） -->
          <view class="custom-actions" wx:if="{{!batchDeleteMode}}">
            <view class="action-btn edit" catchtap="editCustomWordbank" data-id="{{wordbank.id}}">
              <text class="action-icon">✏️</text>
            </view>
            <view class="action-btn delete" catchtap="deleteCustomWordbank" data-id="{{wordbank.id}}">
              <text class="action-icon">🗑️</text>
            </view>
          </view>
          
          <!-- 箭头指示（仅非批量模式显示） -->
          <view class="custom-arrow" wx:if="{{!batchDeleteMode}}">
            <text>›</text>
          </view>
        </view>
      </block>
    </view>

    <!-- 格式说明 -->
    <view class="format-tips">
      <text class="tips-title">词库创建方式：</text>
      <text class="tips-item">📝 文本输入：必须包含单词和释义</text>
      <text class="tips-item">• apple 苹果（单词+释义）</text>
      <text class="tips-item">• apple /ˈæpl/ 苹果（单词+音标+释义）</text>
      <text class="tips-item">• apple /ˈæpl/ 苹果 I like apples.（完整格式）</text>
      <text class="tips-item">📊 Excel批量导入：支持words/phonetic/meaning/example列</text>
      <text class="tips-item">🔄 自动去重：重复单词自动过滤</text>
    </view>
  </view>

  <!-- 大学词汇 -->
  <view class="section">
    <view class="section-title">🎓 大学词汇</view>
    <view class="wordbank-grid">
      <block wx:for="{{wordbanks.college}}" wx:for-item="wordbank" wx:key="id">
        <view class="wordbank-card college-card" 
              bindtap="onWordbankTap" 
              data-id="{{wordbank.id}}" 
              data-type="college">
          <view class="wordbank-icon">
            <text class="icon-text">{{wordbank.id === 'college_cet4' ? '4️⃣' : '6️⃣'}}</text>
          </view>
          <view class="wordbank-info">
            <view class="wordbank-name">{{wordbank.name}}</view>
            <view class="wordbank-count">{{wordbank.count}}词</view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 高考大纲词汇 -->
  <view class="section">
    <view class="section-title">📚 高考大纲词汇</view>
    <view class="wordbank-grid">
      <block wx:for="{{wordbanks.gaokao}}" wx:for-item="wordbank" wx:key="id">
        <view class="wordbank-card gaokao-card" 
              bindtap="onWordbankTap" 
              data-id="{{wordbank.id}}" 
              data-type="gaokao">
          <view class="wordbank-icon">
            <text class="icon-text">🎯</text>
          </view>
          <view class="wordbank-info">
            <view class="wordbank-name">{{wordbank.name}}</view>
            <view class="wordbank-count">{{wordbank.count}}词</view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 教材同步词汇 -->
  <view class="section">
    <view class="section-title">📖 教材同步词汇</view>
    <view class="wordbank-grid">
      <view class="wordbank-card textbook-card" 
            bindtap="onWordbankTap" 
            data-series="renjiao" 
            data-type="textbook">
        <view class="wordbank-icon">
          <text class="icon-text">📘</text>
        </view>
        <view class="wordbank-info">
          <view class="wordbank-name">{{wordbanks.textbook.renjiao.name}}</view>
          <view class="wordbank-count">{{wordbanks.textbook.renjiao.count}}</view>
        </view>
      </view>
      <view class="wordbank-card textbook-card" 
            bindtap="onWordbankTap" 
            data-series="beishi" 
            data-type="textbook">
        <view class="wordbank-icon">
          <text class="icon-text">📗</text>
        </view>
        <view class="wordbank-info">
          <view class="wordbank-name">{{wordbanks.textbook.beishi.name}}</view>
          <view class="wordbank-count">{{wordbanks.textbook.beishi.count}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 常用短语 -->
  <view class="section">
    <view class="section-title">💬 常用短语</view>
    <view class="wordbank-grid">
      <block wx:for="{{wordbanks.phrase}}" wx:for-item="wordbank" wx:key="id">
        <view class="wordbank-card phrase-card" 
              bindtap="onWordbankTap" 
              data-id="{{wordbank.id}}" 
              data-type="phrase">
          <view class="wordbank-icon">
            <text class="icon-text">
              {{wordbank.id === 'phrase_gaopin' ? '🔥' : '⚠️'}}
            </text>
          </view>
          <view class="wordbank-info">
            <view class="wordbank-name">{{wordbank.name}}</view>
            <view class="wordbank-count">{{wordbank.count}}</view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 题型专项词汇 -->
  <view class="section">
    <view class="section-title">📝 题型专项词汇</view>
    <view class="wordbank-grid">
      <block wx:for="{{wordbanks.special}}" wx:for-item="wordbank" wx:key="id">
        <view class="wordbank-card special-card" 
              bindtap="onWordbankTap" 
              data-id="{{wordbank.id}}" 
              data-type="special">
          <view class="wordbank-icon">
            <text class="icon-text">
              {{wordbank.id === 'special_wusan_gaopin_beijing' ? '🔥' :
                wordbank.id === 'special_yuedu_tongyong' ? '📖' :
                wordbank.id === 'special_yuedu_beijing' ? '🏛️' :
                wordbank.id === 'special_wanxing_beijing' ? '🧩' : '🎭'}}
            </text>
          </view>
          <view class="wordbank-info">
            <view class="wordbank-name">{{wordbank.name}}</view>
            <view class="wordbank-count">{{wordbank.count}}词</view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 其他词汇 -->
  <view class="section">
    <view class="section-title">📌 其他词汇</view>
    <view class="wordbank-grid">
      <block wx:for="{{wordbanks.other}}" wx:for-item="wordbank" wx:key="id">
        <view class="wordbank-card other-card" 
              bindtap="onWordbankTap" 
              data-id="{{wordbank.id}}" 
              data-type="other">
          <view class="wordbank-icon">
            <text class="icon-text">
              {{wordbank.id === 'other_buguize' ? '🔄' : 
                wordbank.id === 'other_xingjinci' ? '🔗' : '📋'}}
            </text>
          </view>
          <view class="wordbank-info">
            <view class="wordbank-name">{{wordbank.name}}</view>
            <view class="wordbank-count">{{wordbank.count}}{{wordbank.id === 'other_xingjinci' ? '组' : '词'}}</view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 中考词汇 -->
  <view class="section">
    <view class="section-title">🎯 中考词汇</view>
    <view class="wordbank-grid">
      <block wx:for="{{wordbanks.zhongkao}}" wx:for-item="wordbank" wx:key="id">
        <view class="wordbank-card zhongkao-card" 
              bindtap="onWordbankTap" 
              data-id="{{wordbank.id}}" 
              data-type="zhongkao">
          <view class="wordbank-icon">
            <text class="icon-text">🎯</text>
          </view>
          <view class="wordbank-info">
            <view class="wordbank-name">{{wordbank.name}}</view>
            <view class="wordbank-count">{{wordbank.count}}词</view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 创建词库弹窗 -->
  <view class="create-modal" wx:if="{{showCreateModal}}">
    <view class="modal-overlay" bindtap="hideCreateModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">创建自定义词库</text>
        <view class="close-btn" bindtap="hideCreateModal">✕</view>
      </view>
      
      <view class="modal-body">
        <!-- 词库名称 -->
        <view class="form-group">
          <text class="form-label">词库名称</text>
          <input class="form-input" 
                 placeholder="请输入词库名称" 
                 value="{{newWordbankName}}" 
                 bindinput="onWordbankNameInput" 
                 maxlength="20"/>
        </view>

        <!-- 输入方式选择 -->
        <view class="form-group">
          <text class="form-label">输入方式</text>
          <view class="input-method-tabs">
            <view class="method-tab {{inputMethod === 'text' ? 'active' : ''}}" 
                  bindtap="switchInputMethod" 
                  data-method="text">
              <text class="tab-icon">📝</text>
              <text class="tab-text">文本输入</text>
            </view>
            <view class="method-tab {{inputMethod === 'excel' ? 'active' : ''}}" 
                  bindtap="switchInputMethod" 
                  data-method="excel">
              <text class="tab-icon">📊</text>
              <text class="tab-text">Excel上传</text>
            </view>
          </view>
        </view>

        <!-- 文本输入模式 -->
        <view wx:if="{{inputMethod === 'text'}}" class="form-group">
          <text class="form-label">词汇内容</text>
          <textarea class="form-textarea" 
                    placeholder="{{textareaPlaceholder}}" 
                    value="{{newWordbankContent}}" 
                    bindinput="onWordbankContentInput" 
                    maxlength="10000"/>
          <text class="char-count">{{newWordbankContent.length}}/10000</text>
        </view>

        <!-- Excel上传模式 -->
        <view wx:if="{{inputMethod === 'excel'}}" class="form-group excel-upload-section">
          <text class="form-label">Excel文件上传</text>
          
          <!-- Excel格式要求说明（折叠式） -->
          <view class="excel-format-requirements">
            <view class="requirement-header" bindtap="toggleFormatRequirements">
              <text class="requirement-title">📋 Excel格式要求</text>
              <text class="toggle-icon">{{showFormatRequirements ? '▲' : '▼'}}</text>
            </view>
            
            <view wx:if="{{showFormatRequirements}}" class="requirement-content">
              <view class="requirement-list">
                <text class="requirement-item">• <text class="highlight">words</text> 或 <text class="highlight">单词</text>（必需）</text>
                <text class="requirement-item">• <text class="highlight">meaning</text> 或 <text class="highlight">释义</text>（必需）</text>
                <text class="requirement-item">• <text class="highlight">phonetic</text> 或 <text class="highlight">音标</text>（可选）</text>
                <text class="requirement-item">• <text class="highlight">example</text> 或 <text class="highlight">例句</text>（可选）</text>
                <text class="requirement-item">• 支持词性标注，最多10000个单词</text>
              </view>
              
              <!-- 示例表格 -->
              <view class="excel-example-table">
                <view class="table-container">
                  <view class="table-row header">
                    <text class="table-cell">words</text>
                    <text class="table-cell">phonetic</text>
                    <text class="table-cell">meaning</text>
                    <text class="table-cell">example</text>
                  </view>
                  <view class="table-row">
                    <text class="table-cell">apple</text>
                    <text class="table-cell">/ˈæpl/</text>
                    <text class="table-cell">n. 苹果</text>
                    <text class="table-cell">I like apples.</text>
                  </view>
                  <view class="table-row">
                    <text class="table-cell">play</text>
                    <text class="table-cell">/pleɪ/</text>
                    <text class="table-cell">v. 玩耍</text>
                    <text class="table-cell">He plays ball.</text>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 下载模板按钮 -->
            <view class="template-download">
              <button class="template-btn" bindtap="downloadExcelTemplate">
                <text class="btn-icon">⬇️</text>
                <text class="btn-text">下载模板</text>
              </button>
            </view>
          </view>
          
          <!-- 文件选择 -->
          <view wx:if="{{!selectedFile}}" class="file-upload-area" bindtap="chooseExcelFile">
            <view class="upload-content">
              <text class="upload-icon">📁</text>
              <text class="upload-title">点击选择Excel文件</text>
              <text class="upload-subtitle">支持 .xlsx / .xls 格式，最多10000个单词</text>
            </view>
          </view>

          <!-- 已选择文件 -->
          <view wx:if="{{selectedFile}}" class="file-info-card">
            <view class="file-preview">
              <text class="file-icon">📄</text>
              <view class="file-details">
                <text class="file-name">{{selectedFile.name}}</text>
                <text class="file-size">{{selectedFile.sizeText || '未知大小'}}</text>
              </view>
            </view>
            <view class="file-actions">
              <button class="file-action-btn" bindtap="chooseExcelFile">更换</button>
              <button class="file-action-btn danger" bindtap="removeSelectedFile">删除</button>
            </view>
          </view>

          <!-- 处理选项 -->
          <view wx:if="{{selectedFile}}" class="process-options">
            <view class="option-item">
              <checkbox checked="{{removeDuplicates}}" bindchange="onRemoveDuplicatesChange"/>
              <text class="option-label">自动去除重复词汇</text>
            </view>
          </view>


        </view>
      </view>

              <!-- 底部按钮 -->
        <view class="modal-footer">
          <button class="modal-btn secondary" bindtap="hideCreateModal">取消</button>
          <button class="modal-btn primary" 
                  bindtap="{{inputMethod === 'excel' && selectedFile ? 'processSelectedFile' : 'createCustomWordbank'}}"
                  disabled="{{!newWordbankName || (inputMethod === 'text' && !newWordbankContent) || (inputMethod === 'excel' && !selectedFile) || processing}}">
            {{inputMethod === 'excel' && selectedFile ? (processing ? '处理中...' : '处理Excel并创建') : '创建词库'}}
          </button>
        </view>
    </view>
  </view>
</view> 