/* 考试模式防作弊样式 */

/* 禁用长按选择和复制 */
.exam-mode {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* 禁用所有文本选择 */
.exam-mode view,
.exam-mode text,
.exam-mode button,
.exam-mode input,
.exam-mode textarea {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* 禁用长按菜单 */
.exam-mode image,
.exam-mode canvas,
.exam-mode video {
  pointer-events: none;
  -webkit-touch-callout: none;
}

/* 考试模式状态指示器 */
.exam-status-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #ff4444, #ff6666);
  z-index: 9999;
  animation: examPulse 2s infinite;
}

@keyframes examPulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* 防作弊提示样式 */
.exam-warning {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 40rpx;
  border-radius: 20rpx;
  text-align: center;
  z-index: 10000;
  max-width: 600rpx;
}

.exam-warning-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #ff4444;
}

.exam-warning-content {
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 30rpx;
}

.exam-warning-button {
  background: #007aff;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  border: none;
  font-size: 28rpx;
}

/* 全屏模式样式 */
.exam-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background: white;
}

/* 禁用滚动 */
.exam-no-scroll {
  overflow: hidden;
  height: 100vh;
}

/* 考试模式下的页面容器 */
.exam-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
} 