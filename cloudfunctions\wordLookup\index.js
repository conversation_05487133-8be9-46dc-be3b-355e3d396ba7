const cloud = require('wx-server-sdk')
const axios = require('axios')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 词库集合映射表（与前端保持一致）
const COLLECTION_NAMES = {
  // 中考词汇
  'zhongkao_1600': 'words_zhongkao',

  // 大学词汇
  'college_cet4': 'words_siji',
  'college_cet6': 'words_liuji',

  // 高考大纲词汇
  'gaokao_3500': 'words_3500',
  'gaokao_3500_luan': 'words_3500_luan',
  'gaokao_weikeduo': 'words_weikeduo',

  // 常用短语
  'phrase_gaopin': 'phrase_gaopin',
  'phrase_hunxiao': 'phrase_hunxiao',

  // 题型专项词汇
  'special_wusan_gaopin_beijing': 'words_bjwusan',
  'special_yuedu_tongyong': 'words_reading_tongyong',
  'special_yuedu_beijing': 'words_reading_beijing',
  'special_wanxing_gaopin_beijing': 'words_wanxing_gaopin_beijing',
  'special_wanxing_shucishengyi_beijing': 'words_wanxing_shucishengyi_beijing',

  // 其他词汇
  'other_buguize': 'words_buguize',
  'other_xingjinci': 'words_xingjinci',
  'other_shucishengyi': 'words_shucishengyi_tongyong',

  // 教材同步词汇（人教版）
  'renjiao_bixiu1': 'words_renjiao_bixiu1',
  'renjiao_bixiu2': 'words_renjiao_bixiu2',
  'renjiao_bixiu3': 'words_renjiao_bixiu3',
  'renjiao_xuanxiu1': 'words_renjiao_xuanxiu1',
  'renjiao_xuanxiu2': 'words_renjiao_xuanxiu2',
  'renjiao_xuanxiu3': 'words_renjiao_xuanxiu3',
  'renjiao_xuanxiu4': 'words_renjiao_xuanxiu4',

  // 教材同步词汇（北师版）
  'beishi_bixiu1': 'words_beishi_bixiu1',
  'beishi_bixiu2': 'words_beishi_bixiu2',
  'beishi_bixiu3': 'words_beishi_bixiu3',
  'beishi_xuanxiu1': 'words_beishi_xuanxiu1',
  'beishi_xuanxiu2': 'words_beishi_xuanxiu2',
  'beishi_xuanxiu3': 'words_beishi_xuanxiu3',
  'beishi_xuanxiu4': 'words_beishi_xuanxiu4'
}

// 免费词典API
const FREE_DICT_API = 'https://api.dictionaryapi.dev/api/v2/entries/en/'

exports.main = async (event, context) => {
  const { words } = event // 词汇数组
  
  if (!words || !Array.isArray(words)) {
    return { code: 400, message: '参数错误' }
  }
  
  const results = []
  
  for (const word of words) {
    const wordLower = word.toLowerCase().trim()
    if (!wordLower) continue
    
    try {
      // 1. 先从云端词库查询
      const cloudResult = await searchInCloudDatabase(wordLower)
      if (cloudResult) {
        results.push({
          word: wordLower,
          chinese: cloudResult.chinese || cloudResult.meaning || '',
          phonetic: cloudResult.phonetic || '',
          example: cloudResult.example || '',
          source: 'cloud'
        })
        continue
      }
      
      // 2. 从免费词典API查询
      const apiResult = await searchInFreeAPI(wordLower)
      if (apiResult) {
        results.push({
          word: wordLower,
          chinese: apiResult.chinese || '',
          phonetic: apiResult.phonetic || '',
          example: apiResult.example || '',
          source: 'api'
        })
        continue
      }
      
      // 3. 都没找到，返回基本信息
      results.push({
        word: wordLower,
        chinese: '',
        phonetic: '',
        example: '',
        source: 'none'
      })
      
    } catch (error) {
      console.error(`查询单词 ${wordLower} 失败:`, error)
      results.push({
        word: wordLower,
        chinese: '',
        phonetic: '',
        example: '',
        source: 'error'
      })
    }
  }
  
  return {
    code: 200,
    data: results
  }
}

// 从云端词库搜索
async function searchInCloudDatabase(word) {
  const collections = Object.values(COLLECTION_NAMES)
  
  for (const collectionName of collections) {
    try {
      // 查找包含该单词的文档
      const res = await db.collection(collectionName)
        .where({
          words: word
        })
        .limit(1)
        .get()
      
      if (res.data && res.data.length > 0) {
        const doc = res.data[0]
        return {
          chinese: doc.chinese || doc.meaning || '',
          phonetic: doc.phonetic || '',
          example: doc.example || doc.sentence || ''
        }
      }
    } catch (error) {
      console.error(`在集合 ${collectionName} 中查询失败:`, error)
      // 继续查询下一个集合
    }
  }
  
  return null
}

// 从免费词典API搜索
async function searchInFreeAPI(word) {
  try {
    const response = await axios.get(FREE_DICT_API + word, {
      timeout: 5000
    })
    
    if (response.data && response.data.length > 0) {
      const entry = response.data[0]
      
      // 提取音标
      let phonetic = ''
      if (entry.phonetic) {
        phonetic = entry.phonetic
      } else if (entry.phonetics && entry.phonetics.length > 0) {
        phonetic = entry.phonetics[0].text || ''
      }
      
      // 提取释义
      let chinese = ''
      if (entry.meanings && entry.meanings.length > 0) {
        const meaning = entry.meanings[0]
        if (meaning.definitions && meaning.definitions.length > 0) {
          chinese = meaning.definitions[0].definition || ''
        }
      }
      
      // 提取例句
      let example = ''
      if (entry.meanings && entry.meanings.length > 0) {
        const meaning = entry.meanings[0]
        if (meaning.definitions && meaning.definitions.length > 0) {
          example = meaning.definitions[0].example || ''
        }
      }
      
      return {
        chinese,
        phonetic,
        example
      }
    }
  } catch (error) {
    console.error(`API查询单词 ${word} 失败:`, error.message)
  }
  
  return null
} 