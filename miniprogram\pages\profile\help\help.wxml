<!--pages/profile/help/help.wxml-->

<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">帮助与反馈</text>
      <text class="page-subtitle">遇到问题？告诉我们您的想法</text>
    </view>
  </view>

  <!-- Tab切换 -->
  <view class="tab-container">
    <view class="tab-item {{currentTab === 'submit' ? 'active' : ''}}" 
          bindtap="onTabChange" 
          data-tab="submit">
      <text class="tab-text">提交反馈</text>
    </view>
    <view class="tab-item {{currentTab === 'records' ? 'active' : ''}}" 
          bindtap="onTabChange" 
          data-tab="records">
      <text class="tab-text">我的反馈</text>
    </view>
  </view>

  <!-- 提交反馈内容 -->
  <view wx:if="{{currentTab === 'submit'}}">
    <!-- 问题反馈区域 -->
    <view class="feedback-section">
    <view class="section-title">
      <text class="title-text">问题反馈</text>
      <text class="title-desc">描述您遇到的问题或bug</text>
    </view>
    
    <view class="input-wrapper">
      <textarea 
        class="feedback-input" 
        placeholder="请详细描述您遇到的问题，包括操作步骤、错误现象等信息..."
        value="{{problemText}}"
        bindinput="onProblemInput"
        maxlength="500"
        show-confirm-bar="{{false}}"
      />
      <view class="char-count">{{problemText.length}}/500</view>
    </view>
  </view>

  <!-- 建议征集区域 -->
  <view class="feedback-section">
    <view class="section-title">
      <text class="title-text">建议征集</text>
      <text class="title-desc">您的建议对我们很重要</text>
    </view>
    
    <view class="input-wrapper">
      <textarea 
        class="feedback-input" 
        placeholder="请分享您的宝贵建议，如功能改进、新功能需求等..."
        value="{{suggestionText}}"
        bindinput="onSuggestionInput"
        maxlength="500"
        show-confirm-bar="{{false}}"
      />
      <view class="char-count">{{suggestionText.length}}/500</view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn" bindtap="onSubmitFeedback" disabled="{{submitting}}">
      <text class="btn-text">{{submitting ? '提交中...' : '提交反馈'}}</text>
    </button>
  </view>

    <!-- 提示信息 -->
    <view class="tips-section">
      <view class="tips-content">
        <text class="tips-title">💡 温馨提示</text>
        <view class="tips-list">
          <text class="tips-item">• 反馈内容将帮助我们改进产品</text>
          <text class="tips-item">• 我们会认真阅读每一条反馈</text>
          <text class="tips-item">• 重要问题我们会及时回复</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 反馈记录内容 -->
  <view wx:if="{{currentTab === 'records'}}">
    <!-- 加载中 -->
    <view class="loading-records" wx:if="{{loadingRecords}}">
      <view class="loading-icon"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 反馈记录列表 -->
    <view class="records-list" wx:if="{{showRecords && !loadingRecords}}">
      <view class="record-item" 
            wx:for="{{feedbackRecords}}" 
            wx:key="_id"
            bindtap="onViewFeedback"
            data-index="{{index}}">
        
                 <view class="record-header">
           <view class="record-time">{{item.formattedTime}}</view>
          <view class="record-status {{item.status}}">
            <text wx:if="{{item.status === 'pending'}}">待回复</text>
            <text wx:if="{{item.status === 'replied'}}">已回复</text>
            <text wx:if="{{item.status === 'closed'}}">已关闭</text>
          </view>
        </view>
        
        <view class="record-content">
          <text class="record-preview" wx:if="{{item.problemText}}">
            问题: {{item.problemText.length > 50 ? item.problemText.substring(0, 50) + '...' : item.problemText}}
          </text>
          <text class="record-preview" wx:if="{{item.suggestionText}}">
            建议: {{item.suggestionText.length > 50 ? item.suggestionText.substring(0, 50) + '...' : item.suggestionText}}
          </text>
        </view>
        
        <view class="record-reply" wx:if="{{item.adminReply}}">
          <text class="reply-label">管理员回复:</text>
          <text class="reply-content">{{item.adminReply.length > 60 ? item.adminReply.substring(0, 60) + '...' : item.adminReply}}</text>
        </view>
        
        <view class="record-footer">
          <text class="tap-hint">点击查看详情</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-records" wx:if="{{feedbackRecords.length === 0}}">
        <text class="empty-icon">📝</text>
        <text class="empty-text">暂无反馈记录</text>
        <text class="empty-tip">切换到"提交反馈"开始您的第一次反馈</text>
      </view>
    </view>
  </view>
</view>