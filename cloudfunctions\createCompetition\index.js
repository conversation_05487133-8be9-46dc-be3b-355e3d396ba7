const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 使用种子的随机数生成器
function seededRandom(seed) {
  let x = Math.sin(seed) * 10000;
  return x - Math.floor(x);
}

// 使用种子打乱数组
function shuffleArrayWithSeed(array, seed) {
  const shuffled = [...array];
  let currentSeed = seed;

  for (let i = shuffled.length - 1; i > 0; i--) {
    currentSeed = (currentSeed * 9301 + 49297) % 233280; // 线性同余生成器
    const j = Math.floor(seededRandom(currentSeed) * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }

  return shuffled;
}

exports.main = async (event, context) => {
  console.log('=== 云函数createCompetition开始执行 ===')
  console.log('事件参数:', JSON.stringify(event, null, 2))
  
  try {
    const { name, mode, words, libraryId, libraryName, isRandomOrder = false } = event
    const wxContext = cloud.getWXContext()

    // 超级优化：检测前端传递的数据格式
    const isIndexBasedRequest = words.length === 1 && words[0].totalCount;
    const actualWordsCount = isIndexBasedRequest ? words[0].totalCount : words.length;

    console.log('创建竞赛请求:', {
      name: name,
      mode: mode,
      isIndexBasedRequest: isIndexBasedRequest,
      receivedWordsCount: words.length,
      actualWordsCount: actualWordsCount,
      libraryId: libraryId,
      libraryName: libraryName,
      isRandomOrder: isRandomOrder,
      wordsPerGroup: event.wordsPerGroup
    })
    
    // 验证必要参数
    if (!name || !mode || !words || words.length === 0) {
      return {
        success: false,
        message: '缺少必要参数'
      }
    }

    // 检查是否指定了分组大小（来自客户端的用户选择）
    const wordsPerGroup = event.wordsPerGroup

    if (wordsPerGroup && wordsPerGroup > 0 && actualWordsCount > wordsPerGroup) {
      // 计算将要创建的关卡数量
      const totalLevels = Math.ceil(actualWordsCount / wordsPerGroup)
      console.log(`用户选择每组 ${wordsPerGroup} 个词汇，总计 ${actualWordsCount} 个词汇，将创建 ${totalLevels} 个关卡`)

      // 采用异步创建策略，避免超时
      return await createGroupedCompetitionAsync(name, mode, words, libraryId, libraryName, wxContext, wordsPerGroup, event, isIndexBasedRequest, actualWordsCount, isRandomOrder)
    } else {
      console.log('创建单一竞赛')
      return await createSingleCompetition(name, mode, words, libraryId, libraryName, wxContext, event, isIndexBasedRequest, actualWordsCount, isRandomOrder)
    }

  } catch (error) {
    console.error('创建竞赛失败:', error)
    return {
      success: false,
      message: '创建竞赛失败: ' + error.message
    }
  }
}

// 创建单一竞赛（原有逻辑）
async function createSingleCompetition(name, mode, words, libraryId, libraryName, wxContext, event, isIndexBasedRequest = false, actualWordsCount = 0, isRandomOrder = false) {
  // 获取创建者真实昵称
  let creatorName = '匿名用户';
  try {
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();
    
    if (userResult.data.length > 0) {
      const user = userResult.data[0];
      // 优先使用微信昵称，其次使用用户名
      creatorName = user.wechatInfo?.nickName || user.username || '匿名用户';
    }
  } catch (error) {
    console.log('获取创建者信息失败，使用默认昵称:', error);
  }

  // 生成竞赛ID
  const competitionId = `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  const createTime = new Date()

  // 超级优化：根据存储策略决定数据结构
  let competitionWords = words;
  let wordSelection = null;
  const wordsCount = isIndexBasedRequest ? actualWordsCount : words.length;

  // 判断是否使用索引存储策略
  const isSystemLibrary = libraryId && !libraryId.includes('custom') && !libraryId.includes('mistake');
  const isLargeLibrary = wordsCount >= 100;
  const useIndexBasedStorage = isIndexBasedRequest && isSystemLibrary && isLargeLibrary;

  if (useIndexBasedStorage) {
    console.log('竞赛使用索引存储策略');

    // 生成词汇索引序列
    let wordIndexes = Array.from({length: wordsCount}, (_, i) => i);
    let seed = null;

    if (isRandomOrder) {
      seed = Date.now();
      wordIndexes = shuffleArrayWithSeed(wordIndexes, seed);
      console.log('生成乱序竞赛索引序列，种子:', seed);
    } else {
      console.log('生成顺序竞赛索引序列');
    }

    wordSelection = {
      selectionType: isRandomOrder ? 'random' : 'sequential',
      libraryId: libraryId,
      wordIndexes: wordIndexes,
      totalWords: wordsCount,
      randomSeed: seed
    };

    competitionWords = null; // 不存储完整词汇数据
  }

  // 创建竞赛数据
  const competitionData = {
    _id: competitionId,
    name: name,
    mode: mode,
    words: competitionWords, // 根据策略决定是否存储完整词汇
    wordsCount: wordsCount, // 存储词汇数量
    wordSelection: wordSelection, // 索引存储信息
    storageStrategy: useIndexBasedStorage ? 'index_based' : 'direct',
    libraryId: libraryId || 'custom',
    libraryName: libraryName || '自定义词汇',
    creatorOpenId: wxContext.OPENID,
    creatorName: creatorName,
    createTime: createTime,
    participants: 0,
    results: [], // 存储参与结果
    status: 'active', // active, deleted
    autoDeleteTime: new Date(createTime.getTime() + 7 * 24 * 60 * 60 * 1000), // 7天后自动删除
    type: 'single' // 标识为单一竞赛
  }

  // 根据不同模式添加特定配置
  if (mode === 'elimination') {
    competitionData.gameMode = event.gameMode || '60' // 消消乐游戏模式
  } else if (mode === 'dictation') {
    competitionData.dictationSettings = event.dictationSettings || {
      playCount: 2,
      pauseDuration: 2,
      wordOrder: 'original'
    }
  } else if (mode === 'en2zh' || mode === 'zh2en') {
    competitionData.timeLimit = event.timeLimit || 10 // 每题时间限制
  }

  // 保存到数据库
  try {
    await db.collection('competitions').add({
      data: competitionData
    })
    console.log('单一竞赛创建成功:', {
      id: competitionId,
      mode: mode,
      wordCount: words.length,
      status: 'active'
    })
  } catch (error) {
    console.error('保存竞赛数据失败:', error)
    throw error
  }

  return {
    success: true,
    competitionId: competitionId,
    message: '竞赛创建成功'
  }
}

// 创建分组竞赛（新逻辑）
async function createGroupedCompetition(name, mode, words, libraryId, libraryName, wxContext, wordsPerGroup, event) {
  // 获取创建者真实昵称
  let creatorName = '匿名用户';
  try {
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();
    
    if (userResult.data.length > 0) {
      const user = userResult.data[0];
      creatorName = user.wechatInfo?.nickName || user.username || '匿名用户';
    }
  } catch (error) {
    console.log('获取创建者信息失败，使用默认昵称:', error);
  }

  const createTime = new Date()
  // 多关卡竞赛设置为永久，不自动删除
  const autoDeleteTime = null

  // 分组词汇
  const groups = []
  for (let i = 0; i < words.length; i += wordsPerGroup) {
    groups.push(words.slice(i, i + wordsPerGroup))
  }

  // 生成主竞赛ID
  const masterCompetitionId = `master_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  // 创建主竞赛数据
  const masterCompetitionData = {
    _id: masterCompetitionId,
    name: name,
    mode: mode,
    libraryId: libraryId || 'custom',
    libraryName: libraryName || '自定义词汇',
    creatorOpenId: wxContext.OPENID,
    creatorName: creatorName,
    createTime: createTime,
    participants: 0,
    status: 'active',
    autoDeleteTime: autoDeleteTime, // 多关卡竞赛永久保存
    type: 'master', // 标识为主竞赛
    totalLevels: groups.length,
    totalWords: words.length,
    maxWordsPerLevel: wordsPerGroup,
    levelCompetitions: [] // 存储子关卡ID列表
  }

  // 创建子关卡竞赛
  const levelCompetitions = []
  for (let i = 0; i < groups.length; i++) {
    const levelId = `level_${masterCompetitionId}_${i + 1}`
    const levelName = `${name} - 第${i + 1}关`
    
    const levelCompetitionData = {
      _id: levelId,
      name: levelName,
      mode: mode,
      words: groups[i],
      libraryId: libraryId || 'custom',
      libraryName: libraryName || '自定义词汇',
      creatorOpenId: wxContext.OPENID,
      creatorName: creatorName,
      createTime: createTime,
      participants: 0,
      results: [],
      status: 'active',
      autoDeleteTime: autoDeleteTime, // 子关卡跟随主竞赛，永久保存
      type: 'level', // 标识为子关卡
      masterCompetitionId: masterCompetitionId, // 关联主竞赛
      levelNumber: i + 1,
      totalLevels: groups.length
    }

    // 根据模式添加特定配置
    if (mode === 'elimination') {
      levelCompetitionData.gameMode = '60'
    } else if (mode === 'dictation') {
      levelCompetitionData.dictationSettings = {
        playCount: 2,
        pauseDuration: 2,
        wordOrder: 'original'
      }
    } else if (mode === 'en2zh' || mode === 'zh2en') {
      levelCompetitionData.timeLimit = 10
    }

    levelCompetitions.push(levelCompetitionData)
    masterCompetitionData.levelCompetitions.push(levelId)
  }

  // 批量保存到数据库 - 优化性能
  try {
    console.log(`开始批量创建竞赛: 主竞赛1个 + 子关卡${levelCompetitions.length}个`)
    
    // 准备所有要插入的数据
    const allCompetitions = [masterCompetitionData, ...levelCompetitions]
    
    // 使用批量操作，每批最多10个（优化大量关卡的性能）
    const batchSize = 10
    const batches = []
    
    for (let i = 0; i < allCompetitions.length; i += batchSize) {
      batches.push(allCompetitions.slice(i, i + batchSize))
    }
    
    console.log(`将分${batches.length}批次保存，每批最多${batchSize}个`)
    
    // 执行批量写入
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i]
      console.log(`正在保存第${i + 1}/${batches.length}批，共${batch.length}个竞赛`)
      
      try {
        // 使用Promise.all并发执行当前批次
        const promises = batch.map(data => 
          db.collection('competitions').add({ data })
        )
        
        await Promise.all(promises)
        console.log(`第${i + 1}批保存完成`)
        
        // 每处理一批后稍作延迟，避免数据库压力过大
        if (i < batches.length - 1 && batches.length > 10) {
          await new Promise(resolve => setTimeout(resolve, 50)) // 延迟50ms
        }
        
      } catch (batchError) {
        console.error(`第${i + 1}批保存失败:`, batchError)
        throw new Error(`批量保存失败，第${i + 1}批次出错: ${batchError.message}`)
      }
    }

    console.log('分组竞赛创建成功:', {
      masterId: masterCompetitionId,
      mode: mode,
      totalWords: words.length,
      levels: groups.length,
      maxWordsPerLevel: wordsPerGroup,
      totalCompetitions: allCompetitions.length
    })

    return {
      success: true,
      competitionId: masterCompetitionId,
      type: 'grouped',
      totalLevels: groups.length,
      totalWords: words.length,
      message: `多关卡竞赛创建成功！共${groups.length}关，每关${wordsPerGroup}个词汇`
    }

  } catch (error) {
    console.error('保存分组竞赛数据失败:', error)
    // 如果保存失败，尝试清理已创建的数据
    try {
      console.log('开始清理失败的竞赛数据...')
      await db.collection('competitions').doc(masterCompetitionId).remove()
      
      // 批量清理子关卡
      const cleanupPromises = levelCompetitions.map(levelData => 
        db.collection('competitions').doc(levelData._id).remove().catch(err => {
          console.log(`清理关卡 ${levelData._id} 失败:`, err)
        })
      )
      await Promise.all(cleanupPromises)
      console.log('竞赛数据清理完成')
    } catch (cleanupError) {
      console.error('清理失败的竞赛数据时出错:', cleanupError)
    }
    throw error
  }
}

// 创建分组竞赛（异步策略，避免超时）
async function createGroupedCompetitionAsync(name, mode, words, libraryId, libraryName, wxContext, wordsPerGroup, event, isIndexBasedRequest = false, actualWordsCount = 0, isRandomOrder = false) {
  console.log('开始异步创建分组竞赛')
  
  // 获取创建者真实昵称
  let creatorName = '匿名用户';
  try {
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get();
    
    if (userResult.data.length > 0) {
      const user = userResult.data[0];
      creatorName = user.wechatInfo?.nickName || user.username || '匿名用户';
    }
  } catch (error) {
    console.log('获取创建者信息失败，使用默认昵称:', error);
  }

  const createTime = new Date()
  const autoDeleteTime = null
  const wordsCount = isIndexBasedRequest ? actualWordsCount : words.length;

  // 超级优化：根据存储策略决定数据结构
  let wordSelection = null;
  const isSystemLibrary = libraryId && !libraryId.includes('custom') && !libraryId.includes('mistake');
  const isLargeLibrary = wordsCount >= 100;
  const useIndexBasedStorage = isIndexBasedRequest && isSystemLibrary && isLargeLibrary;

  if (useIndexBasedStorage) {
    console.log('分组竞赛使用索引存储策略');

    // 生成词汇索引序列
    let wordIndexes = Array.from({length: wordsCount}, (_, i) => i);
    let seed = null;

    if (isRandomOrder) {
      seed = Date.now();
      wordIndexes = shuffleArrayWithSeed(wordIndexes, seed);
      console.log('生成乱序分组竞赛索引序列，种子:', seed);
    } else {
      console.log('生成顺序分组竞赛索引序列');
    }

    wordSelection = {
      selectionType: isRandomOrder ? 'random' : 'sequential',
      libraryId: libraryId,
      wordIndexes: wordIndexes,
      totalWords: wordsCount,
      randomSeed: seed
    };
  }

  // 计算分组数量（不需要实际分组词汇）
  const totalLevels = Math.ceil(wordsCount / wordsPerGroup);

  // 生成主竞赛ID
  const masterCompetitionId = `master_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  // 创建主竞赛数据
  const masterCompetitionData = {
    _id: masterCompetitionId,
    name: name,
    mode: mode,
    // 超级优化：不存储完整词汇数据
    words: useIndexBasedStorage ? null : words,
    wordsCount: wordsCount,
    wordSelection: wordSelection,
    storageStrategy: useIndexBasedStorage ? 'index_based' : 'direct',
    libraryId: libraryId || 'custom',
    libraryName: libraryName || '自定义词汇',
    creatorOpenId: wxContext.OPENID,
    creatorName: creatorName,
    createTime: createTime,
    participants: 0,
    status: 'active', // 直接设为活跃状态，避免竞赛列表清空问题
    autoDeleteTime: autoDeleteTime,
    type: 'master',
    totalLevels: totalLevels,
    totalWords: wordsCount,
    maxWordsPerLevel: wordsPerGroup,
    levelCompetitions: [],
    creationProgress: 100, // 主竞赛立即完成
    levelCreationStatus: 'creating' // 用专门字段标记子关卡创建状态
  }

  try {
    // 1. 创建主竞赛
    console.log('步骤1: 创建主竞赛')
    await db.collection('competitions').add({
      data: masterCompetitionData
    })
    console.log('主竞赛创建成功:', masterCompetitionId)

    // 2. 立即返回成功，让前端知道竞赛已创建
    const response = {
      success: true,
      competitionId: masterCompetitionId,
      type: 'grouped',
      totalLevels: totalLevels,
      totalWords: wordsCount,
      message: `多关卡竞赛创建成功！正在后台创建${totalLevels}个关卡...`
    }

    // 3. 异步创建子关卡（不等待完成）
    console.log('步骤2: 开始异步创建子关卡')
    // 传递优化参数给后台创建函数
    createLevelsInBackground(masterCompetitionId, null, mode, libraryId, libraryName, wxContext, createTime, autoDeleteTime, creatorName, event, useIndexBasedStorage, wordSelection, wordsCount, wordsPerGroup)
      .then(() => {
        console.log('所有子关卡创建完成')
      })
      .catch(error => {
        console.error('异步创建子关卡失败:', error)
      })

    return response

  } catch (error) {
    console.error('创建主竞赛失败:', error)
    throw error
  }
}

// 后台异步创建子关卡
async function createLevelsInBackground(masterCompetitionId, groups, mode, libraryId, libraryName, wxContext, createTime, autoDeleteTime, creatorName, event, useIndexBasedStorage = false, wordSelection = null, wordsCount = 0, wordsPerGroup = 10) {
  // 计算总关卡数
  const totalLevels = Math.ceil(wordsCount / wordsPerGroup);
  console.log(`开始后台创建${totalLevels}个子关卡，词汇总数: ${wordsCount}, 每组: ${wordsPerGroup}`)

  try {
    const levelCompetitions = []

    // 分批创建，每批5个
    const batchSize = 5
    for (let batchStart = 0; batchStart < totalLevels; batchStart += batchSize) {
      const batchEnd = Math.min(batchStart + batchSize, totalLevels)
      console.log(`创建第${Math.floor(batchStart/batchSize) + 1}批关卡: ${batchStart + 1}-${batchEnd}`)

      const batchPromises = []

      for (let i = batchStart; i < batchEnd; i++) {
        const levelId = `level_${masterCompetitionId}_${i + 1}`
        const levelName = `第${i + 1}关`
        
        // 计算当前关卡的词汇范围
        const startIndex = i * wordsPerGroup;
        const endIndex = Math.min(startIndex + wordsPerGroup, wordsCount);
        const levelWordsCount = endIndex - startIndex;

        // 根据存储策略生成关卡数据
        let levelWords = null;
        let levelWordSelection = null;

        if (useIndexBasedStorage && wordSelection) {
          // 索引存储策略：只存储当前关卡的索引范围
          const levelWordIndexes = wordSelection.wordIndexes.slice(startIndex, endIndex);

          levelWordSelection = {
            selectionType: wordSelection.selectionType,
            libraryId: wordSelection.libraryId,
            wordIndexes: levelWordIndexes,
            totalWords: levelWordsCount,
            randomSeed: wordSelection.randomSeed,
            levelRange: { start: startIndex, end: endIndex }
          };

          console.log(`关卡${i + 1}使用索引存储，词汇范围: ${startIndex}-${endIndex-1}, 数量: ${levelWordsCount}`);
        } else {
          // 直接存储策略：存储完整词汇（旧逻辑兼容）
          levelWords = groups ? groups[i] : [];
          console.log(`关卡${i + 1}使用直接存储，词汇数量: ${levelWords.length}`);
        }

        const levelCompetitionData = {
          _id: levelId,
          name: levelName,
          mode: mode,
          words: levelWords, // 根据策略决定是否存储完整词汇
          wordsCount: levelWordsCount, // 存储词汇数量
          wordSelection: levelWordSelection, // 索引存储信息
          storageStrategy: useIndexBasedStorage ? 'index_based' : 'direct',
          libraryId: libraryId || 'custom',
          libraryName: libraryName || '自定义词汇',
          creatorOpenId: wxContext.OPENID,
          creatorName: creatorName,
          createTime: createTime,
          participants: 0,
          results: [],
          status: 'active',
          autoDeleteTime: autoDeleteTime,
          type: 'level',
          masterCompetitionId: masterCompetitionId,
          levelNumber: i + 1,
          totalLevels: totalLevels
        }

        // 根据模式添加特定配置
        if (mode === 'elimination') {
          levelCompetitionData.gameMode = event.gameMode || '60'
        } else if (mode === 'dictation') {
          levelCompetitionData.dictationSettings = event.dictationSettings || {
            playCount: 2,
            pauseDuration: 2,
            wordOrder: 'original'
          }
        } else if (mode === 'en2zh' || mode === 'zh2en') {
          levelCompetitionData.timeLimit = event.timeLimit || 10
        }

        levelCompetitions.push(levelId)
        batchPromises.push(
          db.collection('competitions').add({ data: levelCompetitionData })
        )
      }
      
      // 执行当前批次
      await Promise.all(batchPromises)
      console.log(`第${Math.floor(batchStart/batchSize) + 1}批关卡创建完成`)
      
      // 更新主竞赛进度
      const progress = Math.round((batchEnd / totalLevels) * 100)
      await db.collection('competitions').doc(masterCompetitionId).update({
        data: {
          levelCompetitions: levelCompetitions,
          levelCreationProgress: progress
        }
      })

      console.log(`进度更新: ${progress}% (${batchEnd}/${totalLevels})`);

      // 批次间延迟
      if (batchEnd < totalLevels) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
    
    // 所有关卡创建完成，更新主竞赛状态
    await db.collection('competitions').doc(masterCompetitionId).update({
      data: {
        levelCreationStatus: 'completed',
        levelCompetitions: levelCompetitions
      }
    })
    
    console.log('所有子关卡创建完成，主竞赛子关卡创建状态已更新为completed')
    
  } catch (error) {
    console.error('后台创建子关卡失败:', error)
    
    // 更新主竞赛子关卡创建状态为失败
    try {
      await db.collection('competitions').doc(masterCompetitionId).update({
        data: {
          levelCreationStatus: 'error',
          levelCreationError: error.message
        }
      })
    } catch (updateError) {
      console.error('更新竞赛错误状态失败:', updateError)
    }
    
    throw error
  }
} 