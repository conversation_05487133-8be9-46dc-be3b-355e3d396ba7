<view class="container">
  <!-- 导航栏 -->
  <view class="header" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="goBack">
      <text class="back-icon">‹</text>
    </view>
    <view class="nav-title">
      <text wx:if="{{currentStage === 'category'}}">选择词库分类</text>
      <text wx:elif="{{currentStage === 'library'}}">{{selectedCategory.name}}</text>
      <text wx:elif="{{currentStage === 'volume'}}">{{selectedLibrary.name}}</text>
      <text wx:elif="{{currentStage === 'unit'}}">{{selectedVolume.name}}</text>
      <text wx:elif="{{currentStage === 'custom'}}">自定义词库</text>
    </view>
  </view>

  <!-- 测试顺序选择 -->
  <view class="order-selector">
    <view class="order-label">检测顺序：</view>
    <view class="order-options">
      <view class="order-btn {{testOrder === 'random' ? 'active' : ''}}" 
            bindtap="onTestOrderChange" 
            data-order="random">
        🎲 乱序
      </view>
      <view class="order-btn {{testOrder === 'sequential' ? 'active' : ''}}" 
            bindtap="onTestOrderChange" 
            data-order="sequential">
        📋 顺序
      </view>
    </view>
  </view>

  <!-- 双列选择区域 -->
  <view class="selection-area">
    <!-- 分类选择阶段 -->
    <view class="dual-columns" wx:if="{{currentStage === 'category'}}">
      <view class="left-column">
        <view class="column-title">当前阶段</view>
        <view class="category-list">
          <view class="category-item {{selectedCategory && selectedCategory.id === item.id ? 'active' : ''}}" 
                wx:for="{{categories}}" 
                wx:key="id"
                bindtap="onCategorySelect"
                data-category-id="{{item.id}}">
            <text class="category-icon">{{item.icon}}</text>
            <text class="category-name">{{item.name}}</text>
            <text class="arrow">›</text>
          </view>
        </view>
      </view>
      <view class="right-column">
        <view class="column-title">当前版本</view>
        <view class="placeholder">
          <text class="placeholder-text">请先选择左侧分类</text>
        </view>
      </view>
    </view>

    <!-- 词库选择阶段 -->
    <view class="dual-columns" wx:elif="{{currentStage === 'library'}}">
      <view class="left-column">
        <view class="column-title">当前阶段</view>
        <view class="category-list">
          <view class="category-item {{selectedCategory && selectedCategory.id === item.id ? 'active' : ''}}" 
                wx:for="{{categories}}" 
                wx:key="id"
                bindtap="onCategorySelect"
                data-category-id="{{item.id}}">
            <text class="category-icon">{{item.icon}}</text>
            <text class="category-name">{{item.name}}</text>
            <text class="arrow">›</text>
          </view>
        </view>
      </view>
      <view class="right-column">
        <view class="column-title">当前版本</view>
        <view class="library-list" wx:if="{{selectedCategory}}">
          <view class="library-item" 
                wx:for="{{libraries[selectedCategory.id]}}" 
                wx:key="id"
                bindtap="onLibrarySelect"
                data-library-id="{{item.id}}">
            <view class="library-info">
              <text class="library-name">{{item.name}}</text>
              <text class="library-desc" wx:if="{{item.count}}">{{item.count}}</text>
              <text class="library-desc" wx:if="{{item.publisher}}">{{item.publisher}}</text>
              <view class="upload-tag" wx:if="{{item.needsUpload && item.id !== 'gaokao_3500' && item.id !== 'college_cet4' && item.id !== 'college_cet6'}}">上传中</view>
            </view>
            <text class="arrow">›</text>
          </view>
        </view>
        <view class="placeholder" wx:else>
          <text class="placeholder-text">请选择左侧分类</text>
        </view>
      </view>
    </view>

    <!-- 册数选择阶段 -->
    <view class="dual-columns" wx:elif="{{currentStage === 'volume'}}">
      <view class="left-column">
        <view class="column-title">当前阶段</view>
        <view class="category-list">
          <view class="category-item {{selectedCategory && selectedCategory.id === item.id ? 'active' : ''}}" 
                wx:for="{{categories}}" 
                wx:key="id"
                bindtap="onCategorySelect"
                data-category-id="{{item.id}}">
            <text class="category-icon">{{item.icon}}</text>
            <text class="category-name">{{item.name}}</text>
            <text class="arrow">›</text>
          </view>
        </view>
        <view class="selected-info" wx:if="{{selectedLibrary}}">
          <text class="selected-label">已选择：</text>
          <text class="selected-value">{{selectedLibrary.name}}</text>
        </view>
      </view>
      <view class="right-column">
        <view class="column-title">选择册数</view>
        <view class="volume-list">
          <view class="volume-section" wx:if="{{volumes[selectedLibrary.id]}}">
            <!-- 必修册数 -->
            <view class="section-title">必修册数</view>
            <view class="volume-item" 
                  wx:for="{{volumes[selectedLibrary.id]}}" 
                  wx:for-item="volume"
                  wx:key="id"
                  wx:if="{{volume.type === 'required'}}"
                  bindtap="onVolumeSelect"
                  data-volume-id="{{volume.id}}">
              <text class="volume-name">{{volume.name}}</text>
              <text class="arrow">›</text>
            </view>
            
            <!-- 选择性必修册数 -->
            <view class="section-title">选择性必修册数</view>
            <view class="volume-item" 
                  wx:for="{{volumes[selectedLibrary.id]}}" 
                  wx:for-item="volume"
                  wx:key="id"
                  wx:if="{{volume.type === 'elective'}}"
                  bindtap="onVolumeSelect"
                  data-volume-id="{{volume.id}}">
              <text class="volume-name">{{volume.name}}</text>
              <text class="arrow">›</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 单元选择阶段 -->
    <view class="dual-columns" wx:elif="{{currentStage === 'unit'}}">
      <view class="left-column">
        <view class="column-title">当前阶段</view>
        <view class="category-list">
          <view class="category-item {{selectedCategory && selectedCategory.id === item.id ? 'active' : ''}}" 
                wx:for="{{categories}}" 
                wx:key="id"
                bindtap="onCategorySelect"
                data-category-id="{{item.id}}">
            <text class="category-icon">{{item.icon}}</text>
            <text class="category-name">{{item.name}}</text>
            <text class="arrow">›</text>
          </view>
        </view>
        
        <view class="breadcrumb" wx:if="{{selectedLibrary && selectedVolume}}">
          <text class="breadcrumb-item">{{selectedLibrary.name}}</text>
          <text class="breadcrumb-separator">></text>
          <text class="breadcrumb-item">{{selectedVolume.name}}</text>
        </view>
        
        <!-- 单元选择操作 -->
        <view class="unit-controls" wx:if="{{selectedVolume}}">
          <view class="control-title">选择单元</view>
          <view class="control-buttons">
            <button class="control-btn" bindtap="selectAllUnits">全选</button>
            <button class="control-btn" bindtap="clearUnitSelection">清空</button>
          </view>
        </view>
      </view>
      <view class="right-column">
        <view class="column-title">
          <text>选择单元</text>
          <text class="selected-count">已选择 {{selectedUnits.length}} 个</text>
        </view>
        <view class="unit-list">
          <view class="unit-item {{unit.isSelected ? 'selected' : ''}}" 
                wx:for="{{units}}" 
                wx:for-item="unit"
                wx:key="id"
                bindtap="onUnitToggle"
                data-unit-id="{{unit.id}}">
            <view class="unit-checkbox">
              <icon type="{{unit.isSelected ? 'success' : 'circle'}}" size="20"/>
            </view>
            <view class="unit-info">
              <text class="unit-name">{{unit.name}}</text>
              <text class="unit-count" wx:if="{{unit.wordCount === -1}}">词汇量：加载中...</text>
              <text class="unit-count" wx:else>词汇量：{{unit.wordCount}}</text>
            </view>
          </view>
        </view>
        <view class="unit-tip">
          <text>提示：可多选</text>
        </view>
      </view>
    </view>

    <!-- 自定义词库选择阶段 -->
    <view class="dual-columns" wx:elif="{{currentStage === 'custom'}}">
      <view class="left-column">
        <view class="column-title">当前阶段</view>
        <view class="category-list">
          <view class="category-item {{selectedCategory && selectedCategory.id === item.id ? 'active' : ''}}" 
                wx:for="{{categories}}" 
                wx:key="id"
                bindtap="onCategorySelect"
                data-category-id="{{item.id}}">
            <text class="category-icon">{{item.icon}}</text>
            <text class="category-name">{{item.name}}</text>
            <text class="arrow">›</text>
          </view>
        </view>
      </view>
      <view class="right-column">
        <view class="column-title">我的词库</view>
        <view class="custom-list" wx:if="{{customWordbanks.length > 0}}">
          <view class="custom-item" 
                wx:for="{{customWordbanks}}" 
                wx:key="id"
                bindtap="onCustomWordbankSelect"
                data-wordbank-id="{{item.id}}">
            <view class="custom-info">
              <text class="custom-name">{{item.name}}</text>
              <text class="custom-count">{{item.wordCount}}词</text>
            </view>
            <text class="arrow">›</text>
          </view>
        </view>
        <view class="empty-custom" wx:else>
          <text class="empty-text">暂无自定义词库</text>
          <text class="empty-tip">请先在词库页面创建</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部确认按钮 -->
  <view class="bottom-actions">
    <button class="confirm-btn" 
            wx:if="{{currentStage === 'unit'}}"
            bindtap="confirmSelection"
            disabled="{{selectedUnits.length === 0}}">
      开始检测 ({{selectedUnits.length}}个单元)
    </button>
  </view>
</view> 