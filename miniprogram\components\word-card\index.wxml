<view class="word-card">
  <view class="word-header">
    <view class="word-title">
      <text class="word-text">{{word.word}}</text>
      <text class="word-phonetic" wx:if="{{showPhonetic}}">{{word.phonetic}}</text>
    </view>
    <view class="word-actions" wx:if="{{showActions}}">
      <view class="action-btn" bindtap="playAudio">
        <image class="action-icon" src="/assets/icons/audio.png"></image>
      </view>
      <view class="action-btn" bindtap="addToMistakes">
        <image class="action-icon" src="/assets/icons/mistake.png"></image>
      </view>
    </view>
  </view>

  <view class="word-content">
    <block wx:for="{{word.meanings}}" wx:key="index">
      <view class="meaning-item">
        <text class="part-of-speech">{{item.partOfSpeech}}</text>
        <view class="definitions">
          <block wx:for="{{item.definitions}}" wx:for-item="definition" wx:key="index">
            <view class="definition-item">
              <text class="definition-text">{{definition.definition}}</text>
              <text class="example-text" wx:if="{{showExample && definition.example}}">{{definition.example}}</text>
            </view>
          </block>
        </view>
      </view>
    </block>
  </view>
</view> 