const cloud = require('wx-server-sdk');
const crypto = require('crypto');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

// Azure Speech API配置
const AZURE_SPEECH_CONFIG = {
  key: '3rzrJUKwYWPDlC11Qov89prDs54nCfcPv87G7IH58zKr21yTkU26JQQJ99BGAC3pKaRXJ3w3AAAYACOGFiQx',
  region: 'eastasia',
  endpoint: 'https://eastasia.tts.speech.microsoft.com/cognitiveservices/v1',
  timeout: 10000 // 10秒超时
};

// Free Dictionary API配置（备用）
const FREE_DICT_API = {
  baseUrl: 'https://api.dictionaryapi.dev/api/v2/entries/en/',
  timeout: 8000 // 8秒超时
};

/**
 * TTS语音合成云函数
 * 支持特殊格式单词的处理
 */
exports.main = async (event, context) => {
  const {
    text,
    lang = 'en',
    voice = 'en-US-AriaNeural', // Azure语音名称，默认美式女声
    mode = 'practice', // practice: 练习模式, test: 测试模式, tts: 文本转语音模式
    playCount = 2, // 播放次数，仅测试模式有效
    wordType = 'normal', // normal: 普通模式, dictation: 听写模式, translation: 英译汉/汉译英
    rate = 1.0, // 语速 (0.5-2.0)
    pitch = 100 // 音调 (50-150)
  } = event;

  console.log('TTS请求参数:', { text, lang, voice, mode, playCount, wordType, rate, pitch });
  
  if (!text || typeof text !== 'string' || text.trim().length === 0) {
    return {
      success: false,
      message: '文本参数无效',
      error: 'INVALID_TEXT'
    };
  }
  
  const cleanText = text.trim();

  try {
    // 文本转语音模式：直接处理整个文本
    if (mode === 'tts') {
      console.log('文本转语音模式，文本长度:', cleanText.length);



      try {
        const options = { rate, pitch };
        console.log('调用Azure API，选项:', options);

        // 设置10秒超时时间
        const azureResult = await Promise.race([
          fetchAzureSpeechAPI(cleanText, voice, options),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Azure Speech API timeout')), AZURE_SPEECH_CONFIG.timeout)
          )
        ]);

        console.log('Azure API结果:', azureResult);

        if (azureResult && azureResult.success && azureResult.audioUrl) {
          return {
            success: true,
            audioUrl: azureResult.audioUrl,
            source: 'azure-speech'
          };
        } else {
          console.log('Azure API失败，直接返回错误');
          return {
            success: false,
            message: '语音生成失败，请稍后重试或缩短文本'
          };
        }
      } catch (error) {
        console.error('文本转语音处理错误:', error);
        return {
          success: false,
          message: '语音生成超时，请缩短文本或稍后重试'
        };
      }
    }

    // 单词模式：解析特殊格式的单词
    const parsedWords = parseSpecialFormat(cleanText);
    console.log('解析后的单词:', parsedWords);

    // 根据模式和单词类型决定播放逻辑
    const playList = generatePlayList(parsedWords, mode, playCount, wordType);
    console.log('播放列表:', playList);

    // 并发获取所有需要播放的音频，设置总超时时间
    const TOTAL_TIMEOUT = 10000; // 10秒总超时
    const options = { rate, pitch };
    const audioPromises = playList.map(playItem =>
      Promise.race([
        getWordAudio(playItem.word, voice, options),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Audio fetch timeout')), TOTAL_TIMEOUT)
        )
      ]).then(audioResult => ({
        ...playItem,
        audioResult
      })).catch(error => ({
        ...playItem,
        audioResult: { success: false, message: error.message }
      }))
    );
    
    const audioResults = await Promise.all(audioPromises);
    
    // 过滤成功的音频结果
    const successfulResults = audioResults
      .filter(item => item.audioResult.success)
      .map(item => ({
        ...item,
        audioUrl: item.audioResult.audioUrl,
        phonetic: item.audioResult.phonetic,
        meaning: item.audioResult.meaning
      }));
    
    // 如果没有任何音频成功，返回降级结果
    if (successfulResults.length === 0) {
      console.log('所有音频获取失败，返回降级结果');
      return {
        success: true, // 仍然返回成功，但使用降级播放
        data: {
          text: cleanText,
          playList: playList.map(item => ({
            ...item,
            audioUrl: null, // 前端将使用备用TTS
            phonetic: '',
            meaning: '',
            fallback: true
          })),
          totalCount: playList.length,
          source: 'fallback'
        }
      };
    }
    
    // 返回播放列表
    return {
      success: true,
      data: {
        text: cleanText,
        playList: successfulResults,
        totalCount: successfulResults.length,
        source: 'processed'
      }
    };
    
  } catch (error) {
    console.error('TTS云函数异常:', error);
    
    // 发生异常时也返回降级结果
    const fallbackPlayList = [{
      word: cleanText,
      type: 'normal',
      playTimes: mode === 'test' ? playCount : 2,
      audioUrl: null,
      phonetic: '',
      meaning: '',
      fallback: true
    }];
    
    return {
      success: true, // 降级成功
      data: {
        text: cleanText,
        playList: fallbackPlayList,
        totalCount: 1,
        source: 'fallback_error'
      },
      warning: `处理异常，使用降级模式: ${error.message}`
    };
  }
};

/**
 * 解析特殊格式的单词
 */
function parseSpecialFormat(text) {
  const results = [];

  // 1. 处理逗号分隔格式：am, is, are
  const commaFormatRegex = /^([^,]+(?:,\s*[^,]+)+)$/;
  const commaFormatMatch = text.match(commaFormatRegex);

  if (commaFormatMatch) {
    const words = text.split(',').map(w => w.trim()).filter(w => w.length > 0);
    console.log(`检测到逗号分隔格式: ${text}, 分解为: ${words.join(', ')}`);

    words.forEach((word, index) => {
      results.push({
        word: word,
        type: 'comma_separated',
        order: index + 1
      });
    });

    return results;
  }

  // 2. 处理斜杠和等号分隔格式：actor/actress, kilo=kilogram, "drier"="dryer"
  const slashEqualRegex = /^(.+?)[\s]*[\/=][\s]*(.+?)$/;
  const slashEqualMatch = text.match(slashEqualRegex);

  if (slashEqualMatch) {
    const leftPart = slashEqualMatch[1].trim().replace(/^["']|["']$/g, ''); // 去除引号
    const rightPart = slashEqualMatch[2].trim().replace(/^["']|["']$/g, ''); // 去除引号

    console.log(`检测到斜杠/等号分隔格式: ${text}, 分解为: ${leftPart}, ${rightPart}`);

    results.push({
      word: leftPart,
      type: 'slash_equal_left',
      order: 1
    });
    results.push({
      word: rightPart,
      type: 'slash_equal_right',
      order: 2
    });

    return results;
  }

  // 3. 处理星号标记：去除末尾的*
  const asteriskRegex = /^(.+?)\*$/;
  const asteriskMatch = text.match(asteriskRegex);

  if (asteriskMatch) {
    const cleanWord = asteriskMatch[1].trim();
    console.log(`检测到星号标记: ${text}, 清理后: ${cleanWord}`);

    results.push({
      word: cleanWord,
      type: 'asterisk_marked',
      order: 1
    });

    return results;
  }

  // 4. 处理复杂括号格式：AI(=artificial intelligence), knife(pl. knives), awake(awoke, awoken)
  const complexBracketRegex = /^(.+?)\*?\((.+?)\)$/;
  const complexBracketMatch = text.match(complexBracketRegex);

  if (complexBracketMatch) {
    const mainWord = complexBracketMatch[1].trim().replace(/\*$/, ''); // 去除可能的星号
    const bracketContent = complexBracketMatch[2].trim();

    console.log(`检测到复杂括号格式: ${text}, 主词: ${mainWord}, 括号内容: ${bracketContent}`);

    // 忽略括号内容，只读主词
    results.push({
      word: mainWord,
      type: 'complex_bracket',
      order: 1
    });

    return results;
  }

  // 5. 处理短语括号格式：according (to), (real) estate agent
  const phraseBracketRegex = /^(.+?)\s*\((.+?)\)(.*)$|^\((.+?)\)\s*(.+)$/;
  const phraseBracketMatch = text.match(phraseBracketRegex);

  if (phraseBracketMatch) {
    let fullPhrase = '';

    if (phraseBracketMatch[1] && phraseBracketMatch[2]) {
      // according (to) 格式
      fullPhrase = `${phraseBracketMatch[1].trim()} ${phraseBracketMatch[2].trim()}${phraseBracketMatch[3] ? ' ' + phraseBracketMatch[3].trim() : ''}`;
    } else if (phraseBracketMatch[4] && phraseBracketMatch[5]) {
      // (real) estate agent 格式
      fullPhrase = `${phraseBracketMatch[4].trim()} ${phraseBracketMatch[5].trim()}`;
    }

    if (fullPhrase) {
      console.log(`检测到短语括号格式: ${text}, 完整短语: ${fullPhrase}`);

      results.push({
        word: fullPhrase,
        type: 'phrase_bracket',
        order: 1
      });

      return results;
    }
  }

  // 6. 处理中英文对照格式：centre（英）center（美）
  const bilingualRegex = /^(.+?)（[^）]*）(.+?)（[^）]*）$/;
  const bilingualMatch = text.match(bilingualRegex);

  if (bilingualMatch) {
    const word1 = bilingualMatch[1].trim(); // centre
    const word2 = bilingualMatch[2].trim(); // center

    console.log(`检测到中英文对照格式: ${text}, 英式: ${word1}, 美式: ${word2}`);

    results.push({
      word: word1,
      type: 'british', // 英式
      order: 1
    });
    results.push({
      word: word2,
      type: 'american', // 美式
      order: 2
    });

    return results;
  }

  // 7. 处理动词变形格式：pay(paid,paid), burn(-ed, -ed; burnt, burnt)
  const verbFormsRegex = /^(.+?)\*?\((.+?)\)$/;
  const verbFormsMatch = text.match(verbFormsRegex);

  if (verbFormsMatch) {
    const baseForm = verbFormsMatch[1].trim().replace(/\*$/, ''); // 去除可能的星号
    const formsContent = verbFormsMatch[2].trim();

    // 检查是否是动词变形格式（包含逗号或分号）
    if (formsContent.includes(',') || formsContent.includes(';')) {
      console.log(`检测到动词变形格式: ${text}, 基础形式: ${baseForm}`);

      // 只读基础形式，忽略变形
      results.push({
        word: baseForm,
        type: 'verb_base',
        order: 1
      });

      return results;
    }
  }

  // 8. 普通单词（去除可能的星号）
  const cleanText = text.replace(/\*$/, '').trim();

  results.push({
    word: cleanText,
    type: 'normal',
    order: 1
  });

  return results;
}

/**
 * 根据模式和单词类型生成播放列表
 */
function generatePlayList(parsedWords, mode, playCount, wordType) {
  const playList = [];

  // 听写模式
  if (wordType === 'dictation') {
    if (mode === 'test') {
      // 听写测试模式
      if (parsedWords.length > 1 && (parsedWords[0].type === 'comma_separated')) {
        // 逗号分隔格式：am, is, are - 每个单词读一遍
        parsedWords.forEach(wordObj => {
          playList.push({ word: wordObj.word, type: wordObj.type, playTimes: 1 });
        });
      } else if (parsedWords.length > 1 && (parsedWords[0].type === 'slash_equal_left')) {
        // 斜杠/等号分隔格式：actor/actress, kilo=kilogram - 每个部分读一遍
        parsedWords.forEach(wordObj => {
          playList.push({ word: wordObj.word, type: wordObj.type, playTimes: 1 });
        });
      } else if (parsedWords.length > 1 && (parsedWords[0].type === 'british')) {
        // 中英文对照格式：centre（英）center（美）- 英音美音分别读一遍
        parsedWords.forEach(wordObj => {
          playList.push({ word: wordObj.word, type: wordObj.type, playTimes: 1 });
        });
      } else if (parsedWords.length > 1 && parsedWords[0].type === 'american') {
        // 双拼写格式：petrol（美）gas（英）
        if (playCount === 1) {
          // 读1遍：默认读英音（第二个单词）
          playList.push({ word: parsedWords[1].word, type: 'british', playTimes: 1 });
        } else if (playCount === 2) {
          // 读2遍：英音美音分别读一遍
          playList.push({ word: parsedWords[0].word, type: 'american', playTimes: 1 });
          playList.push({ word: parsedWords[1].word, type: 'british', playTimes: 1 });
        } else if (playCount === 3) {
          // 读3遍：英音美音各一遍，再加一次英音
          playList.push({ word: parsedWords[0].word, type: 'american', playTimes: 1 });
          playList.push({ word: parsedWords[1].word, type: 'british', playTimes: 1 });
          playList.push({ word: parsedWords[1].word, type: 'british', playTimes: 1 });
        }
      } else if (parsedWords.length > 1 && parsedWords[0].type === 'base') {
        // 动词变形格式：pay(paid,paid) - 测试模式只读原形
        playList.push({ word: parsedWords[0].word, type: 'base', playTimes: playCount });
      } else {
        // 普通单词、星号标记、复杂括号、短语括号、动词基础形式等
        playList.push({ word: parsedWords[0].word, type: parsedWords[0].type, playTimes: playCount });
      }
    } else {
      // 听写练习模式：默认读2遍
      if (parsedWords.length > 1 && (parsedWords[0].type === 'comma_separated')) {
        // 逗号分隔格式：am, is, are - 每个单词读一遍
        parsedWords.forEach(wordObj => {
          playList.push({ word: wordObj.word, type: wordObj.type, playTimes: 1 });
        });
      } else if (parsedWords.length > 1 && (parsedWords[0].type === 'slash_equal_left')) {
        // 斜杠/等号分隔格式：actor/actress, kilo=kilogram - 每个部分读一遍
        parsedWords.forEach(wordObj => {
          playList.push({ word: wordObj.word, type: wordObj.type, playTimes: 1 });
        });
      } else if (parsedWords.length > 1 && (parsedWords[0].type === 'british')) {
        // 中英文对照格式：centre（英）center（美）- 英音美音分别读一遍
        parsedWords.forEach(wordObj => {
          playList.push({ word: wordObj.word, type: wordObj.type, playTimes: 1 });
        });
      } else if (parsedWords.length > 1 && parsedWords[0].type === 'american') {
        // 双拼写格式：英音美音分别读一遍
        playList.push({ word: parsedWords[0].word, type: 'american', playTimes: 1 });
        playList.push({ word: parsedWords[1].word, type: 'british', playTimes: 1 });
      } else if (parsedWords.length > 1 && parsedWords[0].type === 'base') {
        // 动词变形格式：原形读2遍
        playList.push({ word: parsedWords[0].word, type: 'base', playTimes: 2 });
      } else {
        // 普通单词、星号标记、复杂括号、短语括号、动词基础形式等：读2遍
        playList.push({ word: parsedWords[0].word, type: parsedWords[0].type, playTimes: 2 });
      }
    }
  }
  // 英译汉和汉译英模式
  else if (wordType === 'translation') {
    if (parsedWords.length > 1 && (parsedWords[0].type === 'comma_separated')) {
      // 逗号分隔格式：am, is, are - 每个单词读一遍
      parsedWords.forEach(wordObj => {
        playList.push({ word: wordObj.word, type: wordObj.type, playTimes: 1 });
      });
    } else if (parsedWords.length > 1 && (parsedWords[0].type === 'slash_equal_left')) {
      // 斜杠/等号分隔格式：actor/actress, kilo=kilogram - 每个部分读一遍
      parsedWords.forEach(wordObj => {
        playList.push({ word: wordObj.word, type: wordObj.type, playTimes: 1 });
      });
    } else if (parsedWords.length > 1 && (parsedWords[0].type === 'british')) {
      // 中英文对照格式：centre（英）center（美）- 英音美音分别读一遍
      parsedWords.forEach(wordObj => {
        playList.push({ word: wordObj.word, type: wordObj.type, playTimes: 1 });
      });
    } else if (parsedWords.length > 1 && parsedWords[0].type === 'american') {
      // 双拼写格式：英音美音分别读一遍
      playList.push({ word: parsedWords[0].word, type: 'american', playTimes: 1 });
      playList.push({ word: parsedWords[1].word, type: 'british', playTimes: 1 });
    } else if (parsedWords.length > 1 && parsedWords[0].type === 'base') {
      // 动词变形格式：原形读2遍，过去式和过去分词分别读1遍
      playList.push({ word: parsedWords[0].word, type: 'base', playTimes: 2 });
      for (let i = 1; i < parsedWords.length; i++) {
        playList.push({ word: parsedWords[i].word, type: parsedWords[i].type, playTimes: 1 });
      }
    } else {
      // 普通单词、星号标记、复杂括号、短语括号、动词基础形式等：读2遍
      playList.push({ word: parsedWords[0].word, type: parsedWords[0].type, playTimes: 2 });
    }
  }
  // 普通模式
  else {
    for (const parsedWord of parsedWords) {
      playList.push({ word: parsedWord.word, type: parsedWord.type, playTimes: 1 });
    }
  }
  
  return playList;
}

/**
 * 获取单词音频
 */
async function getWordAudio(word, voiceName = 'en-US-AriaNeural', options = {}) {
  const cleanWord = word.trim().toLowerCase();

  try {
    // 1. 优先尝试Azure Speech API
    try {
      console.log('尝试Azure Speech API...', '语音:', voiceName);
      const azureResult = await Promise.race([
        fetchAzureSpeechAPI(cleanWord, voiceName, options),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Azure Speech API timeout')), AZURE_SPEECH_CONFIG.timeout)
        )
      ]);

      if (azureResult.success && azureResult.audioUrl) {
        console.log('Azure Speech API成功');
        return {
          success: true,
          audioUrl: azureResult.audioUrl,
          phonetic: '',
          meaning: '',
          source: 'azure-speech'
        };
      }
    } catch (error) {
      console.error('Azure Speech API失败:', error.message);
    }

    // 2. 备用：尝试Free Dictionary API
    try {
      console.log('尝试Free Dictionary API...');
      const dictResult = await Promise.race([
        fetchFreeDictionaryAPI(cleanWord),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Dictionary API timeout')), FREE_DICT_API.timeout)
        )
      ]);

      if (dictResult.success && dictResult.audioUrl) {
        console.log('Free Dictionary API成功:', dictResult.audioUrl);
        return {
          success: true,
          audioUrl: dictResult.audioUrl,
          phonetic: dictResult.phonetic || '',
          meaning: dictResult.meaning || '',
          source: 'free-dictionary'
        };
      }
    } catch (error) {
      console.error('Free Dictionary API失败:', error.message);
    }

    // 3. 所有API都失败
    console.log('所有API都失败，单词:', cleanWord);
    return {
      success: false,
      message: '无法获取音频'
    };

  } catch (error) {
    console.error('获取单词音频失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
}

// Azure支持的语音配置
const AZURE_VOICES = {
  // 美式英语
  'en-US-AriaNeural': { lang: 'en-US', gender: 'Female', name: 'en-US-AriaNeural', description: '美式英语-女声(Aria)' },
  'en-US-JennyNeural': { lang: 'en-US', gender: 'Female', name: 'en-US-JennyNeural', description: '美式英语-女声(Jenny)' },
  'en-US-GuyNeural': { lang: 'en-US', gender: 'Male', name: 'en-US-GuyNeural', description: '美式英语-男声(Guy)' },
  'en-US-DavisNeural': { lang: 'en-US', gender: 'Male', name: 'en-US-DavisNeural', description: '美式英语-男声(Davis)' },

  // 英式英语
  'en-GB-SoniaNeural': { lang: 'en-GB', gender: 'Female', name: 'en-GB-SoniaNeural', description: '英式英语-女声(Sonia)' },
  'en-GB-LibbyNeural': { lang: 'en-GB', gender: 'Female', name: 'en-GB-LibbyNeural', description: '英式英语-女声(Libby)' },
  'en-GB-RyanNeural': { lang: 'en-GB', gender: 'Male', name: 'en-GB-RyanNeural', description: '英式英语-男声(Ryan)' },
  'en-GB-ThomasNeural': { lang: 'en-GB', gender: 'Male', name: 'en-GB-ThomasNeural', description: '英式英语-男声(Thomas)' },

  // 中文普通话
  'zh-CN-XiaoxiaoNeural': { lang: 'zh-CN', gender: 'Female', name: 'zh-CN-XiaoxiaoNeural', description: '中文普通话-女声(晓晓)' },
  'zh-CN-YunxiNeural': { lang: 'zh-CN', gender: 'Male', name: 'zh-CN-YunxiNeural', description: '中文普通话-男声(云希)' },
  'zh-CN-XiaoyiNeural': { lang: 'zh-CN', gender: 'Female', name: 'zh-CN-XiaoyiNeural', description: '中文普通话-女声(晓伊)' },
  'zh-CN-YunjianNeural': { lang: 'zh-CN', gender: 'Male', name: 'zh-CN-YunjianNeural', description: '中文普通话-男声(云健)' }
};

// 获取Azure Speech API数据
async function fetchAzureSpeechAPI(text, voiceName = 'en-US-AriaNeural', options = {}) {
  return new Promise((resolve, reject) => {
    const https = require('https');

    // 获取语音配置
    const voiceConfig = AZURE_VOICES[voiceName] || AZURE_VOICES['en-US-AriaNeural'];

    // 处理高级选项
    const rate = options.rate ? `${options.rate}` : '1.0';
    const pitch = options.pitch ? `${options.pitch > 100 ? '+' : ''}${options.pitch - 100}%` : '+0%';

    // 构建SSML文本
    const ssml = `
      <speak version='1.0' xml:lang='${voiceConfig.lang}'>
        <voice xml:lang='${voiceConfig.lang}' xml:gender='${voiceConfig.gender}' name='${voiceConfig.name}'>
          <prosody rate="${rate}" pitch="${pitch}">
            ${text}
          </prosody>
        </voice>
      </speak>
    `.trim();

    console.log('Azure Speech API SSML:', ssml);

    const postData = ssml;
    const requestOptions = {
      hostname: 'eastasia.tts.speech.microsoft.com',
      port: 443,
      path: '/cognitiveservices/v1',
      method: 'POST',
      headers: {
        'Ocp-Apim-Subscription-Key': AZURE_SPEECH_CONFIG.key,
        'Content-Type': 'application/ssml+xml',
        'X-Microsoft-OutputFormat': 'audio-16khz-128kbitrate-mono-mp3',
        'User-Agent': 'WeChat-MiniProgram-TTS/1.0',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: AZURE_SPEECH_CONFIG.timeout
    };

    const req = https.request(requestOptions, (res) => {
      console.log('Azure Speech API响应状态:', res.statusCode);

      if (res.statusCode === 200) {
        const chunks = [];

        res.on('data', (chunk) => {
          chunks.push(chunk);
        });

        res.on('end', () => {
          try {
            const audioBuffer = Buffer.concat(chunks);
            console.log('Azure Speech API音频大小:', audioBuffer.length, 'bytes');

            // 将音频转换为base64 data URL
            const base64Audio = audioBuffer.toString('base64');
            const audioUrl = `data:audio/mp3;base64,${base64Audio}`;

            resolve({
              success: true,
              audioUrl: audioUrl
            });
          } catch (error) {
            console.error('处理Azure Speech API响应失败:', error);
            resolve({
              success: false,
              message: 'Failed to process Azure Speech API response'
            });
          }
        });
      } else {
        let errorData = '';
        res.on('data', (chunk) => {
          errorData += chunk;
        });
        res.on('end', () => {
          console.error('Azure Speech API错误响应:', res.statusCode, errorData);
          resolve({
            success: false,
            message: `Azure Speech API returned status ${res.statusCode}`
          });
        });
      }
    });

    req.on('error', (error) => {
      console.error('Azure Speech API请求失败:', error);
      reject(error);
    });

    req.on('timeout', () => {
      console.error('Azure Speech API请求超时');
      req.destroy();
      reject(new Error('Azure Speech API request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

// 获取Free Dictionary API数据
async function fetchFreeDictionaryAPI(word) {
  return new Promise((resolve, reject) => {
    const https = require('https');
    const url = `${FREE_DICT_API.baseUrl}${encodeURIComponent(word)}`;
    
    console.log('Free Dictionary API URL:', url);
    
    const req = https.request(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; WeChat-MiniProgram-TTS/1.0)',
        'Accept': 'application/json'
      },
      timeout: FREE_DICT_API.timeout
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            const jsonData = JSON.parse(data);
            console.log('Free Dictionary API响应状态:', res.statusCode);
            console.log('Free Dictionary API数据长度:', data.length);
            
            if (Array.isArray(jsonData) && jsonData.length > 0) {
              const wordData = jsonData[0];
              console.log('词汇数据phonetics:', wordData.phonetics);
              
              // 提取音频URL
              let audioUrl = null;
              if (wordData.phonetics && Array.isArray(wordData.phonetics)) {
                for (const phonetic of wordData.phonetics) {
                  console.log('检查phonetic:', phonetic);
                  if (phonetic.audio) {
                    let rawAudioUrl = phonetic.audio;
                    console.log('原始音频URL:', rawAudioUrl);
                    
                    // 处理不同格式的音频URL
                    if (rawAudioUrl.startsWith('//')) {
                      // 补全协议
                      audioUrl = 'https:' + rawAudioUrl;
                    } else if (rawAudioUrl.startsWith('http://') || rawAudioUrl.startsWith('https://')) {
                      // 已经是完整URL
                      audioUrl = rawAudioUrl;
                    } else if (rawAudioUrl.length > 0) {
                      // 其他格式，尝试补全
                      audioUrl = 'https://' + rawAudioUrl;
                    }
                    
                    if (audioUrl) {
                      console.log('处理后音频URL:', audioUrl);
                      break;
                    }
                  }
                }
              }
              
              // 提取音标
              let phoneticText = '';
              if (wordData.phonetic) {
                phoneticText = wordData.phonetic;
              } else if (wordData.phonetics && wordData.phonetics.length > 0) {
                phoneticText = wordData.phonetics[0].text || '';
              }
              
              // 提取释义
              let meaning = '';
              if (wordData.meanings && wordData.meanings.length > 0) {
                const firstMeaning = wordData.meanings[0];
                if (firstMeaning.definitions && firstMeaning.definitions.length > 0) {
                  meaning = firstMeaning.definitions[0].definition || '';
                }
              }
              
              if (audioUrl) {
                resolve({
                  success: true,
                  audioUrl: audioUrl,
                  phonetic: phoneticText,
                  meaning: meaning
                });
              } else {
                resolve({
                  success: false,
                  message: 'No audio found in Free Dictionary API response'
                });
              }
            } else {
              resolve({
                success: false,
                message: 'Invalid response format from Free Dictionary API'
              });
            }
          } else {
            resolve({
              success: false,
              message: `Free Dictionary API returned status ${res.statusCode}`
            });
          }
        } catch (parseError) {
          console.error('解析Free Dictionary API响应失败:', parseError);
          resolve({
            success: false,
            message: 'Failed to parse Free Dictionary API response'
          });
        }
      });
    });
    
    req.on('error', (error) => {
      console.error('Free Dictionary API请求失败:', error);
      reject(error);
    });
    
    req.on('timeout', () => {
      console.error('Free Dictionary API请求超时');
      req.destroy();
      reject(new Error('Free Dictionary API request timeout'));
    });
    
    req.end();
  });
}

