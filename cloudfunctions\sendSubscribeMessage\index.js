const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { templateId, page, data } = event
  try {
    const res = await cloud.openapi.subscribeMessage.send({
      touser: wxContext.OPENID,
      templateId,
      page,
      data
    })
    return { code: 200, data: res }
  } catch (e) {
    return { code: 500, message: '消息推送失败', error: e }
  }
} 