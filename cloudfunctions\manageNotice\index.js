const cloud = require('wx-server-sdk')

// 初始化云能力
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 管理员账号识别配置
const ADMIN_OPENID_PART = '5938DE76' // 微信登录账号ID部分
const ADMIN_PHONE = '15547663399' // 用户名密码登录账号

// 检查是否为管理员
function isAdmin(userOpenId, userInfo = null) {
  // 检查openid（微信登录）
  if (userOpenId && userOpenId.includes(ADMIN_OPENID_PART)) {
    return true
  }

  // 如果有额外的用户信息，检查手机号等字段
  if (userInfo) {
    if (userInfo.phone === ADMIN_PHONE || userInfo.username === ADMIN_PHONE) {
      return true
    }

    // 检查用户ID是否包含管理员标识（适用于微信登录用户）
    if (userInfo._id && userInfo._id.includes(ADMIN_OPENID_PART)) {
      return true
    }
  }

  return false
}

// 获取用户信息用于管理员验证
async function getUserInfoForAdmin(openId) {
  try {
    // 查询用户信息
    const userResult = await db.collection('users').where({ openid: openId }).get()
    if (userResult.data && userResult.data.length > 0) {
      return userResult.data[0]
    }
    return null
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

exports.main = async (event, context) => {
  const { action, data } = event
  const wxContext = cloud.getWXContext()
  const currentUserOpenId = wxContext.OPENID

  try {
    // 获取用户信息用于管理员验证
    const userInfo = await getUserInfoForAdmin(currentUserOpenId)

    // 检查管理员权限
    const isAdminUser = isAdmin(currentUserOpenId, userInfo)

    console.log('通知管理权限检查:', {
      action,
      currentUserOpenId,
      isAdminUser,
      userInfo: userInfo ? {
        username: userInfo.username,
        phone: userInfo.phone,
        _id: userInfo._id
      } : null
    })

    if (!isAdminUser) {
      return {
        success: false,
        message: '无权限执行此操作，仅管理员可管理通知'
      }
    }

    switch (action) {
      case 'add':
        // 添加新通知
        const addResult = await db.collection('notices').add({
          data: {
            content: data.content,
            status: 'active',
            createTime: new Date(),
            updateTime: new Date(),
            createdBy: context.OPENID || 'admin'
          }
        })
        return {
          success: true,
          data: { id: addResult._id },
          message: '通知添加成功'
        }

      case 'update':
        // 更新通知
        const updateResult = await db.collection('notices')
          .doc(data.id)
          .update({
            data: {
              content: data.content,
              status: data.status || 'active',
              updateTime: new Date()
            }
          })
        return {
          success: true,
          data: updateResult,
          message: '通知更新成功'
        }

      case 'delete':
        // 删除通知（软删除，设置为inactive）
        await db.collection('notices')
          .doc(data.id)
          .update({
            data: {
              status: 'inactive',
              updateTime: new Date()
            }
          })
        return {
          success: true,
          message: '通知删除成功'
        }

      case 'list':
        // 获取所有通知列表（管理员用）
        const listResult = await db.collection('notices')
          .orderBy('createTime', 'desc')
          .get()
        return {
          success: true,
          data: listResult.data
        }

      default:
        return {
          success: false,
          error: '无效的操作类型'
        }
    }
  } catch (error) {
    console.error('管理通知失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
} 