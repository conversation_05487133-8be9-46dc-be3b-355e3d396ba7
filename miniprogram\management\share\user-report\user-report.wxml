<!--pages/profile/share/user-report/user-report.wxml-->
<view class="report-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 报告内容 -->
  <view class="report-content" wx:else>
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-avatar">
        <image class="avatar-img" src="{{userAvatar}}" mode="aspectFill" />
      </view>
      <view class="user-info">
        <text class="user-name">{{userName}}</text>
        <text class="user-subtitle">个人分享报告</text>
      </view>
      <button class="export-btn" bindtap="exportReport">导出</button>
    </view>

    <!-- 统计数据概览 -->
    <view class="stats-overview">
      <view class="stats-title">📊 数据概览</view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{statistics.totalTests}}</text>
          <text class="stats-label">总测试次数</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.averageScore}}</text>
          <text class="stats-label">平均分数</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.bestScore}}</text>
          <text class="stats-label">最高分数</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{statistics.accuracy}}%</text>
          <text class="stats-label">平均准确率</text>
        </view>
      </view>
    </view>

    <!-- 详细测试记录 -->
    <view class="all-tests" wx:if="{{testResults.length > 0}}">
      <view class="section-title">📝 所有测试记录</view>
      <view class="test-list">
        <view class="test-item" 
              wx:for="{{testResults}}" 
              wx:key="timestamp"
              data-index="{{index}}"
              bindtap="viewTestDetail"
              bindlongpress="deleteTestRecord">
          <view class="test-header">
            <view class="test-mode-badge mode-{{item.testMode}}">
              {{item.testModeText}}
            </view>
                          <text class="test-time">{{item.timeText}}</text>
          </view>
          <view class="test-content">
            <text class="test-library">{{item.libraryName || '自定义词汇'}}</text>
            <view class="test-stats">
              <text class="test-score" style="color: {{item.score >= 80 ? '#4CAF50' : item.score >= 60 ? '#FF9800' : '#F44336'}}">{{item.score}}分</text>
              <text class="test-accuracy">准确率{{item.accuracy}}%</text>
                              <text class="test-duration">用时{{item.durationText}}</text>
            </view>
          </view>
          <view class="test-footer">
            <text class="test-mistakes">错误：{{item.mistakes ? item.mistakes.length : 0}}个</text>
            <text class="test-detail-hint">点击查看详情</text>
          </view>
        </view>
      </view>
    </view>

  <!-- 测试详情弹窗 -->
  <view class="modal-overlay" wx:if="{{showDetailModal}}" bindtap="closeDetailModal">
    <view class="detail-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">测试详情</text>
        <text class="close-btn" bindtap="closeDetailModal">✕</text>
      </view>
      
      <view class="modal-content">
        <!-- 测试基本信息 -->
        <view class="test-basic-info">
          <view class="info-row">
            <text class="info-label">测试模式：</text>
            <text class="info-value">{{currentTestDetail.testModeText}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">测试时间：</text>
            <text class="info-value">{{currentTestDetail.timeText}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">得分：</text>
            <text class="info-value" style="color: {{currentTestDetail.score >= 80 ? '#4CAF50' : currentTestDetail.score >= 60 ? '#FF9800' : '#F44336'}}">{{currentTestDetail.score}}分</text>
          </view>
          <view class="info-row">
            <text class="info-label">准确率：</text>
            <text class="info-value">{{currentTestDetail.accuracy}}%</text>
          </view>
          <view class="info-row">
            <text class="info-label">用时：</text>
            <text class="info-value">{{currentTestDetail.durationText}}</text>
          </view>
        </view>

        <!-- 错词列表 -->
        <view class="mistakes-section" wx:if="{{allMistakes.length > 0}}">
          <view class="mistakes-header">
            <text class="mistakes-title">错词列表 ({{allMistakes.length}}个)</text>
            <view class="mistakes-actions">
              <button class="action-btn" size="mini" bindtap="selectAllMistakes">全选</button>
              <button class="action-btn" size="mini" bindtap="clearMistakeSelection">清空</button>
            </view>
          </view>
          
          <view class="mistakes-list">
            <view class="mistake-item {{item.selected ? 'selected' : ''}}"
                  wx:for="{{allMistakes}}"
                  wx:key="index"
                  data-index="{{index}}"
                  bindtap="toggleMistakeSelection">
              <view class="mistake-checkbox">
                <text class="checkbox-icon" wx:if="{{item.selected}}">✓</text>
              </view>
              <view class="mistake-content">
                <view class="mistake-word">{{item.wordText}}</view>
                <view class="mistake-details">
                  <text class="user-answer">你的答案：{{item.userAnswerText}}</text>
                  <text class="correct-answer">正确：{{item.correctAnswerText}}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 基于错词重新测试 -->
          <view class="retest-section" wx:if="{{selectedMistakes.length > 0}}">
            <button class="retest-btn" bindtap="retestWithSelectedMistakes">
              基于选中错词重新测试 ({{selectedMistakes.length}}个)
            </button>
          </view>
        </view>

        <!-- 无错词提示 -->
        <view class="no-mistakes" wx:else>
          <text class="no-mistakes-text">🎉 本次测试全部正确！</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 重新测试配置弹窗 -->
  <view class="modal-overlay" wx:if="{{showRetestModal}}" bindtap="closeRetestModal">
    <view class="retest-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">重新测试配置</text>
        <text class="close-btn" bindtap="closeRetestModal">✕</text>
      </view>
      
      <view class="modal-content">
        <view class="config-section">
          <text class="config-title">测试模式</text>
          <view class="mode-options">
            <view class="mode-option {{selectedTestMode === 'en_to_cn' ? 'selected' : ''}}"
                  data-mode="en_to_cn"
                  bindtap="selectTestMode">
              <text class="mode-icon">🇨🇳</text>
              <text class="mode-text">英译汉</text>
            </view>
            <view class="mode-option {{selectedTestMode === 'cn_to_en' ? 'selected' : ''}}"
                  data-mode="cn_to_en"
                  bindtap="selectTestMode">
              <text class="mode-icon">🇺🇸</text>
              <text class="mode-text">汉译英</text>
            </view>
            <view class="mode-option {{selectedTestMode === 'dictation' ? 'selected' : ''}}"
                  data-mode="dictation"
                  bindtap="selectTestMode">
              <text class="mode-icon">🎧</text>
              <text class="mode-text">听写</text>
            </view>
            <view class="mode-option {{selectedTestMode === 'elimination' ? 'selected' : ''}}"
                  data-mode="elimination"
                  bindtap="selectTestMode">
              <text class="mode-icon">🎮</text>
              <text class="mode-text">消消乐</text>
            </view>
          </view>
        </view>

        <view class="config-section">
          <text class="config-title">练习模式</text>
          <view class="practice-options">
            <view class="practice-option {{selectedPracticeMode === 'practice' ? 'selected' : ''}}"
                  data-mode="practice"
                  bindtap="selectPracticeMode">
              <text class="practice-text">练习模式</text>
              <text class="practice-desc">可查看答案，不计时</text>
            </view>
            <view class="practice-option {{selectedPracticeMode === 'test' ? 'selected' : ''}}"
                  data-mode="test"
                  bindtap="selectPracticeMode">
              <text class="practice-text">测试模式</text>
              <text class="practice-desc">正式测试，限时作答</text>
            </view>
          </view>
        </view>

        <view class="config-section">
          <text class="config-title">分享选项</text>
          <view class="share-options">
            <view class="share-option {{selectedShareOption === 'self' ? 'selected' : ''}}"
                  data-option="self"
                  bindtap="selectShareOption">
              <view class="share-content">
                <text class="share-icon">👤</text>
                <view class="share-info">
                  <text class="share-text">自己测试</text>
                  <text class="share-desc">立即开始测试</text>
                </view>
              </view>
              <view class="share-indicator">
                <text class="radio-icon">{{selectedShareOption === 'self' ? '●' : '○'}}</text>
              </view>
            </view>
            <view class="share-option {{selectedShareOption === 'others' ? 'selected' : ''}}"
                  data-option="others"
                  bindtap="selectShareOption">
              <view class="share-content">
                <text class="share-icon">👥</text>
                <view class="share-info">
                  <text class="share-text">分享给他人</text>
                  <text class="share-desc">创建分享链接</text>
                </view>
              </view>
              <view class="share-indicator">
                <text class="radio-icon">{{selectedShareOption === 'others' ? '●' : '○'}}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="config-summary">
          <text class="summary-text">将使用 {{selectedMistakes.length}} 个错词进行{{selectedTestModeText}}{{selectedPracticeModeText}}</text>
        </view>

        <button class="confirm-btn" bindtap="confirmRetest">确认开始</button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{statistics.totalTests === 0}}">
    <view class="empty-icon">📋</view>
    <text class="empty-title">暂无测试记录</text>
    <text class="empty-desc">该用户还没有参与任何分享测试</text>
  </view>

  <!-- 分析建议 -->
  <view class="analysis-section" wx:if="{{statistics.totalTests > 0}}">
    <view class="section-title">💡 分析建议</view>
    <view class="analysis-content">
      <view class="analysis-item" wx:if="{{statistics.averageScore >= 80}}">
        <text class="analysis-icon">🎉</text>
        <text class="analysis-text">表现优秀！平均分达到{{statistics.averageScore}}分，继续保持！</text>
      </view>
      <view class="analysis-item" wx:elif="{{statistics.averageScore >= 60}}">
        <text class="analysis-icon">👍</text>
        <text class="analysis-text">成绩良好，平均分{{statistics.averageScore}}分，还有提升空间。</text>
      </view>
      <view class="analysis-item" wx:else>
        <text class="analysis-icon">💪</text>
        <text class="analysis-text">需要加强练习，平均分{{statistics.averageScore}}分，建议多做练习。</text>
      </view>
      
      <view class="analysis-item" wx:if="{{statistics.totalTests >= 5}}">
        <text class="analysis-icon">🔥</text>
        <text class="analysis-text">已完成{{statistics.totalTests}}次测试，学习态度积极！</text>
      </view>
      
      <view class="analysis-item" wx:if="{{statistics.accuracy >= 85}}">
        <text class="analysis-icon">🎯</text>
        <text class="analysis-text">准确率达到{{statistics.accuracy}}%，答题质量很高！</text>
      </view>
    </view>
  </view>
  </view>
</view> 