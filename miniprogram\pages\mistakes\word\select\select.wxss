.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 操作按钮样式 */
.action-bar {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-btn {
  background-color: #fff;
  padding: 16rpx 30rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #4A90E2;
}

/* 单词列表样式 */
.word-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.word-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.word-item.selected {
  background-color: #F0F7FF;
}

.word-checkbox {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.word-checkbox image {
  width: 100%;
  height: 100%;
}

.word-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.word-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.word-phonetic {
  font-size: 24rpx;
  color: #666;
}

.word-meaning {
  font-size: 28rpx;
  color: #333;
}

/* 底部操作栏样式 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.selection-info {
  font-size: 28rpx;
  color: #666;
}

.next-btn {
  background-color: #ccc;
  color: #fff;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.next-btn.active {
  background-color: #4A90E2;
} 