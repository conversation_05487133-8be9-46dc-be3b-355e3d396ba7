const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()
const usersCollection = db.collection('users')

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  const { updateType, updateData, token } = event

  console.log('更新用户信息 - openid:', openid)
  console.log('更新用户信息 - token:', token ? '有token' : '无token')
  console.log('更新用户信息 - updateType:', updateType)
  console.log('更新用户信息 - updateData:', updateData)

  try {
    // 查找用户 - 优先使用token，其次使用openid
    let userRes;
    if (token) {
      userRes = await usersCollection.where({ token }).get()
    } else if (openid) {
      userRes = await usersCollection.where({ openid }).get()
    } else {
      return {
        code: 400,
        message: '缺少用户标识'
      }
    }

    if (userRes.data.length === 0) {
      return {
        code: 404,
        message: '用户不存在'
      }
    }

    const user = userRes.data[0]
    let updateFields = {}

    switch (updateType) {
      case 'profile':
        // 更新昵称和头像
        if (updateData.nickName) {
          updateFields['wechatInfo.nickName'] = updateData.nickName
        }
        if (updateData.avatarUrl) {
          updateFields['wechatInfo.avatarUrl'] = updateData.avatarUrl
        }
        break

      case 'nickname':
        // 更新昵称
        updateFields['wechatInfo.nickName'] = updateData.nickName
        updateFields['isNewUser'] = false // 清除新用户标记
        break

      case 'avatar':
        // 更新头像
        updateFields['wechatInfo.avatarUrl'] = updateData.avatarUrl
        break

      default:
        return {
          code: 400,
          message: '不支持的更新类型'
        }
    }

    // 更新数据库
    await usersCollection.doc(user._id).update({
      data: {
        ...updateFields,
        updateTime: new Date()
      }
    })

    // 获取更新后的完整用户信息
    const updatedUserRes = await usersCollection.doc(user._id).get()
    const updatedUser = updatedUserRes.data
    
    // 确保返回的数据结构包含所有必要字段
    const completeUserInfo = {
      _id: updatedUser._id,
      openid: updatedUser.openid,
      username: updatedUser.username || updatedUser.wechatInfo?.nickName,

      wechatInfo: {
        nickName: updatedUser.wechatInfo?.nickName || '',
        avatarUrl: updatedUser.wechatInfo?.avatarUrl || '/assets/icons/profile.png'
      },
      membership: updatedUser.membership || { isVip: false, expireTime: null, type: 'free' },
      stats: updatedUser.stats || { totalWords: 0, continuousDays: 0, masteredWords: 0 },
      settings: updatedUser.settings || { dailyWords: 20, reminderEnabled: false },
      createTime: updatedUser.createTime || new Date().toISOString().replace('T', ' ').substring(0, 19),
      updateTime: updatedUser.updateTime || new Date().toISOString().replace('T', ' ').substring(0, 19)
    }
    
    console.log('更新成功，返回数据:', completeUserInfo)
    
    return {
      code: 200,
      message: '更新成功',
      data: completeUserInfo  // 直接返回完整的用户信息，而不是嵌套在userInfo里
    }
  } catch (error) {
    console.error('更新用户信息失败:', error)
    return {
      code: 500,
      message: '服务器错误',
      error: error.message
    }
  }
} 