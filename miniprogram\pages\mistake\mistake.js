const app = getApp();

Page({
  data: {
    // 统计数据
    totalCount: 0, // 错题总数
    reviewedCount: 0, // 已复习数
    remainingCount: 0, // 剩余数

    // 错题列表
    mistakeList: [], // 错题数据

    // 当前选中的单词
    currentWord: null, // 当前查看的单词

    // 音频上下文
    audioContext: null, // 音频上下文
    audioProgress: 0, // 音频进度

    // 弹窗显示状态
    showWordModal: false // 是否显示单词详情
  },

  onLoad() {
    // 初始化音频上下文
    this.initAudioContext();
    
    // 加载错题列表
    this.loadMistakeList();
  },

  onShow() {
    // 每次显示页面时重新加载数据
    this.loadMistakeList();
  },

  onUnload() {
    // 销毁音频上下文
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }
  },

  // 初始化音频上下文
  initAudioContext() {
    this.audioContext = wx.createInnerAudioContext();
    this.audioContext.onTimeUpdate(() => {
      this.setData({
        audioProgress: (this.audioContext.currentTime / this.audioContext.duration) * 100
      });
    });
    this.audioContext.onEnded(() => {
      this.setData({
        audioProgress: 0
      });
    });
  },

  // 加载错题列表
  async loadMistakeList() {
    const db = wx.cloud.database();
    const _ = db.command;

    try {
      const res = await db.collection('mistakes')
        .where({
          _openid: app.globalData.openid
        })
        .orderBy('createTime', 'desc')
        .get();

      const mistakeList = res.data;
      
      // 更新统计数据
      const totalCount = mistakeList.length;
      const reviewedCount = mistakeList.filter(item => item.reviewed).length;
      const remainingCount = totalCount - reviewedCount;

      this.setData({
        mistakeList,
        totalCount,
        reviewedCount,
        remainingCount
      });
    } catch (error) {
      console.error('加载错题列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 显示单词详情
  showWordDetail(e) {
    const { word } = e.currentTarget.dataset;
    this.setData({
      currentWord: word,
      showWordModal: true
    });
  },

  // 关闭单词详情
  closeWordModal() {
    this.setData({
      showWordModal: false,
      currentWord: null
    });
    // 停止音频播放
    if (this.audioContext) {
      this.audioContext.stop();
    }
  },

  // 播放音频
  playAudio() {
    if (!this.data.currentWord) return;
    
    const { audioUrl } = this.data.currentWord;
    if (!audioUrl) return;

    this.audioContext.src = audioUrl;
    this.audioContext.play();
  },

  // 标记为已复习
  async markAsReviewed() {
    if (!this.data.currentWord) return;

    const db = wx.cloud.database();
    const wordId = this.data.currentWord._id;

    try {
      await db.collection('mistakes').doc(wordId).update({
        data: {
          reviewed: true,
          reviewTime: db.serverDate()
        }
      });

      // 重新加载列表
      this.loadMistakeList();
      // 关闭弹窗
      this.closeWordModal();

      wx.showToast({
        title: '已标记为已复习',
        icon: 'success'
      });
    } catch (error) {
      console.error('标记复习状态失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 从错题本中移除
  async removeFromMistake() {
    if (!this.data.currentWord) return;

    const db = wx.cloud.database();
    const wordId = this.data.currentWord._id;

    try {
      await db.collection('mistakes').doc(wordId).remove();

      // 重新加载列表
      this.loadMistakeList();
      // 关闭弹窗
      this.closeWordModal();

      wx.showToast({
        title: '已移除',
        icon: 'success'
      });
    } catch (error) {
      console.error('移除错题失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 开始复习错题
  startReview() {
    if (this.data.remainingCount === 0) {
      wx.showToast({
        title: '没有需要复习的错题',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/review/review?type=mistake'
    });
  }
}); 