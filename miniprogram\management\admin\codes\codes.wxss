.container { padding: 40rpx; }
.form-group { margin-bottom: 30rpx; display: flex; align-items: center; }
.form-group text { width: 160rpx; font-size: 28rpx; }
.form-group input, .picker { flex: 1; border: 1rpx solid #ccc; border-radius: 8rpx; padding: 16rpx; font-size: 28rpx; }
.gen-btn { width: 100%; background: #4A90E2; color: #fff; border-radius: 8rpx; font-size: 32rpx; margin-top: 20rpx; }
.result-section { margin-top: 40rpx; }
.result-title { font-size: 28rpx; font-weight: bold; margin-bottom: 10rpx; }
.code-list { display: flex; flex-wrap: wrap; gap: 16rpx; }
.code-item { background: #f5f5f5; padding: 10rpx 20rpx; border-radius: 8rpx; font-size: 28rpx; } 