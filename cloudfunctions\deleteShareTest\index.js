const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  
  try {
    const { shareIds, type = 'creator' } = event; // shareIds: 数组，type: 'creator' 或 'participant'
    const currentUser = wxContext.OPENID;
    
    if (!shareIds || !Array.isArray(shareIds) || shareIds.length === 0) {
      return {
        success: false,
        message: '请提供要删除的分享ID'
      };
    }
    
    let deleteCount = 0;
    
    for (const shareId of shareIds) {
      try {
        if (type === 'creator') {
          // 创建者删除：完全删除分享测试
          // 支持多种字段匹配方式
          const result = await db.collection('shareTests')
            .where(db.command.and([
              { shareId: shareId },
              db.command.or([
                { createdBy: currentUser },
                { creatorOpenid: currentUser },
                { 'creatorInfo.openid': currentUser }
              ])
            ]))
            .remove();
          
          deleteCount += result.deleted;
          
        } else if (type === 'participant') {
          // 参与者删除：只删除自己的参与记录
          const shareTestResult = await db.collection('shareTests')
            .where({
              shareId: shareId
            })
            .get();
          
          if (shareTestResult.data && shareTestResult.data.length > 0) {
            const shareTestData = shareTestResult.data[0];
            
            // 从访问者列表中移除当前用户
            const updatedVisitors = shareTestData.visitors.filter(v => v.openid !== currentUser);
            
            // 从测试结果中移除当前用户的记录
            const updatedResults = shareTestData.results.filter(r => r.participantOpenid !== currentUser);
            
            // 从关卡进度中移除当前用户
            const updatedLevelProgress = { ...shareTestData.levelProgress };
            delete updatedLevelProgress[currentUser];
            
            await db.collection('shareTests').doc(shareTestData._id).update({
              data: {
                visitors: updatedVisitors,
                results: updatedResults,
                levelProgress: updatedLevelProgress
              }
            });
            
            deleteCount++;
          }
        }
      } catch (error) {
        console.error(`删除分享测试 ${shareId} 失败:`, error);
        // 继续处理其他项目
      }
    }
    
    return {
      success: true,
      data: {
        deleteCount: deleteCount,
        requestedCount: shareIds.length
      }
    };
    
  } catch (error) {
    console.error('删除分享测试失败:', error);
    return {
      success: false,
      message: '删除分享测试失败',
      error: error
    };
  }
}; 