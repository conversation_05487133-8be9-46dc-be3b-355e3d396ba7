const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const {
      libraryId,
      libraryName,
      mode,
      progressData,
      testSettings // 新增：测试配置信息
    } = event

    console.log('保存学习进度:', {
      openid: wxContext.OPENID,
      libraryId,
      mode,
      progressData,
      testSettings
    })

    // 验证必要参数
    if (!libraryId || !mode || !progressData) {
      return {
        success: false,
        message: '缺少必要参数'
      }
    }

    // 构建进度数据
    const learningProgressData = {
      openid: wxContext.OPENID,
      libraryId: libraryId,
      libraryName: libraryName,
      mode: mode,
      ...progressData,
      testSettings: testSettings || {}, // 保存测试配置
      updateTime: db.serverDate(),
      createTime: progressData.createTime || db.serverDate()
    }

    // 查找是否已存在该进度记录
    const existingProgress = await db.collection('learning_progress')
      .where({
        openid: wxContext.OPENID,
        libraryId: libraryId,
        mode: mode
      })
      .get()

    let result
    if (existingProgress.data.length > 0) {
      // 更新现有记录
      result = await db.collection('learning_progress')
        .doc(existingProgress.data[0]._id)
        .update({
          data: learningProgressData
        })
      console.log('更新学习进度成功:', result)
    } else {
      // 创建新记录
      result = await db.collection('learning_progress')
        .add({
          data: learningProgressData
        })
      console.log('创建学习进度成功:', result)
    }

    return {
      success: true,
      message: '保存学习进度成功',
      data: {
        progressId: result._id || existingProgress.data[0]._id
      }
    }

  } catch (error) {
    console.error('保存学习进度失败:', error)
    return {
      success: false,
      message: '保存学习进度失败',
      error: error.message
    }
  }
}
