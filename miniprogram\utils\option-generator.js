/**
 * 选项生成工具
 * 用于生成单词和短语检测的选择题选项
 * 确保所有选项都来自对应的词库，不使用硬编码的备用选项
 */

/**
 * 生成选择题选项
 * @param {Array} dataSource - 数据源（单词或短语数组）
 * @param {string} field - 要提取的字段名（'meaning'、'words'、'chinese'等）
 * @param {string} correctAnswer - 正确答案
 * @param {number} optionCount - 需要的选项数量，默认4个
 * @returns {Array} 包含正确答案的选项数组
 */
function generateOptions(dataSource, field, correctAnswer, optionCount = 4) {
  if (!dataSource || !Array.isArray(dataSource) || dataSource.length === 0) {
    console.error('数据源无效或为空');
    return [correctAnswer];
  }

  if (!correctAnswer) {
    console.error('正确答案不能为空');
    return [];
  }

  const options = [correctAnswer];
  const usedValues = new Set([correctAnswer]);

  // 生成不重复的选项
  let attempts = 0;
  const maxAttempts = Math.min(dataSource.length * 10, 1000); // 限制最大尝试次数

  while (options.length < optionCount && attempts < maxAttempts) {
    attempts++;
    const randomItem = dataSource[Math.floor(Math.random() * dataSource.length)];
    const value = extractFieldValue(randomItem, field);

    if (value && value !== correctAnswer && !usedValues.has(value)) {
      options.push(value);
      usedValues.add(value);
    }
  }

  // 如果选项生成不足，记录详细的错误信息
  if (options.length < optionCount) {
    console.error(`选项生成失败: 需要${optionCount}个选项，实际生成${options.length}个选项`);
    console.error('数据源大小:', dataSource.length, '字段:', field);
    console.error('尝试次数:', attempts);
    console.error('正确答案:', correctAnswer);
    console.error('已生成选项:', options);

    // 分析数据源中有效值的数量
    const validValues = new Set();
    dataSource.forEach(item => {
      const value = extractFieldValue(item, field);
      if (value && value !== correctAnswer) {
        validValues.add(value);
      }
    });
    console.error('数据源中可用的不重复选项数量:', validValues.size);

    if (validValues.size < optionCount - 1) {
      console.error('数据源中可用选项不足，建议检查词库数据质量或增加词库大小');
    }
  }

  // 打乱选项顺序
  return shuffleArray([...options]);
}

/**
 * 从数据项中提取指定字段的值
 * @param {Object} item - 数据项
 * @param {string} field - 字段名
 * @returns {string} 提取的值
 */
function extractFieldValue(item, field) {
  if (!item || typeof item !== 'object') {
    return '';
  }

  let value = '';
  
  switch (field) {
    case 'meaning':
      // 中文释义字段的多种可能名称
      value = item.meaning || item.meanings || item.definition || item.chinese;
      break;
    case 'words':
    case 'word':
      // 英文单词字段的多种可能名称
      value = item.words || item.word || item.english;
      break;
    case 'chinese':
      // 中文字段的多种可能名称
      value = item.chinese || item.meaning;
      break;
    case 'phrase':
      // 短语字段的多种可能名称
      value = item.phrase || item.word || item.words;
      break;
    default:
      // 直接使用字段名
      value = item[field];
      break;
  }

  // 处理复杂的数据结构
  if (Array.isArray(value) && value.length > 0) {
    // 如果是数组，取第一个元素
    value = value[0];
    if (typeof value === 'object' && value.definition) {
      value = value.definition;
    }
  }

  return typeof value === 'string' ? value.trim() : '';
}

/**
 * 打乱数组顺序
 * @param {Array} array - 要打乱的数组
 * @returns {Array} 打乱后的数组
 */
function shuffleArray(array) {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * 生成单词选择题选项
 * @param {Array} words - 单词数组
 * @param {string} testMode - 测试模式（'en_to_cn'、'cn_to_en'）
 * @param {Object} currentWord - 当前单词对象
 * @param {number} optionCount - 选项数量，默认4个
 * @returns {Object} 包含选项数组和正确答案索引的对象
 */
function generateWordOptions(words, testMode, currentWord, optionCount = 4) {
  let field = '';
  let correctAnswer = '';

  if (testMode === 'en_to_cn' || testMode === 'en2zh') {
    // 英译汉：显示英文，选择中文
    field = 'meaning';
    correctAnswer = currentWord.meaning || currentWord.definition || currentWord.chinese;
  } else if (testMode === 'cn_to_en' || testMode === 'zh2en') {
    // 汉译英：显示中文，选择英文
    field = 'word';
    correctAnswer = currentWord.word || currentWord.english;
  }

  if (!correctAnswer) {
    console.error('无法获取正确答案', { testMode, currentWord });
    return { options: [], correctIndex: -1 };
  }

  const options = generateOptions(words, field, correctAnswer, optionCount);
  const correctIndex = options.indexOf(correctAnswer);

  return {
    options,
    correctIndex
  };
}

/**
 * 生成短语选择题选项
 * @param {Array} phrases - 短语数组
 * @param {string} field - 字段名（'chinese'、'phrase'等）
 * @param {string} correctAnswer - 正确答案
 * @param {number} optionCount - 选项数量，默认4个
 * @returns {Array} 选项数组
 */
function generatePhraseOptions(phrases, field, correctAnswer, optionCount = 4) {
  return generateOptions(phrases, field, correctAnswer, optionCount);
}

module.exports = {
  generateOptions,
  generateWordOptions,
  generatePhraseOptions,
  extractFieldValue,
  shuffleArray
};
