const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;

  try {
    // 查询当前用户的学生列表
    const result = await db.collection('students').where({
      teacherOpenid: openid
    }).orderBy('createTime', 'desc').get();

    // 为每个学生获取成绩数据
    const studentsWithScores = await Promise.all(
      result.data.map(async (student) => {
        try {
          const scoresResult = await db.collection('student_scores').where({
            studentId: student._id
          }).orderBy('examDate', 'desc').get();

          return {
            ...student,
            scores: scoresResult.data || []
          };
        } catch (error) {
          console.error('获取学生成绩失败:', error);
          return {
            ...student,
            scores: []
          };
        }
      })
    );

    return {
      success: true,
      data: studentsWithScores
    };
  } catch (error) {
    console.error('获取学生列表失败:', error);
    return {
      success: false,
      message: '获取学生列表失败',
      error: error.message
    };
  }
};
