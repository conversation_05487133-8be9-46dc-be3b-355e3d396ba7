const app = getApp()

Page({
  data: {
    username: '',
    password: '',
    confirmPassword: '',
    isRegistering: false // 防止重复提交
  },

  // 用户名输入
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    })
  },

  // 密码输入
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    })
  },

  // 确认密码输入
  onConfirmPasswordInput(e) {
    this.setData({
      confirmPassword: e.detail.value
    })
  },

  // 处理注册
  async handleRegister() {
    const { username, password, confirmPassword, isRegistering } = this.data

    // 防止重复提交
    if (isRegistering) {
      return
    }

    // 表单验证
    if (!username || !password || !confirmPassword) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      })
      return
    }

    if (username.length < 4 || username.length > 20) {
      wx.showToast({
        title: '用户名长度为4-20个字符',
        icon: 'none'
      })
      return
    }

    if (password.length < 6 || password.length > 20) {
      wx.showToast({
        title: '密码长度为6-20个字符',
        icon: 'none'
      })
      return
    }

    if (password !== confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      })
      return
    }

    try {
      // 设置注册中状态，防止重复提交
      this.setData({
        isRegistering: true
      })

      wx.showLoading({
        title: '注册中...'
      })

      const res = await wx.cloud.callFunction({
        name: 'register',
        data: {
          username,
          password
        }
      })

      console.log('注册云函数返回结果:', res)

      if (res.result && res.result.code === 200) {
        // 注册成功，不自动登录，让用户手动登录
        console.log('注册成功:', res.result.data)

        wx.showModal({
          title: '注册成功',
          content: '账号注册成功！现在可以使用您的账号密码登录了。',
          showCancel: false,
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              console.log('注册成功，准备跳转到登录界面')
              wx.redirectTo({
                url: '/pages/login/login',
                success: () => {
                  console.log('成功跳转到登录界面')
                },
                fail: (error) => {
                  console.error('跳转到登录界面失败:', error)
                }
              })
            }
          }
        })
      } else {
        // 处理注册失败的情况
        const errorMessage = res.result?.message || '注册失败，请重试'
        console.log('注册失败:', errorMessage)
        wx.showToast({
          title: errorMessage,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('注册失败：', error)
      wx.showToast({
        title: error.message || '注册失败，请重试',
        icon: 'none'
      })
    } finally {
      // 确保loading状态被隐藏
      try {
        wx.hideLoading()
      } catch (e) {
        console.log('hideLoading error:', e)
      }

      // 重置注册状态
      this.setData({
        isRegistering: false
      })
    }
  },

  // 返回登录页
  handleBack() {
    wx.navigateBack()
  },

  // 显示用户协议
  showAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/agreement'
    })
  },

  // 显示隐私政策
  showPrivacy() {
    wx.navigateTo({
      url: '/pages/privacy/privacy'
    })
  }
}) 