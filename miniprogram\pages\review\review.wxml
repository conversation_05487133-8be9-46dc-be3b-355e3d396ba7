<!-- 单词复习页面 -->
<view class="container">
  <!-- 顶部进度 -->
  <view class="progress-bar">
    <view class="progress-inner" style="width: {{progress}}%"></view>
  </view>
  <view class="progress-text">{{currentIndex + 1}}/{{totalWords}}</view>

  <!-- 单词卡片 -->
  <view class="word-card">
    <view class="word">{{currentWord.word}}</view>
    <view class="phonetic" wx:if="{{showPhonetic}}">[{{currentWord.phonetic}}]</view>
    <view class="meaning" wx:if="{{showMeaning}}">
      <view class="meaning-item" wx:for="{{currentWord.meanings}}" wx:key="index">
        <text class="part-of-speech">{{item.partOfSpeech}}.</text>
        <text class="definition">{{item.definition}}</text>
      </view>
    </view>
    <view class="example" wx:if="{{showExample}}">
      <view class="example-item" wx:for="{{currentWord.examples}}" wx:key="index">
        <text class="example-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-bar">
    <view class="action-btn" bindtap="playAudio" wx:if="{{showAudio}}">
      <image class="icon" src="/images/audio.png" mode="aspectFit"></image>
      <text>播放发音</text>
    </view>
    <view class="action-btn" bindtap="togglePhonetic">
      <image class="icon" src="/images/phonetic.png" mode="aspectFit"></image>
      <text>{{showPhonetic ? '隐藏音标' : '显示音标'}}</text>
    </view>
    <view class="action-btn" bindtap="toggleMeaning">
      <image class="icon" src="/images/meaning.png" mode="aspectFit"></image>
      <text>{{showMeaning ? '隐藏释义' : '显示释义'}}</text>
    </view>
    <view class="action-btn" bindtap="toggleExample">
      <image class="icon" src="/images/example.png" mode="aspectFit"></image>
      <text>{{showExample ? '隐藏例句' : '显示例句'}}</text>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-bar">
    <view class="btn btn-wrong" bindtap="markAsWrong">不认识</view>
    <view class="btn btn-remember" bindtap="markAsRemember">认识</view>
  </view>

  <!-- 复习完成弹窗 -->
  <view class="modal" wx:if="{{showCompleteModal}}">
    <view class="modal-content">
      <view class="modal-title">复习完成</view>
      <view class="modal-stats">
        <view class="stat-item">
          <text class="stat-label">复习单词</text>
          <text class="stat-value">{{totalWords}}个</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">正确率</text>
          <text class="stat-value">{{correctRate}}%</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">用时</text>
          <text class="stat-value">{{formatTime(reviewTime)}}</text>
        </view>
      </view>
      <view class="modal-buttons">
        <view class="modal-btn" bindtap="goToHome">返回首页</view>
      </view>
    </view>
  </view>
</view> 