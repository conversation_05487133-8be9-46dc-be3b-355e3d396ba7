const app = getApp();
const VIP_CONFIG = require('../../utils/vip-config.js');

Page({
  data: {
    userInfo: {
      nickName: '学习者',
      avatarUrl: '/assets/icons/profile.png'
    },
    userNickname: 'G', // 用户昵称首字母
    currentLibrary: {
      id: '3500',
      name: '3500大纲词汇'
    },
    plan: {
      dailyWords: 20,
      todayLearned: 5
    },
    planProgress: 25,
    todayLearned: 5,
    canLearn: true,
    canReview: true,
    stats: {
      totalWords: 156,
      masteredWords: 89,
      correctRate: 85,
      totalTime: 1200
    },
    records: [
      { date: '今天', words: 5, time: 15 },
      { date: '昨天', words: 20, time: 45 },
      { date: '前天', words: 18, time: 38 }
    ],
    isLoggedIn: false,
    showVipFeatures: VIP_CONFIG.enabled, // 是否显示VIP功能
    todayWords: 0,
    todayMinutes: 0,
    consecutiveDays: 0,
    registerDays: 1,
    userStats: {
      totalWords: 0,
      masteredWords: 0,
      totalTime: 0
    },
    noticeText: '各类词库正在逐步上传完善中，功能需求和bug欢迎通过"帮助与反馈"告诉我们',
    greetingFontSize: 26, // 动态字体大小，降低默认值
    // 快捷方式相关数据
    shortcutSettings: {
      source: 'learning', // learning, received, shared
      mode: '', // 测试模式
      modeText: '',
      library: '', // 词库
      libraryText: '',
      sharer: '', // 分享人（仅收到的分享时使用）
      sharerText: ''
    },
    // 当前显示的任务（每个类型只显示一个）
    currentLearningTask: null,
    currentReceivedTask: null,
    currentSharedTask: null,
    // 所有任务列表（用于切换）
    allLearningTasks: [],
    allReceivedTasks: [],
    allSharedTasks: [],
    // 当前任务索引
    currentLearningIndex: 0,
    currentReceivedIndex: 0,
    currentSharedIndex: 0,
    // 当前分享的任务
    currentShareTask: null,
    // 收到分享详情弹窗
    showReceivedDetailModal: false,
    currentReceivedDetail: null,
    // 弹窗显示状态
    showSettingsModal: false,
    showModeModal: false,
    showLibraryModal: false,
    showSharerModal: false,
    // 选择器数据
    commonLibraries: [
      { id: 'words_3500', name: '高考3500（顺序版）' },
      { id: 'words_3500_luan', name: '高考3500（乱序版）' },
      { id: 'words_cet4', name: '大学英语四级' },
      { id: 'words_cet6', name: '大学英语六级' },
      { id: 'words_bjwusan', name: '五三高频词（北京卷）' }
    ],
    availableSharers: [] // 可用的分享人列表
  },

  onLoad: function() {
    console.log('=== 首页加载 ===');
    console.log('data:', this.data);
    
    // 初始化页面数据
    this.initPageData();
    
    // 初始化用户信息和统计数据
    this.initUserInfo();
    this.loadTodayStats();
    
    // 计算注册天数
    this.calculateRegisterDays();
    
    // 加载通知内容
    this.loadNotice();

    // 初始化快捷方式
    this.initShortcut();

    // 启动时立即加载所有类型的任务数据
    this.loadAllTasksOnStartup();
  },

  // 显示短链接输入框
  showShortLinkInput() {
    wx.showModal({
      title: '短链接访问',
      content: '请输入8位短链接ID',
      editable: true,
      placeholderText: '例如: abc123XY',
      success: (res) => {
        if (res.confirm && res.content) {
          const shortId = res.content.trim();
          if (shortId.length === 8) {
            wx.navigateTo({
              url: `/pages/shortlink/shortlink?id=${shortId}`
            });
          } else {
            wx.showToast({
              title: '请输入8位短链接ID',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 页面显示时触发
   */
  onShow() {
    console.log('=== 首页显示 ===');
    
    // 重新检查登录状态和加载数据
    this.loadUserData();
    
    // 检查是否有从词库页面选择的词库
    const app = getApp();
    if (app.globalData.selectedLibrary) {
      // 更新页面显示的当前词库
      this.setData({
        currentLibrary: app.globalData.selectedLibrary
      });
      // 清除全局数据
      app.globalData.selectedLibrary = null;
    }
    
    // 每次显示页面时刷新数据
    this.refreshData();
    
    // 刷新今日统计数据
    this.loadTodayStats();
    
    // 计算注册天数
    this.calculateRegisterDays();
    
    // 设置自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }
  },

  // 初始化页面数据
  initPageData() {
    try {
      // 尝试加载用户信息和学习数据
      this.loadUserData();
      this.updateProgress();
    } catch (error) {
      console.error('初始化数据失败:', error);
    }
  },

  // 加载用户数据
  async loadUserData() {
    try {
      console.log('=== 首页加载用户数据 ===');
      
      // 获取app实例
      const app = getApp();
      
      // 强制重新检查登录状态
      app.checkLoginStatus();
      
      // 检查登录状态
      const isLoggedIn = app.isLoggedIn();
      console.log('登录状态:', isLoggedIn);
      
      const userInfo = await app.getUserInfo();
      console.log('获取到的用户信息:', userInfo);
      
      // 确保 userInfo.stats 对象存在
      if (userInfo && !userInfo.stats) {
        userInfo.stats = {
          totalWords: 0,
          continuousDays: 0,
          masteredWords: 0
        };
      }
      
      // 计算用户昵称首字母
      const nickname = userInfo?.wechatInfo?.nickName || userInfo?.username || '学习者';
      const userNickname = nickname.charAt(0).toUpperCase();
      
      // 设置用户信息到页面数据中
      this.setData({
        userInfo,
        isLoggedIn,
        userNickname
      });

      // 根据登录状态重新计算问候语和字体大小
      if (isLoggedIn && userInfo && userInfo._id) {
        console.log('用户已登录，显示个性化内容');
        // 登录用户重新计算注册天数
        this.calculateRegisterDays();
      } else {
        console.log('用户未登录，显示默认内容');
        // 未登录用户显示欢迎信息
        const fontSize = this.calculateGreetingFontSize(null);
        this.setData({
          registerDays: null,
          registerDaysText: null,
          greetingFontSize: fontSize
        });
      }
    } catch (error) {
      console.error('加载用户数据失败:', error);
             // 出错时显示默认数据
       const defaultUserInfo = {
         _id: null,
         username: '未登录用户',
         wechatInfo: {
           nickName: '未登录用户',
           avatarUrl: '/assets/icons/profile.png'
         },
         stats: {
           totalWords: 0,
           continuousDays: 0,
           masteredWords: 0
         }
       };
       this.setData({
         userInfo: defaultUserInfo,
         isLoggedIn: false,
         userNickname: '未',
         registerDays: null,
         registerDaysText: null,
         greetingFontSize: this.calculateGreetingFontSize(null)
       });
    }
  },

  // 更新学习进度
  updateProgress() {
    const { todayLearned, plan } = this.data;
    const progress = Math.min(Math.round((todayLearned / plan.dailyWords) * 100), 100);
    
    this.setData({
      planProgress: progress
    });
  },

  // 刷新页面数据
  refreshData() {
    console.log('刷新页面数据');
    this.loadUserData();
    this.updateProgress();
  },

  // 开始学习
  startLearning() {
    // 检查是否登录
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 检查是否能学习新单词
    if (!this.data.canLearn) {
      wx.showToast({
        title: '今日学习任务已完成',
        icon: 'success'
      });
      return;
    }

    // 跳转到学习页面
    wx.navigateTo({
      url: '/pages/learning/learning'
    });
  },

  // 开始复习
  startReview() {
    // 检查是否登录
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 检查是否有单词需要复习
    if (!this.data.canReview) {
      wx.showToast({
        title: '暂无单词需要复习',
        icon: 'none'
      });
      return;
    }

    // 跳转到复习页面
    wx.navigateTo({
      url: '/pages/review/review'
    });
  },

  // 处理功能卡片点击
  onFunctionTap(e) {
    const id = e.currentTarget.dataset.id;
    
    // 检查是否为VIP功能且用户没有VIP权限
    if (id !== 'teacher-tools' && VIP_CONFIG.isVipFunction(id)) {
      const userInfo = this.data.userInfo;
      if (!userInfo || !userInfo.isVip) {
        wx.showModal({
          title: '会员功能',
          content: '此功能需要开通会员才能使用，是否前往开通？',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/vip/vip'
              });
            }
          }
        });
        return;
      }
    }
    
    // 根据功能ID跳转到对应页面
    switch(id) {
      case 'wordtest':
        this.navigateToWordtest();
        break;

      case 'competition':
        this.navigateToCompetition();
        break;
      case 'writing':
        wx.navigateTo({
          url: '/reading-writing/writing/writing'
        });
        break;

      case 'teacher-tools':
        this.navigateToTeacherTools();
        break;
      case 'reading':
        this.navigateToReading();
        break;
      case 'mistakes':
        this.navigateToMistakes();
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  // 切换词库
  onSwitchLibrary() {
    wx.navigateTo({
      url: '/pages/wordbank/library-selector/library-selector'
    });
  },

  // 点击促销横幅
  onPromoBannerTap() {
    // 如果启用了VIP功能，跳转到VIP页面
    if (VIP_CONFIG.enabled) {
      wx.navigateTo({
        url: '/pages/vip/vip'
      });
    } else {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      });
    }
  },

  // 分享给好友
  onShareAppMessage() {
    return {
      title: '墨词自习室 - 师生互动学习空间',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share-logo.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '墨词自习室 - 师生互动学习空间',
      query: '',
      imageUrl: '/assets/images/share-logo.png'
    };
  },

  // 跳转到个人中心
  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    });
  },

  // 跳转到词库
  goToWordbank() {
    wx.switchTab({
      url: '/pages/wordbank/wordbank'
    });
  },

  // 跳转到错题本
  goToMistakes() {
    wx.navigateTo({
      url: '/pages/mistakes/mistakes'
    });
  },

  // 跳转到帮助
  goToHelp() {
    wx.navigateTo({
      url: '/pages/profile/help/help'
    });
  },

  // 跳转到关于
  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },

  // 初始化用户信息
  initUserInfo() {
    // 这里可以添加初始化用户信息的逻辑
  },

  // 加载今日统计数据 - 简化版，不查询数据库
  async loadTodayStats() {
    try {
      // 从本地存储获取今日学习数据
      const todayData = wx.getStorageSync('todayStats') || {};
      const today = new Date().toDateString();
      
      if (todayData.date === today) {
        this.setData({
          todayWords: todayData.words || 0,
          todayMinutes: Math.round((todayData.time || 0) / 60000) || 0,
          consecutiveDays: todayData.consecutiveDays || 0,
          userStats: {
            totalWords: 0,
            masteredWords: 0,
            totalTime: 0
          }
        });
      } else {
        // 如果不是今天的数据，重置
        this.setData({
          todayWords: 0,
          todayMinutes: 0,
          consecutiveDays: 0,
          userStats: {
            totalWords: 0,
            masteredWords: 0,
            totalTime: 0
          }
        });
      }
    } catch (error) {
      console.error('加载今日统计数据失败:', error);
      this.setData({
        todayWords: 0,
        todayMinutes: 0,
        consecutiveDays: 0,
        userStats: {
          totalWords: 0,
          masteredWords: 0,
          totalTime: 0
        }
      });
    }
  },

  // 计算连续学习天数 - 简化版，使用本地存储
  async calculateConsecutiveDays() {
    try {
      // 从本地存储获取连续学习天数
      const streakData = wx.getStorageSync('learningStreak') || { days: 0, lastDate: null };
      return streakData.days || 0;
    } catch (error) {
      console.error('计算连续学习天数失败:', error);
      return 0;
    }
  },

  // 获取用户学习统计 - 简化版，返回默认值
  async getUserLearningStats() {
    try {
      // 从本地存储获取统计数据
      const statsData = wx.getStorageSync('userStats') || {
        totalWords: 0,
        masteredWords: 0,
        totalTime: 0
      };
      return statsData;
    } catch (error) {
      console.error('获取用户学习统计失败:', error);
      return {
        totalWords: 0,
        masteredWords: 0,
        totalTime: 0
      };
    }
  },

  // 导航到单词检测
  navigateToWordtest() {
    wx.navigateTo({
      url: '/pages/wordtest/wordtest'
    });
  },

  // 导航到词库学习
  navigateToWordbank() {
    wx.switchTab({
      url: '/pages/wordbank/wordbank'
    });
  },



  // 导航到听写模式
  navigateToDictation() {
    // 设置returnTo参数，用于wordbank页面识别来源
    wx.switchTab({
      url: '/pages/wordbank/wordbank'
    });
    
    // 通过全局数据传递来源信息
    const app = getApp();
    app.globalData.returnTo = 'dictation';
  },

  // 导航到竞赛模式
  navigateToCompetition() {
    wx.navigateTo({
      url: '/pages/competition/competition'
    });
  },

  // 导航到错题本
  navigateToMistakes() {
    wx.navigateTo({
      url: '/pages/mistakes/mistakes'
    });
  },

  // 导航到阅读材料
  navigateToReading() {
    wx.showActionSheet({
      itemList: ['外媒阅读', '经典小说'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.navigateTo({
            url: '/reading-writing/reading/foreign-media/foreign-media'
          });
        } else if (res.tapIndex === 1) {
          wx.navigateTo({
            url: '/reading-writing/reading/classic-novels/classic-novels'
          });
        }
      }
    });
  },

  // 导航到教师专区
  navigateToTeacherTools() {
    wx.navigateTo({
      url: '/teacher-tools/teacher-tools'
    });
  },

  // 导航到个人中心
  navigateToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    });
  },

  // 计算注册天数
  async calculateRegisterDays() {
    try {
      // 检查用户是否已登录
      if (!this.data.isLoggedIn) {
        console.log('用户未登录，不显示注册天数');
        // 未登录用户显示欢迎信息
        this.setData({
          registerDays: null,
          registerDaysText: null,
          greetingFontSize: 26
        });
        return;
      }

      // 获取用户信息，根据用户的实际注册时间计算
      const userInfo = this.data.userInfo;
      if (!userInfo || !userInfo.createTime) {
        console.log('用户信息不完整，使用默认天数');
        this.setData({
          registerDays: 1,
          registerDaysText: '1天',
          greetingFontSize: 26
        });
        return;
      }

      // 计算从注册日期到今天的天数
      const today = new Date();
      const createTime = new Date(userInfo.createTime);
      const timeDiff = today.getTime() - createTime.getTime();
      const daysDiff = Math.max(1, Math.ceil(timeDiff / (1000 * 3600 * 24))); // 至少1天

      console.log('用户注册时间:', createTime, '今天:', today, '注册天数:', daysDiff);

      // 格式化显示天数
      let formattedDays = this.formatDaysDisplay(daysDiff);

      // 检查文本长度，如果太长则使用紧凑模式
      const nickname = userInfo?.wechatInfo?.nickName || userInfo?.username || '学习者';
      const fullText = `${nickname}，今天是您使用墨词自习室的第${formattedDays}`;

      if (fullText.length > 26) {
        // 文本太长，使用紧凑模式（降低阈值）
        formattedDays = this.formatDaysDisplay(daysDiff, true);
      }

      // 计算动态字体大小
      const fontSize = this.calculateGreetingFontSize(formattedDays);

      this.setData({
        registerDays: daysDiff,
        registerDaysText: formattedDays,
        greetingFontSize: fontSize
      });

    } catch (error) {
      console.error('计算注册天数失败:', error);
      // 如果是登录用户但计算失败，显示默认值
      if (this.data.isLoggedIn) {
        this.setData({
          registerDays: 1,
          registerDaysText: '1天',
          greetingFontSize: 26
        });
      } else {
        // 未登录用户不显示天数
        this.setData({
          registerDays: null,
          registerDaysText: null,
          greetingFontSize: 26
        });
      }
    }
  },

  // 格式化天数显示
  formatDaysDisplay(days, compact = false) {
    if (days >= 365) {
      const years = Math.floor(days / 365);
      const remainingDays = days % 365;
      if (remainingDays === 0) {
        return `${years}年`;
      } else if (remainingDays >= 30) {
        const months = Math.floor(remainingDays / 30);
        const finalDays = remainingDays % 30;
        if (finalDays === 0) {
          return `${years}年${months}个月`;
        } else if (compact) {
          // 紧凑模式：1年1月5天
          return `${years}年${months}月${finalDays}天`;
        } else if (finalDays === 1) {
          return `${years}年${months}个月零1天`;
        } else {
          return `${years}年${months}个月零${finalDays}天`;
        }
      } else if (compact) {
        // 紧凑模式：1年5天
        return `${years}年${remainingDays}天`;
      } else if (remainingDays === 1) {
        return `${years}年零1天`;
      } else {
        return `${years}年零${remainingDays}天`;
      }
    } else if (days >= 30) {
      const months = Math.floor(days / 30);
      const remainingDays = days % 30;
      if (remainingDays === 0) {
        return `${months}个月`;
      } else if (compact) {
        // 紧凑模式：1月5天
        return `${months}月${remainingDays}天`;
      } else if (remainingDays === 1) {
        return `${months}个月零1天`;
      } else {
        return `${months}个月零${remainingDays}天`;
      }
    } else {
      return `${days}天`;
    }
  },

  // 计算问候语字体大小
  calculateGreetingFontSize(daysText) {
    // 获取用户昵称
    const nickname = this.data.userInfo?.wechatInfo?.nickName || this.data.userInfo?.username || '学习者';

    // 构建完整的问候语文本
    let fullText;
    if (this.data.isLoggedIn && daysText) {
      fullText = `${nickname}，今天是您使用墨词自习室的第${daysText}`;
    } else {
      fullText = `${nickname}，欢迎使用墨词自习室`;
    }

    // 根据文本长度动态调整字体大小
    const textLength = fullText.length;

    // 获取系统信息来判断屏幕尺寸
    const systemInfo = wx.getSystemInfoSync();
    const screenWidth = systemInfo.screenWidth;

    let baseFontSize = 26; // 默认字体大小，降低基础大小

    // 根据屏幕宽度调整基础字体大小
    if (screenWidth <= 320) {
      baseFontSize = 20; // 小屏幕
    } else if (screenWidth <= 375) {
      baseFontSize = 22; // 中等屏幕
    } else {
      baseFontSize = 26; // 大屏幕，降低大小
    }

    // 根据文本长度进一步调整
    if (textLength > 28) {
      baseFontSize = Math.max(baseFontSize - 6, 16); // 很长文本，最小16rpx
    } else if (textLength > 24) {
      baseFontSize = Math.max(baseFontSize - 4, 18); // 较长文本，最小18rpx
    } else if (textLength > 20) {
      baseFontSize = Math.max(baseFontSize - 2, 20); // 中等文本，最小20rpx
    }

    return baseFontSize;
  },

  // 加载通知内容
  async loadNotice() {
    try {
      console.log('开始加载通知内容...');

      const result = await wx.cloud.callFunction({
        name: 'getNotice'
      });

      if (result.result && result.result.success && result.result.data) {
        console.log('通知加载成功:', result.result.data);
        this.setData({
          noticeText: result.result.data.content
        });
      } else {
        console.log('使用默认通知内容');
        // 保持默认通知内容
      }
    } catch (error) {
      console.error('加载通知失败:', error);
      // 发生错误时保持默认通知内容
    }
  },

  // ========== 快捷方式相关方法 ==========

  // 显示快捷方式设置弹窗
  showShortcutSettings() {
    this.setData({ showSettingsModal: true });
  },

  // 隐藏快捷方式设置弹窗
  hideShortcutSettings() {
    this.setData({ showSettingsModal: false });
  },

  // 初始化快捷方式
  initShortcut() {
    this.loadShortcutDefaults();
  },

  // 启动时加载所有任务数据
  async loadAllTasksOnStartup() {
    try {
      // 并行加载所有类型的任务
      const [learningTasks, receivedTasks, sharedTasks] = await Promise.all([
        this.loadRealLearningTasks(),
        this.loadRealReceivedTasks(),
        this.loadRealSharedTasks()
      ]);

      // 保存所有任务列表并设置默认任务
      const currentTasks = this.setDefaultTasks(learningTasks, receivedTasks, sharedTasks);

      this.setData({
        allLearningTasks: learningTasks,
        allReceivedTasks: receivedTasks,
        allSharedTasks: sharedTasks,
        currentLearningTask: currentTasks.learning.task,
        currentReceivedTask: currentTasks.received.task,
        currentSharedTask: currentTasks.shared.task,
        currentLearningIndex: currentTasks.learning.index,
        currentReceivedIndex: currentTasks.received.index,
        currentSharedIndex: currentTasks.shared.index
      });

      console.log('所有任务加载完成:', {
        learning: learningTasks.length,
        received: receivedTasks.length,
        shared: sharedTasks.length
      });
    } catch (error) {
      console.error('加载任务失败:', error);
    }
  },

  // 切换学习任务
  switchLearningTask() {
    const { allLearningTasks, currentLearningIndex } = this.data;
    if (allLearningTasks.length <= 1) {
      wx.showToast({
        title: '暂无其他学习任务',
        icon: 'none'
      });
      return;
    }

    const nextIndex = (currentLearningIndex + 1) % allLearningTasks.length;
    this.setData({
      currentLearningIndex: nextIndex,
      currentLearningTask: allLearningTasks[nextIndex]
    });

    wx.showToast({
      title: `切换到: ${allLearningTasks[nextIndex].title}`,
      icon: 'none',
      duration: 1500
    });
  },

  // 切换收到分享任务
  switchReceivedTask() {
    const { allReceivedTasks, currentReceivedIndex } = this.data;
    if (allReceivedTasks.length <= 1) {
      wx.showToast({
        title: '暂无其他收到的分享',
        icon: 'none'
      });
      return;
    }

    const nextIndex = (currentReceivedIndex + 1) % allReceivedTasks.length;
    this.setData({
      currentReceivedIndex: nextIndex,
      currentReceivedTask: allReceivedTasks[nextIndex]
    });

    wx.showToast({
      title: `切换到: ${allReceivedTasks[nextIndex].title}`,
      icon: 'none',
      duration: 1500
    });
  },

  // 切换我的分享任务
  switchSharedTask() {
    const { allSharedTasks, currentSharedIndex } = this.data;
    if (allSharedTasks.length <= 1) {
      wx.showToast({
        title: '暂无其他分享任务',
        icon: 'none'
      });
      return;
    }

    const nextIndex = (currentSharedIndex + 1) % allSharedTasks.length;
    this.setData({
      currentSharedIndex: nextIndex,
      currentSharedTask: allSharedTasks[nextIndex]
    });

    wx.showToast({
      title: `切换到: ${allSharedTasks[nextIndex].title}`,
      icon: 'none',
      duration: 1500
    });
  },

  // 继续新学
  continueNewStudy(e) {
    const task = e.currentTarget.dataset.task;
    console.log('继续新学:', task);

    if (!task || !task.data) {
      wx.showToast({
        title: '任务数据异常',
        icon: 'none'
      });
      return;
    }

    // 跳转到对应的学习页面
    wx.navigateTo({
      url: `/pages/test/test?mode=${task.data.mode}&library=${task.data.libraryId}&source=learning`
    });
  },

  // 开始复习
  startReview(e) {
    const task = e.currentTarget.dataset.task;
    console.log('开始复习:', task);

    if (!task || !task.data) {
      wx.showToast({
        title: '任务数据异常',
        icon: 'none'
      });
      return;
    }

    if (!task.hasReview) {
      wx.showToast({
        title: '暂无需要复习的词汇',
        icon: 'none'
      });
      return;
    }

    // 跳转到复习页面
    wx.navigateTo({
      url: `/pages/test/test?mode=${task.data.mode}&library=${task.data.libraryId}&source=review`
    });
  },

  // 选择关卡开始（多关卡任务）
  selectLevelToStart(e) {
    const task = e.currentTarget.dataset.task;
    console.log('选择关卡开始:', task);

    if (!task || !task.data) {
      wx.showToast({
        title: '任务数据异常',
        icon: 'none'
      });
      return;
    }

    if (task.isExpired) {
      wx.showToast({
        title: '分享已过期',
        icon: 'none'
      });
      return;
    }

    // 跳转到关卡选择界面，与"我收到的分享"页面保持一致
    const shareData = task.data;
    const shareId = shareData.shareId;
    const testType = shareData.testType || shareData.testMode;

    console.log('跳转到关卡选择界面:', shareId, testType);
    wx.navigateTo({
      url: `/pages/competition/level-select/level-select?shareId=${shareId}&mode=share&testType=${testType}`
    });
  },

  // 开始单组测试
  startSingleTest(e) {
    const task = e.currentTarget.dataset.task;
    console.log('开始单组测试:', task);

    if (!task || !task.data) {
      wx.showToast({
        title: '任务数据异常',
        icon: 'none'
      });
      return;
    }

    if (task.isExpired) {
      wx.showToast({
        title: '分享已过期',
        icon: 'none'
      });
      return;
    }

    // 根据测试模式跳转到对应的测试页面
    const shareData = task.data;
    const shareId = shareData.shareId;
    const testMode = shareData.testType || shareData.testMode;

    let url = '';

    switch (testMode) {
      case 'dictation':
      case 'spelling':
        url = `/pages/spelling/practice/practice?shareId=${shareId}&shareMode=share`;
        break;
      case 'en_to_cn':
      case 'cn_to_en':
        url = `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${testMode}`;
        break;
      case 'phrase_en2zh':
        url = `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=en_to_cn&isPhrase=true`;
        break;
      case 'phrase_zh2en':
        url = `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=cn_to_en&isPhrase=true`;
        break;
      case 'elimination':
        url = `/pages/competition/competition?shareId=${shareId}&shareMode=share`;
        break;
      default:
        wx.showToast({
          title: '不支持的测试类型',
          icon: 'none'
        });
        return;
    }

    console.log('跳转到单组测试:', url);
    wx.navigateTo({
      url: url
    });
  },

  // 查看收到分享详情
  viewReceivedDetails(e) {
    const task = e.currentTarget.dataset.task;
    console.log('查看收到分享详情:', task);

    if (!task || !task.data) {
      wx.showToast({
        title: '任务数据异常',
        icon: 'none'
      });
      return;
    }

    const share = task.data;

    // 计算平均分（与"我收到的分享"页面保持一致）
    let averageScore = 0;
    if (share.myInfo && share.myInfo.results && share.myInfo.results.length > 0) {
      const totalScore = share.myInfo.results.reduce((sum, result) => sum + result.score, 0);
      averageScore = Math.round(totalScore / share.myInfo.results.length);
    }

    // 处理关卡数据和正确率
    let levelDataWithAccuracy = [];
    if (share.isMultiLevel && share.myInfo && share.myInfo.progress) {
      const testMode = share.testMode || share.testType;
      levelDataWithAccuracy = this.processLevelDataWithAccuracy(share.myInfo.progress, testMode, share.myInfo.results);
    }

    // 格式化时间显示
    const firstVisitTimeText = share.myInfo?.firstVisitTime ?
      this.formatTime(share.myInfo.firstVisitTime) : '未参与';
    const lastTestTimeText = share.myInfo?.lastTestTime ?
      this.formatTime(share.myInfo.lastTestTime) : '未测试';

    // 为详情弹窗准备数据
    const detailData = {
      ...share,
      shareTitle: task.title,
      testModeText: this.getTestModeText(share.testType || share.testMode),
      averageScore: averageScore,
      levelDataWithAccuracy: levelDataWithAccuracy,
      firstVisitTimeText: firstVisitTimeText,
      lastTestTimeText: lastTestTimeText,
      // 添加progress相关信息
      totalLevels: this.calculateTotalLevels(share),
      completedLevels: share.myInfo?.progress?.completedLevels?.length || 0,
      currentLevel: share.myInfo?.progress?.currentLevel
    };

    this.setData({
      currentReceivedDetail: detailData,
      showReceivedDetailModal: true
    });
  },

  // 查看我的分享详情
  viewMyShareDetails(e) {
    const task = e.currentTarget.dataset.task;
    console.log('查看我的分享详情:', task);

    if (!task || !task.data) {
      wx.showToast({
        title: '任务数据异常',
        icon: 'none'
      });
      return;
    }

    // 跳转到分享管理页面
    wx.navigateTo({
      url: `/pages/profile/share/share?shareId=${task.data.shareId}`
    });
  },

  // 重新分享我的任务
  shareMyTask(e) {
    const task = e.currentTarget.dataset.task;
    console.log('重新分享任务:', task);

    if (!task || !task.data) {
      wx.showToast({
        title: '任务数据异常',
        icon: 'none'
      });
      return;
    }

    // 显示分享选项弹窗，与"我的分享"页面保持一致
    wx.showActionSheet({
      itemList: ['复制测试信息', '分享给微信好友', '分享到朋友圈'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 复制测试信息
            this.copyTaskInfo(task);
            break;
          case 1:
            // 分享给微信好友
            this.shareToWeChatFriends(task);
            break;
          case 2:
            // 分享到朋友圈
            this.shareToMoments(task);
            break;
        }
      }
    });
  },

  // 复制测试信息（与"我的分享"页面格式完全一致）
  copyTaskInfo(task) {
    const shareText = `墨词自习室测试邀请\n\n测试ID: ${task.data.shareId}\n模式: ${task.testType}\n\n请在"墨词自习室"小程序中，进入"我的"->"收到的分享"，输入测试ID参与测试。`;

    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '测试信息已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 分享给微信好友
  shareToWeChatFriends(task) {
    console.log('分享给微信好友:', task);

    // 设置分享数据，供onShareAppMessage使用
    this.setData({
      currentShareTask: {
        ...task,
        shareTitle: `📝 ${task.title} - ${task.testType}`,
        sharePath: `/pages/wordtest/test/test?shareId=${task.data.shareId}&shareMode=share&testMode=${task.data.testType || task.data.testMode}`
      }
    });

    // 提示用户使用右上角分享按钮，与竞赛页面的实现方式一致
    wx.showModal({
      title: '分享给微信好友',
      content: '请点击右上角"..."按钮，选择"转发"来分享给朋友，朋友点击卡片即可直接参与测试！',
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 更新分享菜单，确保右上角分享可用
        wx.updateShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage']
        });
      }
    });
  },

  // 分享到朋友圈
  shareToMoments(task) {
    console.log('分享到朋友圈:', task);

    // 设置分享数据
    this.setData({
      currentShareTask: task
    });

    wx.showModal({
      title: '分享到朋友圈',
      content: '请点击右上角"..."按钮，选择"分享到朋友圈"来分享测试',
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 更新分享菜单，确保朋友圈分享可用
        wx.updateShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline']
        });
      }
    });
  },



  // 设置当前任务为默认显示
  setCurrentAsDefault(e) {
    const type = e.currentTarget.dataset.type;
    let currentTask = null;
    let taskType = '';

    switch (type) {
      case 'learning':
        currentTask = this.data.currentLearningTask;
        taskType = '学习任务';
        break;
      case 'received':
        currentTask = this.data.currentReceivedTask;
        taskType = '收到分享';
        break;
      case 'shared':
        currentTask = this.data.currentSharedTask;
        taskType = '我的分享';
        break;
    }

    if (!currentTask) {
      wx.showToast({
        title: '当前无任务',
        icon: 'none'
      });
      return;
    }

    try {
      // 保存默认任务信息到本地存储
      const defaultTask = {
        type: type,
        taskId: currentTask.id,
        title: currentTask.title,
        testType: currentTask.testType,
        setTime: new Date().toISOString()
      };

      wx.setStorageSync(`defaultTask_${type}`, defaultTask);

      wx.showToast({
        title: `已设为默认${taskType}`,
        icon: 'success',
        duration: 2000
      });

      console.log(`设置默认${taskType}:`, defaultTask);
    } catch (error) {
      console.error('设置默认任务失败:', error);
      wx.showToast({
        title: '设置失败',
        icon: 'none'
      });
    }
  },

  // 处理关卡数据和正确率（与"我收到的分享"页面保持一致）
  processLevelDataWithAccuracy(progress, testMode, allResults) {
    if (!progress) {
      return [];
    }

    const completedLevels = progress.completedLevels || [];
    const currentLevel = progress.currentLevel || 1;

    // 获取总关卡数，如果没有明确的总关卡数，就根据当前关卡和已完成关卡推断
    let totalLevels = progress.totalLevels || progress.maxLevel;
    if (!totalLevels) {
      // 取当前关卡和已完成关卡中的最大值
      const maxCompletedLevel = completedLevels.length > 0 ? Math.max(...completedLevels.map(l => parseInt(l))) : 0;
      totalLevels = Math.max(currentLevel, maxCompletedLevel);
    }

    // 生成所有关卡数据（从1到总关卡数）
    const levelDataArray = [];
    for (let i = 1; i <= totalLevels; i++) {
      // 查找该关卡的测试结果
      const levelResult = allResults && allResults.find(result =>
        result.levelId === i || result.levelId === String(i)
      );

      let accuracy = null;
      let score = 0;
      let correctCount = 0;
      let totalQuestions = 0;
      let isCompleted = completedLevels.includes(i) || completedLevels.includes(String(i));

      if (levelResult) {
        accuracy = parseFloat(levelResult.accuracy || 0);
        score = levelResult.score || 0;
        correctCount = levelResult.correctCount || 0;
        totalQuestions = levelResult.totalQuestions || 0;
        isCompleted = true;
      }

      // 只显示已完成的关卡或当前关卡
      if (isCompleted || i === currentLevel) {
        levelDataArray.push({
          levelIndex: i - 1, // 用于数组索引，从0开始
          levelNumber: i,    // 用于显示，从1开始
          accuracy: accuracy,
          score: score,
          correctCount: correctCount,
          totalQuestions: totalQuestions,
          status: isCompleted ? '已完成' : '进行中',
          isEliminationMode: testMode === 'elimination'
        });
      }
    }

    return levelDataArray;
  },

  // 计算平均正确率
  calculateAverageAccuracy(shareItem) {
    try {
      // 获取所有参与者的测试结果
      const results = shareItem.results || [];
      const visitors = shareItem.visitors || [];

      if (results.length === 0) {
        return 0;
      }

      // 按参与者分组计算每个人的平均正确率
      const participantAccuracies = {};

      results.forEach(result => {
        const openid = result.participantOpenid;
        if (!openid) return;

        let accuracy = 0;

        // 优先使用accuracy字段
        if (result.accuracy !== undefined && result.accuracy !== null) {
          accuracy = parseFloat(result.accuracy);
        }
        // 其次使用correctCount和totalCount计算
        else if (result.correctCount !== undefined && result.totalCount !== undefined && result.totalCount > 0) {
          accuracy = (result.correctCount / result.totalCount) * 100;
        }
        // 最后使用分数转换（300分对应100%）
        else if (result.score !== undefined && result.score > 0) {
          accuracy = Math.min(100, (result.score / 300) * 100);
        }

        // 确保正确率在0-100范围内
        accuracy = Math.max(0, Math.min(100, accuracy));

        if (!participantAccuracies[openid]) {
          participantAccuracies[openid] = [];
        }
        participantAccuracies[openid].push(accuracy);
      });

      // 计算每个参与者的平均正确率
      const participantAverages = [];
      Object.keys(participantAccuracies).forEach(openid => {
        const accuracies = participantAccuracies[openid];
        if (accuracies.length > 0) {
          const average = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
          participantAverages.push(average);
        }
      });

      // 计算所有参与者的平均正确率
      if (participantAverages.length > 0) {
        const overallAverage = participantAverages.reduce((sum, avg) => sum + avg, 0) / participantAverages.length;
        return Math.round(overallAverage);
      }

      return 0;
    } catch (error) {
      console.error('计算平均正确率失败:', error);
      return 0;
    }
  },

  // 计算总关卡数
  calculateTotalLevels(share) {
    // 优先从progress中获取
    if (share.myInfo?.progress?.totalLevels) {
      return share.myInfo.progress.totalLevels;
    }
    if (share.myInfo?.progress?.maxLevel) {
      return share.myInfo.progress.maxLevel;
    }

    // 从分享数据本身获取
    if (share.totalLevels) {
      return share.totalLevels;
    }
    if (share.levelCount) {
      return share.levelCount;
    }

    // 根据已完成关卡和当前关卡推断
    const completedLevels = share.myInfo?.progress?.completedLevels || [];
    const currentLevel = share.myInfo?.progress?.currentLevel || 1;

    if (completedLevels.length > 0) {
      const maxCompletedLevel = Math.max(...completedLevels.map(l => parseInt(l) || 0));
      return Math.max(currentLevel, maxCompletedLevel);
    }

    // 如果都没有，返回当前关卡数
    return currentLevel || 1;
  },

  // 格式化时间显示
  formatTime(timeString) {
    if (!timeString) return '未知';

    try {
      const date = new Date(timeString);
      const now = new Date();

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '时间格式错误';
      }

      // 格式化时间部分
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const timeStr = `${hours}:${minutes}`;

      // 获取日期部分（忽略时间）
      const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const nowOnly = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      // 计算天数差异
      const diffMs = nowOnly - dateOnly;
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      // 计算总的时间差
      const totalDiffMs = now - date;
      const totalDiffHours = Math.floor(totalDiffMs / (1000 * 60 * 60));
      const totalDiffMinutes = Math.floor(totalDiffMs / (1000 * 60));

      if (diffDays === 0) {
        // 今天
        if (totalDiffMinutes < 1) {
          return '刚刚';
        } else if (totalDiffMinutes < 60) {
          return `${totalDiffMinutes}分钟前`;
        } else if (totalDiffHours < 6) {
          return `${totalDiffHours}小时前`;
        } else {
          return `今天 ${timeStr}`;
        }
      } else if (diffDays === 1) {
        return `昨天 ${timeStr}`;
      } else if (diffDays === 2) {
        return `前天 ${timeStr}`;
      } else if (diffDays < 7) {
        return `${diffDays}天前 ${timeStr}`;
      } else {
        // 超过一周，显示具体日期和时间
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${month}月${day}日 ${timeStr}`;
      }
    } catch (error) {
      console.error('formatTime - 错误:', error);
      return '未知';
    }
  },

  // 关闭详情弹窗
  closeReceivedDetailModal() {
    this.setData({
      showReceivedDetailModal: false,
      currentReceivedDetail: null
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
  },

  // 获取测试模式文本（与"我收到的分享"页面保持一致）
  getTestModeText(testType) {
    const modeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写模式',
      'spelling': '拼写模式',
      'phrase_en2zh': '短语英译汉',
      'phrase_zh2en': '短语汉译英',
      'elimination': '消消乐'
    };
    return modeMap[testType] || testType || '未知';
  },

  // 新用户引导 - 去学习（进入单词检测）
  goToWordDetection() {
    wx.navigateTo({
      url: '/pages/detection/detection'
    });
  },

  // 去收到分享页面
  goToReceivedPage() {
    wx.navigateTo({
      url: '/pages/profile/received/received'
    });
  },

  // 去我的分享页面
  goToSharedPage() {
    wx.navigateTo({
      url: '/pages/profile/shared/shared'
    });
  },

  // 创建分享
  goToCreateShare() {
    wx.navigateTo({
      url: '/pages/profile/shared/shared?action=create'
    });
  },

  // 任务点击处理
  onTaskTap(e) {
    const task = e.currentTarget.dataset.task;
    console.log('点击任务:', task);

    switch (task.type) {
      case 'learning':
        // 跳转到学习进度详情
        wx.navigateTo({
          url: `/management/learning-progress/learning-progress`
        });
        break;
      case 'received':
        // 跳转到收到的分享
        wx.navigateTo({
          url: `/pages/profile/received/received`
        });
        break;
      case 'shared':
        // 跳转到我的分享
        wx.navigateTo({
          url: `/pages/profile/share/share`
        });
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  // 微信分享功能
  onShareAppMessage() {
    const { currentShareTask } = this.data;

    if (currentShareTask && currentShareTask.shareTitle && currentShareTask.sharePath) {
      // 分享特定任务
      return {
        title: currentShareTask.shareTitle,
        path: currentShareTask.sharePath,
        imageUrl: '/assets/icons/logo.png',
        success: (res) => {
          console.log('分享成功', res);
          wx.showToast({ title: '分享成功', icon: 'success' });
        },
        fail: (err) => {
          console.error('分享失败', err);
          wx.showToast({ title: '分享失败', icon: 'error' });
        }
      };
    }

    // 默认分享
    return {
      title: '墨词自习室 - 智能学习，个性发展',
      path: '/pages/index/index',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    const { currentShareTask } = this.data;

    if (currentShareTask && currentShareTask.data) {
      // 分享特定任务到朋友圈
      return {
        title: `墨词自习室测试：${currentShareTask.title} - ${currentShareTask.testType}`,
        query: `shareId=${currentShareTask.data.shareId}&shareMode=share&testMode=${currentShareTask.data.testType || currentShareTask.data.testMode}`,
        imageUrl: '/assets/icons/logo.png'
      };
    }

    // 默认分享到朋友圈
    return {
      title: '墨词自习室 - 智能学习，个性发展，共创空间',
      query: '',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 设置默认任务
  setDefaultTasks(learningTasks, receivedTasks, sharedTasks) {
    const result = {
      learning: { task: learningTasks[0] || null, index: 0 },
      received: { task: receivedTasks[0] || null, index: 0 },
      shared: { task: sharedTasks[0] || null, index: 0 }
    };

    try {
      // 检查是否有保存的默认任务
      const defaultLearning = wx.getStorageSync('defaultTask_learning');
      const defaultReceived = wx.getStorageSync('defaultTask_received');
      const defaultShared = wx.getStorageSync('defaultTask_shared');

      // 设置学习进度默认任务
      if (defaultLearning && defaultLearning.taskId && learningTasks.length > 0) {
        const index = learningTasks.findIndex(task => task.id === defaultLearning.taskId);
        if (index !== -1) {
          result.learning = { task: learningTasks[index], index: index };
        }
      }

      // 设置收到分享默认任务
      if (defaultReceived && defaultReceived.taskId && receivedTasks.length > 0) {
        const index = receivedTasks.findIndex(task => task.id === defaultReceived.taskId);
        if (index !== -1) {
          result.received = { task: receivedTasks[index], index: index };
        }
      }

      // 设置我的分享默认任务
      if (defaultShared && defaultShared.taskId && sharedTasks.length > 0) {
        const index = sharedTasks.findIndex(task => task.id === defaultShared.taskId);
        if (index !== -1) {
          result.shared = { task: sharedTasks[index], index: index };
        }
      }

    } catch (error) {
      console.error('设置默认任务失败:', error);
    }

    return result;
  },

  // 加载快捷方式默认设置
  loadShortcutDefaults() {
    try {
      const defaults = wx.getStorageSync('shortcutDefaults') || {};
      const shortcutSettings = {
        source: defaults.source || 'learning',
        mode: defaults.mode || '',
        modeText: defaults.modeText || '',
        library: defaults.library || '',
        libraryText: defaults.libraryText || '',
        sharer: defaults.sharer || '',
        sharerText: defaults.sharerText || ''
      };

      this.setData({ shortcutSettings });

      // 如果是收到的分享，加载分享人列表
      if (shortcutSettings.source === 'received') {
        this.loadAvailableSharers();
      }
    } catch (error) {
      console.error('加载快捷方式默认设置失败:', error);
    }
  },

  // 选择内容来源
  selectSource(e) {
    const source = e.currentTarget.dataset.source;
    const shortcutSettings = { ...this.data.shortcutSettings };
    shortcutSettings.source = source;

    // 切换来源时重置其他筛选条件
    if (source !== 'received') {
      shortcutSettings.sharer = '';
      shortcutSettings.sharerText = '';
    }

    this.setData({ shortcutSettings });

    // 如果是收到的分享，加载分享人列表
    if (source === 'received') {
      this.loadAvailableSharers();
    }
  },

  // 显示模式选择器
  showModeSelector() {
    this.setData({ showModeModal: true });
  },

  // 隐藏模式选择器
  hideModeSelector() {
    this.setData({ showModeModal: false });
  },

  // 选择测试模式
  selectMode(e) {
    const { mode, text } = e.currentTarget.dataset;
    const shortcutSettings = { ...this.data.shortcutSettings };
    shortcutSettings.mode = mode;
    shortcutSettings.modeText = text;

    this.setData({
      shortcutSettings,
      showModeModal: false
    });
  },

  // 显示词库选择器
  showLibrarySelector() {
    this.setData({ showLibraryModal: true });
  },

  // 隐藏词库选择器
  hideLibrarySelector() {
    this.setData({ showLibraryModal: false });
  },

  // 选择词库
  selectLibrary(e) {
    const { library, text } = e.currentTarget.dataset;
    const shortcutSettings = { ...this.data.shortcutSettings };
    shortcutSettings.library = library;
    shortcutSettings.libraryText = text;

    this.setData({
      shortcutSettings,
      showLibraryModal: false
    });
  },

  // 显示分享人选择器
  showSharerSelector() {
    this.setData({ showSharerModal: true });
  },

  // 隐藏分享人选择器
  hideSharerSelector() {
    this.setData({ showSharerModal: false });
  },

  // 选择分享人
  selectSharer(e) {
    const { sharer, text } = e.currentTarget.dataset;
    const shortcutSettings = { ...this.data.shortcutSettings };
    shortcutSettings.sharer = sharer;
    shortcutSettings.sharerText = text;

    this.setData({
      shortcutSettings,
      showSharerModal: false
    });
  },

  // 重置快捷方式设置
  resetShortcut() {
    const shortcutSettings = {
      source: 'learning',
      mode: '',
      modeText: '',
      library: '',
      libraryText: '',
      sharer: '',
      sharerText: ''
    };

    this.setData({ shortcutSettings });
  },

  // 设为默认显示
  setAsDefault() {
    try {
      wx.setStorageSync('shortcutDefaults', this.data.shortcutSettings);
      wx.showToast({
        title: '已设为默认',
        icon: 'success'
      });
    } catch (error) {
      console.error('保存默认设置失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      });
    }
  },

  // 加载真实的学习进度任务
  async loadRealLearningTasks() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getLearningProgress',
        data: { includeReviewWords: true }
      });

      if (!result.result || !result.result.success) {
        console.log('学习进度数据为空');
        return [];
      }

      let progressList = result.result.data || [];

      // 按最近学习时间排序
      progressList.sort((a, b) => {
        const timeA = new Date(a.lastStudyTime || 0);
        const timeB = new Date(b.lastStudyTime || 0);
        return timeB - timeA;
      });

      // 转换为任务格式
      return progressList.map(item => ({
        id: `progress_${item.libraryId}_${item.mode}`,
        title: item.libraryName,
        subtitle: this.getModeText(item.mode),
        progress: `${item.learnedCount || 0}/${item.totalCount || 0}`,
        progressPercent: item.percentage || 0,
        type: 'learning',
        testType: this.getModeText(item.mode),
        learned: item.learnedCount || 0,
        review: item.reviewCount || 0,
        total: item.totalCount || 0,
        lastStudyTime: this.formatStudyTime(item.lastStudyTime),
        hasReview: (item.reviewCount || 0) > 0,
        newProgress: `${item.learnedCount || 0}/${item.totalCount || 0}`,
        reviewProgress: `${item.reviewCount || 0}/${item.learnedCount || 0}`,
        data: item
      }));
    } catch (error) {
      console.error('加载学习进度任务失败:', error);
      return [];
    }
  },

  // 加载真实的收到分享任务
  async loadRealReceivedTasks() {
    try {
      // 使用现有的 getMyShares 云函数获取我参与的分享
      const result = await wx.cloud.callFunction({
        name: 'getMyShares',
        data: { type: 'participated' }
      });

      if (!result.result || !result.result.success) {
        console.log('收到分享数据为空');
        return [];
      }

      let shareList = result.result.data || [];

      // 按创建时间排序
      shareList.sort((a, b) => {
        const timeA = new Date(a.createTime || 0);
        const timeB = new Date(b.createTime || 0);
        return timeB - timeA;
      });

      // 转换为任务格式
      return shareList.map(item => {
        const myInfo = item.myInfo || {};
        const isMultiLevel = item.isMultiLevel || false;

        // 计算完成状态和进度
        let completionStatus = '未完成';
        let completedLevels = 0;
        let totalLevels = 0;
        let progressPercent = 0;
        let progressText = '';
        let totalAccuracy = 0;

        if (isMultiLevel) {
          // 多关卡任务 - 计算已完成关卡的正确率平均值
          const progress = myInfo.progress || item.myProgress;
          totalLevels = item.totalLevels || item.levelCount || 0;

          if (progress && progress.completedLevels) {
            // 从progress.completedLevels数组中获取已完成关卡数
            completedLevels = progress.completedLevels.length;

            if (completedLevels > 0) {
              progressPercent = totalLevels > 0 ? Math.round((completedLevels / totalLevels) * 100) : 0;
              progressText = `已完成 ${completedLevels}/${totalLevels} 关卡`;
              completionStatus = completedLevels >= totalLevels ? '全部完成' : '部分完成';

              // 计算已完成关卡的正确率平均值
              let totalScore = 0;
              let validLevels = 0;

              // 计算已完成关卡的正确率平均值（使用真实的正确率数据）
              let totalAccuracySum = 0;
              let validAccuracyLevels = 0;

              // 遍历已完成的关卡，从results中获取每个关卡的正确率
              progress.completedLevels.forEach((levelId) => {
                // 在results中查找对应关卡的测试结果
                const levelResult = myInfo.results && myInfo.results.find(result =>
                  result.levelId === levelId || result.levelId === String(levelId)
                );

                if (levelResult && levelResult.accuracy !== undefined) {
                  // accuracy可能是字符串格式，需要转换为数字
                  const accuracy = parseFloat(levelResult.accuracy);
                  if (!isNaN(accuracy)) {
                    totalAccuracySum += accuracy;
                    validAccuracyLevels++;
                  }
                }
              });

              if (validAccuracyLevels > 0) {
                totalAccuracy = Math.round(totalAccuracySum / validAccuracyLevels);
              } else {
                // 如果没有找到关卡正确率，使用备用方案
                if (myInfo.results && myInfo.results.length > 0) {
                  // 计算所有测试结果的平均正确率
                  const accuracies = myInfo.results.map(result => {
                    const accuracy = parseFloat(result.accuracy || 0);
                    return isNaN(accuracy) ? 0 : accuracy;
                  });
                  totalAccuracy = Math.round(accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length);
                } else {
                  // 最后的备用方案：从分数推算（假设满分100）
                  let score = myInfo.averageScore || myInfo.bestScore || 0;
                  totalAccuracy = Math.min(100, Math.max(0, Math.round(score / 3))); // 300分对应100%
                }
              }


            } else {
              completionStatus = '未完成';
              totalAccuracy = 0;
            }
          } else {
            // 没有进度信息，检查是否有测试记录
            completionStatus = '未完成';
            completedLevels = 0;
            totalAccuracy = 0;
          }
        } else {
          // 单组任务 - 就是这个任务的正确率
          const testCount = myInfo.testCount || 0;
          const bestScore = myInfo.bestScore || 0;

          if (testCount > 0) {
            progressPercent = Math.min(bestScore, 100);
            progressText = `已测试 ${testCount} 次`;
            completionStatus = bestScore >= 80 ? '已完成' : '已测试';

            // 单组任务的正确率就是最高正确率
            if (myInfo.results && myInfo.results.length > 0) {
              const accuracies = myInfo.results.map(result => {
                const accuracy = parseFloat(result.accuracy || 0);
                return isNaN(accuracy) ? 0 : accuracy;
              });
              totalAccuracy = Math.max(...accuracies); // 使用最高正确率
            } else {
              // 备用方案：从其他字段获取
              let accuracy = myInfo.averageScore || myInfo.bestScore || 0;
              // 如果是分数格式（>100），转换为正确率
              if (accuracy > 100) {
                accuracy = Math.round(accuracy / 3); // 300分对应100%
              }
              totalAccuracy = Math.min(100, Math.max(0, accuracy));
            }


          } else {
            completionStatus = '未完成';
            totalAccuracy = 0;
          }
        }

        return {
          id: `received_${item.shareId}`,
          title: item.libraryName || '分享测试',
          subtitle: `${this.getModeText(item.testType)} - 来自${item.creatorInfo?.nickName || '未知用户'}`,
          progress: progressText,
          progressPercent: progressPercent,
          type: 'received',
          testType: this.getModeText(item.testType),
          time: this.formatShareTime(item.createTime),
          sharer: item.creatorInfo?.nickName || '未知用户',
          isExpired: item.isExpired,
          isMultiLevel: isMultiLevel,
          // 完成状态相关数据
          completionStatus: completionStatus,
          completedLevels: completedLevels,
          totalLevels: totalLevels,
          bestScore: myInfo.bestScore || 0,
          totalAccuracy: totalAccuracy, // 总正确率
          myInfo: myInfo,
          data: item
        };
      });
    } catch (error) {
      console.error('加载收到分享任务失败:', error);
      return [];
    }
  },

  // 加载真实的我的分享任务
  async loadRealSharedTasks() {
    try {
      // 使用 getMyShares 云函数获取我创建的分享
      const result = await wx.cloud.callFunction({
        name: 'getMyShares',
        data: { type: 'created' }
      });

      if (!result.result || !result.result.success) {
        console.log('我的分享数据为空');
        return [];
      }

      let shareList = result.result.data || [];

      // 按创建时间排序
      shareList.sort((a, b) => {
        const timeA = new Date(a.createTime || 0);
        const timeB = new Date(b.createTime || 0);
        return timeB - timeA;
      });

      // 转换为任务格式
      return shareList.map(item => ({
        id: `shared_${item.shareId}`,
        title: item.libraryName || '我的测试',
        subtitle: `${this.getModeText(item.testType)} - ${item.participantCount || 0}人参与`,
        progress: item.isExpired ? '已过期' : '活跃中',
        type: 'shared',
        testType: this.getModeText(item.testType),
        time: this.formatShareTime(item.createTime),
        participants: item.participantCount || 0,
        avgAccuracy: this.calculateAverageAccuracy(item),
        completions: item.stats?.totalTests || 0,
        shareProgress: 100,
        shareStatus: item.isExpired ? '已过期' : '活跃中',
        isExpired: item.isExpired,
        data: item
      }));
    } catch (error) {
      console.error('加载我的分享任务失败:', error);
      return [];
    }
  },

  // 格式化学习时间
  formatStudyTime(timeStr) {
    if (!timeStr) return '最近学习：今天';

    const studyTime = new Date(timeStr);
    const now = new Date();
    const diffDays = Math.floor((now - studyTime) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return '最近学习：今天';
    if (diffDays === 1) return '最近学习：昨天';
    if (diffDays < 7) return `最近学习：${diffDays}天前`;
    return `最近学习：${studyTime.getMonth() + 1}月${studyTime.getDate()}日`;
  },

  // 格式化分享时间
  formatShareTime(timeStr) {
    if (!timeStr) return '今天';

    const shareTime = new Date(timeStr);
    const now = new Date();
    const diffDays = Math.floor((now - shareTime) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return '今天';
    if (diffDays === 1) return '昨天';
    if (diffDays < 7) return `${diffDays}天前`;
    return `${shareTime.getMonth() + 1}月${shareTime.getDate()}日`;
  },

  // 获取模式文本
  getModeText(mode) {
    const modeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写模式',
      'puzzle': '拼写模式',
      'translate': '英译汉',
      'chinese': '汉译英',
      'listening': '听写模式'
    };
    return modeMap[mode] || '英译汉';
  },





  // 加载可用的分享人列表
  async loadAvailableSharers() {
    try {
      // 从收到的分享任务中提取分享人信息
      const { allReceivedTasks } = this.data;
      const sharers = [];
      const sharerMap = new Map();

      allReceivedTasks.forEach(task => {
        if (task.sharer && !sharerMap.has(task.sharer)) {
          sharerMap.set(task.sharer, {
            id: `sharer_${task.sharer}`,
            name: task.sharer
          });
          sharers.push(sharerMap.get(task.sharer));
        }
      });

      this.setData({ availableSharers: sharers });
    } catch (error) {
      console.error('加载分享人列表失败:', error);
      this.setData({ availableSharers: [] });
    }
  }
});