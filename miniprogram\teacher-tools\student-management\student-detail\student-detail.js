Page({
  data: {
    studentId: '',
    student: null,
    loading: true,
    showEditDialog: false,
    showScoreDialog: false,
    editingScore: null,
    
    // 图表相关
    chartData: {
      categories: [],
      series: []
    },
    
    // 编辑成绩表单
    editScore: {
      examName: '',
      score: '',
      totalScore: 150,
      grade: '',
      semester: '',
      examDate: ''
    },

    // 添加成绩表单
    newScore: {
      examName: '',
      score: '',
      totalScore: 150,
      grade: '',
      semester: '',
      examDate: ''
    }
  },

  onLoad(options) {
    if (options.studentId) {
      this.setData({
        studentId: options.studentId
      });
      this.loadStudentDetail();
    }
  },

  // 成绩换算工具函数
  normalizeScore(score, totalScore, targetTotal = 150) {
    if (totalScore === targetTotal) {
      return score;
    }
    return Math.round((score / totalScore) * targetTotal * 10) / 10;
  },

  // 加载学生详情
  async loadStudentDetail() {
    console.log('=== 开始加载学生详情 ===');
    console.log('学生ID:', this.data.studentId);

    wx.showLoading({ title: '加载中...' });

    try {
      console.log('准备调用getStudentDetail云函数');
      const result = await wx.cloud.callFunction({
        name: 'getStudentDetail',
        data: {
          studentId: this.data.studentId
        }
      });

      console.log('云函数调用结果:', result);
      wx.hideLoading();

      if (result.result && result.result.success) {
        const student = result.result.data;
        console.log('获取到学生数据:', student);

        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: `${student.name} - 学习详情`
        });

        // 处理成绩数据，按日期倒序排序（最新的在前）
        const scores = (student.scores || []).sort((a, b) =>
          new Date(b.examDate) - new Date(a.examDate)
        );

        console.log('处理后的成绩数据:', scores);

        // 计算统计数据
        const stats = this.calculateStudentStats(scores);

        // 准备图表数据
        const chartData = this.prepareChartData(scores);

        this.setData({
          student: {
            ...student,
            scores: scores,
            stats: stats
          },
          chartData: chartData,
          loading: false
        });

        console.log('数据设置完成，准备绘制图表');
        // 绘制图表
        this.drawChart();
      } else {
        console.log('云函数返回失败:', result.result?.message);
        wx.showToast({
          title: result.result?.message || '加载失败',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载学生详情异常:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        errCode: error.errCode,
        errMsg: error.errMsg
      });

      wx.showToast({
        title: `加载失败: ${error.message || error.errMsg || '未知错误'}`,
        icon: 'none',
        duration: 3000
      });
      this.setData({ loading: false });
    }
  },

  // 计算学生统计数据
  calculateStudentStats(scores) {
    if (scores.length === 0) {
      return {
        totalExams: 0,
        avgScore: 0,
        bestScore: 0,
        worstScore: 0,
        trend: 'stable',
        improvement: 0
      };
    }

    const totalExams = scores.length;

    // 使用换算后的成绩计算统计数据
    const normalizedScores = scores.map(score =>
      this.normalizeScore(score.score, score.totalScore, 150)
    );

    const avgScore = normalizedScores.reduce((sum, score) => sum + score, 0) / totalExams;
    const bestScore = Math.max(...normalizedScores);
    const worstScore = Math.min(...normalizedScores);

    // 计算趋势（使用换算后的成绩）
    let trend = 'stable';
    let improvement = 0;

    if (scores.length >= 2) {
      // scores现在是按日期倒序排列，scores[0]是最新的，scores[1]是上一次的
      const latestNormalized = this.normalizeScore(scores[0].score, scores[0].totalScore, 150);
      const previousNormalized = this.normalizeScore(scores[1].score, scores[1].totalScore, 150);
      improvement = latestNormalized - previousNormalized;  // 最新 - 上一次 = 进步值

      if (improvement > 0) trend = 'up';
      else if (improvement < 0) trend = 'down';
    }

    return {
      totalExams,
      avgScore: Math.round(avgScore * 10) / 10,
      bestScore: Math.round(bestScore * 10) / 10,
      worstScore: Math.round(worstScore * 10) / 10,
      trend,
      improvement: Math.round(improvement * 10) / 10
    };
  },

  // 准备图表数据
  prepareChartData(scores) {
    if (scores.length === 0) {
      return {
        categories: [],
        series: []
      };
    }

    // 图表需要按时间正序显示，所以要重新排序
    const sortedScores = [...scores].sort((a, b) =>
      new Date(a.examDate) - new Date(b.examDate)
    );

    const categories = sortedScores.map(score => {
      const date = new Date(score.examDate);
      return `${date.getMonth() + 1}/${date.getDate()}`;
    });

    // 使用换算后的成绩作为图表数据
    const series = sortedScores.map(score =>
      this.normalizeScore(score.score, score.totalScore, 150)
    );

    return {
      categories,
      series
    };
  },

  // 绘制分数曲线图
  drawChart() {
    const { chartData } = this.data;
    
    if (chartData.series.length === 0) {
      return;
    }

    const query = wx.createSelectorQuery();
    query.select('#scoreChart').boundingClientRect();
    query.exec((res) => {
      if (res[0]) {
        const canvas = wx.createCanvasContext('scoreChart', this);
        const canvasWidth = res[0].width;
        const canvasHeight = res[0].height;
        
        this.drawScoreChart(canvas, canvasWidth, canvasHeight, chartData);
      }
    });
  },

  // 绘制分数图表
  drawScoreChart(canvas, width, height, data) {
    const padding = 60;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    // 清空画布
    canvas.clearRect(0, 0, width, height);
    
    // 设置背景
    canvas.setFillStyle('#ffffff');
    canvas.fillRect(0, 0, width, height);
    
    if (data.series.length === 0) return;
    
    // 计算数据范围
    const maxScore = Math.max(...data.series);
    const minScore = Math.min(...data.series);
    const scoreRange = maxScore - minScore || 1;
    const yPadding = scoreRange * 0.1;
    const yMin = Math.max(0, minScore - yPadding);
    const yMax = maxScore + yPadding;
    const yRange = yMax - yMin;
    
    // 绘制网格线
    canvas.setStrokeStyle('#f0f0f0');
    canvas.setLineWidth(1);
    
    // 水平网格线
    for (let i = 0; i <= 5; i++) {
      const y = padding + (chartHeight / 5) * i;
      canvas.beginPath();
      canvas.moveTo(padding, y);
      canvas.lineTo(padding + chartWidth, y);
      canvas.stroke();
      
      // Y轴标签
      const value = Math.round(yMax - (yRange / 5) * i);
      canvas.setFillStyle('#666');
      canvas.setFontSize(12);
      canvas.fillText(value.toString(), padding - 30, y + 4);
    }
    
    // 垂直网格线
    const stepX = chartWidth / (data.categories.length - 1 || 1);
    for (let i = 0; i < data.categories.length; i++) {
      const x = padding + stepX * i;
      canvas.beginPath();
      canvas.moveTo(x, padding);
      canvas.lineTo(x, padding + chartHeight);
      canvas.stroke();
      
      // X轴标签
      canvas.setFillStyle('#666');
      canvas.setFontSize(12);
      const text = data.categories[i];
      canvas.fillText(text, x - 15, padding + chartHeight + 20);
    }
    
    // 绘制分数线
    canvas.setStrokeStyle('#4F46E5');
    canvas.setLineWidth(3);
    canvas.beginPath();
    
    for (let i = 0; i < data.series.length; i++) {
      const x = padding + stepX * i;
      const y = padding + chartHeight - ((data.series[i] - yMin) / yRange) * chartHeight;
      
      if (i === 0) {
        canvas.moveTo(x, y);
      } else {
        canvas.lineTo(x, y);
      }
    }
    canvas.stroke();
    
    // 绘制数据点
    canvas.setFillStyle('#4F46E5');
    for (let i = 0; i < data.series.length; i++) {
      const x = padding + stepX * i;
      const y = padding + chartHeight - ((data.series[i] - yMin) / yRange) * chartHeight;
      
      canvas.beginPath();
      canvas.arc(x, y, 4, 0, 2 * Math.PI);
      canvas.fill();
      
      // 显示分数值
      canvas.setFillStyle('#333');
      canvas.setFontSize(12);
      canvas.fillText(data.series[i].toString(), x - 8, y - 10);
    }
    
    canvas.draw();
  },

  // 显示添加成绩对话框
  showAddScoreDialog() {
    const today = new Date();
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    
    this.setData({
      showScoreDialog: true,
      editingScore: null,
      newScore: {
        examName: '',
        score: '',
        totalScore: 150,
        grade: '',
        semester: '',
        examDate: dateStr
      }
    });
  },

  // 显示编辑成绩对话框
  showEditScoreDialog(e) {
    const score = e.currentTarget.dataset.score;
    this.setData({
      showScoreDialog: true,
      editingScore: score,
      editScore: {
        examName: score.examName,
        score: score.score.toString(),
        totalScore: score.totalScore.toString(),
        grade: score.grade || '',
        semester: score.semester || '',
        examDate: score.examDate
      }
    });
  },

  // 隐藏成绩对话框
  hideScoreDialog() {
    this.setData({
      showScoreDialog: false,
      editingScore: null
    });
  },

  // 成绩模态框遮罩层点击
  onScoreModalOverlayClick(e) {
    console.log('=== 成绩模态框遮罩层点击 ===');
    this.hideScoreDialog();
  },

  // 成绩模态框内容区点击
  onScoreModalContentClick(e) {
    console.log('=== 成绩模态框内容区点击 ===');
    // 阻止事件冒泡，不关闭对话框
  },

  // 输入成绩信息
  onScoreInput(e) {
    const { field } = e.currentTarget.dataset;
    let value = e.detail.value;

    // 处理日期选择器的特殊情况
    if (field === 'examDate' && e.type === 'change') {
      value = e.detail.value;
    }

    const prefix = this.data.editingScore ? 'editScore' : 'newScore';
    this.setData({
      [`${prefix}.${field}`]: value
    });
  },

  // 保存成绩
  async saveScore() {
    const { editingScore, editScore, newScore, studentId } = this.data;
    const scoreData = editingScore ? editScore : newScore;
    
    // 验证输入
    if (!scoreData.examName.trim()) {
      wx.showToast({
        title: '请输入考试名称',
        icon: 'none'
      });
      return;
    }

    if (!scoreData.grade.trim()) {
      wx.showToast({
        title: '请输入年级',
        icon: 'none'
      });
      return;
    }

    if (!scoreData.semester.trim()) {
      wx.showToast({
        title: '请输入学期',
        icon: 'none'
      });
      return;
    }

    if (!scoreData.examDate) {
      wx.showToast({
        title: '请选择考试日期',
        icon: 'none'
      });
      return;
    }

    const score = parseFloat(scoreData.score);
    const totalScore = parseFloat(scoreData.totalScore);
    
    if (isNaN(score) || score < 0) {
      wx.showToast({
        title: '请输入有效分数',
        icon: 'none'
      });
      return;
    }

    if (isNaN(totalScore) || totalScore <= 0) {
      wx.showToast({
        title: '请输入有效总分',
        icon: 'none'
      });
      return;
    }

    if (score > totalScore) {
      wx.showToast({
        title: '分数不能超过总分',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '保存中...' });

    try {
      const functionName = editingScore ? 'updateStudentScore' : 'addStudentScore';
      const requestData = {
        studentId: studentId,
        examName: scoreData.examName.trim(),
        score: score,
        totalScore: totalScore,
        grade: scoreData.grade.trim(),
        semester: scoreData.semester.trim(),
        examDate: scoreData.examDate
      };
      
      if (editingScore) {
        requestData.scoreId = editingScore._id;
      }

      const result = await wx.cloud.callFunction({
        name: functionName,
        data: requestData
      });

      if (result.result && result.result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
        this.hideScoreDialog();
        this.loadStudentDetail();
      } else {
        wx.showToast({
          title: result.result?.message || '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存成绩失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 删除成绩
  deleteScore(e) {
    const score = e.currentTarget.dataset.score;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${score.examName}"的成绩吗？`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '删除中...' });
          
          try {
            const result = await wx.cloud.callFunction({
              name: 'deleteStudentScore',
              data: {
                studentId: this.data.studentId,
                scoreId: score._id
              }
            });

            if (result.result && result.result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              this.loadStudentDetail();
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('删除成绩失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          } finally {
            wx.hideLoading();
          }
        }
      }
    });
  }
});
