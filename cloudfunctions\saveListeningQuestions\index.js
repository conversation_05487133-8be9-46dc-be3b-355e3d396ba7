const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const { questionType, audioSegments, questionData } = event;

  try {
    console.log('保存听口训练题目:', { questionType, segmentCount: audioSegments.length, questionCount: questionData.length });

    // 1. 保存到对应的题型集合
    const collectionName = getCollectionName(questionType);
    
    // 2. 构建保存数据
    const saveData = {
      questionType: questionType,
      audioSegments: audioSegments,
      questionData: questionData,
      createdBy: openid,
      createTime: new Date(),
      updateTime: new Date(),
      status: 'active'
    };

    // 3. 根据题型保存到不同集合
    let result;
    switch (questionType) {
      case 'listen_choose':
        result = await saveListenChooseQuestions(saveData);
        break;
      case 'listen_record':
        result = await saveListenRecordQuestions(saveData);
        break;
      case 'read_answer':
        result = await saveReadAnswerQuestions(saveData);
        break;
      default:
        throw new Error('不支持的题型');
    }

    return {
      success: true,
      data: {
        savedCount: result.savedCount,
        collectionName: collectionName
      },
      message: '题目保存成功'
    };

  } catch (error) {
    console.error('保存听口训练题目失败:', error);
    return {
      success: false,
      message: '保存失败: ' + error.message,
      error: error.message
    };
  }
};

// 获取集合名称
function getCollectionName(questionType) {
  const collectionMap = {
    'listen_choose': 'listening_choose_questions',
    'listen_record': 'listening_record_questions',
    'read_answer': 'reading_answer_questions'
  };
  return collectionMap[questionType] || 'listening_questions';
}

// 保存听后选择题
async function saveListenChooseQuestions(data) {
  const { questionData, audioSegments } = data;
  let savedCount = 0;

  // 为每道题保存一条记录
  for (let i = 0; i < questionData.length; i++) {
    const question = questionData[i];
    const audioSegment = audioSegments[question.audioSegmentIndex];

    if (audioSegment) {
      try {
        await db.collection('listening_choose_questions').add({
          data: {
            ...question,
            audioUrl: audioSegment.audioUrl,
            audioFileID: audioSegment.fileID,
            audioDuration: audioSegment.duration,
            createdBy: data.createdBy,
            createTime: data.createTime,
            updateTime: data.updateTime,
            status: data.status
          }
        });
        savedCount++;
      } catch (error) {
        console.error(`保存听后选择题${i + 1}失败:`, error);
      }
    }
  }

  return { savedCount };
}

// 保存听后记录与转述题
async function saveListenRecordQuestions(data) {
  const { questionData, audioSegments } = data;
  let savedCount = 0;

  for (const question of questionData) {
    try {
      // 为听后记录题保存多个音频片段（通常是3遍）
      const questionAudioSegments = audioSegments.slice(0, 3); // 前3个片段用于听后记录

      await db.collection('listening_record_questions').add({
        data: {
          ...question,
          audioSegments: questionAudioSegments.map(segment => ({
            audioUrl: segment.audioUrl,
            audioFileID: segment.fileID,
            duration: segment.duration,
            playOrder: segment.index + 1
          })),
          createdBy: data.createdBy,
          createTime: data.createTime,
          updateTime: data.updateTime,
          status: data.status
        }
      });
      savedCount++;
    } catch (error) {
      console.error('保存听后记录题失败:', error);
    }
  }

  return { savedCount };
}

// 保存朗读并回答问题题
async function saveReadAnswerQuestions(data) {
  const { questionData, audioSegments } = data;
  let savedCount = 0;

  for (const question of questionData) {
    try {
      // 第一个片段是朗读文本，后面的片段是问题
      const readingSegment = audioSegments[0];
      const questionSegments = audioSegments.slice(1);

      await db.collection('reading_answer_questions').add({
        data: {
          ...question,
          readingAudio: readingSegment ? {
            audioUrl: readingSegment.audioUrl,
            audioFileID: readingSegment.fileID,
            duration: readingSegment.duration
          } : null,
          questionAudios: questionSegments.map((segment, index) => ({
            questionIndex: index,
            audioUrl: segment.audioUrl,
            audioFileID: segment.fileID,
            duration: segment.duration
          })),
          createdBy: data.createdBy,
          createTime: data.createTime,
          updateTime: data.updateTime,
          status: data.status
        }
      });
      savedCount++;
    } catch (error) {
      console.error('保存朗读回答题失败:', error);
    }
  }

  return { savedCount };
}
