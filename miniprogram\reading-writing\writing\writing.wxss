/* pages/writing/writing.wxss */
.container {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.page-header {
  text-align: center;
  padding: 60rpx 0 40rpx;
  color: white;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
}

.section {
  margin: 0 30rpx 40rpx;
}

.section-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}

.modules-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.module-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.module-card:active {
  transform: scale(0.98);
}

.module-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.icon-text {
  font-size: 36rpx;
}

.module-content {
  flex: 1;
}

.module-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.module-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.module-arrow {
  font-size: 32rpx;
  color: #999;
  margin-left: 20rpx;
}

.module-description {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.desc-text {
  color: white;
  font-size: 26rpx;
  line-height: 1.5;
}

.topics-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.topic-tag {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  border: 2rpx solid;
  transition: transform 0.2s ease;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
}

.topic-tag:active {
  transform: scale(0.95);
}

.topic-text {
  font-size: 24rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.tips-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 30rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-bullet {
  color: white;
  font-size: 28rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
}

.tip-text {
  color: white;
  font-size: 26rpx;
  line-height: 1.5;
  flex: 1;
}

.notice-card {
  background: #FEF3C7;
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 0 30rpx 40rpx;
  display: flex;
  align-items: flex-start;
}

.notice-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 2rpx;
}

.notice-content {
  flex: 1;
}

.notice-title {
  color: #92400E;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 8rpx;
}

.notice-text {
  color: #92400E;
  font-size: 26rpx;
  line-height: 1.5;
}


