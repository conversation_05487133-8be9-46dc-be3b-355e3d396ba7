const app = getApp();

Page({
  data: {
    sortType: 'count', // 排序类型：count-错误次数，time-添加时间
    sortOrder: 'desc', // 排序顺序：asc-升序，desc-降序
    modeFilter: 'en_to_cn', // 模式筛选：en_to_cn-英译汉，cn_to_en-汉译英，dictation-听写
    words: [], // 错词列表
    allWords: [], // 所有错词（用于分页）
    showBatchActions: false, // 是否显示批量操作
    selectedWords: [], // 选中的单词ID列表
    editMode: false, // 是否为编辑模式
    showAddDialog: false, // 是否显示添加单词对话框
    newWordForm: { // 新单词表单
      word: '',
      meaning: '',
      phonetic: '',
      example: ''
    },
    // 分页相关
    currentPage: 1,
    pageSize: 10,
    totalPages: 0,
    showPageInput: false,
    pageInputValue: '',
    // 统计信息
    statistics: {
      total: 0,
      en_to_cn: 0,
      cn_to_en: 0,
      dictation: 0
    }
  },

  onLoad: function() {
    this.loadWords();
  },

  onShow: function() {
    // 每次显示页面时刷新错词列表
    this.loadWords();
  },

  loadWords: function() {
    const db = wx.cloud.database();
    const _ = db.command;

    // 构建基础查询条件
    let whereCondition = {
      _openid: app.globalData.openid,
      type: 'word'
    };

    // 添加模式筛选条件
    if (this.data.modeFilter !== 'all') {
      whereCondition.testMode = this.data.modeFilter;
    }

    let query = db.collection('mistakes').where(whereCondition);

    // 同时获取统计信息
    Promise.all([
      query.get(),
      this.getStatistics()
    ]).then(([wordsRes, statistics]) => {
      // 按单词分组，统计错误次数
      const wordGroups = {};
      wordsRes.data.forEach(item => {
        const wordKey = item.word || item.wordId;
        if (!wordGroups[wordKey]) {
          wordGroups[wordKey] = {
            ...item,
            errorCount: 1,
            latestTime: item.createTime || item.addTime,
            selected: false,
            modeText: this.getModeText(item.testMode || item.mistakeType)
          };
        } else {
          wordGroups[wordKey].errorCount++;
          // 保留最新的错误记录信息
          if ((item.createTime || item.addTime) > wordGroups[wordKey].latestTime) {
            wordGroups[wordKey] = {
              ...wordGroups[wordKey],
              ...item,
              errorCount: wordGroups[wordKey].errorCount,
              latestTime: item.createTime || item.addTime,
              modeText: this.getModeText(item.testMode || item.mistakeType)
            };
          }
        }
      });

      // 转换为数组并排序
      let allWords = Object.values(wordGroups);

      // 根据排序类型排序
      if (this.data.sortType === 'count') {
        allWords.sort((a, b) => {
          return this.data.sortOrder === 'desc' ?
            b.errorCount - a.errorCount :
            a.errorCount - b.errorCount;
        });
      } else {
        allWords.sort((a, b) => {
          const timeA = new Date(a.latestTime);
          const timeB = new Date(b.latestTime);
          return this.data.sortOrder === 'desc' ?
            timeB - timeA :
            timeA - timeB;
        });
      }

      // 计算分页
      const totalPages = Math.ceil(allWords.length / this.data.pageSize);
      const startIndex = (this.data.currentPage - 1) * this.data.pageSize;
      const endIndex = startIndex + this.data.pageSize;
      const currentPageWords = allWords.slice(startIndex, endIndex);

      // 格式化时间
      const formattedWords = currentPageWords.map(item => ({
        ...item,
        addTime: this.formatTime(item.latestTime)
      }));

      this.setData({
        allWords: allWords,
        words: formattedWords,
        totalPages: totalPages,
        statistics: statistics
      });
    }).catch(console.error);
  },

  // 获取统计信息
  async getStatistics() {
    try {
      const db = wx.cloud.database();
      const result = await db.collection('mistakes')
        .where({
          _openid: app.globalData.openid,
          type: 'word'
        })
        .get();

      const statistics = {
        total: result.data.length,
        en_to_cn: 0,
        cn_to_en: 0,
        dictation: 0
      };

      result.data.forEach(item => {
        const mode = item.testMode || item.mistakeType;
        if (mode === 'en_to_cn' || mode === 'wordtest') {
          statistics.en_to_cn++;
        } else if (mode === 'cn_to_en') {
          statistics.cn_to_en++;
        } else if (mode === 'dictation') {
          statistics.dictation++;
        }
      });

      return statistics;
    } catch (error) {
      console.error('获取统计信息失败:', error);
      return {
        total: 0,
        en_to_cn: 0,
        cn_to_en: 0,
        dictation: 0
      };
    }
  },

  // 获取模式文本
  getModeText(mode) {
    const modeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写',
      'wordtest': '英译汉',
      'competition': '竞赛',
      'manual': '手动添加'
    };
    return modeMap[mode] || '未知';
  },

  formatTime: function(timestamp) {
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  },

  onFilterTap: function(e) {
    const type = e.currentTarget.dataset.type;
    
    // 如果点击的是当前选中的筛选类型，则切换排序顺序
    if (type === this.data.filterType) {
      this.setData({
        sortOrder: this.data.sortOrder === 'asc' ? 'desc' : 'asc'
      });
    } else {
      this.setData({
        filterType: type,
        sortOrder: 'desc'
      });
    }

    // 重新加载错词列表
    this.loadWords();
  },

  // 模式筛选
  onModeFilterTap: function(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({
      modeFilter: mode,
      currentPage: 1 // 重置到第一页
    });

    // 重新加载错词列表
    this.loadWords();
  },

  // 排序切换
  onSortTap: function() {
    const newSortType = this.data.sortType === 'count' ? 'time' : 'count';
    this.setData({
      sortType: newSortType,
      currentPage: 1 // 重置到第一页
    });

    // 重新加载错词列表
    this.loadWords();
  },

  // 分页相关方法
  onPrevPage: function() {
    if (this.data.currentPage > 1) {
      this.setData({
        currentPage: this.data.currentPage - 1
      });
      this.loadWords();
    }
  },

  onNextPage: function() {
    if (this.data.currentPage < this.data.totalPages) {
      this.setData({
        currentPage: this.data.currentPage + 1
      });
      this.loadWords();
    }
  },

  onPageInputTap: function() {
    this.setData({
      showPageInput: true,
      pageInputValue: this.data.currentPage.toString()
    });
  },

  onPageInputChange: function(e) {
    this.setData({
      pageInputValue: e.detail.value
    });
  },

  onPageInputConfirm: function() {
    const page = parseInt(this.data.pageInputValue);
    if (page >= 1 && page <= this.data.totalPages) {
      this.setData({
        currentPage: page,
        showPageInput: false
      });
      this.loadWords();
    } else {
      wx.showToast({
        title: '页码超出范围',
        icon: 'none'
      });
    }
  },

  onPageInputCancel: function() {
    this.setData({
      showPageInput: false
    });
  },

  // 切换编辑模式
  toggleEditMode: function() {
    const editMode = !this.data.editMode;
    this.setData({
      editMode: editMode,
      selectedWords: editMode ? [] : this.data.selectedWords,
      showBatchActions: false
    });
    
    // 重置所有单词的选中状态
    if (!editMode) {
      const words = this.data.words.map(word => ({
        ...word,
        selected: false
      }));
      this.setData({ words });
    }
  },

  // 全选/取消全选
  onSelectAllTap: function() {
    const allSelected = this.data.selectedWords.length === this.data.words.length;
    if (allSelected) {
      // 取消全选
      this.setData({
        selectedWords: [],
        words: this.data.words.map(word => ({
          ...word,
          selected: false
        }))
      });
    } else {
      // 全选
      const allWordIds = this.data.words.map(word => word._id);
      this.setData({
        selectedWords: allWordIds,
        words: this.data.words.map(word => ({
          ...word,
          selected: true
        }))
      });
    }
  },

  // 选择单词
  onWordSelect: function(e) {
    if (!this.data.editMode) return;
    
    const index = e.currentTarget.dataset.index;
    const words = [...this.data.words];
    words[index].selected = !words[index].selected;
    
    const selectedWords = words.filter(word => word.selected).map(word => word._id);
    
    this.setData({
      words: words,
      selectedWords: selectedWords,
      showBatchActions: selectedWords.length > 0
    });
  },

  // 全选/取消全选
  toggleSelectAll: function() {
    const allSelected = this.data.selectedWords.length === this.data.words.length;
    const words = this.data.words.map(word => ({
      ...word,
      selected: !allSelected
    }));
    
    const selectedWords = allSelected ? [] : words.map(word => word._id);
    
    this.setData({
      words: words,
      selectedWords: selectedWords,
      showBatchActions: selectedWords.length > 0
    });
  },

  // 批量删除
  onBatchDelete: function() {
    if (this.data.selectedWords.length === 0) return;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${this.data.selectedWords.length} 个单词吗？`,
      success: (res) => {
        if (res.confirm) {
          this.deleteSelectedWords();
        }
      }
    });
  },

  // 删除选中的单词
  deleteSelectedWords: function() {
    const db = wx.cloud.database();
    const deletePromises = this.data.selectedWords.map(wordId => {
      return db.collection('mistakes').doc(wordId).remove();
    });
    
    Promise.all(deletePromises).then(() => {
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
      this.setData({
        selectedWords: [],
        showBatchActions: false,
        editMode: false
      });
      this.loadWords();
    }).catch(error => {
      console.error('批量删除失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    });
  },

  // 显示添加单词对话框
  showAddWordDialog: function() {
    this.setData({
      showAddDialog: true,
      newWordForm: {
        word: '',
        meaning: '',
        phonetic: '',
        example: ''
      }
    });
  },

  // 隐藏添加单词对话框
  hideAddWordDialog: function() {
    this.setData({
      showAddDialog: false
    });
  },

  // 表单输入
  onFormInput: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`newWordForm.${field}`]: value
    });
  },

  // 手动添加单词
  onAddWord: function() {
    const { word, meaning, phonetic, example } = this.data.newWordForm;
    
    if (!word.trim()) {
      wx.showToast({
        title: '请输入单词',
        icon: 'none'
      });
      return;
    }
    
    if (!meaning.trim()) {
      wx.showToast({
        title: '请输入中文含义',
        icon: 'none'
      });
      return;
    }
    
    // 调用云函数添加错题
    wx.cloud.callFunction({
      name: 'addMistake',
      data: {
        userId: app.globalData.openid,
        wordId: word.trim(),
        type: 'word',
        extra: {
          word: word.trim(),
          meaning: meaning.trim(),
          phonetic: phonetic.trim(),
          example: example.trim(),
          mistakeType: 'manual', // 标识为手动添加
          source: '手动添加',
          createTime: new Date()
        }
      }
    }).then(result => {
      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });
      this.hideAddWordDialog();
      this.loadWords();
    }).catch(error => {
      console.error('添加单词失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    });
  },

  onReviewTap: function() {
    if (this.data.words.length === 0) {
      wx.showToast({
        title: '暂无错词可复习',
        icon: 'none'
      });
      return;
    }

    // 直接开始当前模式的复习
    const currentMode = this.data.modeFilter;
    this.startReview(currentMode);
  },

  // 导出Excel
  onExportExcel: function() {
    const words = this.getWordsForCurrentTab();

    if (words.length === 0) {
      wx.showToast({
        title: '该模式下暂无错词',
        icon: 'none'
      });
      return;
    }

    // 准备Excel数据
    const excelData = this.prepareExcelData(words);

    // 生成Excel文件
    this.generateExcelFile(excelData);
  },

  // 准备Excel数据
  prepareExcelData: function(words) {
    // 表头
    const headers = ['错词频次', '单词', '音标', '中文意思'];

    // 数据行
    const rows = words.map(word => [
      word.errorCount || 1,
      word.word || '',
      word.phonetic || '',
      word.meaning || ''
    ]);

    return {
      headers,
      rows,
      fileName: `错词本_${this.getModeDisplayName(this.data.modeFilter)}_${this.formatDate(new Date())}.csv`
    };
  },

  // 获取模式显示名称
  getModeDisplayName: function(mode) {
    const modeNames = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写'
    };
    return modeNames[mode] || mode;
  },

  // 格式化日期
  formatDate: function(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}${month}${day}_${hours}${minutes}`;
  },

  // 生成Excel文件
  generateExcelFile: function(data) {
    wx.showLoading({
      title: '正在生成文件...',
      mask: true
    });

    try {
      // 生成CSV格式的内容
      let csvContent = '';

      // 添加BOM以支持中文
      csvContent += '\uFEFF';

      // 添加表头
      csvContent += data.headers.join(',') + '\n';

      // 添加数据行
      data.rows.forEach(row => {
        // 处理包含逗号的字段，用双引号包围
        const processedRow = row.map(cell => {
          const cellStr = String(cell);
          if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
            return '"' + cellStr.replace(/"/g, '""') + '"';
          }
          return cellStr;
        });
        csvContent += processedRow.join(',') + '\n';
      });

      // 检查内容大小，如果太大则直接复制到剪贴板
      if (csvContent.length > 50000) { // 50KB限制
        wx.hideLoading();
        this.copyExcelDataToClipboard();
        wx.showModal({
          title: '文件过大',
          content: '数据已复制到剪贴板，您可以粘贴到Excel中使用',
          showCancel: false
        });
        return;
      }

      // 清理旧文件
      this.cleanupOldFiles().then(() => {
        // 将内容写入临时文件
        const fs = wx.getFileSystemManager();
        const fileName = data.fileName;
        const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

        fs.writeFile({
          filePath: filePath,
          data: csvContent,
          encoding: 'utf8',
          success: () => {
            wx.hideLoading();
            this.shareExcelFile(filePath, fileName);
          },
          fail: (err) => {
            wx.hideLoading();
            console.error('写入文件失败:', err);
            // 降级到剪贴板方案
            this.copyExcelDataToClipboard();
            wx.showModal({
              title: '文件生成失败',
              content: '数据已复制到剪贴板，您可以粘贴到Excel中使用',
              showCancel: false
            });
          }
        });
      }).catch(() => {
        wx.hideLoading();
        // 降级到剪贴板方案
        this.copyExcelDataToClipboard();
      });
    } catch (error) {
      wx.hideLoading();
      console.error('生成Excel失败:', error);
      // 降级到剪贴板方案
      this.copyExcelDataToClipboard();
    }
  },

  // 清理旧文件
  cleanupOldFiles: function() {
    return new Promise((resolve) => {
      try {
        const fs = wx.getFileSystemManager();
        fs.readdir({
          dirPath: wx.env.USER_DATA_PATH,
          success: (res) => {
            const filesToDelete = res.files.filter(file =>
              file.includes('错词本_') && (file.endsWith('.csv') || file.endsWith('.html'))
            );

            // 删除旧文件
            let deleteCount = 0;
            if (filesToDelete.length === 0) {
              resolve();
              return;
            }

            filesToDelete.forEach(file => {
              fs.unlink({
                filePath: `${wx.env.USER_DATA_PATH}/${file}`,
                complete: () => {
                  deleteCount++;
                  if (deleteCount === filesToDelete.length) {
                    resolve();
                  }
                }
              });
            });
          },
          fail: () => {
            resolve(); // 即使失败也继续
          }
        });
      } catch (error) {
        resolve(); // 即使失败也继续
      }
    });
  },

  // 分享Excel文件
  shareExcelFile: function(filePath, fileName) {
    wx.showActionSheet({
      itemList: ['分享给朋友', '复制到剪贴板'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 分享给朋友
            wx.shareFileMessage({
              filePath: filePath,
              fileName: fileName,
              success: () => {
                wx.showToast({
                  title: '分享成功',
                  icon: 'success'
                });
              },
              fail: (err) => {
                console.error('分享失败:', err);
                // 降级到剪贴板
                this.copyExcelDataToClipboard();
                wx.showModal({
                  title: '分享失败',
                  content: '数据已复制到剪贴板，您可以粘贴到Excel中使用',
                  showCancel: false
                });
              }
            });
            break;
          case 1:
            // 复制到剪贴板
            this.copyExcelDataToClipboard();
            break;
        }
      }
    });
  },

  // 复制Excel数据到剪贴板
  copyExcelDataToClipboard: function() {
    const words = this.getWordsForCurrentTab();
    let clipboardText = '错词频次\t单词\t音标\t中文意思\n';

    words.forEach(word => {
      clipboardText += `${word.errorCount || 1}\t${word.word || ''}\t${word.phonetic || ''}\t${word.meaning || ''}\n`;
    });

    wx.setClipboardData({
      data: clipboardText,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 开始复习错词
  startReview: function(testMode) {
    // 获取当前标签页的错词
    const words = this.getWordsForCurrentTab();

    if (words.length === 0) {
      wx.showToast({
        title: '该模式下暂无错词',
        icon: 'none'
      });
      return;
    }

    // 将错词数据存储到全局，供测试页面使用
    const app = getApp();
    app.globalData.learningData = {
      words: words,
      testMode: testMode,
      libraryName: '错词本',
      isFromMistakes: true
    };

    // 跳转到模式选择页面
    wx.navigateTo({
      url: `/pages/wordtest/mode-select/mode-select?testMode=${testMode}&total=${words.length}&fromMistakes=true`
    });
  },

  // 获取当前标签页的错词
  getWordsForCurrentTab: function() {
    const currentMode = this.data.modeFilter;
    const allWords = this.data.allWords; // 使用allWords获取完整数据

    // 根据testMode过滤错词
    return allWords.filter(word => word.testMode === currentMode);
  },

  // 复习选中的单词
  onReviewSelectedTap: function() {
    const selectedWords = this.data.selectedWords;

    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请先选择要复习的单词',
        icon: 'none'
      });
      return;
    }

    // 获取选中单词的完整数据
    const allWords = this.data.allWords;
    const selectedWordsData = allWords.filter(word =>
      selectedWords.includes(word._id)
    );

    if (selectedWordsData.length === 0) {
      wx.showToast({
        title: '选中的单词数据异常',
        icon: 'none'
      });
      return;
    }

    // 检查选中单词的测试模式是否一致
    const testModes = [...new Set(selectedWordsData.map(word => word.testMode))];

    if (testModes.length === 1) {
      // 所有选中单词都是同一模式，直接开始复习
      this.startReviewWithWords(testModes[0], selectedWordsData);
    } else {
      // 多种模式混合，让用户选择复习模式
      wx.showActionSheet({
        itemList: ['英译汉', '汉译英', '听写'],
        success: (res) => {
          let testMode = '';
          switch (res.tapIndex) {
            case 0:
              testMode = 'en_to_cn';
              break;
            case 1:
              testMode = 'cn_to_en';
              break;
            case 2:
              testMode = 'dictation';
              break;
          }
          // 过滤出对应模式的单词，如果没有则使用全部选中的单词
          const modeWords = selectedWordsData.filter(word => word.testMode === testMode);
          const wordsToReview = modeWords.length > 0 ? modeWords : selectedWordsData;
          this.startReviewWithWords(testMode, wordsToReview);
        }
      });
    }
  },

  // 使用指定单词开始复习
  startReviewWithWords: function(testMode, words) {
    if (words.length === 0) {
      wx.showToast({
        title: '没有可复习的单词',
        icon: 'none'
      });
      return;
    }

    // 将错词数据存储到全局，供测试页面使用
    const app = getApp();
    app.globalData.learningData = {
      words: words,
      testMode: testMode,
      libraryName: '错词本',
      isFromMistakes: true
    };

    // 跳转到模式选择页面
    wx.navigateTo({
      url: `/pages/wordtest/mode-select/mode-select?testMode=${testMode}&total=${words.length}&fromMistakes=true`
    });
  },

  onMasteredTap: function(e) {
    const wordId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '提示',
      content: '确定将此单词标记为已掌握吗？',
      success: (res) => {
        if (res.confirm) {
          // 从错词本中删除该单词
          const db = wx.cloud.database();
          db.collection('mistakes').doc(wordId).remove().then(() => {
            // 重新加载错词列表
            this.loadWords();
            
            wx.showToast({
              title: '已标记为掌握',
              icon: 'success'
            });
          }).catch(console.error);
        }
      }
    });
  }
}); 