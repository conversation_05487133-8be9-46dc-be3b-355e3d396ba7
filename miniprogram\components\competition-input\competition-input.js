Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    placeholder: {
      type: String,
      value: '请输入竞赛名称（例如：我的单词挑战）'
    },
    defaultValue: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    inputValue: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onInput(e) {
      this.setData({
        inputValue: e.detail.value
      });
    },

    // 当输入框获取焦点时，清空输入框
    onFocus() {
      // 不需要做什么，placeholder 会自动消失
    },

    onCancel() {
      this.setData({
        inputValue: '',
        show: false
      });
      this.triggerEvent('cancel');
    },

    onConfirm() {
      const { inputValue } = this.data;
      this.triggerEvent('confirm', { value: inputValue });
      this.setData({
        inputValue: '',
        show: false
      });
    },

    // 显示对话框
    showDialog(defaultValue = '') {
      this.setData({
        show: true,
        inputValue: defaultValue || ''
      });
    }
  }
}) 