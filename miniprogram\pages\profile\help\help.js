// pages/profile/help/help.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    problemText: '',
    suggestionText: '',
    submitting: false,
    currentTab: 'submit', // submit 或 records
    feedbackRecords: [],
    loadingRecords: false,
    showRecords: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 如果当前是记录tab，加载反馈记录
    if (this.data.currentTab === 'records') {
      this.loadFeedbackRecords();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 输入问题反馈
  onProblemInput(e) {
    this.setData({
      problemText: e.detail.value
    });
  },

  // 输入建议
  onSuggestionInput(e) {
    this.setData({
      suggestionText: e.detail.value
    });
  },

  // 提交反馈
  async onSubmitFeedback() {
    const { problemText, suggestionText } = this.data;
    
    // 检查是否有内容
    if (!problemText.trim() && !suggestionText.trim()) {
      wx.showToast({
        title: '请填写反馈内容',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      // 获取用户信息
      const app = getApp();
      const userInfo = await app.getUserInfo();
      
      // 获取设备信息
      const systemInfo = wx.getSystemInfoSync();
      
      // 调用云函数提交反馈
      const result = await wx.cloud.callFunction({
        name: 'manageFeedback',
        data: {
          action: 'submit',
          data: {
            userNickName: userInfo?.nickName || userInfo?.wechatInfo?.nickName || '匿名用户',
            problemText: problemText.trim(),
            suggestionText: suggestionText.trim(),
            deviceInfo: {
              model: systemInfo.model,
              platform: systemInfo.platform,
              version: systemInfo.version,
              SDKVersion: systemInfo.SDKVersion
            }
          }
        }
      });

      if (result.result && result.result.success) {
        // 显示成功提示
        wx.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            setTimeout(() => {
              // 清空输入框
              this.setData({
                problemText: '',
                suggestionText: ''
              });
              
              // 返回上一页
              wx.navigateBack();
            }, 2000);
          }
        });
      } else {
        throw new Error(result.result?.error || '提交失败');
      }

    } catch (error) {
      console.error('提交反馈失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 切换tab
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tab });
    
    // 如果切换到记录tab，加载反馈记录
    if (tab === 'records') {
      this.loadFeedbackRecords();
    }
  },

  // 格式化时间戳
  formatTime(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 加载用户的反馈记录
  async loadFeedbackRecords() {
    this.setData({ loadingRecords: true });

    try {
      const result = await wx.cloud.callFunction({
        name: 'manageFeedback',
        data: {
          action: 'getUserFeedbacks'
        }
      });

      if (result.result && result.result.success) {
        // 为每条记录添加格式化时间
        const records = (result.result.data || []).map(record => ({
          ...record,
          formattedTime: this.formatTime(record.timestamp || record.createTime)
        }));

        this.setData({
          feedbackRecords: records,
          showRecords: true
        });
      } else {
        throw new Error(result.result?.error || '加载失败');
      }

    } catch (error) {
      console.error('加载反馈记录失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loadingRecords: false });
    }
  },

  // 查看反馈详情
  onViewFeedback(e) {
    const { index } = e.currentTarget.dataset;
    const feedback = this.data.feedbackRecords[index];
    
    let content = '';
    if (feedback.problemText) {
      content += `问题反馈：\n${feedback.problemText}\n\n`;
    }
    if (feedback.suggestionText) {
      content += `建议内容：\n${feedback.suggestionText}`;
    }
    if (feedback.adminReply) {
      content += `\n\n━━━━━━━━━━━━━━━━━━\n管理员回复：\n${feedback.adminReply}`;
    } else {
      content += '\n\n━━━━━━━━━━━━━━━━━━\n暂无管理员回复';
    }
    
    wx.showModal({
      title: '反馈详情',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  }
})