const cloud = require('wx-server-sdk')

// 初始化云能力
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    // 获取最新的通知
    const result = await db.collection('notices')
      .where({
        status: 'active' // 只获取启用的通知
      })
      .orderBy('createTime', 'desc')
      .limit(1)
      .get()

    if (result.data.length > 0) {
      return {
        success: true,
        data: {
          content: result.data[0].content,
          id: result.data[0]._id,
          updateTime: result.data[0].updateTime
        }
      }
    } else {
      // 返回默认通知
      return {
        success: true,
        data: {
          content: '各类词库正在逐步上传完善中，功能需求和bug欢迎通过"帮助与反馈"告诉我们',
          id: 'default',
          updateTime: new Date()
        }
      }
    }
  } catch (error) {
    console.error('获取通知失败:', error)
    return {
      success: false,
      error: error.message,
      data: {
        content: '各类词库正在逐步上传完善中，功能需求和bug欢迎通过"帮助与反馈"告诉我们',
        id: 'default',
        updateTime: new Date()
      }
    }
  }
} 