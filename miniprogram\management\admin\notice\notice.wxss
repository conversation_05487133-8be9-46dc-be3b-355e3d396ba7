.container {
  padding: 32rpx;
  background: #f8fafc;
  min-height: 100vh;
}

/* 当前通知显示区域 */
.current-notice-section {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #3b82f6;
}

.current-notice-section .section-title {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.subtitle {
  font-size: 22rpx;
  font-weight: 400;
  color: #64748b;
}

.current-notice-loading {
  padding: 60rpx 0;
  text-align: center;
}

.current-notice-empty {
  padding: 60rpx 0;
  text-align: center;
}

.empty-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #64748b;
  margin-bottom: 8rpx;
}

.empty-hint {
  font-size: 22rpx;
  color: #94a3b8;
}

.current-notice-content {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8rpx;
  padding: 24rpx;
  border: 1rpx solid #0ea5e9;
}

.current-notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.current-notice-badge {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.edit-current-btn {
  background: #10b981;
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  border: none;
  height: auto;
  line-height: 1.2;
}

.current-notice-text {
  font-size: 28rpx;
  color: #1e293b;
  line-height: 1.6;
  margin-bottom: 12rpx;
  word-break: break-word;
  background: white;
  padding: 16rpx;
  border-radius: 6rpx;
  border: 1rpx solid #e0f2fe;
}

.current-notice-time {
  font-size: 22rpx;
  color: #64748b;
  text-align: right;
}

/* 添加通知区域 */
.add-notice-section {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 24rpx;
}

.notice-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #e2e8f0;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 24rpx;
  box-sizing: border-box;
}

.add-btn {
  width: 100%;
  background: #4f46e5;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 8rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-btn[disabled] {
  background: #cbd5e1;
  color: #94a3b8;
}

/* 通知列表区域 */
.notices-section {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.loading-container,
.empty-container {
  padding: 80rpx 0;
  text-align: center;
}

.loading-text,
.empty-text {
  color: #64748b;
  font-size: 28rpx;
}

/* 通知项样式 */
.notice-item {
  padding: 24rpx;
  border: 1rpx solid #e2e8f0;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  transition: all 0.2s ease;
}

.notice-item:last-child {
  margin-bottom: 0;
}

.notice-item.active {
  border-color: #10b981;
  background: #f0fdf4;
}

.notice-item.inactive {
  border-color: #f59e0b;
  background: #fffbeb;
  opacity: 0.8;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.status-badge {
  display: inline-block;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-badge.active {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.inactive {
  background: #fef3c7;
  color: #92400e;
}

.notice-time {
  font-size: 22rpx;
  color: #64748b;
}

.notice-content {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.5;
  word-break: break-word;
} 