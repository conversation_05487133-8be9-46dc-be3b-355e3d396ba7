<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-info" wx:if="{{isLoggedIn}}">
      <view class="user-card" bindtap="goToPersonalCenter">
        <view class="avatar-section">
          <image 
            class="user-avatar" 
            src="{{userInfo.wechatInfo.avatarUrl || '/assets/icons/profile.png'}}"
            mode="aspectFill"
          />
          <view class="avatar-edit">
            <text class="edit-icon">✏️</text>
          </view>
        </view>
        
        <view class="user-info-section">
          <view class="user-name">{{userInfo.wechatInfo.nickName || userInfo.username || '未设置昵称'}}</view>
          <view class="user-details">
            <view class="detail-item">
              <text class="detail-icon">📱</text>
              <text class="detail-text">{{shortUserId || '未知'}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-icon">👑</text>
              <text class="detail-text">{{userInfo.vipInfo && userInfo.vipInfo.isVip ? 'VIP会员' : '普通用户'}}</text>
            </view>
          </view>
        </view>
        
        <view class="profile-actions">
          <text class="setting-hint">点击设置</text>
          <text class="setting-arrow">›</text>
        </view>
      </view>
    </view>
    
    <!-- 未登录状态 -->
    <view class="login-card" wx:else>
      <view class="login-content">
        <image class="login-avatar" src="/assets/icons/profile.png" />
        <view class="login-info">
          <text class="login-title">欢迎使用墨词自习室</text>
          <text class="login-desc">登录后享受更多个性化功能</text>
        </view>
        <button class="login-btn" bindtap="goToLogin">
          <text class="login-text">立即登录</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 会员开通区域 -->
  <view class="vip-section" wx:if="{{showVipFeatures && isLoggedIn && (!userInfo.vipInfo || !userInfo.vipInfo.isVip)}}">
    <view class="vip-card" bindtap="goToVip">
      <view class="vip-content">
        <view class="vip-icon">👑</view>
        <view class="vip-text">
          <text class="vip-title">开通会员</text>
          <text class="vip-subtitle">解锁全部功能，享受专属服务</text>
        </view>
        <view class="vip-arrow">→</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-title">学习管理</view>
      
      <view class="menu-item" bindtap="goToMyShare">
        <view class="menu-icon-wrapper blue">
          <text class="menu-icon">📤</text>
        </view>
        <view class="menu-content">
          <text class="menu-title-text">我的分享</text>
          <text class="menu-desc">管理分享的学习任务</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>
      
      <view class="menu-item" bindtap="goToReceivedShare">
        <view class="menu-icon-wrapper cyan">
          <text class="menu-icon">📥</text>
        </view>
        <view class="menu-content">
          <text class="menu-title-text">收到的分享</text>
          <text class="menu-desc">查看他人分享的任务</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>
      
      <view class="menu-item" bindtap="goToLearningProgress">
        <view class="menu-icon-wrapper purple">
          <text class="menu-icon">📈</text>
        </view>
        <view class="menu-content">
          <text class="menu-title-text">学习进度</text>
          <text class="menu-desc">查看各词库的学习进度</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>
    </view>
    
    <view class="menu-group">
      <view class="menu-title">设置与帮助</view>
      
      <view class="menu-item" bindtap="goToHelp">
        <view class="menu-icon-wrapper green">
          <text class="menu-icon">❓</text>
        </view>
        <view class="menu-content">
          <text class="menu-title-text">帮助与反馈</text>
          <text class="menu-desc">使用帮助和问题反馈</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>
      
      <view class="menu-item" bindtap="goToCustomerService">
        <view class="menu-icon-wrapper purple">
          <text class="menu-icon">💬</text>
        </view>
        <view class="menu-content">
          <text class="menu-title-text">联系客服</text>
          <text class="menu-desc">扫码添加微信好友，获得专属服务</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>
      
      <view class="menu-item" bindtap="goToAbout">
        <view class="menu-icon-wrapper indigo">
          <text class="menu-icon">ℹ️</text>
        </view>
        <view class="menu-content">
          <text class="menu-title-text">关于我们</text>
          <text class="menu-desc">了解更多应用信息</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>
    </view>
  </view>

  <!-- 管理员功能 -->
  <view class="admin-section" wx:if="{{isAdmin}}">
    <view class="menu-group">
      <view class="menu-title">管理功能</view>
      
      <view class="menu-item admin-item" bindtap="goToAdminTools">
        <view class="menu-icon-wrapper red">
          <text class="menu-icon">🔧</text>
          <view class="badge" wx:if="{{unreadFeedbackCount > 0}}">
            <text class="badge-text">{{unreadFeedbackCount > 99 ? '99+' : unreadFeedbackCount}}</text>
          </view>
        </view>
        <view class="menu-content">
          <text class="menu-title-text">管理员工具</text>
          <text class="menu-desc">通知管理和用户反馈</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{isLoggedIn}}">
    <view class="logout-btn" bindtap="handleLogout">
      <text class="logout-text">退出登录</text>
    </view>
  </view>
</view>