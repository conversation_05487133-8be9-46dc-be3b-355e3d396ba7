<view class="container">
  <!-- 进度条 -->
  <view class="progress-bar">
    <view class="progress" style="width: {{progress}}%"></view>
    <view class="progress-text">{{currentIndex + 1}}/{{total}}</view>
  </view>

  <!-- 计时器（仅在限时模式下显示） -->
  <view class="timer" wx:if="{{timeLimit !== 'unlimited'}}">
    <text>{{remainingTime}}s</text>
  </view>

  <!-- 练习卡片 -->
  <view class="practice-card">
    <!-- 练习模式 -->
    <block wx:if="{{mode === 'practice'}}">
      <view class="word-header">
        <text class="word-text">{{currentWord.word}}</text>
        <view class="word-phonetic" bindtap="playAudio">
          <text>{{currentWord.phonetic}}</text>
          <image class="speaker-icon" src="/assets/icons/speaker.png" mode="aspectFit"></image>
        </view>
      </view>
      <view class="word-meaning">{{currentWord.definition}}</view>
      <view class="word-example" wx:if="{{currentWord.example}}">
        <text class="example-text">{{currentWord.example}}</text>
        <text class="example-translation">{{currentWord.exampleTranslation}}</text>
      </view>
    </block>

    <!-- 测试模式 -->
    <block wx:else>
      <!-- 英译汉 -->
      <block wx:if="{{testType === 'en2zh'}}">
        <view class="word-header">
          <text class="word-text">{{currentWord.word}}</text>
          <view class="word-phonetic" bindtap="playAudio">
            <text>{{currentWord.phonetic}}</text>
            <image class="speaker-icon" src="/assets/icons/speaker.png" mode="aspectFit"></image>
          </view>
        </view>
        <view class="options-list">
          <view class="option-item {{selectedOption === index ? 'selected' : ''}}" 
                wx:for="{{options}}" 
                wx:key="index"
                bindtap="onOptionSelect"
                data-index="{{index}}">
            {{item}}
          </view>
        </view>
      </block>

      <!-- 汉译英 -->
      <block wx:if="{{testType === 'zh2en'}}">
        <view class="word-meaning">{{currentWord.definition}}</view>
        <view class="options-list">
          <view class="option-item {{selectedOption === index ? 'selected' : ''}}" 
                wx:for="{{options}}" 
                wx:key="index"
                bindtap="onOptionSelect"
                data-index="{{index}}">
            {{item}}
          </view>
        </view>
      </block>

      <!-- 单词消消乐 -->
      <block wx:if="{{testType === 'game'}}">
        <view class="game-grid">
          <view class="game-row" wx:for="{{gameBoard}}" wx:for-item="row" wx:for-index="rowIndex" wx:key="rowIndex">
            <view class="game-cell {{cell.selected ? 'selected' : ''}} {{cell.matched ? 'matched' : ''}}"
                  wx:for="{{row}}" 
                  wx:for-item="cell" 
                  wx:key="index"
                  bindtap="onGameCellTap"
                  data-row="{{rowIndex}}"
                  data-col="{{index}}">
              {{cell.text}}
            </view>
          </view>
        </view>
      </block>
    </block>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons" wx:if="{{mode === 'practice'}}">
    <button class="action-btn" bindtap="onNextWord">下一个</button>
  </view>

  <!-- 完成提示 -->
  <view class="completion-modal" wx:if="{{showCompletion}}">
    <view class="completion-content">
      <view class="completion-title">练习完成</view>
      <view class="completion-stats">
        <view class="stat-item">
          <text class="stat-label">用时</text>
          <text class="stat-value">{{totalTime}}秒</text>
        </view>
        <view class="stat-item" wx:if="{{mode === 'test'}}">
          <text class="stat-label">正确率</text>
          <text class="stat-value">{{correctRate}}%</text>
        </view>
      </view>
      <view class="completion-buttons">
        <button class="completion-btn" bindtap="onAddToMistakes" wx:if="{{mode === 'test' && mistakes.length > 0}}">
          加入错题本
        </button>
        <button class="completion-btn" bindtap="onRetryMistakes" wx:if="{{mode === 'test' && mistakes.length > 0}}">
          错题重练
        </button>
        <button class="completion-btn" bindtap="onBackToHome">返回首页</button>
      </view>
    </view>
  </view>
</view> 