/* 学习页面样式 */
.learning-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9ff 0%, #ffffff 100%);
  padding: 20rpx;
}

/* 顶部进度条 */
.progress-header {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.score-text {
  font-size: 26rpx;
  color: #007bff;
  font-weight: bold;
}

.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 学习区域 */
.learning-area {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(0,0,0,0.1);
  margin-bottom: 30rpx;
}

/* 题目区域 */
.question-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.question-content {
  margin-bottom: 30rpx;
}

.word-display {
  margin-bottom: 20rpx;
}

.word-text {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.phonetic {
  display: block;
  font-size: 32rpx;
  color: #666;
  font-style: italic;
}

.meaning-display {
  margin-bottom: 20rpx;
}

.meaning-text {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.question-prompt {
  font-size: 28rpx;
  color: #666;
}

/* 选项区域 */
.options-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.option-item {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
}

.option-item.selected {
  border-color: #007bff;
  background: #e3f2fd;
}

.option-item.correct {
  border-color: #28a745;
  background: #d4edda;
  color: #155724;
}

.option-item.wrong {
  border-color: #dc3545;
  background: #f8d7da;
  color: #721c24;
}

.option-text {
  font-size: 30rpx;
  line-height: 1.4;
  flex: 1;
}

.option-icon {
  font-size: 32rpx;
  font-weight: bold;
  margin-left: 16rpx;
}

/* 结果页面 */
.result-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
}

.result-card {
  background: white;
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  text-align: center;
  box-shadow: 0 12rpx 40rpx rgba(0,0,0,0.15);
  width: 100%;
  max-width: 600rpx;
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
}

.result-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
  border-top: 1rpx solid #eee;
  border-bottom: 1rpx solid #eee;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #007bff;
}

.result-details {
  margin: 40rpx 0;
  text-align: left;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.detail-value.correct {
  color: #28a745;
}

.detail-value.wrong {
  color: #dc3545;
}

.result-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(0, 123, 255, 0.4);
}

.action-btn.secondary {
  background: white;
  color: #007bff;
  border: 2rpx solid #007bff;
}

/* 动画效果 */
.option-item {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-card {
  animation: zoomIn 0.5s ease;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
} 