<view class="container">
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 编辑内容 -->
  <view class="edit-content" wx:else>
    <view class="edit-form">
      <!-- 词库名称 -->
      <view class="form-group">
        <text class="form-label">词库名称</text>
        <input 
          class="form-input" 
          placeholder="请输入词库名称" 
          value="{{wordbankName}}" 
          bindinput="onWordbankNameInput"
          maxlength="20"
        />
      </view>

      <!-- 输入方式选择 -->
      <view class="form-group">
        <text class="form-label">词汇内容</text>
        <view class="input-methods">
          <button class="method-btn {{inputMethod === 'text' ? 'active' : ''}}" 
                  bindtap="switchInputMethod" 
                  data-method="text">
            📝 文本编辑
          </button>
          <button class="method-btn {{inputMethod === 'excel' ? 'active' : ''}}" 
                  bindtap="switchInputMethod" 
                  data-method="excel"
                  disabled="true">
            📊 Excel上传（开发中）
          </button>
        </view>
      </view>

      <!-- 文本输入模式 -->
      <view class="form-group" wx:if="{{inputMethod === 'text'}}">
        <textarea 
          class="form-textarea" 
          placeholder="{{textareaPlaceholder}}" 
          value="{{wordbankContent}}" 
          bindinput="onWordbankContentInput"
          maxlength="10000"
        />
        <text class="char-count">{{wordbankContent.length}}/10000</text>
      </view>

      <!-- 格式说明 -->
      <view class="format-tips">
        <text class="tips-title">格式说明：</text>
        <text class="tips-item">• apple 苹果（单词+释义，必需格式）</text>
        <text class="tips-item">• apple /ˈæpl/ 苹果（单词+音标+释义）</text>
        <text class="tips-item">• apple /ˈæpl/ 苹果 I like apples.（完整格式）</text>
        <text class="tips-item">每行一个单词，必须包含释义，重复单词将自动过滤</text>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button class="action-btn cancel" bindtap="cancelEdit">取消</button>
      <button class="action-btn save" bindtap="saveWordbank" disabled="{{saving}}">
        {{saving ? '保存中...' : '保存'}}
      </button>
    </view>
  </view>
</view> 