const app = getApp();

Page({
  data: {
    wordbankId: '',
    wordbank: null,
    wordbankName: '',
    wordbankContent: '',
    words: [],
    loading: true,
    saving: false,
    inputMethod: 'text',
    selectedFile: null,
    textareaPlaceholder: `请输入单词和释义（必须包含）：
格式示例：
apple 苹果
play v. 玩耍
good adj. 好的 This is good.

每行一个单词，支持音标和例句...`
  },

  onLoad(options) {
    const { wordbankId } = options;
    
    if (!wordbankId) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({ wordbankId });
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '编辑词库'
    });

    // 加载词库数据
    this.loadWordbankData();
  },

  // 加载词库数据
  async loadWordbankData() {
    this.setData({ loading: true });

    try {
      const db = wx.cloud.database();
      const userInfo = await app.getUserInfo();
      
      if (!userInfo || !userInfo.openid) {
        throw new Error('用户未登录');
      }

      // 查询词库数据
      const result = await db.collection('custom_wordbanks')
        .where({
          id: this.data.wordbankId,
          creatorOpenid: userInfo.openid
        })
        .get();

      if (result.data.length === 0) {
        throw new Error('词库不存在或无权访问');
      }

      const wordbank = result.data[0];
      const words = wordbank.words || [];
      
      // 将词汇转换为文本格式
      const wordbankContent = words.map(word => {
        let line = word.word;
        if (word.phonetic) line += ` ${word.phonetic}`;
        if (word.chinese) line += ` ${word.chinese}`;
        if (word.example) line += ` ${word.example}`;
        return line;
      }).join('\n');

      this.setData({
        wordbank: wordbank,
        wordbankName: wordbank.name,
        wordbankContent: wordbankContent,
        words: words,
        loading: false
      });

    } catch (error) {
      console.error('加载词库数据失败:', error);
      this.setData({ loading: false });
      
      wx.showModal({
        title: '加载失败',
        content: error.message || '无法加载词库数据',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 词库名称输入
  onWordbankNameInput(e) {
    this.setData({
      wordbankName: e.detail.value
    });
  },

  // 词库内容输入
  onWordbankContentInput(e) {
    this.setData({
      wordbankContent: e.detail.value
    });
  },

  // 切换输入方式
  switchInputMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      inputMethod: method,
      selectedFile: null
    });
  },

  // 解析词汇内容（必须包含释义）
  parseWordbankContent(content) {
    const lines = content.trim().split('\n');
    const words = [];
    const seenWords = new Set();

    lines.forEach((line, index) => {
      line = line.trim();
      if (!line) return;

      const parts = line.split(/\s+/);
      if (parts.length < 2) {
        console.warn(`第${index + 1}行格式错误：必须包含单词和释义`);
        return;
      }

      const word = parts[0].toLowerCase();
      if (seenWords.has(word)) return;
      seenWords.add(word);

      let phonetic = '';
      let meaning = '';
      let example = '';

      // 查找音标
      const phoneticMatch = line.match(/\/[^\/]+\//);
      if (phoneticMatch) {
        phonetic = phoneticMatch[0];
        // 移除音标后重新解析
        const withoutPhonetic = line.replace(phoneticMatch[0], '').trim();
        const newParts = withoutPhonetic.split(/\s+/);
        meaning = newParts[1] || '';
        example = newParts.slice(2).join(' ') || '';
      } else {
        meaning = parts[1] || '';
        example = parts.slice(2).join(' ') || '';
      }

      // 验证必须有释义
      if (!meaning) {
        console.warn(`第${index + 1}行缺少释义：${word}`);
        return;
      }

      words.push({
        word: word,
        phonetic: phonetic,
        meaning: meaning,
        example: example,
        // 保持兼容性，同时保存为chinese字段
        chinese: meaning
      });
    });

    return words;
  },



  // 保存词库
  async saveWordbank() {
    if (!this.data.wordbankName) {
      wx.showToast({
        title: '请输入词库名称',
        icon: 'none'
      });
      return;
    }

    if (!this.data.wordbankContent && this.data.inputMethod === 'text') {
      wx.showToast({
        title: '请输入词汇内容',
        icon: 'none'
      });
      return;
    }

    if (this.data.saving) return;

    this.setData({ saving: true });

    try {
      wx.showLoading({
        title: '解析词汇中...',
        mask: true
      });

      const userInfo = await app.getUserInfo();
      if (!userInfo || !userInfo.openid) {
        throw new Error('用户未登录');
      }

      // 解析词汇
      let words = this.parseWordbankContent(this.data.wordbankContent);
      
      if (words.length === 0) {
        wx.hideLoading();
        wx.showToast({
          title: '未识别到有效词汇',
          icon: 'none'
        });
        this.setData({ saving: false });
        return;
      }

      // 更新词库
      wx.showLoading({
        title: '保存中...',
        mask: true
      });

      const db = wx.cloud.database();
      await db.collection('custom_wordbanks')
        .where({
          id: this.data.wordbankId,
          creatorOpenid: userInfo.openid
        })
        .update({
          data: {
            name: this.data.wordbankName,
            words: words,
            wordCount: words.length,
            updateTime: db.serverDate()
          }
        });

      wx.hideLoading();
      
      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 2000
      });

      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      wx.hideLoading();
      console.error('保存词库失败:', error);
      
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  // 取消编辑
  cancelEdit() {
    wx.showModal({
      title: '提示',
      content: '确定要放弃编辑吗？未保存的内容将会丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  }
}); 