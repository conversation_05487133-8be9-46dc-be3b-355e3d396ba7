const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { wordbankId } = event;
  
  try {
    console.log('开始删除词库:', wordbankId, '用户:', wxContext.OPENID);
    
    // 首先查询词库是否存在且属于当前用户
    const queryResult = await db.collection('custom_wordbanks')
      .where({
        _id: wordbankId,
        creatorOpenid: wxContext.OPENID
      })
      .get();
    
    console.log('查询结果:', queryResult);
    
    if (!queryResult.data || queryResult.data.length === 0) {
      return {
        success: false,
        code: 404,
        message: '词库不存在或无权删除'
      };
    }
    
    // 执行删除操作
    const deleteResult = await db.collection('custom_wordbanks')
      .doc(wordbankId)
      .remove();
    
    console.log('删除结果:', deleteResult);
    
    if (deleteResult.stats && deleteResult.stats.removed > 0) {
      return {
        success: true,
        code: 200,
        message: '删除成功',
        deletedCount: deleteResult.stats.removed
      };
    } else {
      return {
        success: false,
        code: 500,
        message: '删除失败，没有记录被删除'
      };
    }
    
  } catch (error) {
    console.error('删除词库失败:', error);
    return {
      success: false,
      code: 500,
      message: '删除操作失败',
      error: error.message
    };
  }
}; 