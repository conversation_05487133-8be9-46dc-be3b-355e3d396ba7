Page({

  /**
   * 页面的初始数据
   */
  data: {
    shareId: '',
    shareData: null,
    loading: true,
    participants: [], // 参与者列表
    statistics: {
      totalParticipants: 0,
      totalTests: 0,
      averageScore: 0,
      highestScore: 0,
      completionRate: 0
    },
    showDetail: false,
    selectedParticipant: null,
    isMultiLevel: false,
    totalLevels: 1,
    currentTab: 'participants', // participants, levels (删除了overview)
    selectedMistakes: [], // 选中的错词
    allMistakesSelected: false, // 是否全选
    levelsData: [], // 关卡数据
    selectedMistakesMap: {}, // 选中错词的映射
    // 多组错词选择相关
    selectedGroupMistakes: [], // 选中的多组错词
    allGroupMistakesSelected: false, // 是否全选多组错词
    selectedGroupMistakesMap: {}, // 选中多组错词的映射
    scoreExplanation: '', // 分值说明
    shareInfo: null // 分享信息
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { shareId } = options;
    if (shareId) {
      this.setData({ shareId });
      this.loadShareData();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时重新加载数据
    if (this.data.shareId) {
      this.loadShareData();
    }
  },

  /**
   * 加载分享数据
   */
  async loadShareData() {
    try {
      this.setData({ loading: true });
      
      // 优先从云端获取分享数据
      let shareData = null;
      
      try {
        const result = await wx.cloud.callFunction({
          name: 'getShareTest',
          data: { shareId: this.data.shareId }
        });
        
        if (result.result.success) {
          shareData = result.result.data;
        }
      } catch (cloudError) {
        console.log('从云端获取分享数据失败:', cloudError);
      }
      
      // 如果云端没有数据，尝试从本地获取
      if (!shareData) {
        const shareTests = wx.getStorageSync('shareTests') || {};
        shareData = shareTests[this.data.shareId];
      }
      
      if (!shareData) {
        wx.showToast({
          title: '分享不存在',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 处理参与者数据
      const participants = this.processParticipants(shareData);
      
      // 计算统计信息
      const statistics = this.calculateStatistics(shareData, participants);
      
      // 如果是多关卡，计算关卡数据
      let levelsData = [];
      if (shareData.isMultiLevel) {
        levelsData = this.calculateLevelsData(shareData.totalLevels || 1, participants);
      }

      // 生成分值说明
      const scoreExplanation = this.generateScoreExplanation(shareData.testType || shareData.testMode, shareData);

      this.setData({
        shareData: shareData,
        participants: participants,
        statistics: statistics,
        isMultiLevel: shareData.isMultiLevel || false,
        totalLevels: shareData.totalLevels || 1,
        levelsData: levelsData,
        scoreExplanation: scoreExplanation,
        loading: false
      });
      
    } catch (error) {
      console.error('加载分享数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 生成分值说明
   */
  generateScoreExplanation(testMode, shareData) {
    const explanations = {
      'en_to_cn': '英译汉模式：每题10分',
      'cn_to_en': '汉译英模式：每题10分',
      'dictation': '听写模式：每题10分',
      'elimination': '消消乐模式：每消除一对词汇得10分，额外时间奖励最多20分',
      'puzzle': '填字游戏：每个字母1分，完成单词有额外奖励分',
      'phrase_en2zh': '短语测试：每题10分',
      'phrase_zh2en': '短语测试：每题10分'
    };

    let explanation = explanations[testMode] || '每题10分';

    // 添加关卡信息
    if (shareData) {
      const totalLevels = shareData.totalLevels || 1;

      // 动态获取真实的每关单词数量
      let wordsPerLevel = 10; // 默认值

      // 优先从 wordsPerLevel 字段获取
      if (shareData.wordsPerLevel) {
        wordsPerLevel = shareData.wordsPerLevel;
      }
      // 其次从 wordsPerGroup 字段获取
      else if (shareData.wordsPerGroup) {
        wordsPerLevel = shareData.wordsPerGroup;
      }
      // 最后从 wordCount 字段获取（如果是单关卡）
      else if (shareData.wordCount && totalLevels === 1) {
        wordsPerLevel = shareData.wordCount;
      }
      // 如果是多关卡但只有总词汇数，计算平均值
      else if (shareData.wordCount && totalLevels > 1) {
        wordsPerLevel = Math.ceil(shareData.wordCount / totalLevels);
      }

      console.log('分值说明 - 总关卡数:', totalLevels, '每关词汇数:', wordsPerLevel);
      console.log('shareData字段:', {
        wordsPerLevel: shareData.wordsPerLevel,
        wordsPerGroup: shareData.wordsPerGroup,
        wordCount: shareData.wordCount,
        totalLevels: shareData.totalLevels
      });

      explanation += `。共${totalLevels}个关卡，每关${wordsPerLevel}个词汇`;
    }

    return explanation;
  },

  /**
   * 处理参与者数据
   */
  processParticipants(shareData) {
    const participants = [];
    const visitors = shareData.visitors || [];
    const results = shareData.results || [];
    const levelProgress = shareData.levelProgress || {};
    
    visitors.forEach(visitor => {
      // 获取该用户的所有测试结果
      const userResults = results.filter(r => r.participantOpenid === visitor.openid);
      
      // 获取该用户的关卡进度
      const userProgress = levelProgress[visitor.openid];
      
      // 计算统计信息
      const testCount = userResults.length;
      const scores = userResults.map(r => r.score);
      const bestScore = scores.length > 0 ? Math.max(...scores) : 0;
      const averageScore = scores.length > 0 ? Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length) : 0;
      const latestResult = userResults.length > 0 ? userResults[userResults.length - 1] : null;
      
      // 计算多组任务进度
      let groupProgress = null;
      if (shareData.isMultiLevel || shareData.totalGroups > 1) {
        const totalGroups = shareData.totalGroups || shareData.totalLevels || 1;
        const completedGroups = userProgress ? (userProgress.completedLevels || []).length : 0;
        const currentGroup = userProgress ? userProgress.currentLevel : 1;
        const progressPercentage = Math.round((completedGroups / totalGroups) * 100);

        // 收集所有错词
        const allMistakes = [];
        userResults.forEach(result => {
          if (result.mistakes && result.mistakes.length > 0) {
            allMistakes.push(...result.mistakes);
          }
        });

        // 去重错词并格式化
        const uniqueMistakes = [];
        const seenWords = new Set();
        allMistakes.forEach(mistake => {
          // 确保错词数据格式正确
          let wordText = '';
          let correctAnswer = '';
          let userAnswer = '';

          if (typeof mistake === 'string') {
            wordText = mistake;
          } else if (mistake && typeof mistake === 'object') {
            // 处理嵌套的word对象
            if (typeof mistake.word === 'object' && mistake.word !== null) {
              wordText = mistake.word.word || mistake.word.words || mistake.word.english || mistake.word.text || '';
            } else {
              wordText = mistake.word || mistake.text || mistake.english || mistake.question || '';
            }

            // 处理正确答案
            if (typeof mistake.correctAnswer === 'object' && mistake.correctAnswer !== null) {
              correctAnswer = mistake.correctAnswer.meaning || mistake.correctAnswer.chinese || mistake.correctAnswer.word || '';
            } else {
              correctAnswer = mistake.correctAnswer || mistake.chinese || mistake.meaning || '';
            }

            // 处理用户答案 - 添加更多字段支持
            if (typeof mistake.userAnswer === 'object' && mistake.userAnswer !== null) {
              userAnswer = mistake.userAnswer.meaning || mistake.userAnswer.word || mistake.userAnswer.text || '';
            } else if (typeof mistake.selectedAnswer === 'object' && mistake.selectedAnswer !== null) {
              userAnswer = mistake.selectedAnswer.meaning || mistake.selectedAnswer.word || mistake.selectedAnswer.text || '';
            } else {
              userAnswer = mistake.userAnswer || mistake.selectedAnswer || mistake.answer || '';
            }
          }

          // 确保wordText不为空且不是"[object Object]"
          if (wordText && wordText !== '[object Object]' && !seenWords.has(wordText)) {
            seenWords.add(wordText);
            uniqueMistakes.push({
              word: wordText,
              correctAnswer: correctAnswer || '未知',
              userAnswer: userAnswer || '未答'
            });
          }
        });

        groupProgress = {
          totalGroups: totalGroups,
          completedGroups: completedGroups,
          currentGroup: currentGroup,
          progressPercentage: progressPercentage,
          allMistakes: uniqueMistakes,
          isCompleted: completedGroups >= totalGroups
        };
      }

      // 尝试从测试结果中获取用户信息
      let userNickName = visitor.nickName || '匿名用户';
      let userAvatar = visitor.avatar || visitor.avatarUrl || '';

      // 如果访问者信息中没有昵称，尝试从测试结果中获取
      if (userNickName === '匿名用户' && userResults.length > 0) {
        const latestResult = userResults[userResults.length - 1];

        // 尝试多种可能的用户信息字段
        if (latestResult.participantInfo) {
          userNickName = latestResult.participantInfo.nickName || userNickName;
          userAvatar = latestResult.participantInfo.avatar || latestResult.participantInfo.avatarUrl || userAvatar;
        } else if (latestResult.userInfo) {
          userNickName = latestResult.userInfo.nickName || userNickName;
          userAvatar = latestResult.userInfo.avatar || latestResult.userInfo.avatarUrl || userAvatar;
        } else if (latestResult.nickName) {
          // 直接从结果中获取
          userNickName = latestResult.nickName || userNickName;
          userAvatar = latestResult.avatar || latestResult.avatarUrl || userAvatar;
        }
      }

      // 如果仍然没有头像，使用默认头像
      if (!userAvatar) {
        userAvatar = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0';
      }

      // 计算总正确率和总错词数量
      let totalCorrect = 0;
      let totalQuestions = 0;
      let totalMistakes = 0;
      let overallAccuracy = 0;

      // 先计算基础数据
      userResults.forEach(result => {
        if (result.correctCount !== undefined && result.correctCount !== null &&
            result.totalCount !== undefined && result.totalCount !== null) {
          totalCorrect += result.correctCount;
          totalQuestions += result.totalCount;
        }
        if (result.mistakes && Array.isArray(result.mistakes)) {
          totalMistakes += result.mistakes.length;
        }
      });

      // 计算总正确率
      if (shareData.isMultiLevel) {
        // 多关卡任务：优先使用已完成关卡的平均正确率
        const completedLevels = userProgress ? (userProgress.completedLevels || []) : [];
        let accuracySum = 0;
        let validAccuracyCount = 0;

        // 尝试从已完成的关卡中计算平均正确率
        userResults.forEach(result => {
          const levelId = result.levelId || result.level || result.levelNumber || result.groupIndex;
          if (levelId && completedLevels.includes(levelId)) {
            if (result.accuracy !== undefined && result.accuracy !== null && !isNaN(result.accuracy)) {
              accuracySum += parseFloat(result.accuracy);
              validAccuracyCount++;
            } else if (result.correctCount !== undefined && result.totalCount !== undefined &&
                      result.totalCount > 0 && !isNaN(result.correctCount) && !isNaN(result.totalCount)) {
              const accuracy = Math.round((result.correctCount / result.totalCount) * 100);
              accuracySum += accuracy;
              validAccuracyCount++;
            }
          }
        });

        if (validAccuracyCount > 0) {
          overallAccuracy = Math.round(accuracySum / validAccuracyCount);
        } else if (totalQuestions > 0) {
          // 如果没有已完成关卡的数据，使用总体正确率
          overallAccuracy = Math.round((totalCorrect / totalQuestions) * 100);
        } else {
          overallAccuracy = 0;
        }
      } else {
        // 单组任务：直接计算总体正确率
        if (totalQuestions > 0) {
          overallAccuracy = Math.round((totalCorrect / totalQuestions) * 100);
        } else {
          // 如果没有totalCount数据，尝试从accuracy字段计算
          let accuracySum = 0;
          let validCount = 0;
          userResults.forEach(result => {
            if (result.accuracy !== undefined && result.accuracy !== null && !isNaN(result.accuracy)) {
              accuracySum += parseFloat(result.accuracy);
              validCount++;
            }
          });
          overallAccuracy = validCount > 0 ? Math.round(accuracySum / validCount) : 0;
        }
      }

      // 确保overallAccuracy是一个有效的数字
      if (isNaN(overallAccuracy) || overallAccuracy === null || overallAccuracy === undefined) {
        overallAccuracy = 0;
      }

      participants.push({
        openid: visitor.openid,
        nickName: userNickName,
        avatar: userAvatar,
        firstVisitTime: visitor.firstVisitTime,
        lastTestTime: visitor.lastTestTime,
        visitCount: visitor.visitCount || 0,
        testCount: testCount,
        bestScore: bestScore,
        averageScore: averageScore,
        latestScore: latestResult ? latestResult.score : 0,
        latestAccuracy: latestResult ? latestResult.accuracy : 0,
        latestTime: latestResult ? latestResult.usedTime : null, // 消消乐模式的用时
        latestTestTime: latestResult ? latestResult.submitTime : null,
        mistakes: latestResult ? this.formatMistakes(latestResult.mistakes || []) : [],
        results: userResults,
        progress: userProgress,
        // 多关卡相关
        currentLevel: userProgress ? userProgress.currentLevel : 1,
        completedLevels: userProgress ? userProgress.completedLevels : [],
        levelScores: userProgress ? userProgress.scores : {},
        // 多组任务进度
        groupProgress: groupProgress,
        // 总体统计
        overallAccuracy: overallAccuracy,
        totalMistakes: totalMistakes
      });
    });
    
    // 按最近测试时间排序
    participants.sort((a, b) => {
      const timeA = a.latestTestTime || a.lastTestTime || 0;
      const timeB = b.latestTestTime || b.lastTestTime || 0;
      return timeB - timeA;
    });
    
    return participants;
  },

  /**
   * 格式化错词数据
   */
  formatMistakes(mistakes) {
    if (!mistakes || !Array.isArray(mistakes)) {
      return [];
    }

    return mistakes.map(mistake => {
      if (typeof mistake === 'string') {
        return {
          word: mistake,
          correctAnswer: '',
          userAnswer: ''
        };
      } else if (mistake && typeof mistake === 'object') {
        // 处理嵌套的word对象
        let wordText = '';
        if (typeof mistake.word === 'object' && mistake.word !== null) {
          wordText = mistake.word.word || mistake.word.words || mistake.word.english || mistake.word.text || '';
        } else {
          wordText = mistake.word || mistake.text || mistake.english || mistake.question || '';
        }

        // 处理正确答案
        let correctAnswer = '';
        if (typeof mistake.correctAnswer === 'object' && mistake.correctAnswer !== null) {
          correctAnswer = mistake.correctAnswer.meaning || mistake.correctAnswer.chinese || mistake.correctAnswer.word || '';
        } else {
          correctAnswer = mistake.correctAnswer || mistake.chinese || mistake.meaning || '';
        }

        // 处理用户答案
        let userAnswer = '';
        if (typeof mistake.userAnswer === 'object' && mistake.userAnswer !== null) {
          userAnswer = mistake.userAnswer.meaning || mistake.userAnswer.word || mistake.userAnswer.text || '';
        } else if (typeof mistake.selectedAnswer === 'object' && mistake.selectedAnswer !== null) {
          userAnswer = mistake.selectedAnswer.meaning || mistake.selectedAnswer.word || mistake.selectedAnswer.text || '';
        } else {
          userAnswer = mistake.userAnswer || mistake.selectedAnswer || mistake.answer || '';
        }

        return {
          word: wordText,
          correctAnswer: correctAnswer || '未知',
          userAnswer: userAnswer || '未答'
        };
      }
      return {
        word: '',
        correctAnswer: '',
        userAnswer: ''
      };
    }).filter(mistake => mistake.word && mistake.word !== '[object Object]'); // 过滤掉空的错词和对象字符串
  },

  /**
   * 计算统计信息
   */
  calculateStatistics(shareData, participants) {
    const results = shareData.results || [];
    const totalParticipants = participants.length;
    const totalTests = results.length;
    
    let totalScore = 0;
    let highestScore = 0;
    let completedParticipants = 0;
    
    results.forEach(result => {
      totalScore += result.score;
      highestScore = Math.max(highestScore, result.score);
    });
    
    // 计算完成率
    if (shareData.isMultiLevel) {
      participants.forEach(participant => {
        if (participant.completedLevels.length === shareData.totalLevels) {
          completedParticipants++;
        }
      });
    } else {
      completedParticipants = participants.filter(p => p.testCount > 0).length;
    }
    
    return {
      totalParticipants: totalParticipants,
      totalTests: totalTests,
      averageScore: totalTests > 0 ? Math.round(totalScore / totalTests) : 0,
      highestScore: highestScore,
      completionRate: totalParticipants > 0 ? Math.round((completedParticipants / totalParticipants) * 100) : 0
    };
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tab });
  },

  /**
   * 查看参与者详情
   */
  viewParticipantDetail(e) {
    const index = e.currentTarget.dataset.index;
    const participant = this.data.participants[index];

    // 处理关卡分数和正确率数据
    const levelScoresWithAccuracy = this.processLevelScoresWithAccuracy(participant);

    // 计算完成进度百分比
    let progressPercentage = 0;
    if (participant.groupProgress && participant.groupProgress.progressPercentage !== undefined) {
      progressPercentage = participant.groupProgress.progressPercentage;
    } else if (participant.completedLevels && participant.completedLevels.length > 0) {
      progressPercentage = Math.round((participant.completedLevels.length / this.data.totalLevels) * 100);
    }

    this.setData({
      selectedParticipant: {
        ...participant,
        levelScoresWithAccuracy: levelScoresWithAccuracy,
        progressPercentage: progressPercentage
      },
      showDetail: true,
      selectedMistakes: [], // 重置选中的错词
      selectedMistakesMap: {}, // 重置选中错词映射
      allMistakesSelected: false // 重置全选状态
    });
  },

  /**
   * 处理关卡分数和正确率数据
   */
  processLevelScoresWithAccuracy(participant) {
    const levelScoresWithAccuracy = [];
    const levelScores = participant.levelScores || {};
    const results = participant.results || [];

    // 遍历所有关卡
    for (let i = 1; i <= this.data.totalLevels; i++) {
      let score = 0;
      let accuracy = 0;

      // 查找该关卡的测试结果
      const levelResult = results.find(result => {
        // 尝试多种可能的关卡标识
        const levelIdentifiers = [
          result.levelId,
          result.level,
          result.levelNumber,
          result.groupIndex,
          result.currentLevel
        ];

        // 特殊处理：如果是第1关且没有明确的关卡标识，可能是单关卡数据
        if (i === 1 && levelIdentifiers.every(id => id === undefined || id === null)) {
          return true; // 第1关默认匹配没有关卡标识的结果
        }

        return levelIdentifiers.includes(i) || levelIdentifiers.includes(i.toString()) || levelIdentifiers.includes(i - 1);
      });

      if (levelResult) {
        // 优先从结果中获取分数
        if (levelResult.score !== undefined && levelResult.score !== null) {
          score = levelResult.score;
        }

        // 获取正确率
        if (levelResult.accuracy !== undefined && levelResult.accuracy !== null) {
          accuracy = levelResult.accuracy;
        } else if (levelResult.correctCount !== undefined && levelResult.totalCount !== undefined && levelResult.totalCount > 0) {
          accuracy = Math.round((levelResult.correctCount / levelResult.totalCount) * 100);
        } else if (levelResult.score !== undefined && levelResult.score !== null && levelResult.score > 0) {
          // 如果有分数但没有正确率数据，根据分数估算正确率
          // 假设每题10分，可以根据分数推算正确率
          const estimatedAccuracy = Math.min(100, Math.round(levelResult.score));
          accuracy = estimatedAccuracy;
        }
      }

      // 如果从结果中没有获取到分数，尝试从levelScores中获取
      if (score === 0) {
        const possibleScoreKeys = [
          i,                    // 直接用关卡号
          i.toString(),         // 字符串形式的关卡号
          i - 1,               // 从0开始的索引
          (i - 1).toString(),  // 字符串形式的从0开始索引
          `level_${i}`,        // level_1 格式
          `level${i}`,         // level1 格式
          `${i}`               // 纯字符串
        ];

        for (const key of possibleScoreKeys) {
          if (levelScores[key] !== undefined && levelScores[key] !== null && levelScores[key] > 0) {
            score = levelScores[key];
            break;
          }
        }
      }

      // 如果仍然没有正确率但有分数，再次尝试估算
      if (accuracy === 0 && score > 0) {
        accuracy = Math.min(100, Math.round(score));
      }

      // 获取用时数据（消消乐模式）
      let time = null;
      if (this.data.shareData && (this.data.shareData.testType === 'elimination' || this.data.shareData.testMode === 'elimination')) {
        // 从结果中查找用时数据
        const levelResult = results.find(r =>
          r.levelId === i || r.level === i || r.levelNumber === i || r.groupIndex === i
        );
        if (levelResult && levelResult.usedTime) {
          time = levelResult.usedTime;
        }
      }

      levelScoresWithAccuracy.push({
        levelIndex: i,
        score: score,
        accuracy: accuracy,
        time: time
      });
    }

    return levelScoresWithAccuracy;
  },

  /**
   * 关闭详情弹窗
   */
  closeDetail() {
    this.setData({
      showDetail: false,
      selectedParticipant: null
    });
  },

  /**
   * 阻止冒泡
   */
  stopPropagation(e) {
    // 阻止事件冒泡
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '未知';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) {
      return '刚刚';
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`;
    } else if (diff < 2592000000) {
      return `${Math.floor(diff / 86400000)}天前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * 导出数据
   */
  exportData() {
    const { shareData, participants, statistics } = this.data;
    
    // 构建导出数据
    const exportData = {
      shareInfo: {
        shareId: shareData.shareId,
        testType: shareData.testType,
        libraryName: shareData.libraryName,
        createTime: shareData.createTime,
        expireTime: shareData.expireTime,
        isMultiLevel: shareData.isMultiLevel,
        totalLevels: shareData.totalLevels,
        wordsCount: shareData.words ? shareData.words.length : 0
      },
      statistics: statistics,
      participants: participants.map(p => ({
        nickName: p.nickName,
        testCount: p.testCount,
        bestScore: p.bestScore,
        averageScore: p.averageScore,
        latestScore: p.latestScore,
        latestAccuracy: p.latestAccuracy,
        currentLevel: p.currentLevel,
        completedLevels: p.completedLevels,
        firstVisitTime: this.formatTime(p.firstVisitTime),
        lastTestTime: this.formatTime(p.latestTestTime)
      }))
    };
    
    // 复制到剪贴板
    wx.setClipboardData({
      data: JSON.stringify(exportData, null, 2),
      success: () => {
        wx.showToast({
          title: '数据已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 删除分享
   */
  deleteShare() {
    wx.showModal({
      title: '确认删除',
      content: '删除后所有参与者将无法继续访问此分享测试，是否确认删除？',
      confirmText: '确认删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.doDeleteShare();
        }
      }
    });
  },

  /**
   * 执行删除分享
   */
  async doDeleteShare() {
    try {
      wx.showLoading({
        title: '删除中...',
        mask: true
      });
      
      // 调用云函数删除分享
      const result = await wx.cloud.callFunction({
        name: 'deleteShareTest',
        data: {
          shareId: this.data.shareId,
          deleteType: 'creator' // 创建者删除
        }
      });
      
      wx.hideLoading();
      
      if (result.result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: result.result.message || '删除失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('删除分享失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    }
  },

  /**
   * 分享给其他人
   */
  shareToOthers() {
    const { shareId, shareData } = this.data;
    
    wx.showActionSheet({
      itemList: ['复制测试ID', '分享给微信好友', '分享到企业微信'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.copyTestId();
            break;
          case 1:
            this.shareToWeChat();
            break;
          case 2:
            this.shareToEnterpriseWeChat();
            break;
        }
      }
    });
  },

  /**
   * 复制测试ID
   */
  copyTestId() {
    wx.setClipboardData({
      data: this.data.shareId,
      success: () => {
        wx.showToast({
          title: '测试ID已复制',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 分享到微信
   */
  shareToWeChat() {
    const { shareData } = this.data;

    // 设置分享信息，让用户通过右上角菜单分享
    this.setData({
      shareInfo: {
        title: `${shareData.libraryName || '词汇测试'} - ${this.getTestModeText(shareData.testType || shareData.testMode)}`,
        path: `/pages/wordtest/test/test?shareId=${shareData.shareId}&shareMode=share&testMode=${shareData.testType || shareData.testMode}`,
        imageUrl: '/images/share-cover.png'
      }
    });

    // 显示分享菜单
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        wx.showToast({
          title: '请点击右上角分享',
          icon: 'none',
          duration: 2000
        });
      },
      fail: (error) => {
        console.error('显示分享菜单失败:', error);
        wx.showToast({
          title: '分享功能暂不可用',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 获取测试模式文本
   */
  getTestModeText(testMode) {
    const modeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写',
      'elimination': '消消乐',
      'phrase_en2zh': '短语英译汉',
      'phrase_zh2en': '短语汉译英'
    };
    return modeMap[testMode] || '词汇测试';
  },

  /**
   * 分享到企业微信
   */
  shareToEnterpriseWeChat() {
    wx.navigateToMiniProgram({
      appId: 'wx2b03c6e691cd7370',
      path: '',
      extraData: {
        shareId: this.data.shareId,
        shareData: this.data.shareData
      },
      success: () => {
        console.log('跳转企业微信成功');
      },
      fail: (error) => {
        console.error('跳转企业微信失败:', error);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadShareData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 切换错词选择
   */
  toggleMistakeSelection(e) {
    const word = e.currentTarget.dataset.word;
    const selectedMistakes = this.data.selectedMistakes;
    const selectedMistakesMap = this.data.selectedMistakesMap;
    const index = selectedMistakes.indexOf(word);
    
    if (index > -1) {
      selectedMistakes.splice(index, 1);
      delete selectedMistakesMap[word];
    } else {
      selectedMistakes.push(word);
      selectedMistakesMap[word] = true;
    }
    
    this.setData({
      selectedMistakes: selectedMistakes,
      selectedMistakesMap: selectedMistakesMap,
      allMistakesSelected: selectedMistakes.length === this.data.selectedParticipant.mistakes.length
    });
  },

  /**
   * 全选/取消全选错词
   */
  selectAllMistakes() {
    const allMistakesSelected = !this.data.allMistakesSelected;
    const mistakes = this.data.selectedParticipant.mistakes || [];
    
    let selectedMistakes = [];
    let selectedMistakesMap = {};
    
    if (allMistakesSelected) {
      selectedMistakes = mistakes.map(m => m.word);
      mistakes.forEach(m => {
        selectedMistakesMap[m.word] = true;
      });
    }
    
    this.setData({
      allMistakesSelected: allMistakesSelected,
      selectedMistakes: selectedMistakes,
      selectedMistakesMap: selectedMistakesMap
    });
  },

  /**
   * 显示创建测试选项
   */
  showCreateTestOptions() {
    const selectedMistakes = this.data.selectedMistakes;
    const mistakes = this.data.selectedParticipant.mistakes;
    
    // 获取选中的错词详细信息
    const selectedWords = mistakes.filter(m => selectedMistakes.includes(m.word));
    
    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请选择错词',
        icon: 'none'
      });
      return;
    }
    
    wx.showActionSheet({
      itemList: ['英译汉测试', '汉译英测试', '消消乐游戏', '听写测试'],
      success: (res) => {
        let testType = '';
        switch (res.tapIndex) {
          case 0:
            testType = 'en_to_cn';
            break;
          case 1:
            testType = 'cn_to_en';
            break;
          case 2:
            testType = 'elimination';
            break;
          case 3:
            testType = 'dictation';
            break;
        }
        
        if (testType) {
          this.createTestFromMistakes(testType, selectedWords);
        }
      }
    });
  },

  /**
   * 计算关卡数据
   */
  calculateLevelsData(totalLevels, participants) {
    const levelsData = [];

    for (let i = 0; i < totalLevels; i++) {
      const levelIndex = i + 1;

      // 计算完成这一关的参与者
      const completedParticipants = participants.filter(p =>
        p.completedLevels && p.completedLevels.includes(levelIndex)
      );

      // 计算这一关的正确率和错词数量
      let totalAccuracy = 0;
      let totalMistakes = 0;
      let validAccuracyCount = 0;

      participants.forEach(p => {
        // 查找该参与者在这一关的测试结果
        const levelResults = p.results ? p.results.filter(r =>
          (r.levelId === levelIndex || r.level === levelIndex || r.levelNumber === levelIndex || r.groupIndex === levelIndex)
        ) : [];

        levelResults.forEach(result => {
          if (result.accuracy !== undefined && result.accuracy !== null) {
            totalAccuracy += result.accuracy;
            validAccuracyCount++;
          } else if (result.correctCount !== undefined && result.totalCount !== undefined && result.totalCount > 0) {
            const accuracy = Math.round((result.correctCount / result.totalCount) * 100);
            totalAccuracy += accuracy;
            validAccuracyCount++;
          }

          if (result.mistakes && result.mistakes.length > 0) {
            totalMistakes += result.mistakes.length;
          }
        });
      });

      const averageAccuracy = validAccuracyCount > 0 ? Math.round(totalAccuracy / validAccuracyCount) : 0;

      // 计算平均分数（消消乐模式使用）
      let totalScore = 0;
      let validScoreCount = 0;
      completedParticipants.forEach(p => {
        const score = p.levelScores ? (p.levelScores[levelIndex] || 0) : 0;
        if (score > 0) {
          totalScore += score;
          validScoreCount++;
        }
      });
      const averageScore = validScoreCount > 0 ? Math.round(totalScore / validScoreCount) : 0;

      // 准备这一关的所有参与者列表（包括未完成的）
      const levelParticipants = participants.map(p => {
        const isCompleted = p.completedLevels && p.completedLevels.includes(levelIndex);
        const score = p.levelScores ? (p.levelScores[levelIndex] || 0) : 0;
        const hasAttempted = p.levelScores && p.levelScores[levelIndex] !== undefined;

        return {
          openid: p.openid,
          nickName: p.nickName,
          avatar: p.avatar,
          score: score,
          isCompleted: isCompleted,
          hasAttempted: hasAttempted,
          status: isCompleted ? '已完成' : (hasAttempted ? '未完成' : '未开始')
        };
      }).sort((a, b) => {
        // 排序：已完成的在前，然后按分数排序
        if (a.isCompleted && !b.isCompleted) return -1;
        if (!a.isCompleted && b.isCompleted) return 1;
        if (a.isCompleted && b.isCompleted) return b.score - a.score;
        if (a.hasAttempted && !b.hasAttempted) return -1;
        if (!a.hasAttempted && b.hasAttempted) return 1;
        return b.score - a.score;
      });

      levelsData.push({
        levelIndex: levelIndex,
        completedCount: completedParticipants.length,
        averageAccuracy: averageAccuracy,
        averageScore: averageScore, // 消消乐模式使用
        totalMistakes: totalMistakes,
        participants: levelParticipants,
        totalParticipants: participants.length
      });
    }

    return levelsData;
  },

  /**
   * 从错词创建测试
   */
  createTestFromMistakes(testType, selectedWords) {
    // 准备词汇数据
    const wordsData = selectedWords.map(mistake => ({
      word: mistake.word,
      translation: mistake.translation || mistake.correct,
      phonetic: mistake.phonetic || '',
      example: mistake.example || '',
      exampleTranslation: mistake.exampleTranslation || ''
    }));
    
    // 保存到全局数据
    const app = getApp();
    app.globalData.selectedWordsForTest = wordsData;
    app.globalData.learningData = {
      words: wordsData,
      libraryId: 'mistake_review',
      libraryName: '错词复习'
    };
    
    // 根据测试类型跳转到对应页面
    let url = '';
    if (testType === 'en_to_cn' || testType === 'cn_to_en') {
      url = `/pages/wordtest/mode-select/mode-select?testMode=${testType}&libraryId=mistake_review`;
    } else if (testType === 'elimination') {
      url = `/pages/wordtest/mode-select/mode-select?testMode=elimination&libraryId=mistake_review`;
    } else if (testType === 'dictation') {
      url = `/pages/spelling/mode-select/mode-select?libraryId=mistake_review`;
    }
    
    if (url) {
      wx.navigateTo({
        url: url,
        success: () => {
          // 关闭详情弹窗
          this.setData({
            showDetail: false,
            selectedMistakes: [],
            allMistakesSelected: false
          });
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'error'
          });
        }
      });
    }
  },

  /**
   * 全选/取消全选多组错词
   */
  selectAllGroupMistakes() {
    const allGroupMistakesSelected = !this.data.allGroupMistakesSelected;
    const mistakes = this.data.selectedParticipant.groupProgress?.allMistakes || [];

    let selectedGroupMistakes = [];
    let selectedGroupMistakesMap = {};

    if (allGroupMistakesSelected) {
      selectedGroupMistakes = mistakes.map(m => m.word);
      mistakes.forEach(m => {
        selectedGroupMistakesMap[m.word] = true;
      });
    }

    this.setData({
      selectedGroupMistakes,
      selectedGroupMistakesMap,
      allGroupMistakesSelected
    });
  },

  /**
   * 切换多组错词选择
   */
  toggleGroupMistakeSelection(e) {
    const word = e.currentTarget.dataset.word;
    const selectedGroupMistakes = this.data.selectedGroupMistakes;
    const selectedGroupMistakesMap = this.data.selectedGroupMistakesMap;
    const index = selectedGroupMistakes.indexOf(word);

    if (index > -1) {
      selectedGroupMistakes.splice(index, 1);
      selectedGroupMistakesMap[word] = false;
    } else {
      selectedGroupMistakes.push(word);
      selectedGroupMistakesMap[word] = true;
    }

    this.setData({
      selectedGroupMistakes,
      selectedGroupMistakesMap,
      allGroupMistakesSelected: false
    });
  },

  /**
   * 显示多组错词测试选项
   */
  showCreateGroupTestOptions() {
    const selectedGroupMistakes = this.data.selectedGroupMistakes;
    const mistakes = this.data.selectedParticipant.groupProgress?.allMistakes || [];

    // 获取选中的错词详细信息
    const selectedWords = mistakes.filter(m => selectedGroupMistakes.includes(m.word));

    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请选择错词',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: ['英译汉测试', '汉译英测试', '消消乐游戏', '听写测试'],
      success: (res) => {
        let testType = '';
        switch (res.tapIndex) {
          case 0:
            testType = 'en_to_cn';
            break;
          case 1:
            testType = 'cn_to_en';
            break;
          case 2:
            testType = 'elimination';
            break;
          case 3:
            testType = 'dictation';
            break;
        }

        if (testType) {
          this.createTestFromGroupMistakes(testType, selectedWords);
        }
      }
    });
  },

  /**
   * 从多组错词创建测试
   */
  createTestFromGroupMistakes(testType, selectedWords) {
    // 准备词汇数据
    const wordsData = selectedWords.map(mistake => ({
      word: mistake.word,
      translation: mistake.translation || mistake.correct,
      phonetic: mistake.phonetic || '',
      example: mistake.example || '',
      exampleTranslation: mistake.exampleTranslation || ''
    }));

    // 保存到全局数据
    const app = getApp();
    app.globalData.selectedWordsForTest = wordsData;
    app.globalData.learningData = {
      words: wordsData,
      libraryId: 'group_mistake_review',
      libraryName: '多组错词复习'
    };

    // 根据测试类型跳转到对应页面
    let url = '';
    if (testType === 'en_to_cn' || testType === 'cn_to_en') {
      url = `/pages/wordtest/mode-select/mode-select?testMode=${testType}&libraryId=group_mistake_review`;
    } else if (testType === 'elimination') {
      url = `/pages/wordtest/mode-select/mode-select?testMode=elimination&libraryId=group_mistake_review`;
    } else if (testType === 'dictation') {
      url = `/pages/spelling/mode-select/mode-select?libraryId=group_mistake_review`;
    }

    if (url) {
      wx.navigateTo({
        url: url,
        success: () => {
          // 关闭详情弹窗
          this.setData({
            showDetail: false,
            selectedGroupMistakes: [],
            selectedGroupMistakesMap: {},
            allGroupMistakesSelected: false
          });
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'error'
          });
        }
      });
    }
  },

  /**
   * 分享给好友
   */
  onShareAppMessage() {
    const { shareInfo, shareData } = this.data;

    if (shareInfo) {
      return shareInfo;
    }

    // 默认分享信息
    if (shareData) {
      return {
        title: `${shareData.libraryName || '词汇测试'} - ${this.getTestModeText(shareData.testType || shareData.testMode)}`,
        path: `/pages/wordtest/test/test?shareId=${shareData.shareId}&shareMode=share&testMode=${shareData.testType || shareData.testMode}`,
        imageUrl: '/images/share-cover.png'
      };
    }

    return {
      title: '词汇测试分享',
      path: '/pages/index/index'
    };
  }
});