<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 顶部装饰 -->
  <view class="login-header">
    <view class="logo-section">
      <image class="app-logo" src="/assets/icons/logo.png" mode="aspectFit"></image>
      <text class="app-name">墨词自习室</text>
      <text class="app-slogan">师生互动学习空间</text>
    </view>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <!-- 账号密码登录 -->
    <view class="form-section">
      <view class="input-group">
        <view class="input-wrapper">
          <view class="input-icon">👤</view>
          <input 
            class="form-input" 
            type="text" 
            placeholder="请输入用户名" 
            value="{{username}}"
            bindinput="onUsernameInput"
            maxlength="20"
          />
        </view>
      </view>

      <view class="input-group">
        <view class="input-wrapper">
          <view class="input-icon">🔒</view>
          <input 
            class="form-input" 
            type="{{showPassword ? 'text' : 'password'}}" 
            placeholder="请输入密码" 
            value="{{password}}"
            bindinput="onPasswordInput"
            maxlength="20"
          />
          <view class="eye-icon" bindtap="togglePasswordVisibility">
            <text class="eye-text">{{showPassword ? '🙈' : '👁️'}}</text>
          </view>
        </view>
      </view>

      <view class="forgot-password">
        <text class="forgot-link">忘记密码？</text>
      </view>
    </view>
  </view>

  <!-- 登录按钮 -->
  <view class="login-actions">
    <button 
      class="login-button" 
      bindtap="handleLogin"
    >
      账号登录
    </button>

    <!-- 微信一键登录按钮 -->
    <button 
      class="wechat-login-button" 
      bindtap="handleWechatLogin"
    >
      <view class="wechat-icon">🟢</view>
      <text class="wechat-text">微信一键登录</text>
    </button>
  </view>

  <!-- 注册提示 -->
  <view class="register-section">
    <text class="register-text">还没有账号？</text>
    <text class="register-link" bindtap="handleRegister">立即注册</text>
  </view>

  <!-- 协议条款 -->
  <view class="agreement-section">
    <view class="agreement-checkbox">
      <checkbox-group bindchange="onAgreeTermsChange">
        <checkbox
          class="checkbox"
          value="agree"
          checked="{{agreeTerms}}"
          color="#667eea"
        />
      </checkbox-group>
      <text class="agreement-text">我已阅读并同意</text>
      <text class="agreement-link" bindtap="showAgreement">《用户协议》</text>
      <text class="agreement-text">和</text>
      <text class="agreement-link" bindtap="showPrivacy">《隐私政策》</text>
    </view>
  </view>
</view> 