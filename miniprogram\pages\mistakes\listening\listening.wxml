<view class="container">
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-item {{filterType === 'time' ? 'active' : ''}}" bindtap="onFilterTap" data-type="time">
      <text>添加时间</text>
    </view>
    <view class="filter-item {{filterType === 'source' ? 'active' : ''}}" bindtap="onFilterTap" data-type="source">
      <text>来源</text>
    </view>
  </view>

  <!-- 操作按钮栏 -->
  <view class="action-bar">
    <!-- 开始练习按钮 -->
    <view class="practice-btn" bindtap="onPracticeTap">
      开始练习
    </view>
    
    <!-- 管理按钮 -->
    <view class="manage-actions">
      <button class="action-btn secondary" bindtap="showAddMistakeDialog">
        <text class="btn-icon">+</text>
        <text>添加错题</text>
      </button>
      <button class="action-btn {{editMode ? 'primary' : 'secondary'}}" bindtap="toggleEditMode">
        <text class="btn-icon">{{editMode ? '✓' : '✏️'}}</text>
        <text>{{editMode ? '完成' : '管理'}}</text>
      </button>
    </view>
  </view>

  <!-- 批量操作栏 -->
  <view class="batch-actions" wx:if="{{showBatchActions}}">
    <view class="batch-info">
      <text>已选择 {{selectedMistakes.length}} 个错题</text>
    </view>
    <view class="batch-buttons">
      <button class="batch-btn" bindtap="toggleSelectAll">
        {{selectedMistakes.length === mistakes.length ? '取消全选' : '全选'}}
      </button>
      <button class="batch-btn primary" bindtap="onReviewSelectedTap" wx:if="{{selectedMistakes.length > 0}}">
        复习选中
      </button>
      <button class="batch-btn danger" bindtap="onBatchDelete">
        删除选中
      </button>
    </view>
  </view>

  <!-- 错题列表 -->
  <view class="mistake-list">
    <view 
      class="mistake-item {{editMode ? 'edit-mode' : ''}} {{item.selected ? 'selected' : ''}}" 
      wx:for="{{mistakes}}" 
      wx:key="_id"
      bindtap="{{editMode ? 'onMistakeSelect' : ''}}"
      data-index="{{index}}"
    >
      <!-- 选择框 -->
      <view class="mistake-checkbox" wx:if="{{editMode}}">
        <text class="checkbox-icon">{{item.selected ? '☑️' : '☐'}}</text>
      </view>
      
      <!-- 错题信息 -->
      <view class="mistake-info">
        <view class="mistake-word">{{item.word}}</view>
        <view class="mistake-phonetic" wx:if="{{item.phonetic}}">[{{item.phonetic}}]</view>
        <view class="mistake-meaning">{{item.meaning}}</view>
        <view class="mistake-example" wx:if="{{item.example}}">例句：{{item.example}}</view>
        <view class="mistake-meta">
          <view class="mistake-source">来源：{{item.source || item.mistakeType || '听力练习'}}</view>
          <view class="mistake-time">添加时间：{{item.createTime}}</view>
        </view>
      </view>
      
      <!-- 单个操作按钮 -->
      <view class="mistake-actions" wx:if="{{!editMode}}">
        <view class="action-btn small" bindtap="onMasteredTap" data-id="{{item._id}}">
          已掌握
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{mistakes.length === 0 && !loading}}">
    <view class="empty-icon">🎧</view>
    <text class="empty-text">暂无听力错题</text>
    <text class="empty-desc">错题会在听力测试模式下自动收集</text>
    <button class="empty-action" bindtap="showAddMistakeDialog">手动添加错题</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 添加错题对话框 -->
  <view class="modal-overlay" wx:if="{{showAddDialog}}" bindtap="hideAddMistakeDialog">
    <view class="add-mistake-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">添加听力错题</text>
        <text class="modal-close" bindtap="hideAddMistakeDialog">×</text>
      </view>
      
      <view class="modal-body">
        <view class="form-group">
          <label class="form-label">单词 *</label>
          <input 
            class="form-input" 
            placeholder="请输入英文单词"
            value="{{newMistakeForm.word}}"
            data-field="word"
            bindinput="onFormInput"
          />
        </view>
        
        <view class="form-group">
          <label class="form-label">中文含义 *</label>
          <input 
            class="form-input" 
            placeholder="请输入中文含义"
            value="{{newMistakeForm.meaning}}"
            data-field="meaning"
            bindinput="onFormInput"
          />
        </view>
        
        <view class="form-group">
          <label class="form-label">音标</label>
          <input 
            class="form-input" 
            placeholder="请输入音标（可选）"
            value="{{newMistakeForm.phonetic}}"
            data-field="phonetic"
            bindinput="onFormInput"
          />
        </view>
        
        <view class="form-group">
          <label class="form-label">音频链接</label>
          <input 
            class="form-input" 
            placeholder="请输入音频URL（可选）"
            value="{{newMistakeForm.audioUrl}}"
            data-field="audioUrl"
            bindinput="onFormInput"
          />
        </view>
        
        <view class="form-group">
          <label class="form-label">例句</label>
          <textarea 
            class="form-textarea" 
            placeholder="请输入例句（可选）"
            value="{{newMistakeForm.example}}"
            data-field="example"
            bindinput="onFormInput"
          ></textarea>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="hideAddMistakeDialog">取消</button>
        <button class="modal-btn confirm" bindtap="onAddMistake">添加</button>
      </view>
    </view>
  </view>
</view> 