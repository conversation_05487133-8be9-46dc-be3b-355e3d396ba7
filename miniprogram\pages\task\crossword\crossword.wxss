/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  box-sizing: border-box;
}

/* 游戏标题栏 */
.game-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 24rpx 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.game-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.difficulty, .progress {
  font-size: 24rpx;
  color: #666;
}

.progress {
  font-weight: 600;
  color: #4A90E2;
}

/* 游戏网格 */
.crossword-grid {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(7, 1fr);
  gap: 2rpx;
  aspect-ratio: 1;
  max-width: 500rpx;
  margin: 0 auto;
}

.grid-cell {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8rpx;
  overflow: hidden;
}

.grid-cell.white {
  background: #fff;
  border: 2rpx solid #e0e0e0;
}

.grid-cell.black {
  background: #333;
}

.cell-number {
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  font-size: 20rpx;
  color: #666;
  z-index: 2;
  line-height: 1;
  font-weight: 600;
}

.cell-input {
  width: 100%;
  height: 100%;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  background: transparent;
  border: none;
  text-transform: uppercase;
}

.cell-input.selected {
  background: rgba(74, 144, 226, 0.2);
  color: #4A90E2;
}

.cell-input[disabled] {
  color: #4A90E2;
  background: rgba(74, 144, 226, 0.1);
}

/* 线索区域 */
.clues-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.clues-tabs {
  display: flex;
  background: #f8f9fa;
}

.tab-item {
  flex: 1;
  padding: 24rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #666;
  transition: all 0.3s ease;
  position: relative;
}

.tab-item.active {
  color: #4A90E2;
  background: white;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: #4A90E2;
  border-radius: 2rpx 2rpx 0 0;
}

.clues-list {
  max-height: 400rpx;
  padding: 20rpx;
}

.clue-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 12rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.clue-item.selected {
  background: rgba(74, 144, 226, 0.1);
  border-color: #4A90E2;
}

.clue-item.completed {
  background: rgba(76, 175, 80, 0.1);
  border-color: #4CAF50;
}

.clue-number {
  min-width: 60rpx;
  height: 60rpx;
  background: #4A90E2;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.clue-item.completed .clue-number {
  background: #4CAF50;
}

.clue-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.clue-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.clue-length {
  font-size: 22rpx;
  color: #999;
  font-style: italic;
}

.clue-status {
  width: 40rpx;
  text-align: center;
}

.check-icon {
  font-size: 32rpx;
  color: #4CAF50;
  font-weight: bold;
}

/* 游戏控制按钮 */
.game-controls {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.control-btn {
  padding: 20rpx 16rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.control-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.hint-btn {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
}

.check-btn {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  color: white;
}

.clear-btn {
  background: linear-gradient(135deg, #F44336, #D32F2F);
  color: white;
}

.solve-btn {
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
  color: white;
}

/* 成功弹窗 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.success-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx 40rpx;
  margin: 40rpx;
  text-align: center;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.success-modal.show .modal-content {
  transform: scale(1);
}

.success-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20rpx); }
  60% { transform: translateY(-10rpx); }
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.success-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 32rpx;
  line-height: 1.4;
}

.success-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.success-stats text {
  font-size: 24rpx;
  color: #4A90E2;
  font-weight: 600;
}

.modal-buttons {
  display: flex;
  gap: 20rpx;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.modal-btn.primary {
  background: #4A90E2;
  color: white;
}

.modal-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.modal-btn:active {
  transform: scale(0.95);
}

/* 响应式调整 */
@media (max-width: 320px) {
  .grid-container {
    max-width: 400rpx;
  }
  
  .clue-text {
    font-size: 26rpx;
  }
  
  .control-btn {
    font-size: 22rpx;
    padding: 16rpx 12rpx;
  }
} 