/* 模式选择页面样式 */
.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 40rpx 30rpx 120rpx;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 50rpx;
}

.header-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.page-title {
  display: block;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 模式卡片容器 */
.mode-cards {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 40rpx;
}

/* 模式卡片 */
.mode-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mode-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.mode-card.selected {
  background: white;
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

.mode-card.selected::before {
  transform: scaleX(1);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.mode-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.mode-info {
  flex: 1;
}

.mode-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.mode-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.mode-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 8rpx 0;
}

.feature-icon {
  color: #4CAF50;
  font-size: 20rpx;
  margin-right: 8rpx;
  font-weight: bold;
}

.feature-text {
  font-size: 20rpx;
  color: #555;
}

/* 测试配置区域 */
.test-config {
  margin-bottom: 40rpx;
}

.config-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  margin-bottom: 24rpx;
}

.title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.subtitle {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 时间选项 */
.time-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.time-option {
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.time-option.selected {
  background: #e3f2fd;
  border-color: #667eea;
  transform: scale(1.02);
}

.option-label {
  display: block;
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.option-desc {
  display: block;
  font-size: 20rpx;
  color: #666;
}

/* 分享选项 */
.share-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.share-option {
  background: rgba(255, 255, 255, 0.95);
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  padding: 18rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.share-option.selected {
  background: #e3f2fd;
  border-color: #2196f3;
  box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.2);
}

.option-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  color: #2196f3;
}

.option-info {
  flex: 1;
}

.option-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.option-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  line-height: 1.3;
}

/* 提示内容样式 */
.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
}

.action-btn::after {
  border: none;
}

.action-btn.secondary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2rpx solid rgba(102, 126, 234, 0.3);
}

.action-btn.secondary:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.98);
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
}

.action-btn.primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.action-btn.disabled {
  background: #f5f5f5;
  color: #ccc;
  box-shadow: none;
}

.action-btn.disabled:active {
  transform: none;
}



/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mode-cards, .test-config {
  animation: fadeInUp 0.6s ease-out;
}

.mode-cards {
  animation-delay: 0.1s;
}

.test-config {
  animation-delay: 0.2s;
}

/* 分享弹窗样式 */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.share-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: calc(100% - 80rpx);
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.share-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-info {
  margin-bottom: 40rpx;
}

.share-desc {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.share-note {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.share-desc {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.share-note {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.share-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 32rpx;
  border: none;
  gap: 15rpx;
  transition: all 0.2s ease;
}

.copy-btn {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  border: 2rpx solid #dee2e6;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.copy-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.wechat-share-btn {
  background: linear-gradient(135deg, #07c160 0%, #00ae42 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(7, 193, 96, 0.3);
}

.wechat-share-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.4);
}

.timeline-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 107, 0.3);
}

.timeline-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.4);
}

.enterprise-wechat-btn {
  background: linear-gradient(135deg, #576b95 0%, #4a5a7a 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(87, 107, 149, 0.3);
}

.enterprise-wechat-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(87, 107, 149, 0.4);
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
  border: 2rpx solid #ddd;
}

.share-btn-icon {
  font-size: 32rpx;
}

.share-btn-text {
  font-size: 28rpx;
  color: inherit;
}