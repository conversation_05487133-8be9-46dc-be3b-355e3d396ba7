/* 页面容器 */
.container {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #fff;
}

/* 顶部标题 */
.header {
  margin-bottom: 60rpx;
  text-align: center;
}

.title {
  font-size: 40rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}

.date {
  font-size: 24rpx;
  color: #999;
}

/* 政策内容 */
.content {
  padding-bottom: 40rpx;
}

/* 章节 */
.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 段落 */
.paragraph {
  display: block;
  margin-bottom: 20rpx;
}

/* 列表 */
.list {
  margin: 20rpx 0;
  padding-left: 20rpx;
}

.list-item {
  display: block;
  margin-bottom: 10rpx;
} 