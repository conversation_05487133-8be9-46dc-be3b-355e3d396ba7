const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const { studentId, scoreId } = event;

  try {
    // 验证必填字段
    if (!studentId || !scoreId) {
      return {
        success: false,
        message: '学生ID和成绩ID不能为空'
      };
    }

    // 验证学生是否属于当前教师
    const studentResult = await db.collection('students').doc(studentId).get();
    
    if (!studentResult.data) {
      return {
        success: false,
        message: '学生不存在'
      };
    }

    if (studentResult.data.teacherOpenid !== openid) {
      return {
        success: false,
        message: '无权限删除该学生成绩'
      };
    }

    // 验证成绩记录是否存在
    const scoreResult = await db.collection('student_scores').doc(scoreId).get();
    
    if (!scoreResult.data) {
      return {
        success: false,
        message: '成绩记录不存在'
      };
    }

    if (scoreResult.data.studentId !== studentId) {
      return {
        success: false,
        message: '成绩记录与学生不匹配'
      };
    }

    // 删除成绩记录
    await db.collection('student_scores').doc(scoreId).remove();

    return {
      success: true,
      message: '成绩删除成功'
    };
  } catch (error) {
    console.error('删除学生成绩失败:', error);
    return {
      success: false,
      message: '删除学生成绩失败',
      error: error.message
    };
  }
};
