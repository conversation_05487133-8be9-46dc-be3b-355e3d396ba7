const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 管理员账号识别配置
const ADMIN_OPENID_PART = '5938DE76' // 微信登录账号ID部分
const ADMIN_PHONE = '15547663399' // 用户名密码登录账号

// 检查是否为管理员
function isAdmin(userOpenId, userInfo = null) {
  // 检查openid（微信登录）
  if (userOpenId && userOpenId.includes(ADMIN_OPENID_PART)) {
    return true
  }

  // 如果有额外的用户信息，检查手机号等字段
  if (userInfo) {
    if (userInfo.phone === ADMIN_PHONE || userInfo.username === ADMIN_PHONE) {
      return true
    }

    // 检查用户ID是否包含管理员标识（适用于微信登录用户）
    if (userInfo._id && userInfo._id.includes(ADMIN_OPENID_PART)) {
      return true
    }
  }

  return false
}

// 获取用户信息用于管理员验证
async function getUserInfoForAdmin(openId) {
  try {
    // 查询用户信息
    const userResult = await db.collection('users').where({ openid: openId }).get()
    if (userResult.data && userResult.data.length > 0) {
      return userResult.data[0]
    }
    return null
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

exports.main = async (event, context) => {
  try {
    const { mode, sortBy = 'latest' } = event
    

    
    // 如果是获取分组竞赛详情模式
    if (mode === 'getGroupedDetail') {
      return await getGroupedCompetitionDetail(event)
    }
    
    // 如果是切换置顶模式
    if (mode === 'togglePin') {
      return await toggleCompetitionPin(event)
    }

    
    // 构建查询条件 - 包含创建中和活跃状态的竞赛
    console.log('查询竞赛列表，模式:', mode)
    
    // 模式兼容性处理：新旧模式并存查询
    const getAllModes = (mode) => {
      const modeMap = {
        'en2zh': ['en2zh', 'en_to_cn'],      // 新模式兼容旧模式
        'zh2en': ['zh2en', 'cn_to_en'],      // 新模式兼容旧模式
        'dictation': ['dictation'],           // 模式未变
        'elimination': ['elimination']        // 模式未变
      }
      return modeMap[mode] || [mode]
    }
    
    const supportedModes = getAllModes(mode)
    console.log('兼容模式查询:', { 
      requestMode: mode, 
      supportedModes: supportedModes 
    })

    let query = db.collection('competitions').where({
      status: db.command.in(['active', 'creating']),
      mode: db.command.in(supportedModes),  // 使用兼容模式列表查询
      type: db.command.neq('level')  // 直接在数据库查询时排除子关卡
    })

    // 额外日志记录，便于调试
    console.log('查询条件:', {
      status: 'active或creating',
      mode: supportedModes  // 显示实际的兼容模式列表
    })

    // 根据排序方式确定排序字段
    let orderBy = 'createTime'
    let order = 'desc'
    
    if (sortBy === 'participants') {
      orderBy = 'participants'
      order = 'desc'
    } else if (sortBy === 'wordCount') {
      orderBy = 'words'
      order = 'desc'
    }

    // 执行查询（如果集合不存在，返回空数据）
    let result
    try {
      result = await query.orderBy(orderBy, order).limit(50).get()
      console.log('查询结果数量:', result.data.length)
      console.log('兼容模式查询成功，找到竞赛:', result.data.length, '个')
    } catch (error) {
      if (error.errCode === -502005) {
        // 集合不存在，返回空数据
        return {
          success: true,
          data: []
        }
      }
      throw error
    }
    
    // 格式化数据，只返回单一竞赛和主竞赛（不返回子关卡）
    console.log('开始过滤子关卡，原始数量:', result.data.length)
    
    const competitions = result.data
      .filter(comp => {
        // 明确过滤掉子关卡：只有type明确为'level'的才过滤
        const isLevel = comp.type === 'level';
        
        // 保留所有非level类型的竞赛
        const shouldKeep = !isLevel;
        
        // 添加详细的过滤调试
        if (!shouldKeep) {
          console.log('过滤掉子关卡:', {
            id: comp._id,
            name: comp.name,
            type: comp.type,
            mode: comp.mode,
            reason: 'type是level'
          })
        } else {
          console.log('保留竞赛:', {
            id: comp._id,
            name: comp.name,
            type: comp.type || '未设置',
            mode: comp.mode
          })
        }
        
        return shouldKeep;
      })
      .map(async (comp) => {
        const baseData = {
          id: comp._id,
          name: comp.name,
          mode: comp.mode,
          participants: comp.participants || 0,
          creatorName: comp.creatorName || '匿名用户',
          creatorOpenId: comp.creatorOpenId,
          libraryName: comp.libraryName || '自定义词汇',
          createTime: formatTime(comp.createTime),
          gameMode: comp.gameMode,
          timeLimit: comp.timeLimit,
          dictationSettings: comp.dictationSettings,
          type: comp.type || 'single', // 竞赛类型：single-单一竞赛，master-分组竞赛主体
          pinned: comp.pinned || false, // 是否置顶
          status: comp.status, // 竞赛状态
          levelCreationStatus: comp.levelCreationStatus, // 子关卡创建状态
          levelCreationProgress: comp.levelCreationProgress || 100 // 子关卡创建进度
        }

        // 根据竞赛类型设置不同的数据
        if (comp.type === 'master') {
          // 分组竞赛：需要计算所有子关卡的统计数据
          const masterStats = await calculateMasterCompetitionStats(comp._id);
          baseData.wordCount = comp.totalWords || 0
          baseData.totalLevels = comp.totalLevels || 0
          baseData.maxWordsPerLevel = comp.maxWordsPerLevel || 0
          baseData.participants = masterStats.uniqueParticipants // 使用计算出的唯一参与者数量
          baseData.topScore = masterStats.topScore // 使用计算出的最高分
        } else {
          // 单一竞赛：显示实际词汇数
          // 支持索引存储策略：优先使用wordsCount字段，其次使用words数组长度
          if (comp.wordsCount && comp.wordsCount > 0) {
            baseData.wordCount = comp.wordsCount; // 索引存储策略的词汇数量
          } else {
            baseData.wordCount = comp.words ? comp.words.length : 0; // 直接存储策略的词汇数量
          }
          baseData.topScore = getTopScore(comp.results || [])
        }

        return baseData
      })

    // 等待所有异步操作完成
    const competitionsResolved = await Promise.all(competitions);

    // 根据词汇数量排序时，需要在内存中重新排序
    if (sortBy === 'wordCount') {
      competitionsResolved.sort((a, b) => b.wordCount - a.wordCount)
    }

    console.log('过滤后最终返回竞赛数量:', competitionsResolved.length)

    return {
      success: true,
      data: competitionsResolved
    }
    
  } catch (error) {
    console.error('获取竞赛列表失败:', error)
    return {
      success: false,
      message: '获取竞赛列表失败: ' + error.message,
      data: []
    }
  }
}

// 格式化时间
function formatTime(timestamp) {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

// 获取最高分
function getTopScore(results) {
  if (!results || results.length === 0) {
    return null
  }
  
  return Math.max(...results.map(r => r.score || 0))
}



// 获取分组竞赛详情函数
async function getGroupedCompetitionDetail(event) {
  try {
    const { masterCompetitionId } = event
    
    if (!masterCompetitionId) {
      return {
        success: false,
        message: '缺少主竞赛ID'
      }
    }

    // 获取主竞赛信息
    let masterResult
    try {
      masterResult = await db.collection('competitions').doc(masterCompetitionId).get()
    } catch (error) {
      if (error.errCode === -502005) {
        return {
          success: false,
          message: '竞赛不存在'
        }
      }
      throw error
    }
    
    if (!masterResult.data) {
      return {
        success: false,
        message: '竞赛不存在'
      }
    }

    const masterCompetition = masterResult.data
    
    // 检查竞赛状态
    if (masterCompetition.status !== 'active') {
      return {
        success: false,
        message: '竞赛已结束或被删除'
      }
    }

    // 检查是否过期
    const now = new Date()
    if (masterCompetition.autoDeleteTime && now > new Date(masterCompetition.autoDeleteTime)) {
      return {
        success: false,
        message: '竞赛已过期'
      }
    }

    // 获取所有子关卡信息（增加limit以支持更多关卡）
    const levelResults = await db.collection('competitions').where({
      masterCompetitionId: masterCompetitionId,
      type: 'level',
      status: 'active'
    }).orderBy('levelNumber', 'asc').limit(1000).get()

    const levels = levelResults.data.map(level => ({
      id: level._id,
      name: level.name,
      levelNumber: level.levelNumber,
      wordCount: level.words ? level.words.length : 0,
      participants: level.participants || 0,
      words: level.words,
      gameMode: level.gameMode,
      timeLimit: level.timeLimit,
      dictationSettings: level.dictationSettings,
      results: level.results || [], // 保留results数组用于计算用户进度
      // 计算该关卡的最高分和完成率
      topScore: getTopScoreForLevel(level.results || []),
      completionRate: getCompletionRateForLevel(level.results || [])
    }))

    // 计算整体统计数据
    const totalParticipants = Math.max(...levels.map(l => l.participants), 0)
    const totalCompletions = levels.reduce((sum, level) => sum + (level.participants || 0), 0)
    const averageScore = calculateAverageScoreForLevels(levels)

    // 获取所有用户的完成进度和分数
    const userProgress = {}
    const userScores = {} // 新增：用户分数记录
    console.log('=== 开始计算用户进度和分数 ===')

    for (const level of levels) {
      console.log(`检查关卡 ${level.levelNumber} (${level.id})，结果数量: ${level.results ? level.results.length : 0}`)

      if (level.results && level.results.length > 0) {
        level.results.forEach(result => {
          const userOpenId = result.openid || result.userOpenId || result.openId
          console.log(`检查用户 ${userOpenId} 在关卡 ${level.levelNumber} 的结果:`, {
            score: result.score,
            accuracy: result.accuracy,
            completed: result.completed,
            correctCount: result.correctCount,
            totalCount: result.totalCount
          })

          if (userOpenId) {
            if (!userProgress[userOpenId]) {
              userProgress[userOpenId] = []
            }
            if (!userScores[userOpenId]) {
              userScores[userOpenId] = {}
            }

            // 保留最高分（刷分逻辑）
            const currentScore = userScores[userOpenId][level.id] || 0
            if ((result.score || 0) > currentScore) {
              userScores[userOpenId][level.id] = result.score || 0
            }

            // 只有真正通过关卡的才算完成（completed为true或正确率≥80%）
            const isPassed = result.completed === true ||
                           (result.accuracy && result.accuracy >= 80) ||
                           (result.correctCount && result.totalCount &&
                            (result.correctCount / result.totalCount) >= 0.8)

            console.log(`用户 ${userOpenId} 关卡 ${level.levelNumber} 是否通过: ${isPassed}`)

            if (isPassed && !userProgress[userOpenId].includes(level.id)) {
              userProgress[userOpenId].push(level.id)
              console.log(`✅ 用户 ${userOpenId} 完成关卡 ${level.levelNumber}`)
            }
          }
        })
      }
    }
    
    console.log('=== 用户进度计算完成 ===')
    console.log('最终用户进度:', userProgress)

    // 返回分组竞赛详情
    return {
      success: true,
      data: {
        id: masterCompetition._id,
        name: masterCompetition.name,
        mode: masterCompetition.mode,
        type: 'master',
        libraryId: masterCompetition.libraryId,
        libraryName: masterCompetition.libraryName,
        creatorName: masterCompetition.creatorName || '匿名用户',
        createTime: masterCompetition.createTime,
        totalLevels: masterCompetition.totalLevels,
        totalWords: masterCompetition.totalWords,
        maxWordsPerLevel: masterCompetition.maxWordsPerLevel,
        totalParticipants: totalParticipants,
        totalCompletions: totalCompletions,
        averageScore: averageScore,
        levels: levels,
        userProgress: userProgress, // 用户进度信息
        userScores: userScores // 新增：用户分数信息
      }
    }
    
  } catch (error) {
    console.error('获取分组竞赛详情失败:', error)
    return {
      success: false,
      message: '获取竞赛详情失败: ' + error.message
    }
  }
}

// 获取关卡最高分
function getTopScoreForLevel(results) {
  if (!results || results.length === 0) return 0
  return Math.max(...results.map(r => r.score || 0))
}

// 获取关卡完成率
function getCompletionRateForLevel(results) {
  if (!results || results.length === 0) return 0
  // 只统计真正通过的用户（completed为true或正确率≥80%）
  const completedCount = results.filter(r => {
    return r.completed === true || 
           (r.accuracy && r.accuracy >= 80) ||
           (r.correctCount && r.totalCount && (r.correctCount / r.totalCount) >= 0.8)
  }).length
  return Math.round((completedCount / results.length) * 100)
}

// 计算关卡平均分
function calculateAverageScoreForLevels(levels) {
  const allScores = []
  levels.forEach(level => {
    if (level.topScore > 0) {
      allScores.push(level.topScore)
    }
  })
  
  if (allScores.length === 0) return 0
  return Math.round(allScores.reduce((sum, score) => sum + score, 0) / allScores.length)
}

// 切换竞赛置顶状态函数
async function toggleCompetitionPin(event) {
  try {
    const { competitionId, pinned } = event
    const wxContext = cloud.getWXContext()
    const currentUserOpenId = wxContext.OPENID

    if (!competitionId) {
      return {
        success: false,
        message: '缺少竞赛ID'
      }
    }

    // 获取用户信息用于管理员验证
    const userInfo = await getUserInfoForAdmin(currentUserOpenId)

    // 检查管理员权限
    const isAdminUser = isAdmin(currentUserOpenId, userInfo)

    console.log('置顶权限检查:', {
      currentUserOpenId,
      isAdminUser,
      userInfo: userInfo ? {
        username: userInfo.username,
        phone: userInfo.phone,
        _id: userInfo._id
      } : null
    })

    if (!isAdminUser) {
      return {
        success: false,
        message: '无权限执行此操作，仅管理员可置顶竞赛'
      }
    }

    // 更新竞赛的置顶状态
    await db.collection('competitions').doc(competitionId).update({
      data: {
        pinned: pinned,
        pinTime: pinned ? new Date() : null // 记录置顶时间
      }
    })

    console.log(`竞赛 ${competitionId} 置顶状态已更新为: ${pinned}`)

    return {
      success: true,
      message: pinned ? '置顶成功' : '取消置顶成功'
    }

  } catch (error) {
    console.error('切换置顶状态失败:', error)
    return {
      success: false,
      message: '操作失败: ' + error.message
    }
  }
}

// 计算多关卡竞赛的统计数据
async function calculateMasterCompetitionStats(masterCompetitionId) {
  try {
    console.log('计算多关卡竞赛统计数据:', masterCompetitionId);
    
    // 获取所有子关卡
    const levelResults = await db.collection('competitions').where({
      masterCompetitionId: masterCompetitionId,
      type: 'level',
      status: 'active'
    }).get();
    
    if (levelResults.data.length === 0) {
      return {
        uniqueParticipants: 0,
        topScore: 0
      };
    }
    
    const userScores = {}; // 存储每个用户的总分
    const uniqueParticipantsSet = new Set();
    
    // 遍历所有子关卡，统计每个用户的总分
    levelResults.data.forEach(level => {
      const results = level.results || [];
      results.forEach(result => {
        const openId = result.openid || result.openId || result.userOpenId;
        if (openId && result.score && result.score > 0) {
          uniqueParticipantsSet.add(openId);
          
          // 检查是否通过了这个关卡（completed为true或正确率≥80%）
          const isPassed = result.completed === true || 
                         (result.accuracy && result.accuracy >= 80) ||
                         (result.correctCount && result.totalCount && 
                          (result.correctCount / result.totalCount) >= 0.8);
          
          if (isPassed) {
            if (!userScores[openId]) {
              userScores[openId] = 0;
            }
            userScores[openId] += result.score; // 累加用户的关卡分数
          }
        }
      });
    });
    
    const uniqueParticipants = uniqueParticipantsSet.size;
    const allUserTotalScores = Object.values(userScores);
    const topScore = allUserTotalScores.length > 0 ? Math.max(...allUserTotalScores) : 0;
    
    console.log('多关卡竞赛统计结果:', {
      masterCompetitionId,
      levelCount: levelResults.data.length,
      uniqueParticipants,
      topScore,
      userScores: Object.keys(userScores).length,
      userTotalScores: allUserTotalScores
    });
    
    return {
      uniqueParticipants,
      topScore
    };
    
  } catch (error) {
    console.error('计算多关卡竞赛统计数据失败:', error);
    return {
      uniqueParticipants: 0,
      topScore: 0
    };
  }
} 