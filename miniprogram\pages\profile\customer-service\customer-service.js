Page({
  data: {
    qrcodePath: '/assets/images/service-qrcode.png'
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '联系客服'
    });
  },

  // 保存二维码到相册
  saveQRCode() {
    // 检查微信版本和环境
    const systemInfo = wx.getSystemInfoSync();
    console.log('保存图片 - 系统信息:', {
      platform: systemInfo.platform,
      version: systemInfo.version,
      SDKVersion: systemInfo.SDKVersion
    });

    // 直接尝试保存，不预先检查权限
    wx.saveImageToPhotosAlbum({
      filePath: this.data.qrcodePath,
      success: () => {
        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('直接保存失败:', err);
        // 如果直接保存失败，尝试权限处理流程
        this.handleSaveFailure(err, this.data.qrcodePath);
      }
    });
  },

  // 处理保存失败的情况
  handleSaveFailure(err, tempFilePath) {
    console.error('保存图片失败详情:', err);

    if (err.errMsg.includes('privacy api banned') || err.errMsg.includes('banned')) {
      // API被禁用，提供替代方案
      this.showPrivacyApiBannedDialog();
    } else if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
      // 权限问题，尝试权限流程
      this.handlePermissionFlow(tempFilePath);
    } else {
      // 其他错误，显示通用错误处理
      this.showFallbackOptions();
    }
  },

  // 显示隐私API被禁用的对话框
  showPrivacyApiBannedDialog() {
    wx.showModal({
      title: '保存功能暂不可用',
      content: '由于微信政策限制，当前版本暂时无法直接保存图片到相册。\n\n建议您：\n1. 截屏保存当前页面\n2. 等待后续版本更新\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 提示用户截屏
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },

  // 处理权限流程
  handlePermissionFlow(tempFilePath) {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启相册权限',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      // 用户开启了权限，重新尝试保存
                      this.retrySaveImage(tempFilePath);
                    }
                  }
                });
              }
            }
          });
        } else {
          // 尝试请求权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.retrySaveImage(tempFilePath);
            },
            fail: () => {
              this.showFallbackOptions();
            }
          });
        }
      },
      fail: () => {
        this.showFallbackOptions();
      }
    });
  },

  // 重新尝试保存图片
  retrySaveImage(tempFilePath) {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: () => {
        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('重试保存失败:', err);
        this.showFallbackOptions();
      }
    });
  },

  // 显示备选方案
  showFallbackOptions() {
    wx.showModal({
      title: '保存失败',
      content: '图片保存失败，您可以：\n\n1. 截屏保存当前页面\n2. 稍后重试\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '稍后重试',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },

  // 图片加载错误
  onImageError(e) {
    console.error('二维码图片加载失败:', e);
    wx.showModal({
      title: '图片加载失败',
      content: '二维码图片无法显示，请联系开发者或通过其他方式联系客服',
      showCancel: false
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '墨词自习室客服联系方式',
      path: '/pages/profile/customer-service/customer-service',
      imageUrl: '/assets/images/service-qrcode.png'
    };
  }
}); 