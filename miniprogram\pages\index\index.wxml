<!-- 专业简洁首页 -->
<view class="luxury-container">
  <!-- 顶部横幅 -->
  <view class="luxury-header" bindtap="goToAbout">
    <view class="header-content">
      <view class="brand-section">
        <view class="brand-logo">
          <image class="logo-image" src="/assets/icons/logo.png" mode="aspectFit"></image>
        </view>
        <view class="brand-info">
          <text class="app-title">墨词自习室</text>
          <text class="app-subtitle">智能学习 · 个性发展 · 共创空间</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能卡片区域 -->
  <view class="luxury-functions">
    <view class="functions-title">
      <text class="title-text">功能探索</text>
      <view class="title-underline"></view>
    </view>
    
    <view class="function-grid">
      <!-- 单词检测 -->
      <view class="luxury-card card-gradient-blue" bindtap="onFunctionTap" data-id="wordtest">
        <view class="card-bg-pattern"></view>
        <view class="card-glow"></view>
        <view class="card-inner">
          <view class="card-icon-container">
            <text class="card-icon">🔍</text>
          </view>
          <view class="card-text">
            <text class="card-title">单词检测</text>
            <text class="card-subtitle">检测词汇掌握情况</text>
          </view>
        </view>
      </view>



      <!-- 单词竞赛 -->
      <view class="luxury-card card-gradient-purple" bindtap="onFunctionTap" data-id="competition">
        <view class="card-bg-pattern"></view>
        <view class="card-glow"></view>
        <view class="card-inner">
          <view class="card-icon-container">
            <text class="card-icon">🏆</text>
          </view>
          <view class="card-text">
            <text class="card-title">单词竞赛</text>
            <text class="card-subtitle">排名竞技模式</text>
          </view>
        </view>
      </view>

      <!-- 写作积累 -->
      <view class="luxury-card card-gradient-red" bindtap="onFunctionTap" data-id="writing">
        <view class="card-bg-pattern"></view>
        <view class="card-glow"></view>
        <view class="card-inner">
          <view class="card-icon-container">
            <text class="card-icon">✍️</text>
          </view>
          <view class="card-text">
            <text class="card-title">写作积累</text>
            <text class="card-subtitle">素材与范文积累</text>
          </view>
        </view>
      </view>



      <!-- 教师工具箱 -->
      <view class="luxury-card card-gradient-orange" bindtap="onFunctionTap" data-id="teacher-tools">
        <view class="card-bg-pattern"></view>
        <view class="card-glow"></view>
        <view class="card-inner">
          <view class="card-icon-container">
            <text class="card-icon">👨‍🏫</text>
          </view>
          <view class="card-text">
            <text class="card-title">教师工具箱</text>
            <text class="card-subtitle">专业教学辅助工具</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分割线 -->
    <view class="section-divider"></view>

    <!-- 快捷方式区域 -->
    <view class="shortcut-section">
      <!-- 内容来源选择 -->
      <view class="source-selector">
        <view class="source-option {{shortcutSettings.source === 'learning' ? 'active' : ''}}"
              bindtap="selectSource" data-source="learning">
          <text class="source-text">自我学习</text>
        </view>
        <view class="source-option {{shortcutSettings.source === 'received' ? 'active' : ''}}"
              bindtap="selectSource" data-source="received">
          <text class="source-text">收到分享</text>
        </view>
        <view class="source-option {{shortcutSettings.source === 'shared' ? 'active' : ''}}"
              bindtap="selectSource" data-source="shared">
          <text class="source-text">我的分享</text>
        </view>
      </view>

      <!-- 任务列表 -->
      <view class="shortcut-tasks">
        <!-- 自我学习任务（照搬学习进度样式）-->
        <view class="learning-progress-card" wx:if="{{shortcutSettings.source === 'learning' && currentLearningTask}}">
          <view class="task-info">
            <view class="task-header">
              <view class="title-row">
                <text class="task-name">{{currentLearningTask.title}}</text>
                <view class="task-type">
                  <text class="mode-text">{{currentLearningTask.testType || '英译汉'}}</text>
                </view>
                <view class="switch-task-btn" bindtap="switchLearningTask">
                  <text class="switch-text">切换</text>
                </view>
              </view>
              <!-- 设为默认显示按钮 -->
              <view class="set-default-btn" bindtap="setCurrentAsDefault" data-type="learning">
                <text class="set-default-text">设为默认显示</text>
              </view>
              <text class="last-study-time">{{currentLearningTask.lastStudyTime || '最近学习：今天'}}</text>
            </view>

            <view class="progress-stats">
              <view class="stat-item">
                <text class="stat-number">{{currentLearningTask.learned || 0}}</text>
                <text class="stat-label">已学</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{currentLearningTask.review || 0}}</text>
                <text class="stat-label">复习</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{currentLearningTask.total || 0}}</text>
                <text class="stat-label">总计</text>
              </view>
            </view>

            <view class="progress-bar-container">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{currentLearningTask.progressPercent || 0}}%"></view>
              </view>
              <text class="progress-text">{{currentLearningTask.progress || '0/0'}}</text>
            </view>
          </view>

          <!-- 操作按钮卡片（照搬学习进度样式）-->
          <view class="action-cards">
            <!-- 继续新学卡片 -->
            <view class="action-card primary" bindtap="continueNewStudy" data-task="{{currentLearningTask}}">
              <view class="card-header">
                <text class="card-title">继续新学</text>
                <text class="card-progress">{{currentLearningTask.newProgress || '0/0'}}</text>
              </view>
              <text class="card-desc">学习新词汇</text>
            </view>

            <!-- 开始复习卡片 -->
            <view class="action-card review {{currentLearningTask.hasReview ? 'active' : 'disabled'}}" bindtap="startReview" data-task="{{currentLearningTask}}">
              <view class="card-header">
                <text class="card-title">开始复习</text>
                <text class="card-progress">{{currentLearningTask.reviewProgress || '0/0'}}</text>
              </view>
              <text class="card-desc">复习背过的词汇</text>
            </view>
          </view>
        </view>

        <!-- 自我学习新用户引导界面 -->
        <view class="learning-progress-card" wx:if="{{shortcutSettings.source === 'learning' && !currentLearningTask}}">
          <view class="task-info">
            <view class="task-header">
              <view class="title-row">
                <text class="task-name">开始你的学习之旅</text>
                <view class="task-type">
                  <text class="mode-text">新用户</text>
                </view>
              </view>
              <!-- 设为默认显示按钮 -->
              <view class="set-default-btn" bindtap="setCurrentAsDefault" data-type="learning">
                <text class="set-default-text">设为默认显示</text>
              </view>
              <text class="last-study-time">欢迎使用墨词自习室</text>
            </view>

            <view class="progress-stats">
              <view class="stat-item">
                <text class="stat-number">0</text>
                <text class="stat-label">已学</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">0</text>
                <text class="stat-label">复习</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">3500</text>
                <text class="stat-label">总计</text>
              </view>
            </view>

            <view class="progress-bar-container">
              <view class="progress-bar">
                <view class="progress-fill" style="width: 0%"></view>
              </view>
              <text class="progress-text">开始学习吧！</text>
            </view>
          </view>

          <!-- 新用户引导按钮 -->
          <view class="action-cards">
            <view class="action-card primary" bindtap="goToWordDetection">
              <view class="card-header">
                <text class="card-title">去学习</text>
                <text class="card-progress">开始</text>
              </view>
              <text class="card-desc">进入单词检测开始学习</text>
            </view>

            <view class="action-card review disabled">
              <view class="card-header">
                <text class="card-title">暂无复习</text>
                <text class="card-progress">0/0</text>
              </view>
              <text class="card-desc">学习后才能复习</text>
            </view>
          </view>
        </view>

        <!-- 收到分享任务（统一使用学习进度样式）-->
        <view class="learning-progress-card" wx:if="{{shortcutSettings.source === 'received' && currentReceivedTask}}">
          <view class="task-info">
            <view class="task-header">
              <view class="title-row">
                <text class="task-name">{{currentReceivedTask.title}}</text>
                <view class="task-type">
                  <text class="mode-text">{{currentReceivedTask.testType || '汉译英'}}</text>
                </view>
                <view class="switch-task-btn" bindtap="switchReceivedTask">
                  <text class="switch-text">切换</text>
                </view>
              </view>
              <!-- 设为默认显示按钮 -->
              <view class="set-default-btn" bindtap="setCurrentAsDefault" data-type="received">
                <text class="set-default-text">设为默认显示</text>
              </view>
              <text class="last-study-time">{{currentReceivedTask.time || '分享时间：今天'}}</text>
            </view>

            <view class="progress-stats">
              <view class="stat-item">
                <text class="stat-number">{{currentReceivedTask.sharer || '未知'}}</text>
                <text class="stat-label">分享人</text>
              </view>
              <!-- 单组任务显示完成状态 -->
              <view wx:if="{{!currentReceivedTask.isMultiLevel}}" class="stat-item">
                <text class="stat-number">{{currentReceivedTask.completionStatus || '未完成'}}</text>
                <text class="stat-label">完成状态</text>
              </view>
              <!-- 多关卡任务显示完成进度 -->
              <view wx:else class="stat-item">
                <text class="stat-number">{{currentReceivedTask.completedLevels || 0}}/{{currentReceivedTask.totalLevels || 0}}</text>
                <text class="stat-label">完成关卡</text>
              </view>
              <!-- 总正确率 -->
              <view class="stat-item">
                <text class="stat-number">{{currentReceivedTask.totalAccuracy || 0}}%</text>
                <text class="stat-label">总正确率</text>
              </view>
            </view>

            <view class="progress-bar-container" wx:if="{{currentReceivedTask.progressPercent > 0}}">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{currentReceivedTask.progressPercent || 0}}%"></view>
              </view>
              <text class="progress-text">{{currentReceivedTask.progress}}</text>
            </view>
          </view>

          <!-- 操作按钮（根据任务类型和状态显示）-->
          <view class="action-cards">
            <!-- 多关卡任务 -->
            <view wx:if="{{currentReceivedTask.isMultiLevel && !currentReceivedTask.isExpired}}" class="action-card primary" bindtap="selectLevelToStart" data-task="{{currentReceivedTask}}">
              <view class="card-header">
                <text class="card-title">选择关卡开始</text>
                <text class="card-progress">多关卡</text>
              </view>
              <text class="card-desc">选择关卡进行测试</text>
            </view>

            <!-- 单组任务 -->
            <view wx:elif="{{!currentReceivedTask.isMultiLevel && !currentReceivedTask.isExpired}}" class="action-card primary" bindtap="startSingleTest" data-task="{{currentReceivedTask}}">
              <view class="card-header">
                <text class="card-title">{{(currentReceivedTask.myInfo && currentReceivedTask.myInfo.testCount > 0) ? '重新测试' : '开始测试'}}</text>
                <text class="card-progress">{{currentReceivedTask.myInfo && currentReceivedTask.myInfo.testCount > 0 ? '已测试' : '未开始'}}</text>
              </view>
              <text class="card-desc">{{(currentReceivedTask.myInfo && currentReceivedTask.myInfo.testCount > 0) ? '再次参与测试' : '参与分享测试'}}</text>
            </view>

            <!-- 已过期任务 -->
            <view wx:else class="action-card disabled">
              <view class="card-header">
                <text class="card-title">已过期</text>
                <text class="card-progress">过期</text>
              </view>
              <text class="card-desc">分享已过期</text>
            </view>

            <!-- 查看详情按钮 -->
            <view class="action-card review" bindtap="viewReceivedDetails" data-task="{{currentReceivedTask}}">
              <view class="card-header">
                <text class="card-title">查看详情</text>
                <text class="card-progress">详情</text>
              </view>
              <text class="card-desc">查看分享信息</text>
            </view>
          </view>
        </view>

        <!-- 收到分享新用户引导界面 -->
        <view class="learning-progress-card" wx:if="{{shortcutSettings.source === 'received' && !currentReceivedTask}}">
          <view class="task-info">
            <view class="task-header">
              <view class="title-row">
                <text class="task-name">暂无收到的分享</text>
                <view class="task-type">
                  <text class="mode-text">空状态</text>
                </view>
              </view>
              <!-- 设为默认显示按钮 -->
              <view class="set-default-btn" bindtap="setCurrentAsDefault" data-type="received">
                <text class="set-default-text">设为默认显示</text>
              </view>
              <text class="last-study-time">等待他人分享测试给你</text>
            </view>

            <view class="progress-stats">
              <view class="stat-item">
                <text class="stat-number">0</text>
                <text class="stat-label">收到分享</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">0</text>
                <text class="stat-label">已参与</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">0</text>
                <text class="stat-label">平均分</text>
              </view>
            </view>

            <view class="progress-bar-container">
              <view class="progress-bar">
                <view class="progress-fill" style="width: 0%"></view>
              </view>
              <text class="progress-text">暂无分享测试</text>
            </view>
          </view>

          <!-- 空状态引导按钮 -->
          <view class="action-cards">
            <view class="action-card review disabled">
              <view class="card-header">
                <text class="card-title">暂无测试</text>
                <text class="card-progress">等待中</text>
              </view>
              <text class="card-desc">等待他人分享测试</text>
            </view>

            <view class="action-card review" bindtap="goToReceivedPage">
              <view class="card-header">
                <text class="card-title">查看全部</text>
                <text class="card-progress">列表</text>
              </view>
              <text class="card-desc">查看收到分享页面</text>
            </view>
          </view>
        </view>

        <!-- 我的分享任务（统一使用学习进度样式）-->
        <view class="learning-progress-card" wx:if="{{shortcutSettings.source === 'shared' && currentSharedTask}}">
          <view class="task-info">
            <view class="task-header">
              <view class="title-row">
                <text class="task-name">{{currentSharedTask.title}}</text>
                <view class="task-type">
                  <text class="mode-text">{{currentSharedTask.testType || '听写模式'}}</text>
                </view>
                <view class="switch-task-btn" bindtap="switchSharedTask">
                  <text class="switch-text">切换</text>
                </view>
              </view>
              <!-- 设为默认显示按钮 -->
              <view class="set-default-btn" bindtap="setCurrentAsDefault" data-type="shared">
                <text class="set-default-text">设为默认显示</text>
              </view>
              <text class="last-study-time">{{currentSharedTask.time || '创建时间：今天'}}</text>
            </view>

            <view class="progress-stats">
              <view class="stat-item">
                <text class="stat-number">{{currentSharedTask.participants || 0}}</text>
                <text class="stat-label">参与人数</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{currentSharedTask.avgAccuracy || 0}}%</text>
                <text class="stat-label">平均正确率</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{currentSharedTask.completions || 0}}</text>
                <text class="stat-label">完成次数</text>
              </view>
            </view>

            <view class="progress-bar-container">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{currentSharedTask.shareProgress || 100}}%"></view>
              </view>
              <text class="progress-text">分享状态：{{currentSharedTask.shareStatus || '已发布'}}</text>
            </view>
          </view>

          <!-- 操作按钮（统一使用学习进度样式）-->
          <view class="action-cards">
            <view class="action-card primary" bindtap="viewMyShareDetails" data-task="{{currentSharedTask}}">
              <view class="card-header">
                <text class="card-title">查看详情</text>
                <text class="card-progress">管理</text>
              </view>
              <text class="card-desc">查看分享数据</text>
            </view>

            <view class="action-card review" bindtap="shareMyTask" data-task="{{currentSharedTask}}">
              <view class="card-header">
                <text class="card-title">继续分享</text>
                <text class="card-progress">分享</text>
              </view>
              <text class="card-desc">分享给更多人</text>
            </view>
          </view>
        </view>

        <!-- 我的分享新用户引导界面 -->
        <view class="learning-progress-card" wx:if="{{shortcutSettings.source === 'shared' && !currentSharedTask}}">
          <view class="task-info">
            <view class="task-header">
              <view class="title-row">
                <text class="task-name">暂无创建的分享</text>
                <view class="task-type">
                  <text class="mode-text">空状态</text>
                </view>
              </view>
              <!-- 设为默认显示按钮 -->
              <view class="set-default-btn" bindtap="setCurrentAsDefault" data-type="shared">
                <text class="set-default-text">设为默认显示</text>
              </view>
              <text class="last-study-time">创建分享测试与他人互动</text>
            </view>

            <view class="progress-stats">
              <view class="stat-item">
                <text class="stat-number">0</text>
                <text class="stat-label">已创建</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">0</text>
                <text class="stat-label">参与人数</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">0</text>
                <text class="stat-label">平均正确率</text>
              </view>
            </view>

            <view class="progress-bar-container">
              <view class="progress-bar">
                <view class="progress-fill" style="width: 0%"></view>
              </view>
              <text class="progress-text">开始创建分享吧！</text>
            </view>
          </view>

          <!-- 空状态引导按钮 -->
          <view class="action-cards">
            <view class="action-card primary" bindtap="goToCreateShare">
              <view class="card-header">
                <text class="card-title">创建分享</text>
                <text class="card-progress">新建</text>
              </view>
              <text class="card-desc">创建测试分享给他人</text>
            </view>

            <view class="action-card review" bindtap="goToSharedPage">
              <view class="card-header">
                <text class="card-title">查看全部</text>
                <text class="card-progress">列表</text>
              </view>
              <text class="card-desc">查看我的分享页面</text>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{shortcutTasks.length === 0}}">
          <text class="empty-icon">📋</text>
          <text class="empty-text">暂无任务</text>
          <text class="empty-hint">选择不同来源查看任务</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷方式设置弹窗 -->
  <view class="settings-modal {{showSettingsModal ? 'show' : ''}}" wx:if="{{showSettingsModal}}">
    <view class="modal-mask" bindtap="hideShortcutSettings"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">快捷方式设置</text>
        <view class="modal-close" bindtap="hideShortcutSettings">×</view>
      </view>

      <view class="modal-body">
        <!-- 筛选条件 -->
        <view class="filter-section">
          <!-- 测试模式选择 -->
          <view class="filter-item">
            <text class="filter-label">测试模式</text>
            <view class="filter-selector" bindtap="showModeSelector">
              <text class="selector-text">{{shortcutSettings.modeText || '全部模式'}}</text>
              <text class="selector-arrow">▼</text>
            </view>
          </view>

          <!-- 词库选择 -->
          <view class="filter-item">
            <text class="filter-label">词库</text>
            <view class="filter-selector" bindtap="showLibrarySelector">
              <text class="selector-text">{{shortcutSettings.libraryText || '全部词库'}}</text>
              <text class="selector-arrow">▼</text>
            </view>
          </view>

          <!-- 分享人选择（仅收到的分享时显示） -->
          <view class="filter-item" wx:if="{{shortcutSettings.source === 'received'}}">
            <text class="filter-label">分享人</text>
            <view class="filter-selector" bindtap="showSharerSelector">
              <text class="selector-text">{{shortcutSettings.sharerText || '全部分享人'}}</text>
              <text class="selector-arrow">▼</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="modal-actions">
          <view class="action-btn secondary" bindtap="resetShortcut">
            <text>重置</text>
          </view>
          <view class="action-btn primary" bindtap="setAsDefault">
            <text>设为默认显示</text>
          </view>
        </view>
      </view>
    </view>
  </view>



  <!-- 模式选择器 -->
  <view class="selector-modal {{showModeModal ? 'show' : ''}}" wx:if="{{showModeModal}}">
    <view class="modal-mask" bindtap="hideModeSelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择测试模式</text>
        <view class="modal-close" bindtap="hideModeSelector">×</view>
      </view>
      <view class="modal-body">
        <view class="mode-option {{!shortcutSettings.mode ? 'selected' : ''}}"
              bindtap="selectMode" data-mode="" data-text="全部模式">
          <text class="option-text">全部模式</text>
          <text class="option-check" wx:if="{{!shortcutSettings.mode}}">✓</text>
        </view>
        <view class="mode-option {{shortcutSettings.mode === 'en_to_cn' ? 'selected' : ''}}"
              bindtap="selectMode" data-mode="en_to_cn" data-text="英译汉">
          <text class="option-text">英译汉</text>
          <text class="option-check" wx:if="{{shortcutSettings.mode === 'en_to_cn'}}">✓</text>
        </view>
        <view class="mode-option {{shortcutSettings.mode === 'cn_to_en' ? 'selected' : ''}}"
              bindtap="selectMode" data-mode="cn_to_en" data-text="汉译英">
          <text class="option-text">汉译英</text>
          <text class="option-check" wx:if="{{shortcutSettings.mode === 'cn_to_en'}}">✓</text>
        </view>
        <view class="mode-option {{shortcutSettings.mode === 'dictation' ? 'selected' : ''}}"
              bindtap="selectMode" data-mode="dictation" data-text="听写模式">
          <text class="option-text">听写模式</text>
          <text class="option-check" wx:if="{{shortcutSettings.mode === 'dictation'}}">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 词库选择器 -->
  <view class="selector-modal {{showLibraryModal ? 'show' : ''}}" wx:if="{{showLibraryModal}}">
    <view class="modal-mask" bindtap="hideLibrarySelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择词库</text>
        <view class="modal-close" bindtap="hideLibrarySelector">×</view>
      </view>
      <view class="modal-body">
        <view class="library-option {{!shortcutSettings.library ? 'selected' : ''}}"
              bindtap="selectLibrary" data-library="" data-text="全部词库">
          <text class="option-text">全部词库</text>
          <text class="option-check" wx:if="{{!shortcutSettings.library}}">✓</text>
        </view>
        <view class="library-option {{shortcutSettings.library === library.id ? 'selected' : ''}}"
              wx:for="{{commonLibraries}}" wx:key="id" wx:for-item="library"
              bindtap="selectLibrary" data-library="{{library.id}}" data-text="{{library.name}}">
          <text class="option-text">{{library.name}}</text>
          <text class="option-check" wx:if="{{shortcutSettings.library === library.id}}">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 分享人选择器 -->
  <view class="selector-modal {{showSharerModal ? 'show' : ''}}" wx:if="{{showSharerModal}}">
    <view class="modal-mask" bindtap="hideSharerSelector"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择分享人</text>
        <view class="modal-close" bindtap="hideSharerSelector">×</view>
      </view>
      <view class="modal-body">
        <view class="sharer-option {{!shortcutSettings.sharer ? 'selected' : ''}}"
              bindtap="selectSharer" data-sharer="" data-text="全部分享人">
          <text class="option-text">全部分享人</text>
          <text class="option-check" wx:if="{{!shortcutSettings.sharer}}">✓</text>
        </view>
        <view class="sharer-option {{shortcutSettings.sharer === sharer.id ? 'selected' : ''}}"
              wx:for="{{availableSharers}}" wx:key="id" wx:for-item="sharer"
              bindtap="selectSharer" data-sharer="{{sharer.id}}" data-text="{{sharer.name}}">
          <text class="option-text">{{sharer.name}}</text>
          <text class="option-check" wx:if="{{shortcutSettings.sharer === sharer.id}}">✓</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 收到分享详情弹窗 -->
<view wx:if="{{showReceivedDetailModal}}" class="modal-overlay" bindtap="closeReceivedDetailModal">
  <view class="detail-modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">测试详情</text>
      <text class="modal-close" bindtap="closeReceivedDetailModal">×</text>
    </view>

    <scroll-view class="modal-body" scroll-y="true">
      <view class="detail-section">
        <view class="detail-row">
          <text class="detail-label">测试名称:</text>
          <text class="detail-value">{{currentReceivedDetail.shareTitle}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">测试类型:</text>
          <text class="detail-value">{{currentReceivedDetail.testModeText}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">测试ID:</text>
          <text class="detail-value">{{currentReceivedDetail.shareId}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">首次参与:</text>
          <text class="detail-value">{{currentReceivedDetail.firstVisitTimeText}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">最近测试:</text>
          <text class="detail-value">{{currentReceivedDetail.lastTestTimeText}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">测试次数:</text>
          <text class="detail-value">{{(currentReceivedDetail.myInfo && currentReceivedDetail.myInfo.testCount) || 0}}次</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">最高分:</text>
          <text class="detail-value">{{(currentReceivedDetail.myInfo && currentReceivedDetail.myInfo.bestScore) || 0}}分</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">平均分:</text>
          <text class="detail-value">{{currentReceivedDetail.averageScore}}分</text>
        </view>

        <!-- 多关卡任务详情 -->
        <view wx:if="{{currentReceivedDetail.isMultiLevel}}" class="level-detail">
          <view class="detail-row">
            <text class="detail-label">总关卡数:</text>
            <text class="detail-value">{{currentReceivedDetail.totalLevels || '未知'}}关</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">当前关卡:</text>
            <text class="detail-value">第{{(currentReceivedDetail.myInfo && currentReceivedDetail.myInfo.progress && currentReceivedDetail.myInfo.progress.currentLevel) || 1}}关</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">完成关卡:</text>
            <text class="detail-value">{{(currentReceivedDetail.myInfo && currentReceivedDetail.myInfo.progress && currentReceivedDetail.myInfo.progress.completedLevels && currentReceivedDetail.myInfo.progress.completedLevels.length) || 0}}关</text>
          </view>

          <!-- 关卡数据详情 -->
          <view class="level-scores">
            <view class="section-title">关卡数据</view>
            <view class="level-score-list">
              <view
                wx:for="{{currentReceivedDetail.levelDataWithAccuracy}}"
                wx:key="levelIndex"
                class="level-score-item"
              >
                <view class="level-header">
                  <text class="level-name">第{{item.levelNumber || item.levelIndex}}关</text>
                  <view class="level-stats">
                    <text class="level-score">{{item.score}}分</text>
                    <!-- 消消乐模式不显示正确率，其他模式只有在有正确率数据时才显示 -->
                    <text wx:if="{{!item.isEliminationMode && item.accuracy !== null}}" class="level-accuracy">正确率{{item.accuracy}}%</text>
                  </view>
                </view>
              </view>

            </view>
          </view>
        </view>

      </view>
    </scroll-view>
  </view>
</view>

