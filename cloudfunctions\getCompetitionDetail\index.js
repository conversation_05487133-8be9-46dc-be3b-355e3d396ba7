const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 使用种子的随机数生成器
function seededRandom(seed) {
  let x = Math.sin(seed) * 10000;
  return x - Math.floor(x);
}

// 使用种子打乱数组
function shuffleArrayWithSeed(array, seed) {
  const shuffled = [...array];
  let currentSeed = seed;

  for (let i = shuffled.length - 1; i > 0; i--) {
    currentSeed = (currentSeed * 9301 + 49297) % 233280; // 线性同余生成器
    const j = Math.floor(seededRandom(currentSeed) * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }

  return shuffled;
}

// 根据词库ID获取对应的集合名称
function getLibraryCollection(libraryId) {
  // 词库ID到集合名称的映射
  const libraryMap = {
    'words_3500': 'words_3500',
    'words_3500_luan': 'words_3500',
    'words_cet4': 'words_cet4',
    'words_cet6': 'words_cet6',
    'words_zhongkao': 'words_zhongkao',
    'words_bjwusan': 'words_bjwusan',
    'phrase_gaopin': 'phrase_gaopin',
    'phrase_zhongkao': 'phrase_zhongkao',
    'phrase_cet4': 'phrase_cet4',
    'phrase_cet6': 'phrase_cet6'
  };

  return libraryMap[libraryId] || 'words_3500'; // 默认使用高考词库
}

exports.main = async (event, context) => {
  try {
    const { competitionId } = event
    
    if (!competitionId) {
      return {
        success: false,
        message: '缺少竞赛ID'
      }
    }

    // 获取竞赛信息
    let result
    try {
      result = await db.collection('competitions').doc(competitionId).get()
    } catch (error) {
      if (error.errCode === -502005) {
        return {
          success: false,
          message: '竞赛不存在'
        }
      }
      throw error
    }
    
    if (!result.data) {
      return {
        success: false,
        message: '竞赛不存在'
      }
    }

    const competition = result.data

    // 检查竞赛状态
    if (competition.status !== 'active') {
      return {
        success: false,
        message: '竞赛已结束或被删除'
      }
    }

    // 检查是否过期
    const now = new Date()
    if (competition.autoDeleteTime && now > new Date(competition.autoDeleteTime)) {
      return {
        success: false,
        message: '竞赛已过期'
      }
    }

    // 处理词汇数据：支持索引存储策略
    let words = competition.words;

    // 如果使用索引存储策略，需要动态加载词汇
    if (!words && competition.wordSelection && competition.storageStrategy === 'index_based') {
      console.log('检测到索引存储竞赛，开始动态加载词汇');

      try {
        const { libraryId, wordIndexes, totalWords, randomSeed } = competition.wordSelection;

        // 从对应的词库集合中获取词汇
        const libraryCollection = getLibraryCollection(libraryId);
        const libraryResult = await db.collection(libraryCollection).get();

        if (libraryResult.data && libraryResult.data.length > 0) {
          const allWords = libraryResult.data;

          // 根据索引获取词汇
          words = wordIndexes.map(index => allWords[index]).filter(word => word);

          console.log(`动态加载完成: ${words.length}/${totalWords} 个词汇`);
        } else {
          console.error('词库数据为空:', libraryId);
          words = [];
        }
      } catch (error) {
        console.error('动态加载词汇失败:', error);
        words = [];
      }
    }

    // 返回竞赛详情
    return {
      success: true,
      data: {
        id: competition._id,
        name: competition.name,
        mode: competition.mode,
        words: words,
        libraryId: competition.libraryId,
        libraryName: competition.libraryName,
        gameMode: competition.gameMode, // 消消乐模式
        timeLimit: competition.timeLimit, // 时间限制
        dictationSettings: competition.dictationSettings, // 听写设置
        participants: competition.participants || 0,
        creatorName: competition.creatorName || '匿名用户',
        createTime: competition.createTime
      }
    }
    
  } catch (error) {
    console.error('获取竞赛详情失败:', error)
    return {
      success: false,
      message: '获取竞赛详情失败: ' + error.message
    }
  }
} 