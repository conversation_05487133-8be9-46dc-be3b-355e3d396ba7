const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('=== addStudent云函数开始执行 ===');
  console.log('接收到的参数:', event);

  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const { name, gender, school } = event;

  try {
    // 验证必填字段
    if (!name || !gender || !school) {
      console.log('验证失败 - 缺少必填字段:', { name, gender, school });
      return {
        success: false,
        message: '学生姓名、性别和学校不能为空'
      };
    }

    console.log('验证通过，准备添加学生:', { name, gender, school });

    // 检查并创建students集合（如果不存在）
    try {
      await db.collection('students').limit(1).get();
      console.log('students集合已存在');
    } catch (error) {
      if (error.errCode === -502005) {
        console.log('students集合不存在，尝试创建...');
        // 集合不存在，通过添加一个临时文档来创建集合，然后删除
        try {
          const tempResult = await db.collection('students').add({
            data: {
              _temp: true,
              createTime: new Date()
            }
          });
          console.log('临时文档创建成功，准备删除...');
          await db.collection('students').doc(tempResult._id).remove();
          console.log('students集合创建成功');
        } catch (createError) {
          console.error('创建集合失败:', createError);
          throw createError;
        }
      } else {
        throw error;
      }
    }

    // 添加学生
    const result = await db.collection('students').add({
      data: {
        name: name.trim(),
        gender: gender.trim(),
        school: school.trim(),
        teacherOpenid: openid,
        createTime: new Date(),
        updateTime: new Date()
      }
    });

    console.log('学生添加成功，ID:', result._id);
    return {
      success: true,
      data: {
        _id: result._id
      },
      message: '学生添加成功'
    };
  } catch (error) {
    console.error('添加学生失败:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });
    return {
      success: false,
      message: '添加学生失败: ' + error.message,
      error: error.message,
      errorCode: error.code
    };
  }
};
