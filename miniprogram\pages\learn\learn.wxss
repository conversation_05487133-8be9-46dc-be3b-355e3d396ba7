.container {
  min-height: 100vh;
  padding: 40rpx;
  background-color: #f8f9fa;
  position: relative;
}

/* 进度条 */
.progress-bar {
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-inner {
  height: 100%;
  background-color: #4a90e2;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

/* 单词卡片 */
.word-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.word {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 24rpx;
}

.phonetic {
  font-size: 32rpx;
  color: #666;
  text-align: center;
  margin-bottom: 32rpx;
}

.meaning {
  margin-bottom: 32rpx;
}

.meaning-item {
  margin-bottom: 16rpx;
}

.part-of-speech {
  font-size: 28rpx;
  color: #4a90e2;
  margin-right: 16rpx;
}

.definition {
  font-size: 28rpx;
  color: #333;
}

.example {
  border-top: 2rpx solid #f0f0f0;
  padding-top: 32rpx;
}

.example-item {
  margin-bottom: 16rpx;
}

.example-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 操作按钮 */
.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
}

.action-btn .icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.action-btn text {
  font-size: 24rpx;
  color: #666;
}

/* 底部按钮 */
.bottom-bar {
  position: fixed;
  left: 40rpx;
  right: 40rpx;
  bottom: 40rpx;
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}

.btn {
  flex: 1;
  height: 96rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
}

.btn-difficult {
  background-color: #ff4d4f;
}

.btn-normal {
  background-color: #faad14;
}

.btn-easy {
  background-color: #52c41a;
}

/* 完成弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 32rpx;
}

.modal-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-buttons {
  display: flex;
  gap: 24rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  background-color: #4a90e2;
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

/* 短语测试样式 */
.phrase-test-container {
  min-height: 100vh;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.progress-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.progress-section .progress-bar {
  flex: 1;
  margin: 0 20rpx;
  background: rgba(255, 255, 255, 0.2);
}

.progress-section .progress-inner {
  background: #fff;
}

.progress-section .progress-text,
.progress-section .score-text {
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
}

.question-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.question-title {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.phrase-display {
  text-align: center;
  margin-bottom: 40rpx;
}

.phrase-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.meaning-display {
  text-align: center;
  margin-bottom: 40rpx;
}

.meaning-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.options-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.option-item.correct {
  background: #f6ffed;
  border-color: #52c41a;
}

.option-item.wrong {
  background: #fff2f0;
  border-color: #ff4d4f;
}

.option-label {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #e6f7ff;
  color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.option-item.correct .option-label {
  background: #52c41a;
  color: #fff;
}

.option-item.wrong .option-label {
  background: #ff4d4f;
  color: #fff;
}

.option-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.result-section {
  text-align: center;
  padding: 60rpx 40rpx;
}

.result-header {
  margin-bottom: 60rpx;
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.result-score {
  font-size: 72rpx;
  font-weight: bold;
  color: #ffd700;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.result-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 60rpx;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.result-stats .stat-item {
  text-align: center;
}

.result-stats .stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 8rpx;
}

.result-stats .stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.result-actions {
  display: flex;
  gap: 20rpx;
}

.result-actions .action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.result-actions .action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.result-actions .action-btn.primary {
  background: #fff;
  color: #667eea;
}