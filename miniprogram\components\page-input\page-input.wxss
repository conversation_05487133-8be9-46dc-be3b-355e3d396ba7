.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-body {
  padding: 40rpx 30rpx;
}

.input-container {
  position: relative;
}

.page-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  box-sizing: border-box;
  text-align: center;
  transition: all 0.3s ease;
}

.page-input:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.1);
}

.input-placeholder {
  color: #adb5bd;
  font-size: 28rpx;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.btn-cancel, .btn-confirm {
  flex: 1;
  height: 100rpx;
  margin: 0;
  border-radius: 0;
  font-size: 32rpx;
  line-height: 100rpx;
  border: none;
  background: none;
}

.btn-cancel {
  background: #f8f9fa;
  color: #6c757d;
  border-right: 1rpx solid #f0f0f0;
}

.btn-confirm {
  background: #4a90e2;
  color: white;
}

.btn-cancel:active {
  background: #e9ecef;
}

.btn-confirm:active {
  background: #357abd;
}
