<!-- 错题本页面 -->
<view class="container">
  <!-- 顶部统计 -->
  <view class="stats-bar">
    <view class="stat-item">
      <text class="stat-value">{{totalCount}}</text>
      <text class="stat-label">错题总数</text>
    </view>
    <view class="stat-item">
      <text class="stat-value">{{reviewedCount}}</text>
      <text class="stat-label">已复习</text>
    </view>
    <view class="stat-item">
      <text class="stat-value">{{remainingCount}}</text>
      <text class="stat-label">待复习</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-bar">
    <button 
      class="action-button review"
      bindtap="onStartReview"
      wx:if="{{totalCount > 0}}"
    >开始复习</button>
    <button 
      class="action-button test"
      bindtap="onStartTest"
      wx:if="{{totalCount > 0}}"
    >错题测试</button>
  </view>

  <!-- 错题列表 -->
  <view class="mistake-list" wx:if="{{mistakes.length > 0}}">
    <view 
      class="mistake-item"
      wx:for="{{mistakes}}"
      wx:key="_id"
      bindtap="showWordDetail"
      data-word="{{item}}"
    >
      <view class="word-info">
        <view class="word">{{item.word}}</view>
        <view class="phonetic">/{{item.phonetic}}/</view>
      </view>
      <view class="word-meaning">{{item.meaning}}</view>
      <view class="word-status {{item.reviewed ? 'reviewed' : ''}}">
        {{item.reviewed ? '已复习' : '待复习'}}
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{mistakes.length === 0}}">
    <image class="empty-icon" src="/images/empty.png" mode="aspectFit"></image>
    <text class="empty-text">暂无错题</text>
    <text class="empty-tip">完成测试后，错题会自动加入错题本</text>
  </view>

  <!-- 单词详情弹窗 -->
  <view class="word-modal" wx:if="{{showModal}}" bindtap="hideWordDetail">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">单词详情</text>
        <view class="close-button" bindtap="hideWordDetail">×</view>
      </view>
      <view class="modal-body">
        <view class="word-info">
          <view class="word">{{currentWord.word}}</view>
          <view class="phonetic">/{{currentWord.phonetic}}/</view>
        </view>
        <view class="meaning">{{currentWord.meaning}}</view>
        <view class="example" wx:if="{{currentWord.example}}">
          <text class="example-text">{{currentWord.example}}</text>
          <text class="example-translation">{{currentWord.exampleTranslation}}</text>
        </view>
        <view class="audio-player">
          <view class="audio-header">
            <text class="audio-title">发音</text>
            <text class="audio-subtitle">点击播放音频</text>
          </view>
          <view class="audio-controls">
            <view class="play-button" bindtap="playAudio">
              <image class="play-icon" src="/images/play.png" mode="aspectFit"></image>
            </view>
            <slider class="audio-slider" value="{{audioProgress}}" bindchange="onSliderChange" block-size="12" activeColor="#4CAF50" backgroundColor="#e0e0e0"/>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-button review" bindtap="markAsReviewed" wx:if="{{!currentWord.reviewed}}">标记为已复习</button>
        <button class="modal-button remove" bindtap="removeFromMistakeBook">移出错题本</button>
      </view>
    </view>
  </view>
</view> 