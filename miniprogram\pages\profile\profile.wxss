/* 我的页面样式 */
.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 180rpx;
}

/* 用户信息区域 */
.user-section {
  margin-bottom: 30rpx;
}

.user-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.user-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.9);
}

.avatar-section {
  position: relative;
  margin-right: 24rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid white;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.avatar-edit {
  position: absolute;
  bottom: -5rpx;
  right: -5rpx;
  width: 36rpx;
  height: 36rpx;
  background: #4A90E2;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.3);
}

.edit-icon {
  font-size: 20rpx;
  color: white;
}

.user-info-section {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-icon {
  font-size: 22rpx;
}

.detail-text {
  font-size: 24rpx;
  color: #666;
}

.profile-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.setting-hint {
  font-size: 20rpx;
  color: #999;
}

.setting-arrow {
  font-size: 32rpx;
  color: #4A90E2;
  font-weight: 300;
}

/* 未登录状态 */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.login-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  opacity: 0.6;
}

.login-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.login-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.login-desc {
  font-size: 24rpx;
  color: #666;
}

.login-btn {
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.3);
}

.login-btn:active {
  transform: scale(0.98);
}

.login-text {
  color: white;
}

/* VIP区域 */
.vip-section {
  margin-bottom: 30rpx;
}

.vip-card {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 6rpx 24rpx rgba(255, 165, 0, 0.3);
}

.vip-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.vip-icon {
  font-size: 40rpx;
}

.vip-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.vip-title {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
}

.vip-subtitle {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
}

.vip-arrow {
  font-size: 28rpx;
  color: white;
}

/* 菜单区域 */
.menu-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.menu-group {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
}

.menu-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #666;
  padding: 20rpx 24rpx 10rpx;
  background: rgba(248, 249, 250, 0.5);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  transition: all 0.3s ease;
  position: relative;
}

.menu-item:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 80rpx;
  right: 24rpx;
  height: 1rpx;
  background: rgba(0, 0, 0, 0.05);
}

.menu-item:active {
  background: rgba(74, 144, 226, 0.05);
  transform: scale(0.98);
}

.menu-item.admin-item {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.05));
}

.menu-item.admin-item:active {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 107, 107, 0.1));
}

.menu-icon-wrapper {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  position: relative;
}

/* 角标样式 */
.badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}

.badge-text {
  font-size: 18rpx;
  color: white;
  font-weight: 600;
  line-height: 1;
  padding: 0 6rpx;
}

.menu-icon-wrapper.blue {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.2), rgba(74, 144, 226, 0.1));
}

.menu-icon-wrapper.cyan {
  background: linear-gradient(135deg, rgba(80, 227, 194, 0.2), rgba(80, 227, 194, 0.1));
}

.menu-icon-wrapper.orange {
  background: linear-gradient(135deg, rgba(245, 166, 35, 0.2), rgba(245, 166, 35, 0.1));
}

.menu-icon-wrapper.gray {
  background: linear-gradient(135deg, rgba(108, 117, 125, 0.2), rgba(108, 117, 125, 0.1));
}

.menu-icon-wrapper.green {
  background: linear-gradient(135deg, rgba(126, 211, 33, 0.2), rgba(126, 211, 33, 0.1));
}

.menu-icon-wrapper.purple {
  background: linear-gradient(135deg, rgba(189, 16, 224, 0.2), rgba(189, 16, 224, 0.1));
}

.menu-icon-wrapper.indigo {
  background: linear-gradient(135deg, rgba(108, 92, 231, 0.2), rgba(108, 92, 231, 0.1));
}

.menu-icon-wrapper.red {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 107, 107, 0.1));
}

.menu-icon {
  font-size: 32rpx;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.menu-title-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.menu-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.3;
}

.menu-arrow {
  font-size: 32rpx;
  color: #ccc;
  font-weight: 300;
}

/* 退出登录区域 */
.logout-section {
  margin-top: 40rpx;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.logout-btn:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.98);
}

.logout-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
} 