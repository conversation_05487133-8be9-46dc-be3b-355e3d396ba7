<!--pages/profile/learning-progress/learning-progress.wxml-->
<view class="progress-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">学习进度</text>
    <text class="page-subtitle">查看各词库的学习进度</text>
    <view class="header-actions" wx:if="{{!empty && !loading}}">
      <button
        class="edit-btn {{editMode ? 'active' : ''}}"
        bindtap="toggleEditMode"
      >
        {{editMode ? '完成' : '管理'}}
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{empty}}">
    <view class="empty-icon">📚</view>
    <text class="empty-title">暂无学习进度</text>
    <text class="empty-desc">开始学习后，学习进度将显示在这里</text>
    <button class="empty-btn" bindtap="goToWordBank">开始学习</button>
  </view>

  <!-- 编辑模式工具栏 -->
  <view class="edit-toolbar" wx:if="{{editMode && !empty}}">
    <view class="toolbar-info">
      <text class="selected-count">已选择 {{selectedCount}} 项</text>
    </view>
    <view class="toolbar-actions">
      <button
        class="batch-btn danger"
        bindtap="batchDelete"
        disabled="{{selectedCount === 0}}"
      >
        批量删除
      </button>
    </view>
  </view>

  <!-- 学习进度列表 -->
  <view class="progress-list" wx:if="{{!empty && !loading}}">
    <view
      class="progress-card {{editMode ? 'edit-mode' : ''}} {{editMode && item.isSelected ? 'selected' : ''}}"
      wx:for="{{progressList}}"
      wx:key="key"
      wx:for-item="item"
      bindlongpress="onProgressLongPress"
      data-key="{{item.key}}"
    >
      <!-- 选择框 (编辑模式) -->
      <view
        wx:if="{{editMode}}"
        class="progress-selector"
        catchtap="toggleSelectProgress"
        data-key="{{item.key}}"
      >
        <view class="selector-circle {{item.isSelected ? 'selected' : ''}}">
          <text wx:if="{{item.isSelected}}" class="selector-check">✓</text>
        </view>
      </view>

      <!-- 进度信息 -->
      <view class="progress-info">
        <view class="progress-header">
          <view class="title-row">
            <text class="progress-title">{{item.libraryName}}</text>
            <view class="mode-badge">
              <text class="mode-text">{{item.modeText}}</text>
            </view>
          </view>
          <text class="last-study-time">{{item.lastStudyTimeText}}</text>
        </view>

        <view class="progress-stats">
          <view class="stat-item">
            <text class="stat-number">{{item.percentage || 0}}%</text>
            <text class="stat-label">完成度</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{item.groupProgress || '0/0'}}</text>
            <text class="stat-label">关卡进度</text>
          </view>
          <view class="stat-item" wx:if="{{item.reviewInfo && item.reviewInfo.totalReviewWords > 0}}">
            <text class="stat-number">{{item.reviewInfo.totalReviewWords}}</text>
            <text class="stat-label">待复习</text>
          </view>
        </view>

        <!-- 进度条 -->
        <view class="progress-bar-container">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.percentage}}%"></view>
          </view>
          <text class="progress-text">{{item.progressText || item.detailText}}</text>
        </view>
      </view>

      <!-- 操作按钮卡片 (非编辑模式) -->
      <view class="action-cards" wx:if="{{!editMode}}">
        <!-- 继续新学卡片 -->
        <view
          class="action-card primary"
          data-item="{{item}}"
          bindtap="continueNewStudy"
        >
          <view class="card-header">
            <text class="card-title">继续新学</text>
            <text class="card-progress">{{item.groupProgress || '0/0'}}</text>
          </view>
          <text class="card-desc">学习新词汇</text>
        </view>

        <!-- 开始复习卡片 -->
        <view
          class="action-card review {{item.reviewInfo && item.reviewInfo.totalReviewWords > 0 ? 'active' : 'disabled'}}"
          data-item="{{item}}"
          bindtap="startReview"
        >
          <view class="card-header">
            <text class="card-title">开始复习</text>
            <text class="card-progress">{{item.reviewProgress || '0/0'}}</text>
          </view>
          <text class="card-desc">复习背过的词汇</text>
        </view>

        <!-- 删除按钮 -->
        <button
          class="delete-btn"
          data-item="{{item}}"
          bindtap="clearProgress"
        >
          删除
        </button>
      </view>
    </view>
  </view>
  <!-- 使用说明 -->
  <view class="help-section" wx:if="{{!empty && !loading && !editMode}}">
    <view class="help-title">💡 使用说明</view>
    <view class="help-content">
      <text class="help-item">• 点击"继续新学"直接跳转到对应测试模式继续学习</text>
      <text class="help-item">• 点击"开始复习"进行智能复习（艾宾浩斯记忆曲线+错词复习）</text>
      <text class="help-item">• 关卡进度显示当前关卡/总关卡数</text>
      <text class="help-item">• 复习进度显示已复习/待复习词汇数</text>
      <text class="help-item">• 复习功能会自动合并需要复习的词汇和对应模式的错词</text>
      <text class="help-item">• 长按进度卡片进入管理模式</text>
    </view>
  </view>
</view>