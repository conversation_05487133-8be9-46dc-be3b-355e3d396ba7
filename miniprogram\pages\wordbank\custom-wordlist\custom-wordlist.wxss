.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 加载状态 */
.loading, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #007AFF;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .error-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.retry-btn {
  margin-top: 30rpx;
  padding: 16rpx 48rpx;
  background-color: #007AFF;
  color: white;
  font-size: 28rpx;
  border-radius: 8rpx;
  border: none;
}

/* 词库内容 */
.wordbank-content {
  padding-bottom: env(safe-area-inset-bottom);
}

.wordbank-header {
  background: white;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.wordbank-info {
  flex: 1;
}

.wordbank-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.wordbank-count {
  font-size: 26rpx;
  color: #666;
}

.wordbank-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 32rpx;
  font-size: 26rpx;
  border-radius: 8rpx;
  border: none;
  line-height: 1.5;
}

.action-btn.edit {
  background-color: #007AFF;
  color: white;
}

.action-btn.delete {
  background-color: #ff4757;
  color: white;
}

/* 选择模式工具栏 */
.selection-toolbar {
  background: white;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.selection-info {
  font-size: 28rpx;
  color: #333;
}

.selection-actions {
  display: flex;
  gap: 20rpx;
}

.select-btn {
  padding: 10rpx 24rpx;
  font-size: 24rpx;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 6rpx;
  border: none;
}

/* 词汇列表 */
.words-list {
  padding: 20rpx;
}

.word-item {
  background: white;
  margin-bottom: 16rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.word-item.selected {
  background-color: #e3f2fd;
  border: 2rpx solid #007AFF;
}

.checkbox {
  margin-right: 20rpx;
}

.word-content {
  flex: 1;
}

.word-main {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.word-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}

.phonetic {
  font-size: 26rpx;
  color: #666;
}

.word-meaning {
  margin-bottom: 8rpx;
}

.chinese {
  font-size: 28rpx;
  color: #555;
}

.word-example {
  margin-top: 8rpx;
}

.example {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.add-btn {
  padding: 20rpx 60rpx;
  background-color: #007AFF;
  color: white;
  font-size: 30rpx;
  border-radius: 10rpx;
  border: none;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.start-btn {
  width: 100%;
  padding: 24rpx 0;
  background-color: #007AFF;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 12rpx;
  border: none;
} 