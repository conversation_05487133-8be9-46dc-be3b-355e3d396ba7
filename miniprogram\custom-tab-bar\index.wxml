<view class="tab-bar">
  <view class="tab-item" 
        wx:for="{{list}}" 
        wx:key="index" 
        data-path="{{item.pagePath}}" 
        data-index="{{index}}" 
        bindtap="switchTab">
    <view class="tab-icon-wrapper">
      <view class="tab-icon {{selected === index ? 'active' : ''}}">
        {{selected === index ? item.activeIcon : item.icon}}
      </view>
      <view class="tab-indicator {{selected === index ? 'show' : ''}}"></view>
    </view>
    <view class="tab-text {{selected === index ? 'active' : ''}}">
      {{item.text}}
    </view>
  </view>
</view> 