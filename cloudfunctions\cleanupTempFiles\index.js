// 清理临时文件云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { fileID, delayHours = 1 } = event;
  
  console.log('设置文件清理任务:', { fileID, delayHours });
  
  try {
    // 计算删除时间
    const deleteTime = new Date(Date.now() + delayHours * 60 * 60 * 1000);
    
    // 记录到数据库，用于定时清理
    await db.collection('temp_files').add({
      data: {
        fileID: fileID,
        deleteTime: deleteTime,
        createTime: new Date(),
        status: 'pending'
      }
    });
    
    // 设置延时删除（使用setTimeout，但由于云函数超时限制，实际通过定时触发器处理）
    console.log(`文件 ${fileID} 将在 ${deleteTime} 删除`);
    
    return {
      success: true,
      message: `文件清理任务已设置，将在${delayHours}小时后删除`,
      deleteTime: deleteTime
    };
    
  } catch (error) {
    console.error('设置文件清理任务失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};
