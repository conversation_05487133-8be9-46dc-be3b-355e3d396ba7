<view class="teacher-auth-modal" wx:if="{{showTeacherAuthModal}}">
  <view class="modal-mask" bindtap="onMaskTap"></view>
  <view class="modal-content">
    <!-- 头部 -->
    <view class="modal-header">
      <view class="header-icon">🔐</view>
      <view class="header-title">教师权限验证</view>
      <view class="header-subtitle">此功能为教师专用，请输入权限密码</view>
    </view>
    
    <!-- 密码输入区域 -->
    <view class="password-section">
      <view class="input-wrapper">
        <input 
          class="password-input" 
          type="{{teacherAuthPasswordVisible ? 'text' : 'password'}}"
          placeholder="请输入教师权限密码"
          value="{{teacherAuthPassword}}"
          bindinput="onPasswordInput"
          focus="{{true}}"
          maxlength="20"
        />
        <view class="eye-icon" bindtap="onTogglePasswordVisibility">
          <text class="icon">{{teacherAuthPasswordVisible ? '👁️' : '👁️‍🗨️'}}</text>
        </view>
      </view>
      
      <!-- 密码强度指示器 -->
      <view class="password-hint" wx:if="{{teacherAuthPassword.length > 0}}">
        <text class="hint-text">密码长度: {{teacherAuthPassword.length}}</text>
      </view>
    </view>
    
    <!-- 按钮区域 -->
    <view class="button-section">
      <button class="auth-btn cancel-btn" bindtap="onCancel">
        <text class="btn-text">取消</text>
      </button>
      <button class="auth-btn confirm-btn" bindtap="onConfirm" disabled="{{teacherAuthPassword.length === 0}}">
        <text class="btn-text">验证</text>
      </button>
    </view>
    
    <!-- 底部提示 -->
    <view class="modal-footer">
      <text class="footer-text">🔒 权限验证成功后将永久保存</text>
    </view>
  </view>
</view> 