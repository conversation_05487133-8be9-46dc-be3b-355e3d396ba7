/* pages/login/login.wxss */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  position: relative;
}

/* 顶部装饰 */
.login-header {
  padding: 100rpx 60rpx 80rpx 60rpx;
  text-align: center;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.app-slogan {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录模式切换 */
.mode-tabs {
  display: flex;
  margin: 0 60rpx 40rpx 60rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50rpx;
  padding: 8rpx;
  backdrop-filter: blur(10rpx);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  border-radius: 42rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.tab-text {
  font-size: 32rpx;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: #667eea;
}

.tab-item:not(.active) .tab-text {
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  margin: 0 60rpx 60rpx 60rpx;
}

.form-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 60rpx 48rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 40rpx;
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 20rpx;
  padding: 24rpx 32rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.input-icon {
  font-size: 36rpx;
  margin-right: 24rpx;
  width: 36rpx;
  text-align: center;
}

.form-input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  background: transparent;
  border: none;
}

.form-input::placeholder {
  color: #999;
}

.eye-icon {
  padding: 12rpx;
  margin-left: 16rpx;
}

.eye-text {
  font-size: 32rpx;
  color: #666;
}

.code-input {
  margin-right: 16rpx;
}

.code-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  min-width: 180rpx;
}

.code-button::after {
  border: none;
}

.code-button.disabled {
  background: #cccccc;
  color: #999;
}

.forgot-password {
  text-align: right;
  margin-top: 24rpx;
}

.forgot-link {
  font-size: 28rpx;
  color: #667eea;
}

/* 登录按钮 */
.login-actions {
  margin: 0 60rpx 60rpx 60rpx;
}

.login-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 32rpx 0;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.login-button::after {
  border: none;
}

.login-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
}

.login-button.loading {
  background: #cccccc;
  transform: none;
  box-shadow: none;
}

.wechat-login-button {
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 28rpx 0;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.wechat-login-button::after {
  border: none;
}

.wechat-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.wechat-text {
  color: #333;
}

/* 注册提示 */
.register-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.register-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-right: 16rpx;
}

.register-link {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: bold;
  text-decoration: underline;
}

/* 协议条款 */
.agreement-section {
  padding: 0 60rpx 60rpx 60rpx;
}

.agreement-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.checkbox {
  margin-right: 16rpx;
  transform: scale(0.8);
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-right: 8rpx;
}

.agreement-link {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: underline;
  margin: 0 8rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .login-header {
    padding: 80rpx 40rpx 60rpx 40rpx;
  }
  
  .app-name {
    font-size: 42rpx;
  }
  
  .mode-tabs {
    margin: 0 40rpx 32rpx 40rpx;
  }
  
  .login-form {
    margin: 0 40rpx 40rpx 40rpx;
  }
  
  .form-section {
    padding: 40rpx 32rpx;
  }
  
  .login-actions {
    margin: 0 40rpx 40rpx 40rpx;
  }
  
  .agreement-section {
    padding: 0 40rpx 40rpx 40rpx;
  }
} 