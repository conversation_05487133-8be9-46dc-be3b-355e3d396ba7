/* 音频处理工具页面样式 */
.container {
  padding: 20rpx;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  padding: 30rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #e5e7eb;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: #4F46E5;
  color: white;
}

.step.completed .step-number {
  background: #10B981;
  color: white;
}

.step-label {
  font-size: 22rpx;
  color: #666;
  text-align: center;
}

.step.active .step-label {
  color: #4F46E5;
  font-weight: 500;
}

.step.completed .step-label {
  color: #10B981;
}

.step-line {
  flex: 1;
  height: 4rpx;
  background: #e5e7eb;
  margin: 0 20rpx;
  transition: all 0.3s ease;
}

.step-line.completed {
  background: #10B981;
}

/* 步骤内容 */
.step-content {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 上传区域 */
.upload-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.upload-card {
  background: #f9fafb;
  border: 2rpx dashed #d1d5db;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
}

.upload-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.upload-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.upload-info {
  flex: 1;
}

.upload-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.upload-desc {
  font-size: 24rpx;
  color: #666;
}

.upload-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.file-size {
  font-size: 22rpx;
  color: #666;
}

.upload-btn {
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  border-radius: 20rpx;
  background: #4F46E5;
  color: white;
  border: none;
}

/* 配置区域 */
.config-section {
  margin-bottom: 40rpx;
}

.config-card {
  background: #f9fafb;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
  gap: 16rpx;
}

.slider-container {
  width: 100%;
  padding: 10rpx 0;
  margin-bottom: 20rpx;
}

.slider-container slider {
  width: 100%;
  height: 40rpx;
}

.config-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.config-picker {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: white;
  border-radius: 8rpx;
  border: 2rpx solid #e5e7eb;
  min-width: 200rpx;
  justify-content: space-between;
}

.picker-arrow {
  color: #9ca3af;
  font-size: 24rpx;
}

.config-value {
  font-size: 24rpx;
  color: #4F46E5;
  font-weight: bold;
  min-width: 60rpx;
  text-align: right;
}

.type-description {
  font-size: 24rpx;
  color: #666;
  padding: 16rpx;
  background: white;
  border-radius: 8rpx;
  margin-top: 16rpx;
}

.config-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

/* 操作区域 */
.action-section {
  text-align: center;
}

.process-btn {
  width: 100%;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 16rpx;
  border: none;
}

.process-btn.enabled {
  background: linear-gradient(135deg, #4F46E5, #7C3AED);
  color: white;
}

.process-btn.disabled {
  background: #f3f4f6;
  color: #9ca3af;
}

/* 处理进度 */
.process-section {
  text-align: center;
  padding: 40rpx 20rpx;
}

.process-header {
  margin-bottom: 40rpx;
}

.process-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
}

.process-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.process-desc {
  font-size: 26rpx;
  color: #666;
}

.progress-container {
  margin-bottom: 30rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #e5e7eb;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4F46E5, #7C3AED);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #4F46E5;
}

.process-status {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.process-tips {
  text-align: left;
  background: #f9fafb;
  border-radius: 12rpx;
  padding: 24rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 预览区域 */
.preview-section {
  padding: 20rpx 0;
}

.preview-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.preview-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.preview-stats {
  font-size: 26rpx;
  color: #666;
}

.highlight {
  color: #4F46E5;
  font-weight: bold;
}

.segments-list {
  margin-bottom: 40rpx;
}

.segment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.segment-info {
  flex: 1;
}

.segment-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.segment-details {
  display: flex;
  gap: 20rpx;
  font-size: 22rpx;
  color: #666;
}

.segment-actions {
  margin-left: 20rpx;
}

.preview-btn {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
  background: #4F46E5;
  color: white;
  border: none;
}

.preview-btn.playing {
  background: #10B981;
}

.questions-preview {
  margin-bottom: 40rpx;
}

.questions-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.question-item {
  padding: 20rpx;
  background: #f9fafb;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}

.question-number {
  font-size: 24rpx;
  color: #4F46E5;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.question-content {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

.preview-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 12rpx;
  border: none;
  text-align: center;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.secondary {
  background: #f3f4f6;
  color: #666;
}

.action-btn.primary {
  background: #4F46E5;
  color: white;
}

/* 完成区域 */
.complete-section {
  text-align: center;
  padding: 60rpx 20rpx;
}

.complete-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.complete-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.complete-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 50rpx;
}

.complete-actions {
  display: flex;
  gap: 20rpx;
}
