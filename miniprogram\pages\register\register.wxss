/* 页面容器 */
.container {
  min-height: 100vh;
  padding: 40rpx;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 顶部标题 */
.header {
  margin-top: 80rpx;
  margin-bottom: 80rpx;
  text-align: center;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

/* 注册表单 */
.form {
  width: 100%;
  margin-bottom: 60rpx;
}

.input-group {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.input {
  font-size: 32rpx;
  color: #333;
  height: 48rpx;
}

/* 注册按钮 */
.actions {
  width: 100%;
  margin-bottom: 40rpx;
}

.btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #4a90e2;
  color: #fff;
}

.btn-primary[disabled] {
  background-color: #ccc;
  color: #999;
}

.btn-default {
  background-color: #fff;
  color: #4a90e2;
  border: 2rpx solid #4a90e2;
}

.btn-default[disabled] {
  background-color: #f5f5f5;
  color: #ccc;
  border: 2rpx solid #ccc;
}

.login-link {
  font-size: 28rpx;
  color: #4CAF50;
  text-align: center;
}

/* 用户协议 */
.footer {
  margin-top: auto;
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.agreement {
  font-size: 24rpx;
  color: #999;
}

.agreement-link {
  font-size: 24rpx;
  color: #4CAF50;
  margin: 0 4rpx;
} 