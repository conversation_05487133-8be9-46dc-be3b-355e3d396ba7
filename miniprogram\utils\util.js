// 格式化时间
const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

// 格式化数字
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

// 格式化日期
const formatDate = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  return `${[year, month, day].map(formatNumber).join('-')}`
}

// 计算两个日期之间的天数
const getDaysBetween = (date1, date2) => {
  const oneDay = 24 * 60 * 60 * 1000
  const diffDays = Math.round(Math.abs((date1 - date2) / oneDay))
  return diffDays
}

// 生成随机字符串
const randomString = length => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 生成短用户ID
const getShortUserId = (fullId) => {
  if (!fullId) return '未知'
  
  // 取后8位字符并转为大写，这样会从32位变成8位
  if (fullId.length > 8) {
    return fullId.slice(-8).toUpperCase()
  }
  
  // 如果ID本身就很短，直接返回大写
  return fullId.toUpperCase()
}

// 防抖函数
const debounce = (func, wait) => {
  let timeout
  return function (...args) {
    clearTimeout(timeout)
    timeout = setTimeout(() => {
      func.apply(this, args)
    }, wait)
  }
}

// 节流函数
const throttle = (func, wait) => {
  let timeout
  let previous = 0
  return function (...args) {
    const now = Date.now()
    const remaining = wait - (now - previous)
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      func.apply(this, args)
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now()
        timeout = null
        func.apply(this, args)
      }, remaining)
    }
  }
}

// 检查是否为空对象
const isEmptyObject = obj => {
  return Object.keys(obj).length === 0
}

// 深拷贝对象
const deepClone = obj => {
  if (obj === null || typeof obj !== 'object') return obj
  const clone = Array.isArray(obj) ? [] : {}
  for (let key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      clone[key] = deepClone(obj[key])
    }
  }
  return clone
}

// 显示成功提示
const showSuccess = (title, duration = 1500) => {
  wx.showToast({
    title: title,
    icon: 'success',
    duration: duration
  })
}

// 显示错误提示
const showError = (title, duration = 1500) => {
  wx.showToast({
    title: title,
    icon: 'error',
    duration: duration
  })
}

// 显示加载提示
const showLoading = (title = '加载中') => {
  wx.showLoading({
    title: title,
    mask: true
  })
}

// 隐藏加载提示
const hideLoading = () => {
  wx.hideLoading()
}

// 显示确认对话框
const showConfirm = (title, content) => {
  return new Promise((resolve, reject) => {
    wx.showModal({
      title: title,
      content: content,
      success: res => {
        if (res.confirm) {
          resolve(true)
        } else {
          resolve(false)
        }
      },
      fail: reject
    })
  })
}

module.exports = {
  formatTime,
  formatDate,
  getDaysBetween,
  randomString,
  getShortUserId,
  debounce,
  throttle,
  isEmptyObject,
  deepClone,
  showSuccess,
  showError,
  showLoading,
  hideLoading,
  showConfirm
} 