Page({
  data: {
    inputText: '',
    maxLength: 1000,
    voiceIndex: 0,
    voiceOptions: [
      { name: 'en-US-AriaNeural', description: '美式英语-女声(Aria)', lang: 'en-US' },
      { name: 'en-US-JennyNeural', description: '美式英语-女声(<PERSON>)', lang: 'en-US' },
      { name: 'en-US-GuyNeural', description: '美式英语-男声(<PERSON>)', lang: 'en-US' },
      { name: 'en-US-DavisNeural', description: '美式英语-男声(<PERSON>)', lang: 'en-US' },
      { name: 'en-GB-SoniaNeural', description: '英式英语-女声(<PERSON>)', lang: 'en-GB' },
      { name: 'en-GB-LibbyNeural', description: '英式英语-女声(<PERSON>)', lang: 'en-GB' },
      { name: 'en-GB-RyanNeural', description: '英式英语-男声(<PERSON>)', lang: 'en-GB' },
      { name: 'en-GB-ThomasNeural', description: '英式英语-男声(<PERSON>)', lang: 'en-GB' },
      { name: 'zh-CN-XiaoxiaoNeural', description: '中文普通话-女声(晓晓)', lang: 'zh-CN' },
      { name: 'zh-CN-YunxiNeural', description: '中文普通话-男声(云希)', lang: 'zh-CN' }
    ],
    speechRate: 1.0,
    pitch: 100,
    generating: false,
    audioUrl: '',
    isPlaying: false,
    audioContext: null,
    canGenerate: false,
    canPreview: false,
    canDownload: false
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '文本转语音'
    });

    // 初始化音频上下文
    this.initAudioContext();

    // 初始化按钮状态
    this.updateButtonStates();
  },

  onUnload() {
    // 页面卸载时安全销毁音频上下文
    try {
      if (this.data.audioContext && typeof this.data.audioContext.destroy === 'function') {
        this.data.audioContext.destroy();
        console.log('音频上下文已销毁');
      }
    } catch (error) {
      console.error('销毁音频上下文失败:', error);
    }
  },

  // 初始化音频上下文
  initAudioContext() {
    try {
      // 先销毁旧的音频上下文
      if (this.data.audioContext && typeof this.data.audioContext.destroy === 'function') {
        this.data.audioContext.destroy();
      }

      const audioContext = wx.createInnerAudioContext();
      audioContext.autoplay = false;
      audioContext.loop = false;

      audioContext.onPlay(() => {
        this.setData({ isPlaying: true });
      });

      audioContext.onPause(() => {
        this.setData({ isPlaying: false });
      });

      audioContext.onEnded(() => {
        this.setData({ isPlaying: false });
      });

      audioContext.onError((error) => {
        console.error('音频播放错误:', error);
        this.setData({ isPlaying: false });
        wx.showToast({
          title: '播放失败',
          icon: 'none'
        });
      });

      this.setData({ audioContext });
      console.log('音频上下文初始化完成');
    } catch (error) {
      console.error('初始化音频上下文失败:', error);
    }
  },

  // 文本输入
  onTextInput(e) {
    const inputText = e.detail.value;
    this.setData({
      inputText: inputText
    }, () => {
      this.updateButtonStates();
    });
  },

  // 语音选择
  onVoiceChange(e) {
    this.setData({
      voiceIndex: parseInt(e.detail.value)
    });
  },

  // 语速调节
  onRateChange(e) {
    this.setData({
      speechRate: parseFloat(e.detail.value)
    });
  },

  // 音调调节
  onPitchChange(e) {
    this.setData({
      pitch: parseInt(e.detail.value)
    });
  },

  // 生成语音
  async onGenerate() {
    const { inputText, voiceIndex, voiceOptions, speechRate, pitch } = this.data;

    console.log('开始生成语音:', {
      inputText: inputText,
      inputTextTrim: inputText.trim(),
      inputTextLength: inputText.trim().length,
      voiceIndex: voiceIndex,
      speechRate: speechRate,
      pitch: pitch
    });

    if (!inputText.trim()) {
      wx.showToast({
        title: '请输入文本',
        icon: 'none'
      });
      return;
    }



    this.setData({
      generating: true
    }, () => {
      this.updateButtonStates();
    });

    try {
      const selectedVoice = voiceOptions[voiceIndex];
      
      // 调用云函数生成语音
      const result = await wx.cloud.callFunction({
        name: 'ttsSpeak',
        data: {
          text: inputText.trim(),
          voice: selectedVoice.name,
          mode: 'tts',
          playCount: 1,
          wordType: 'normal',
          rate: speechRate,
          pitch: pitch
        }
      });

      console.log('云函数调用结果:', result);
      console.log('result.result:', result.result);

      if (result.result && result.result.success) {
        // 处理不同的返回格式
        let audioUrl = result.result.audioUrl;

        console.log('直接获取的audioUrl:', audioUrl);

        // 如果没有直接的audioUrl，尝试从data中获取
        if (!audioUrl && result.result.data) {
          console.log('从data中查找audioUrl:', result.result.data);
          if (result.result.data.playList && result.result.data.playList.length > 0) {
            const firstItem = result.result.data.playList[0];
            audioUrl = firstItem.audioUrl;
            console.log('从playList获取audioUrl:', audioUrl);
          } else if (result.result.data.audioUrl) {
            audioUrl = result.result.data.audioUrl;
            console.log('从data.audioUrl获取:', audioUrl);
          }
        }

        console.log('最终audioUrl:', audioUrl);

        // 如果还是没有audioUrl，但是有playList，可能是降级模式
        if (!audioUrl && result.result.data && result.result.data.source === 'fallback_error') {
          console.log('检测到降级模式，但无可用音频');
          throw new Error('语音生成失败：降级模式无可用音频源');
        }

        if (audioUrl) {
          this.setData({
            audioUrl: audioUrl
          }, () => {
            this.updateButtonStates();
          });

          wx.showToast({
            title: '生成成功',
            icon: 'success'
          });
        } else {
          throw new Error('未获取到音频URL');
        }
      } else {
        throw new Error(result.result?.message || '生成失败');
      }
    } catch (error) {
      console.error('语音生成失败:', error);

      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        generating: false
      }, () => {
        this.updateButtonStates();
      });
    }
  },

  // 预览播放
  onPreview() {
    const { audioContext, audioUrl, isPlaying } = this.data;

    if (!audioUrl) {
      wx.showToast({
        title: '请先生成语音',
        icon: 'none'
      });
      return;
    }

    try {
      if (!audioContext || typeof audioContext.play !== 'function') {
        console.error('音频上下文无效，重新初始化');
        this.initAudioContext();
        return;
      }

      if (isPlaying) {
        audioContext.pause();
      } else {
        audioContext.src = audioUrl;
        audioContext.play();
      }
    } catch (error) {
      console.error('播放音频失败:', error);
      wx.showToast({
        title: '播放失败',
        icon: 'none'
      });
    }
  },

  // 生成下载链接
  async onDownload() {
    const { audioUrl, inputText } = this.data;

    if (!audioUrl) {
      wx.showToast({
        title: '请先生成语音',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '生成下载链接...'
      });

      // 生成文件名
      const timestamp = new Date().getTime();
      const textPrefix = inputText.substring(0, 8).replace(/[^\w]/g, '') || 'audio';
      const fileName = `${textPrefix}_${timestamp}.mp3`;

      if (audioUrl.startsWith('data:audio/')) {
        // Base64格式音频，需要上传到云存储
        console.log('上传Base64音频到云存储');

        // 提取Base64数据
        const base64Data = audioUrl.split(',')[1];

        // 写入临时文件
        const fs = wx.getFileSystemManager();
        const tempFilePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

        fs.writeFileSync(tempFilePath, base64Data, 'base64');

        // 上传到云存储
        const uploadResult = await wx.cloud.uploadFile({
          cloudPath: `temp-audio/${fileName}`,
          filePath: tempFilePath
        });

        if (uploadResult.fileID) {
          // 获取临时下载链接
          const tempUrlResult = await wx.cloud.getTempFileURL({
            fileList: [uploadResult.fileID]
          });

          if (tempUrlResult.fileList && tempUrlResult.fileList[0] && tempUrlResult.fileList[0].tempFileURL) {
            const downloadUrl = tempUrlResult.fileList[0].tempFileURL;

            // 复制链接到剪贴板
            await wx.setClipboardData({
              data: downloadUrl
            });

            wx.hideLoading();
            wx.showModal({
              title: '下载链接已复制',
              content: `下载链接已复制到剪贴板！\n\n请在浏览器中粘贴打开即可下载音频文件。\n\n注意：链接1小时后自动失效`,
              confirmText: '知道了',
              showCancel: false
            });

            // 调用云函数设置1小时后删除文件
            wx.cloud.callFunction({
              name: 'cleanupTempFiles',
              data: {
                fileID: uploadResult.fileID,
                delayHours: 1
              }
            }).catch(error => {
              console.error('设置文件清理失败:', error);
            });

          } else {
            throw new Error('获取下载链接失败');
          }
        } else {
          throw new Error('上传文件失败');
        }
      } else {
        // URL格式音频，直接复制链接
        await wx.setClipboardData({
          data: audioUrl
        });

        wx.hideLoading();
        wx.showModal({
          title: '下载链接已复制',
          content: '下载链接已复制到剪贴板！\n\n请在浏览器中粘贴打开即可下载音频文件。',
          confirmText: '知道了',
          showCancel: false
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('生成下载链接失败:', error);

      // 降级方案：提供手动保存说明
      wx.showModal({
        title: '生成链接失败',
        content: '无法生成下载链接，请尝试：\n\n1. 点击"预览"播放音频\n2. 长按音频播放器保存\n\n或稍后重试',
        confirmText: '知道了',
        showCancel: false
      });
    }
  },





  // 更新按钮状态
  updateButtonStates() {
    const { inputText, audioUrl, generating } = this.data;
    const canGenerate = inputText.trim().length > 0 && !generating;
    const canPreview = audioUrl.length > 0 && !generating;
    const canDownload = audioUrl.length > 0 && !generating;

    console.log('更新按钮状态:', {
      inputText: inputText,
      inputTextLength: inputText.length,
      inputTextTrimLength: inputText.trim().length,
      audioUrl: audioUrl,
      audioUrlLength: audioUrl.length,
      generating: generating,
      canGenerate: canGenerate,
      canPreview: canPreview,
      canDownload: canDownload
    });

    this.setData({
      canGenerate: canGenerate,
      canPreview: canPreview,
      canDownload: canDownload
    });
  }
});
