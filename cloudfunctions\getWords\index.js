const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 集合名称映射表（与前端保持一致）
const COLLECTION_NAMES = {
  // 中考词汇
  'zhongkao_1600': 'words_zhongkao',
  
  // 大学词汇
  'college_cet4': 'words_siji',
  'college_cet6': 'words_liuji',
  
  // 高考大纲词汇
  'gaokao_3500': 'words_3500',
  'gaokao_3500_luan': 'words_3500_luan',
  'gaokao_weikeduo': 'words_weikeduo',
  
  // 人教版教材词汇（按册调用，通过unit字段区分单元）
  'renjiao_bixiu1': 'words_renjiao_bixiu1',           // 人教版必修第一册
  'renjiao_bixiu2': 'words_renjiao_bixiu2',           // 人教版必修第二册
  'renjiao_bixiu3': 'words_renjiao_bixiu3',           // 人教版必修第三册
  'renjiao_xuanxiu1': 'words_renjiao_xuanxiu1',       // 人教版选择性必修第一册
  'renjiao_xuanxiu2': 'words_renjiao_xuanxiu2',       // 人教版选择性必修第二册
  'renjiao_xuanxiu3': 'words_renjiao_xuanxiu3',       // 人教版选择性必修第三册
  'renjiao_xuanxiu4': 'words_renjiao_xuanxiu4',       // 人教版选择性必修第四册
  
  // 北师版教材词汇（按册调用，通过unit字段区分单元）
  'beishi_bixiu1': 'words_beishi_bixiu1',             // 北师版必修第一册
  'beishi_bixiu2': 'words_beishi_bixiu2',             // 北师版必修第二册
  'beishi_bixiu3': 'words_beishi_bixiu3',             // 北师版必修第三册
  'beishi_xuanxiu1': 'words_beishi_xuanxiu1',         // 北师版选择性必修第一册
  'beishi_xuanxiu2': 'words_beishi_xuanxiu2',         // 北师版选择性必修第二册
  'beishi_xuanxiu3': 'words_beishi_xuanxiu3',         // 北师版选择性必修第三册
  'beishi_xuanxiu4': 'words_beishi_xuanxiu4',         // 北师版选择性必修第四册
  
  // 兼容旧版本的映射（保持向后兼容）
  'textbook_renjiao_bixiu1': 'words_renjiao_bixiu1',
  'textbook_renjiao_bixiu2': 'words_renjiao_bixiu2',
  'textbook_renjiao_bixiu3': 'words_renjiao_bixiu3',
  'textbook_renjiao_xuanxiu1': 'words_renjiao_xuanxiu1',
  'textbook_renjiao_xuanxiu2': 'words_renjiao_xuanxiu2',
  'textbook_renjiao_xuanxiu3': 'words_renjiao_xuanxiu3',
  'textbook_renjiao_xuanxiu4': 'words_renjiao_xuanxiu4',
  'textbook_beishi_bixiu1': 'words_beishi_bixiu1',
  'textbook_beishi_bixiu2': 'words_beishi_bixiu2',
  'textbook_beishi_bixiu3': 'words_beishi_bixiu3',
  'textbook_beishi_xuanxiu1': 'words_beishi_xuanxiu1',
  'textbook_beishi_xuanxiu2': 'words_beishi_xuanxiu2',
  'textbook_beishi_xuanxiu3': 'words_beishi_xuanxiu3',
  'textbook_beishi_xuanxiu4': 'words_beishi_xuanxiu4',
  
  // 自定义词库（特殊处理）
  'custom_wordbanks': 'custom_wordbanks',
  
  // 题型专项词汇
  'special_wusan_gaopin_beijing': 'words_bjwusan',
  'special_yuedu_tongyong': 'words_reading_tongyong',
  'special_yuedu_beijing': 'words_reading_beijing',
  'special_wanxing_gaopin_beijing': 'words_wanxing_gaopin_beijing',
  'special_wanxing_shucishengyi_beijing': 'words_wanxing_shucishengyi_beijing',

  // 常用短语
  'phrase_gaopin': 'phrase_gaopin',
  'phrase_hunxiao': 'phrase_hunxiao',

  // 其他词汇
  'other_buguize': 'words_buguize',
  'other_xingjinci': 'words_xingjinci',
  'other_shucishengyi': 'words_shucishengyi_tongyong',
  
  // 兼容旧版本的映射（保持向后兼容）
  'outline_3500': 'words_3500',
  'weikeduo': 'words_weikeduo',
  'renjiao_bixiu_1': 'words_renjiao_bixiu1_2',
  'renjiao_bixiu_2': 'words_renjiao_bixiu1_2',
  'renjiao_xuanxiu_1': 'words_renjiao_xuanxiu1_2',
  'renjiao_xuanxiu_2': 'words_renjiao_xuanxiu1_2',
  'beishi_bixiu_1': 'words_beishi_bixiu1_2',
  'beishi_bixiu_2': 'words_beishi_bixiu1_2',
  'beishi_xuanxiu_1': 'words_beishi_xuanxiu1_2',
  'beishi_xuanxiu_2': 'words_beishi_xuanxiu1_2',
  'yuedu': 'words_yuedu',
  'shucishengyi': 'words_shucishengyi',
  'xingjinci': 'words_xingjinci',
  'buguize': 'words_buguize'
};

exports.main = async (event, context) => {
  const { libraryId, skip = 0, limit = 20, keyword = '', getTotalCount = false, unit = '' } = event
  
  console.log('云函数参数:', { libraryId, skip, limit, keyword, getTotalCount, unit })
  
  if (!libraryId) {
    return { code: 400, message: '缺少libraryId参数' }
  }

  // 根据libraryId获取对应的集合名称
  const collectionName = COLLECTION_NAMES[libraryId]
  
  if (!collectionName) {
    console.log('无效的libraryId:', libraryId, '可用的:', Object.keys(COLLECTION_NAMES))
    return { code: 400, message: `无效的libraryId: ${libraryId}` }
  }

  try {
    // 特殊处理自定义词库
    if (libraryId.startsWith('custom_')) {
      // 自定义词库通过ID查询
      const customWordbank = await db.collection('custom_wordbanks')
        .where({ id: libraryId })
        .get()
      
      if (customWordbank.data.length === 0) {
        return { code: 404, message: '自定义词库不存在' }
      }
      
      const wordbank = customWordbank.data[0]
      let words = wordbank.words || []
      
      // 关键词过滤
      if (keyword) {
        words = words.filter(word => 
          word.word.toLowerCase().includes(keyword.toLowerCase()) ||
          (word.chinese && word.chinese.includes(keyword)) ||
          (word.meaning && word.meaning.includes(keyword))
        )
      }
      
      // 分页处理
      const total = words.length
      const paginatedWords = words.slice(skip, skip + limit)
      
      // 格式化数据以匹配返回格式
      const formattedWords = paginatedWords.map(word => ({
        word: word.word,
        words: word.word, // 兼容字段
        chinese: word.chinese || word.meaning || '',
        meaning: word.chinese || word.meaning || '',
        phonetic: word.phonetic || '',
        example: word.example || ''
      }))
      
      return {
        code: 200,
        data: formattedWords,
        total: getTotalCount ? total : undefined,
        skip: skip,
        limit: limit
      }
    }
    
    // 使用对应的集合
    let query = db.collection(collectionName)
    
    // 构建查询条件
    let whereConditions = {}
    
    // 添加单元过滤（教材同步词汇专用）
    if (unit) {
      whereConditions.unit = unit
    }
    
    // 添加关键词搜索
    if (keyword) {
      // 支持搜索单词和中文释义
      whereConditions.$or = [
        {
          words: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        },
        {
          chinese: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        },
        {
          meaning: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        }
      ]
    }
    
    // 应用查询条件
    if (Object.keys(whereConditions).length > 0) {
      query = query.where(whereConditions)
    }

    // 如果需要获取总数
    let totalCount = null
    if (getTotalCount) {
      const countRes = await query.count()
      totalCount = countRes.total
      console.log('获取到总数:', totalCount)
    }

    // 获取词汇数据
    // 云数据库单次查询最大限制是1000，如果超过需要分批
    const actualLimit = Math.min(limit, 1000)
    console.log('实际查询limit:', actualLimit)
    
    const res = await query.skip(skip).limit(actualLimit).get()
    console.log('查询结果:', res.data.length, '个词汇')
    
    const result = { 
      code: 200, 
      data: res.data,
      total: res.data.length,
      collectionName: collectionName
    }
    
    // 如果获取了总数，添加到结果中
    if (getTotalCount) {
      result.totalCount = totalCount
    }
    
    return result
  } catch (e) {
    console.error('获取词汇失败:', e)
    return { 
      code: 500, 
      message: '获取失败', 
      error: e.message
    }
  }
} 