const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const { studentIds } = event;

  try {
    if (!studentIds || !Array.isArray(studentIds) || studentIds.length === 0) {
      return {
        success: false,
        message: '请提供要删除的学生ID列表'
      };
    }

    // 验证所有学生都属于当前教师
    const studentsResult = await db.collection('students').where({
      _id: db.command.in(studentIds),
      teacherOpenid: openid
    }).get();

    if (studentsResult.data.length !== studentIds.length) {
      return {
        success: false,
        message: '部分学生不存在或无权限删除'
      };
    }

    // 开始删除操作
    let deletedCount = 0;
    let deletedScoresCount = 0;

    // 1. 先删除相关的成绩记录
    const scoresResult = await db.collection('student_scores').where({
      studentId: db.command.in(studentIds)
    }).get();

    for (const score of scoresResult.data) {
      await db.collection('student_scores').doc(score._id).remove();
      deletedScoresCount++;
    }

    // 2. 删除学生记录
    for (const studentId of studentIds) {
      await db.collection('students').doc(studentId).remove();
      deletedCount++;
    }

    return {
      success: true,
      data: {
        deletedStudents: deletedCount,
        deletedScores: deletedScoresCount
      },
      message: `成功删除${deletedCount}个学生和${deletedScoresCount}条成绩记录`
    };

  } catch (error) {
    console.error('批量删除学生失败:', error);
    return {
      success: false,
      message: '批量删除失败',
      error: error.message
    };
  }
};
