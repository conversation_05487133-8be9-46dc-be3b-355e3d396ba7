.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 160rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  display: flex;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 9999;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24rpx 8rpx 16rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
}

.tab-item:active {
  transform: scale(0.95);
}

.tab-icon-wrapper {
  position: relative;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-icon {
  font-size: 48rpx;
  transition: all 0.3s ease;
  filter: grayscale(100%);
  transform: scale(0.9);
}

.tab-icon.active {
  filter: grayscale(0%);
  transform: scale(1.1);
  animation: tabIconBounce 0.6s ease;
}

.tab-indicator {
  position: absolute;
  bottom: -2rpx;
  left: 50%;
  transform: translateX(-50%) scale(0);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #4A90E2, #357ABD);
  border-radius: 3rpx;
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.tab-indicator.show {
  transform: translateX(-50%) scale(1);
}

.tab-text {
  font-size: 22rpx;
  color: #8E8E93;
  font-weight: 500;
  transition: all 0.3s ease;
  letter-spacing: 0.5rpx;
}

.tab-text.active {
  color: #4A90E2;
  font-weight: 600;
  transform: scale(1.05);
}

/* 动画效果 */
@keyframes tabIconBounce {
  0% { transform: scale(0.9); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1.1); }
}

/* 为不同的tab添加特殊效果 */
.tab-item:nth-child(1) .tab-icon.active {
  text-shadow: 0 0 20rpx rgba(74, 144, 226, 0.3);
}

.tab-item:nth-child(2) .tab-icon.active {
  text-shadow: 0 0 20rpx rgba(80, 227, 194, 0.3);
}

.tab-item:nth-child(3) .tab-icon.active {
  text-shadow: 0 0 20rpx rgba(245, 166, 35, 0.3);
}

.tab-item:nth-child(4) .tab-icon.active {
  text-shadow: 0 0 20rpx rgba(189, 16, 224, 0.3);
}

/* 毛玻璃背景增强 */
.tab-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
  z-index: -1;
} 