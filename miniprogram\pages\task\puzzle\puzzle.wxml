<view class="container">
  <!-- 游戏标题栏 -->
  <view class="game-header">
    <view class="header-content">
      <text class="game-title">单词消消乐{{isCustomMode ? ' - 第' + currentGroup + '关' : ''}}</text>
      <view class="game-info">
        <text class="mode-text">{{isCustomMode ? '自定义词汇' : modeText}}</text>
        <text class="progress" wx:if="{{isCustomMode && totalGroups > 1}}">{{currentGroup}}/{{totalGroups}}</text>
        <text class="timer" wx:if="{{gameMode !== 'practice'}}">{{timeLeft}}s</text>
        <text class="score">得分: {{score}}</text>
      </view>
    </view>
  </view>

  <!-- 游戏模式选择（游戏开始前显示） -->
  <view class="mode-selector {{gameStarted ? 'hidden' : ''}}" wx:if="{{!gameStarted}}">
    <view class="mode-title">选择游戏模式</view>
    
    <view class="mode-options">
      <view 
        class="mode-card practice"
        bindtap="selectMode"
        data-mode="practice"
      >
        <view class="mode-icon">🎯</view>
        <text class="mode-name">练习模式</text>
        <text class="mode-desc">不限时间，随意练习</text>
      </view>

      <view 
        class="mode-card challenge"
        bindtap="selectMode"
        data-mode="10"
      >
        <view class="mode-icon">⚡</view>
        <text class="mode-name">闪电模式</text>
        <text class="mode-desc">10秒限时挑战</text>
      </view>

      <view 
        class="mode-card challenge"
        bindtap="selectMode"
        data-mode="30"
      >
        <view class="mode-icon">🔥</view>
        <text class="mode-name">火速模式</text>
        <text class="mode-desc">30秒快速配对</text>
      </view>

      <view 
        class="mode-card challenge"
        bindtap="selectMode"
        data-mode="60"
      >
        <view class="mode-icon">⏰</view>
        <text class="mode-name">标准模式</text>
        <text class="mode-desc">1分钟正常节奏</text>
      </view>

      <view 
        class="mode-card challenge"
        bindtap="selectMode"
        data-mode="300"
      >
        <view class="mode-icon">🎓</view>
        <text class="mode-name">学习模式</text>
        <text class="mode-desc">5分钟深度学习</text>
      </view>
    </view>
  </view>

  <!-- 游戏区域 -->
  <view class="game-area {{gameStarted ? '' : 'hidden'}}">
    <!-- 游戏统计 -->
    <view class="game-stats">
      <view class="stat-item">
        <text class="stat-label">已配对</text>
        <text class="stat-value">{{matchedPairs}}/{{totalPairs}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">连击</text>
        <text class="stat-value combo">×{{combo}}</text>
      </view>
      <view class="stat-item" wx:if="{{gameMode !== 'practice'}}">
        <text class="stat-label">剩余时间</text>
        <text class="stat-value time">{{timeLeft}}s</text>
      </view>
    </view>

    <!-- 游戏网格 -->
    <view class="game-grid">
      <view class="blocks-container">
        <view 
          class="word-block {{item.type}} {{item.matched ? 'matched' : ''}} {{item.selected ? 'selected' : ''}}"
          wx:for="{{wordBlocks}}"
          wx:if="{{item.visible}}"
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="selectBlock"
          style="left: {{item.x}}rpx; top: {{item.y}}rpx; width: {{item.width}}rpx; height: {{item.height}}rpx;"
        >
          <view class="block-content">
            <text class="block-text" style="font-size: {{item.fontSize}}rpx;">{{item.text}}</text>
          </view>
          <view class="match-effect {{item.showEffect ? 'show' : ''}}">
            <text>+{{item.points}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 游戏控制按钮 -->
    <view class="game-controls">
      <view class="control-btn shuffle-btn" bindtap="shuffleBlocks">
        <text>🔀 重排</text>
      </view>
      <view class="control-btn hint-btn" bindtap="showHint">
        <text>💡 提示</text>
      </view>
      <view class="control-btn pause-btn" bindtap="pauseGame" wx:if="{{gameMode !== 'practice'}}">
        <text>⏸️ 暂停</text>
      </view>
      <view class="control-btn restart-btn" bindtap="restartGame">
        <text>🔄 重新开始</text>
      </view>
    </view>
    

  </view>

  <!-- 暂停弹窗 -->
  <view class="pause-modal {{showPause ? 'show' : ''}}" bindtap="hidePauseModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="pause-icon">⏸️</view>
      <text class="pause-title">游戏暂停</text>
      <view class="modal-buttons">
        <view class="modal-btn secondary" bindtap="resumeGame">继续游戏</view>
        <view class="modal-btn primary" bindtap="backToMenu">返回菜单</view>
      </view>
    </view>
  </view>

  <!-- 游戏结束弹窗 -->
  <view class="result-modal {{showResult ? 'show' : ''}}" bindtap="hideResultModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="result-icon">{{resultIcon}}</view>
      <text class="result-title">{{resultTitle}}</text>
      <view class="result-stats">
        <view class="result-item">
          <text class="result-label">最终得分</text>
          <text class="result-value">{{finalScore}}</text>
        </view>
        <view class="result-item" wx:if="{{gameMode !== 'practice'}}">
          <text class="result-label">用时</text>
          <text class="result-value">{{usedTime}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">配对成功</text>
          <text class="result-value">{{matchedPairs}}/{{totalPairs}}</text>
        </view>
        <view class="result-item">
          <text class="result-label">最大连击</text>
          <text class="result-value">×{{maxCombo}}</text>
        </view>
      </view>
      <view class="modal-buttons">
        <!-- 竞赛模式下的四个按钮 -->
        <view class="modal-btn-grid" wx:if="{{competitionMode}}">
          <!-- 挑战下一关 -->
          <view class="modal-btn next-level" wx:if="{{showNextLevelBtn}}" bindtap="goToNextLevel">
            挑战下一关
          </view>
          <view class="modal-btn next-level" wx:elif="{{showNextCompetitionLevelBtn}}" bindtap="goToNextCompetitionLevel">
            挑战下一关
          </view>
          <view class="modal-btn disabled" wx:else>
            已完成全部关卡
          </view>

          <!-- 返回关卡列表 -->
          <view class="modal-btn secondary" bindtap="backToLevelList">
            返回关卡列表
          </view>

          <!-- 查看排行榜 -->
          <view class="modal-btn secondary" bindtap="viewCompetitionRanking">
            查看排行榜
          </view>

          <!-- 分享成绩 -->
          <view class="modal-btn share-result" bindtap="shareGameResult">
            分享成绩
          </view>
        </view>

        <!-- 非竞赛模式下的按钮 -->
        <view class="modal-buttons-normal" wx:else>
          <view class="modal-btn next-level" wx:if="{{showNextLevelBtn}}" bindtap="goToNextLevel">
            下一关 ({{currentGroup + 1}}/{{totalGroups}})
          </view>
          <view class="modal-btn share-result" bindtap="shareGameResult" wx:if="{{!showNextLevelBtn}}">分享成绩</view>
          <view class="modal-btn secondary" bindtap="restartGame" wx:if="{{!showNextLevelBtn}}">再玩一次</view>
          <view class="modal-btn primary" bindtap="backToMenu">返回菜单</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 飘分特效 -->
  <view class="floating-scores" wx:if="{{floatingScores.length > 0}}">
    <view 
      class="floating-score {{item.show ? 'show' : ''}}"
      wx:for="{{floatingScores}}"
      wx:key="id"
    >
      <text class="score-text">+{{item.points}}</text>
      <text class="combo-text" wx:if="{{item.combo > 1}}">×{{item.combo}} 连击!</text>
    </view>
  </view>

  <!-- 烟花特效 -->
  <view class="fireworks-container" wx:if="{{showFireworks}}">
    <view 
      class="firework"
      wx:for="{{fireworks}}"
      wx:key="id"
      style="left: {{item.x}}%; top: {{item.y}}%; animation-delay: {{item.delay}}ms;"
    >
      <view class="firework-spark spark1"></view>
      <view class="firework-spark spark2"></view>
      <view class="firework-spark spark3"></view>
      <view class="firework-spark spark4"></view>
      <view class="firework-spark spark5"></view>
      <view class="firework-spark spark6"></view>
    </view>
  </view>

  <!-- 隐藏的画布，用于生成分享图片 -->
  <canvas 
    canvas-id="gameResultCanvas" 
    style="width: 375px; height: 600px; position: fixed; top: -1000px; left: -1000px;"
  ></canvas>

  <!-- 分享弹窗 -->
  <view class="share-modal" wx:if="{{showShareModal}}" bindtap="closeShareModal">
    <view class="share-content" catchtap="">
      <view class="share-header">
        <text class="share-title">分享游戏成绩</text>
        <text class="close-btn" bindtap="closeShareModal">✕</text>
      </view>
      
      <view class="share-info">
        <text class="share-desc">选择分享方式，与朋友分享你的游戏成绩</text>
        <text class="share-note">让朋友看到你的学习成果</text>
      </view>

      <view class="share-buttons">
        <button 
          class="wechat-share-btn" 
          open-type="share"
          bindtap="onShareButtonTap"
        >
          <text class="share-btn-icon">📤</text>
          <text class="share-btn-text">分享给朋友</text>
        </button>
        
        <button 
          class="copy-btn" 
          bindtap="saveGameResultToAlbum"
        >
          <text class="copy-btn-icon">💾</text>
          <text class="copy-btn-text">保存到相册</text>
        </button>
      </view>
    </view>
  </view>
</view> 