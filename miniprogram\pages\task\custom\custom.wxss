.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 输入区域样式 */
.input-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.input-header {
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.input-area {
  width: 100%;
  height: 300rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 32rpx;
  line-height: 1.6;
  box-sizing: border-box;
}

.word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 检测结果样式 */
.result-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.result-header {
  margin-bottom: 20rpx;
}

.word-list {
  max-height: 800rpx;
  overflow-y: auto;
}

.word-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.word-item:last-child {
  border-bottom: none;
}

.word-info {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.word {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.phonetic {
  font-size: 28rpx;
  color: #666;
}

.word-detail {
  margin-bottom: 20rpx;
}

.definition {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
}

.example {
  margin-top: 10rpx;
  padding: 10rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.example-text {
  font-size: 28rpx;
  color: #666;
  font-style: italic;
}

.word-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 30rpx;
  transition: all 0.3s;
}

.action-btn:active {
  background-color: #eee;
}

.action-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 底部按钮样式 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 20rpx;
}

.primary-btn, .secondary-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.primary-btn {
  background-color: #4CAF50;
  color: #fff;
}

.primary-btn[disabled] {
  background-color: #ccc;
  color: #fff;
}

.secondary-btn {
  background-color: #fff;
  color: #4CAF50;
  border: 2rpx solid #4CAF50;
}

.secondary-btn[disabled] {
  background-color: #fff;
  color: #ccc;
  border-color: #ccc;
}

/* 记忆提示弹窗样式 */
.mnemonic-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.modal-body {
  padding: 30rpx;
}

.mnemonic-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
} 