/* 帮助与反馈页面样式 */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.header-content {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.page-subtitle {
  font-size: 24rpx;
  color: #666;
}

/* 反馈区域 */
.feedback-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.title-desc {
  font-size: 22rpx;
  color: #666;
}

.input-wrapper {
  position: relative;
}

.feedback-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  font-size: 26rpx;
  line-height: 1.5;
  box-sizing: border-box;
  background: #fafafa;
}

.feedback-input:focus {
  border-color: #4A90E2;
  background: white;
}

.char-count {
  position: absolute;
  bottom: 10rpx;
  right: 15rpx;
  font-size: 20rpx;
  color: #999;
}

/* 提交按钮 */
.submit-section {
  margin: 40rpx 0;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  color: white;
  border: none;
  border-radius: 50rpx;
  height: 90rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(74, 144, 226, 0.3);
}

.submit-btn:active {
  transform: scale(0.98);
}

.submit-btn[disabled] {
  background: #ccc;
  box-shadow: none;
}

.btn-text {
  color: white;
}

/* 提示信息 */
.tips-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-top: 20rpx;
}

.tips-content {
  text-align: center;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.tips-item {
  font-size: 24rpx;
  color: #666;
  text-align: left;
  line-height: 1.5;
}

/* Tab切换 */
.tab-container {
  display: flex;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 25rpx 0;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #4A90E2, #357ABD);
}

.tab-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: white;
  font-weight: bold;
}

/* 反馈记录 */
.loading-records {
  text-align: center;
  padding: 60rpx 0;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4A90E2;
  border-radius: 50%;
  margin: 0 auto 20rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}

.records-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.record-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background 0.2s ease;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background: #f8f9fa;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.record-time {
  font-size: 22rpx;
  color: #999;
}

.record-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.record-status.pending {
  background: #fff3cd;
  color: #856404;
}

.record-status.replied {
  background: #d4edda;
  color: #155724;
}

.record-status.closed {
  background: #f8d7da;
  color: #721c24;
}

.record-content {
  margin-bottom: 15rpx;
}

.record-preview {
  display: block;
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.record-reply {
  background: #f8f9fa;
  padding: 15rpx;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
}

.reply-label {
  font-size: 22rpx;
  color: #4A90E2;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.reply-content {
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
}

.record-footer {
  text-align: right;
}

.tap-hint {
  font-size: 20rpx;
  color: #999;
}

/* 空状态 */
.empty-records {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.empty-tip {
  font-size: 22rpx;
  color: #999;
} 