const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const { studentId, examName, score, totalScore, grade, semester, examDate } = event;

  try {
    // 验证必填字段
    if (!studentId || !examName || score === undefined || score === null) {
      return {
        success: false,
        message: '学生ID、考试名称和分数不能为空'
      };
    }

    if (!grade || !semester) {
      return {
        success: false,
        message: '年级和学期不能为空'
      };
    }

    // 验证分数
    if (isNaN(score) || score < 0) {
      return {
        success: false,
        message: '分数必须是有效的非负数'
      };
    }

    if (isNaN(totalScore) || totalScore <= 0) {
      return {
        success: false,
        message: '总分必须是有效的正数'
      };
    }

    if (score > totalScore) {
      return {
        success: false,
        message: '分数不能超过总分'
      };
    }

    // 验证学生是否属于当前教师
    const studentResult = await db.collection('students').doc(studentId).get();
    
    if (!studentResult.data) {
      return {
        success: false,
        message: '学生不存在'
      };
    }

    if (studentResult.data.teacherOpenid !== openid) {
      return {
        success: false,
        message: '无权限为该学生添加成绩'
      };
    }

    // 添加成绩记录
    const result = await db.collection('student_scores').add({
      data: {
        studentId: studentId,
        examName: examName.trim(),
        score: parseFloat(score),
        totalScore: parseFloat(totalScore),
        grade: grade.trim(),
        semester: semester.trim(),
        examDate: examDate || new Date().toISOString().split('T')[0],
        teacherOpenid: openid,
        createTime: new Date(),
        updateTime: new Date()
      }
    });

    return {
      success: true,
      data: {
        _id: result._id
      },
      message: '成绩添加成功'
    };
  } catch (error) {
    console.error('添加学生成绩失败:', error);
    return {
      success: false,
      message: '添加学生成绩失败',
      error: error.message
    };
  }
};
