<view class="container">
  <view class="articles-list">
    <view class="article-card"
          wx:for="{{articles}}"
          wx:key="id"
          bindtap="onArticleTap"
          data-id="{{item.id}}">
      <view class="article-header">
        <view class="article-meta">
          <text class="article-source">{{item.source}}</text>
          <text class="article-date">{{item.publishDate}}</text>
        </view>
        <view class="article-badges">
          <text class="difficulty-badge difficulty-{{item.difficulty}}">{{item.difficultyText}}</text>
          <text class="category-badge">{{item.category}}</text>
        </view>
      </view>

      <text class="article-title">{{item.title}}</text>
      <text class="article-summary">{{item.summary}}</text>

      <view class="article-footer">
        <view class="article-tags">
          <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">#{{tag}}</text>
        </view>
        <text class="read-time">{{item.readTime}}</text>
      </view>
    </view>
  </view>
</view>