# 墨词自习室 - 防作弊锁定功能使用说明

## 功能概述

防作弊锁定功能为所有测试模式提供全屏专注体验，防止用户在测试过程中退出或使用其他功能。

## 已实现的测试页面

### 1. 听写测试 (`pages/spelling/practice/practice.js`)
- **触发条件**: `practiceMode === 'test'`
- **锁定时机**: 用户确认开始测试时
- **解锁时机**: 测试完成或用户强制退出

### 2. 单词测试 (`pages/wordtest/test/test.js`)
- **触发条件**: `isTestMode === true` (非练习模式)
- **锁定时机**: 用户确认开始测试时
- **解锁时机**: 测试完成或用户强制退出

### 3. 消消乐游戏 (`pages/task/puzzle/puzzle.js`)
- **触发条件**: `gameMode !== 'practice'` (限时模式)
- **锁定时机**: 选择游戏模式时
- **解锁时机**: 游戏结束或用户强制退出

## 核心功能特性

### 🛡️ 强力导航拦截（新增强化）
- **全方位拦截**: wx.navigateBack、wx.reLaunch、wx.redirectTo、wx.switchTab
- **系统级阻止**: 拦截导航栏返回按钮点击
- **物理按键**: 屏蔽手机物理返回键
- **页面生命周期**: 重写onUnload事件拦截

### 🔒 防作弊措施
- **全屏沉浸**: 隐藏导航栏，沉浸式状态栏体验
- **屏幕锁定**: 保持屏幕常亮，防止自动熄灭
- **操作限制**: 禁用下拉刷新、长按操作
- **应用监控**: 实时监听应用切换事件

### 🚨 智能退出保护（全面升级）
- **三级防护**: 渐进式退出确认机制
- **防重复触发**: 智能识别快速连续退出尝试
- **人性化交互**: 随机鼓励语句和友好提示
- **强制确认**: 多次尝试后的最终确认对话框
- **数据记录**: 完整记录所有退出尝试和中断行为

### 📊 智能数据统计
- **行为追踪**: 详细记录用户退出尝试模式
- **中断分析**: 保存测试中断的具体原因
- **完整性验证**: 确保测试结果真实可靠
- **性能监控**: 跟踪锁定功能的性能影响

## 使用方法

### 1. 引入模块
```javascript
const examLock = require('../../../utils/exam-lock');
```

### 2. 启用锁定
```javascript
examLock.enable({
  examName: '测试名称',
  onBackAttempt: (attemptCount) => {
    // 用户尝试退出时的回调
    console.log(`退出尝试: ${attemptCount}/3`);
  },
  onExitConfirm: () => {
    // 用户确认退出时的回调
    // 处理测试中断逻辑
    wx.navigateBack();
  }
});
```

### 3. 禁用锁定
```javascript
examLock.disable();
```

### 4. 状态查询
```javascript
// 检查是否处于锁定状态
const isLocked = examLock.isExamLocked();

// 获取锁定时长
const duration = examLock.getLockDuration();
```

## 测试流程

### 标准测试流程
1. 用户选择测试模式
2. 显示防作弊模式确认对话框
3. 用户确认后启用锁定功能
4. 进入全屏测试环境
5. 测试完成后自动解除锁定
6. 返回正常模式

### 强制退出流程
1. 用户尝试返回/退出
2. 系统显示第一次警告
3. 重复尝试显示第二次警告
4. 第三次尝试显示退出确认对话框
5. 用户确认后解除锁定并退出

## 最佳实践

### 1. 在页面onLoad中检查测试模式
```javascript
onLoad(options) {
  const { mode, practiceMode } = options;
  
  // 如果是测试模式，准备启用锁定
  if (mode === 'test' || practiceMode === 'test') {
    this.setData({ isTestMode: true });
  }
}
```

### 2. 在测试开始前启用锁定
```javascript
startTest() {
  if (this.data.isTestMode) {
    this.enableExamMode();
  }
  // 继续测试逻辑...
}
```

### 3. 在测试结束时解除锁定
```javascript
endTest() {
  if (this.data.examMode) {
    this.disableExamMode();
  }
  // 处理测试结果...
}
```

### 4. 在页面卸载时确保解锁
```javascript
onUnload() {
  if (this.data.examMode) {
    this.disableExamMode();
  }
  // 其他清理工作...
}
```

## 注意事项

1. **兼容性**: 功能主要针对微信小程序，部分API可能在其他平台无效
2. **用户体验**: 锁定功能会限制用户操作，应在测试前明确告知用户
3. **错误处理**: 确保在所有可能的退出路径都正确解除锁定
4. **数据安全**: 中断记录包含敏感信息，注意数据保护

## 故障排除

### 锁定无法解除
- 检查页面是否正确调用了 `disableExamMode()`
- 确认 `examLock.disable()` 被正确执行
- 重启小程序可强制解除所有锁定

### 返回键仍可使用
- 确认 `examLock.enable()` 被正确调用
- 检查页面的 `onUnload` 方法是否被正确重写
- 验证小程序版本是否支持相关API

### 全屏模式异常
- 检查导航栏设置是否正确
- 确认设备是否支持状态栏控制
- 验证CSS样式是否正确应用

## 版本历史

- **v1.0**: 基础防作弊锁定功能
- **v1.1**: 添加全屏模式和屏幕常亮
- **v1.2**: 完善退出保护机制
- **v1.3**: 增加数据记录和统计功能
- **v1.4**: 🔥 **重大更新** - 强化导航拦截和页面恢复机制
  - 全方位导航API拦截（wx.navigateBack、wx.reLaunch、wx.redirectTo、wx.switchTab）
  - 智能页面卸载检测和自动恢复
  - 优化退出确认流程，支持多级导航备选方案
  - 修复"继续测试"按钮无法返回测试页面的问题
  - 增强用户体验，添加友好的状态提示和导航反馈
- **v1.5**: 🎯 **智能测试恢复** - 完全解决重复初始化问题
  - 智能测试状态保存和恢复机制
  - 避免测试恢复时重复显示确认对话框
  - 防止音频重复播放和任务重复执行
  - 无缝测试体验恢复，保持测试连续性
  - 自动清理临时状态，优化存储管理 