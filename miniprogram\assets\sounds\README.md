# 音效文件说明

由于微信小程序对音频文件的限制，本项目采用以下音效解决方案：

## 音效实现方案

### 1. 震动反馈
- 使用 `wx.vibrateShort()` API 提供触觉反馈
- 不同类型的操作使用不同的震动模式
- 兼容性好，所有设备都支持

### 2. 系统提示音
- 使用微信小程序内置的提示音效
- 通过 `wx.showToast()` 的 icon 参数触发系统音效
- 包括 'success' 和 'error' 两种音效

### 3. 音频文件（可选）
如果需要使用自定义音频文件，请确保：
- 文件格式：支持 mp3, wav, m4a 等格式
- 文件大小：建议小于 100KB
- 文件路径：放置在 assets/sounds/ 目录下
- 域名配置：如使用在线音频，需在小程序后台配置合法域名

## 音效类型

### 英译汉测试模式
- `correct.wav` - 答对时的提示音（高音调）
- `wrong.wav` - 答错时的提示音（低音调）

### 消消乐游戏
- `match.wav` - 普通匹配成功音效
- `combo2.wav` - 2连击音效
- `combo3.wav` - 3连击音效
- `combo5.wav` - 5连击及以上音效
- `wrong_match.wav` - 匹配错误音效

## 使用说明

当前实现主要依赖震动反馈，如需添加真实音频文件：

1. 将音频文件放置在对应路径
2. 确保文件名与代码中的引用一致
3. 测试音频播放功能
4. 如遇到播放问题，系统会自动降级到震动反馈

## 注意事项

- 音效播放可能受到用户设备设置影响
- 建议提供开关选项让用户控制音效
- 在开发者工具中音效可能无法正常播放，请在真机上测试
