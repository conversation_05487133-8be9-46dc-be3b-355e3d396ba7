const app = getApp();
const examLock = require('../../../utils/exam-lock');

Page({
  data: {
    // 游戏状态
    gameStarted: false,
    gameMode: 'practice', // practice, 10, 30, 60, 300
    modeText: '',
    timeLeft: 0,
    timer: null,
    startTime: 0,
    
    // 游戏数据
    score: 0,
    combo: 0,
    maxCombo: 0,
    matchedPairs: 0,
    totalPairs: 0,
    
    // 错题数据
    wrongAttempts: [], // 记录错误的匹配尝试
    
    // 词汇数据
    sampleWords: [
      { english: 'HELLO', chinese: '你好' },
      { english: 'WORLD', chinese: '世界' },
      { english: 'LOVE', chinese: '爱' },
      { english: 'BOOK', chinese: '书' },
      { english: 'CAT', chinese: '猫' },
      { english: 'DOG', chinese: '狗' },
      { english: 'APPLE', chinese: '苹果' },
      { english: 'WATER', chinese: '水' },
      { english: 'FIRE', chinese: '火' },
      { english: 'EARTH', chinese: '地球' },
      { english: 'SUN', chinese: '太阳' },
      { english: 'MOON', chinese: '月亮' },
      { english: 'STAR', chinese: '星星' },
      { english: 'FLOWER', chinese: '花' },
      { english: 'TREE', chinese: '树' }
    ],
    
    // 游戏方块
    wordBlocks: [],
    selectedBlock: null,
    

    
    // 弹窗状态
    showPause: false,
    showResult: false,
    resultIcon: '',
    resultTitle: '',
    finalScore: 0,
    usedTime: '',
    
    // 自定义游戏数据
    isCustomMode: false,
    currentGroup: 1,
    totalGroups: 1,
    showNextLevelBtn: false,
    
    // 分享模式数据
    isSharedMode: false,
    shareId: '',
    
    // 特效数据
    floatingScores: [], // 飘分特效
    showFireworks: false, // 烟花特效
    fireworks: [], // 烟花数据
    
    // 分享弹窗相关
    showShareModal: false, // 是否显示分享弹窗
    currentShareData: null, // 当前分享数据
    
    hasNextCompetitionLevel: false, // 是否有下一关竞赛
    nextCompetitionLevelId: null, // 下一关竞赛ID（保持兼容性）
    nextLevelId: null, // 下一关ID（与其他模式保持一致）
    nextLevelNumber: null // 下一关关卡编号
  },

  onLoad(options) {
    const { mode, group, total, gameMode, shareId, isShared, shareMode, resumeTest, testStateId, competitionId, levelId } = options;
    
    console.log('📋 消消乐游戏页面加载参数:', options);
    
    // 检查是否是测试恢复
    const isTestResume = resumeTest === 'true';
    let restoredState = null;
    
    if (isTestResume && testStateId) {
      console.log('🔄 检测到消消乐测试恢复，尝试恢复状态...');
      restoredState = examLock.restoreTestState(testStateId);
    }
    
    // 设置恢复状态
    this.setData({
      isTestResume: isTestResume,
      examMode: isTestResume && restoredState ? true : false
    });
    
    // 检查是否为分享模式创建测试
    if (shareMode === 'create') {
      console.log('=== 消消乐分享模式创建 ===');
      
      // 设置分享创建模式状态
      this.setData({
        shareCreateMode: true
      });
      
      // 跳转到单词选择页面
      setTimeout(() => {
        wx.navigateTo({
          url: `/pages/wordbank/wordlist/wordlist?mode=test&testMode=elimination&shareMode=create`,
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }, 500);
      return;
    }
    
    if (competitionId) {
      // 竞赛模式，从全局数据中加载（数据已在competition页面设置）
      this.initCompetitionGame(competitionId, gameMode);
    } else if (shareId && isShared) {
      // 分享模式，从分享数据中加载
      this.initSharedGame(shareId, gameMode, levelId);
    } else if (mode === 'custom') {
      // 自定义模式，使用用户选择的词汇
      this.initCustomGame(group, total, gameMode);
    } else {
      // 默认模式，使用示例词汇
      this.initializeGame();
    }
    
    // 如果是测试恢复，先恢复状态，再启用锁定模式
    if (isTestResume && restoredState) {
      console.log('🔄 消消乐测试恢复：恢复状态并启用锁定模式');
      this.restoreGameState(restoredState);
    } else if (isTestResume && !restoredState) {
      // 没有恢复状态，显示错误信息
      console.log('⚠️ 没有找到恢复状态');
      wx.showModal({
        title: '恢复失败',
        content: '没有找到游戏状态，将开始新的游戏。',
        showCancel: false,
        success: () => {
          // 继续正常流程
        }
      });
    }
  },

  // 初始化游戏
  initializeGame() {
    // 根据容器大小决定方块数量，确保能完整放下且不重叠
    // 计算最大可容纳的词汇数量
    const maxPairs = this.calculateMaxWords(); // 动态计算最大词汇数
    const gameWords = this.shuffleArray([...this.data.sampleWords]).slice(0, maxPairs);
    this.setData({
      totalPairs: gameWords.length,
      isCustomMode: false
    });
    
    this.generateWordBlocks(gameWords);
  },

  // 初始化自定义游戏
  initCustomGame(currentGroup, totalGroups, gameMode) {
    console.log('=== 初始化自定义游戏 ===');
    console.log('传入参数:', { currentGroup, totalGroups, gameMode });
    
    const app = getApp();
    const gameData = app.globalData.eliminationGameData;
    
    console.log('全局游戏数据:', gameData);
    
    if (!gameData || !gameData.words) {
      wx.showToast({ title: '游戏数据加载失败', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    const parsedCurrentGroup = parseInt(currentGroup) || 1;
    const parsedTotalGroups = parseInt(totalGroups) || 1;
    
    console.log('设置游戏数据:', {
      totalPairs: gameData.words.length,
      isCustomMode: true,
      currentGroup: parsedCurrentGroup,
      totalGroups: parsedTotalGroups,
      wordsPerGroup: gameData.wordsPerGroup
    });

    this.setData({
      totalPairs: gameData.words.length,
      isCustomMode: true,
      currentGroup: parsedCurrentGroup,
      totalGroups: parsedTotalGroups,
      // 添加学习进度记录需要的参数
      libraryId: gameData.libraryId,
      libraryName: gameData.libraryName,
      isGrouped: parsedTotalGroups > 1, // 如果总组数大于1，则为分组模式
      wordsPerGroup: gameData.wordsPerGroup
    });
    
    // 生成游戏方块，但不直接开始游戏，让用户选择模式
    this.generateWordBlocks(gameData.words);
    
    // 如果有传入游戏模式，直接开始游戏
    if (gameMode) {
      this.setGameMode(gameMode);
    }
    
    console.log('游戏初始化完成，当前页面数据:', this.data);
  },

  // 初始化竞赛游戏
  initCompetitionGame(competitionId, gameMode) {
    console.log('=== 初始化竞赛游戏 ===');
    console.log('竞赛ID:', competitionId);
    console.log('游戏模式:', gameMode);

    const app = getApp();
    const gameData = app.globalData.eliminationGameData;

    console.log('全局竞赛游戏数据:', gameData);

    if (!gameData || !gameData.words || !gameData.competitionMode) {
      wx.showToast({ title: '竞赛数据加载失败', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    // 如果有gameMode参数，立即设置gameStarted为true，避免显示模式选择界面
    const gameStartedStatus = gameMode ? true : false;

    // 获取正确的关卡数字，优先使用levelNumber，否则使用currentGroup
    const currentLevelNumber = gameData.levelNumber || gameData.currentGroup || 1;
    const totalLevels = gameData.totalLevels || gameData.totalGroups || 1;

    console.log('设置关卡信息:', {
      levelNumber: gameData.levelNumber,
      currentGroup: gameData.currentGroup,
      finalCurrentGroup: currentLevelNumber,
      totalLevels: totalLevels
    });

    this.setData({
      totalPairs: gameData.words.length,
      isCustomMode: true,
      currentGroup: currentLevelNumber, // 使用正确的关卡数字
      totalGroups: totalLevels, // 使用正确的总关卡数
      competitionMode: true,
      competitionId: competitionId,
      gameStarted: gameStartedStatus,  // 立即设置游戏开始状态
      gameEnded: false,
      // 添加学习进度记录需要的参数
      libraryId: gameData.libraryId,
      libraryName: gameData.libraryName,
      isGrouped: totalLevels > 1, // 如果总关卡数大于1，则为分组模式
      wordsPerGroup: gameData.wordsPerGroup
    });

    // 生成游戏方块
    this.generateWordBlocks(gameData.words);

    // 如果有传入游戏模式，直接开始游戏
    if (gameMode) {
      // 使用setTimeout确保页面渲染完成后再设置游戏模式
      setTimeout(() => {
        this.setGameMode(gameMode);
      }, 100);
    }
    
    console.log('竞赛游戏初始化完成');
  },

  // 初始化分享游戏
  async initSharedGame(shareId, gameMode, levelId = null) {
    wx.showLoading({ title: '加载分享测试...' });
    
    let shareTestData = null;
    
    try {
      // 首先尝试从云端获取分享测试数据
      try {
        const result = await wx.cloud.callFunction({
          name: 'getShareTest',
          data: { shareId: shareId }
        });

        if (result.result.success) {
          shareTestData = result.result.data;
          console.log('从云端加载消消乐分享测试成功:', shareTestData);
        }
      } catch (cloudError) {
        console.error('从云端加载消消乐分享测试失败:', cloudError);
        // 不再尝试本地存储，因为分享测试必须在云端
      }

      wx.hideLoading();

      if (!shareTestData || (!shareTestData.words && !shareTestData.wordSelection)) {
        wx.showModal({
          title: '分享测试不存在',
          content: '该分享测试不存在或已过期。可能的原因：\n1. 分享链接错误\n2. 测试已过期（7天有效期）\n3. 创建者未成功上传到云端\n\n请联系分享者重新创建分享测试。',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载分享测试失败:', error);
      wx.showToast({ title: '加载失败', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 记录被分享人信息
    this.recordSharedTestAccess(shareId, shareTestData);

    // 计算总关卡数（使用分享数据中保存的每组词汇数量）
    const maxWordsPerGroup = shareTestData.wordsPerGroup || this.calculateMaxWords();
    const totalGroups = shareTestData.allWords ? Math.ceil(shareTestData.allWords.length / maxWordsPerGroup) : 1;

    // 如果指定了levelId，直接加载指定关卡
    if (levelId) {
      console.log('指定关卡模式，加载第', levelId, '关');
      this.loadSpecificLevel(shareTestData, maxWordsPerGroup, totalGroups, shareId, parseInt(levelId), gameMode);
      return;
    }

    // 如果是多关卡模式且没有指定关卡，检测用户进度并跳转到关卡选择界面
    if (totalGroups > 1) {
      console.log('检测到多关卡消消乐分享测试，跳转到关卡选择界面');
      wx.hideLoading();

      // 跳转到关卡选择界面
      wx.redirectTo({
        url: `/pages/competition/level-select/level-select?shareId=${shareId}&mode=share&testType=elimination&refresh=true`,
        fail: (err) => {
          console.error('跳转到关卡选择失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
          // 如果跳转失败，继续当前流程
          this.continueSharedGameInit(shareTestData, maxWordsPerGroup, totalGroups, shareId);
        }
      });
      return;
    }

    // 单关卡模式，继续原有流程
    this.continueSharedGameInit(shareTestData, maxWordsPerGroup, totalGroups, shareId);
  },

  // 加载指定关卡
  loadSpecificLevel(shareTestData, maxWordsPerGroup, totalGroups, shareId, levelNumber, gameMode) {
    wx.hideLoading();

    // 计算关卡词汇范围
    const startIndex = (levelNumber - 1) * maxWordsPerGroup;
    const endIndex = Math.min(startIndex + maxWordsPerGroup, shareTestData.allWords.length);
    const levelWords = shareTestData.allWords.slice(startIndex, endIndex);

    console.log(`加载第${levelNumber}关词汇:`, {
      startIndex,
      endIndex,
      levelWords: levelWords.length,
      totalWords: shareTestData.allWords.length
    });

    // 设置页面数据
    this.setData({
      totalPairs: levelWords.length,
      isCustomMode: true,
      currentGroup: levelNumber,
      totalGroups: totalGroups,
      isSharedMode: true,
      shareId: shareId,
      libraryId: shareTestData.libraryId,
      libraryName: shareTestData.libraryName,
      isGrouped: totalGroups > 1,
      wordsPerGroup: maxWordsPerGroup,
      levelId: levelNumber // 记录当前关卡ID
    });

    // 设置游戏数据到全局
    const app = getApp();
    app.globalData.eliminationGameData = {
      words: levelWords,
      allWords: shareTestData.allWords,
      currentGroup: levelNumber,
      totalGroups: totalGroups,
      wordsPerGroup: maxWordsPerGroup,
      mode: gameMode || shareTestData.eliminationGameMode,
      fromCustom: true,
      isShared: true,
      shareId: shareId,
      levelId: levelNumber
    };

    // 生成游戏方块并开始游戏
    this.generateWordBlocks(levelWords);
    if (gameMode || shareTestData.eliminationGameMode) {
      this.setGameMode(gameMode || shareTestData.eliminationGameMode);
    }
  },

  // 继续分享游戏初始化（单关卡模式）
  continueSharedGameInit(shareTestData, maxWordsPerGroup, totalGroups, shareId) {
    this.setData({
      totalPairs: shareTestData.words.length,
      isCustomMode: true,
      currentGroup: 1,
      totalGroups: totalGroups,
      isSharedMode: true,
      shareId: shareId,
      // 添加学习进度记录需要的参数
      libraryId: shareTestData.libraryId,
      libraryName: shareTestData.libraryName,
      isGrouped: totalGroups > 1, // 如果总组数大于1，则为分组模式
      wordsPerGroup: maxWordsPerGroup
    });

    // 设置游戏数据到全局（供其他功能使用）
    const app = getApp();
    app.globalData.eliminationGameData = {
      words: shareTestData.words,
      allWords: shareTestData.allWords, // 保存所有词汇，用于多关卡
      currentGroup: 1,
      totalGroups: totalGroups,
      wordsPerGroup: maxWordsPerGroup, // 保存每组词汇数量
      mode: gameMode || shareTestData.eliminationGameMode,
      fromCustom: true,
      isShared: true,
      shareId: shareId
    };
    
    // 生成游戏方块并直接开始游戏
    this.generateWordBlocks(shareTestData.words);
    if (gameMode || shareTestData.eliminationGameMode) {
      this.setGameMode(gameMode || shareTestData.eliminationGameMode);
    }
  },

  // 记录分享测试访问
  recordSharedTestAccess(shareId, shareTestData) {
    try {
      const currentUser = wx.getStorageSync('userInfo') || {};
      const currentTime = Date.now();
      
      // 检查是否已记录过该用户的访问
      if (!shareTestData.visitors) {
        shareTestData.visitors = [];
      }
      
      const existingVisitor = shareTestData.visitors.find(visitor => 
        visitor.openid === currentUser.openid
      );
      
      if (!existingVisitor && currentUser.openid) {
        // 添加新访问者记录
        shareTestData.visitors.push({
          openid: currentUser.openid,
          nickName: currentUser.nickName || '匿名用户',
          avatar: currentUser.avatarUrl || '',
          firstVisitTime: currentTime,
          lastVisitTime: currentTime,
          visitCount: 1,
          testCount: 0,
          remark: ''
        });
        
        // 更新存储
        const shareTests = wx.getStorageSync('shareTests') || {};
        shareTests[shareId] = shareTestData;
        wx.setStorageSync('shareTests', shareTests);
        
        console.log('记录新访问者:', currentUser.nickName);
      } else if (existingVisitor) {
        // 更新已有访问者的访问时间和次数
        existingVisitor.lastVisitTime = currentTime;
        existingVisitor.visitCount = (existingVisitor.visitCount || 1) + 1;
        
        // 更新存储
        const shareTests = wx.getStorageSync('shareTests') || {};
        shareTests[shareId] = shareTestData;
        wx.setStorageSync('shareTests', shareTests);
        
        console.log('更新访问者记录:', existingVisitor.nickName);
      }
    } catch (error) {
      console.error('记录分享测试访问失败:', error);
    }
  },

  // 生成游戏方块
  generateWordBlocks(words) {
    const blocks = [];
    
    // 动态计算网格配置（单位：rpx）
    const gridConfig = this.calculateGridConfig(words.length);
    
    console.log('生成方块，词汇数量:', words.length, '网格配置:', gridConfig);
    
    // 计算方块的精确位置
    const positions = [];
    for (let row = 0; row < gridConfig.rows; row++) {
      for (let col = 0; col < gridConfig.cols; col++) {
        positions.push({
          x: gridConfig.paddingX + col * (gridConfig.blockWidth + gridConfig.spacingX),
          y: gridConfig.paddingY + row * (gridConfig.blockHeight + gridConfig.spacingY)
        });
      }
    }

    // 只使用需要的位置数量
    const neededPositions = positions.slice(0, words.length * 2);
    // 随机打乱位置
    const shuffledPositions = this.shuffleArray(neededPositions);

    let id = 0;
    
    // 计算合适的字体大小
    const baseFontSize = Math.min(gridConfig.blockWidth / 6, gridConfig.blockHeight / 2, 28);
    const fontSize = Math.max(baseFontSize, 16); // 最小字体16rpx

    words.forEach((word, index) => {
      // 英文方块
      blocks.push({
        id: id,
        text: word.english,
        type: 'english',
        pairId: index,
        matched: false,
        selected: false,
        showEffect: false,
        points: 0,
        x: shuffledPositions[id].x,
        y: shuffledPositions[id].y,
        width: gridConfig.blockWidth,
        height: gridConfig.blockHeight,
        fontSize: fontSize,
        visible: true
      });
      id++;

      // 中文方块
      blocks.push({
        id: id,
        text: word.chinese,
        type: 'chinese',
        pairId: index,
        matched: false,
        selected: false,
        showEffect: false,
        points: 0,
        x: shuffledPositions[id].x,
        y: shuffledPositions[id].y,
        width: gridConfig.blockWidth,
        height: gridConfig.blockHeight,
        fontSize: fontSize,
        visible: true
      });
      id++;
    });
    
    this.setData({
      wordBlocks: blocks
    });
  },

  // 动态计算网格配置
  calculateGridConfig(wordCount) {
    // 容器尺寸（单位：rpx）
    const containerWidth = 710;  // 750 - 40 (左右边距)
    const containerHeight = 1200;
    
    // 计算需要的总位置数（词汇数 * 2）
    const totalPositions = wordCount * 2;
    
    console.log('计算网格配置:', { wordCount, totalPositions, containerWidth, containerHeight });
    
    // 基础配置
    const baseConfig = {
      paddingX: 20,
      paddingY: 20,
      spacingX: 12,
      spacingY: 12
    };
    
    // 根据词汇数量确定最佳列数和行数
    let bestConfig = null;
    let maxCardSize = 0;
    
    // 尝试不同的列数配置
    for (let cols = 2; cols <= Math.min(8, totalPositions); cols++) {
      const rows = Math.ceil(totalPositions / cols);
      
      // 计算可用空间
      const availableWidth = containerWidth - baseConfig.paddingX * 2 - baseConfig.spacingX * (cols - 1);
      const availableHeight = containerHeight - baseConfig.paddingY * 2 - baseConfig.spacingY * (rows - 1);
      
      // 计算方块尺寸
      const blockWidth = Math.floor(availableWidth / cols);
      const blockHeight = Math.floor(availableHeight / rows);
      
      // 检查是否有效（最小尺寸限制）
      if (blockWidth >= 50 && blockHeight >= 30) {
        const cardSize = blockWidth * blockHeight;
        const utilization = totalPositions / (cols * rows);
        
        // 优先考虑卡片大小和空间利用率
        const score = cardSize * utilization;
        
        console.log(`尝试配置 ${cols}x${rows}:`, {
          blockWidth,
          blockHeight,
          cardSize,
          utilization,
          score
        });
        
        if (score > maxCardSize) {
          maxCardSize = score;
          bestConfig = {
            cols,
            rows,
            blockWidth,
            blockHeight,
            paddingX: baseConfig.paddingX,
            paddingY: baseConfig.paddingY,
            spacingX: baseConfig.spacingX,
            spacingY: baseConfig.spacingY
          };
        }
      }
    }
    
    // 特殊处理：针对不同词汇数量优化布局
    if (!bestConfig) {
      let cols, rows;
      
      if (totalPositions <= 4) {
        cols = 2;
        rows = Math.ceil(totalPositions / cols);
      } else if (totalPositions <= 12) {
        cols = 3;
        rows = Math.ceil(totalPositions / cols);
      } else if (totalPositions <= 24) {
        cols = 4;
        rows = Math.ceil(totalPositions / cols);
      } else if (totalPositions <= 40) {
        cols = 5;
        rows = Math.ceil(totalPositions / cols);
      } else {
        cols = 6;
        rows = Math.ceil(totalPositions / cols);
      }
      
      const availableWidth = containerWidth - baseConfig.paddingX * 2 - baseConfig.spacingX * (cols - 1);
      const availableHeight = containerHeight - baseConfig.paddingY * 2 - baseConfig.spacingY * (rows - 1);
      
      bestConfig = {
        cols,
        rows,
        blockWidth: Math.floor(availableWidth / cols),
        blockHeight: Math.floor(availableHeight / rows),
        paddingX: baseConfig.paddingX,
        paddingY: baseConfig.paddingY,
        spacingX: baseConfig.spacingX,
        spacingY: baseConfig.spacingY
      };
    }
    
    console.log('最终选择的配置:', bestConfig);
    
    // 确保卡片不会太小
    if (bestConfig.blockWidth < 80) {
      bestConfig.blockWidth = Math.max(80, bestConfig.blockWidth);
    }
    if (bestConfig.blockHeight < 40) {
      bestConfig.blockHeight = Math.max(40, bestConfig.blockHeight);
    }
    
    return bestConfig;
  },

  // 计算最大可容纳的词汇数量
  calculateMaxWords() {
    const containerWidth = 710;
    const containerHeight = 1200;
    const minBlockWidth = 80;
    const minBlockHeight = 40;
    const paddingX = 20;
    const paddingY = 10;
    const spacingX = 15;
    const spacingY = 10;
    
    // 计算最大可能的行列数
    const maxCols = Math.floor((containerWidth - paddingX * 2 + spacingX) / (minBlockWidth + spacingX));
    const maxRows = Math.floor((containerHeight - paddingY * 2 + spacingY) / (minBlockHeight + spacingY));
    
    // 最大位置数 = 最大行数 * 最大列数
    const maxPositions = maxCols * maxRows;
    
    // 最大词汇数 = 最大位置数 / 2
    const maxWords = Math.floor(maxPositions / 2);
    
    console.log('计算最大词汇数:', {
      containerWidth,
      containerHeight,
      maxCols,
      maxRows,
      maxPositions,
      maxWords
    });
    
    return maxWords;
  },

  // 移除自动重排功能，保持游戏难度适中

  // 重新分配方块位置（仅用于未匹配的方块）
  reassignBlockPositions(unmatched) {
    // 使用与生成时相同的网格配置（单位：rpx）
    const totalWords = this.data.totalPairs;
    const gridConfig = this.calculateGridConfig(totalWords);

    // 创建位置网格
    const positions = [];
    for (let row = 0; row < gridConfig.rows; row++) {
      for (let col = 0; col < gridConfig.cols; col++) {
        if (positions.length < unmatched.length) {
          positions.push({
            x: gridConfig.paddingX + col * (gridConfig.blockWidth + gridConfig.spacingX),
            y: gridConfig.paddingY + row * (gridConfig.blockHeight + gridConfig.spacingY)
          });
        }
      }
    }

    // 随机打乱位置
    const shuffledPositions = this.shuffleArray(positions);
    
    // 分配新位置给未匹配的方块
    unmatched.forEach((block, index) => {
      if (shuffledPositions[index]) {
        block.x = shuffledPositions[index].x;
        block.y = shuffledPositions[index].y;
      }
    });
  },

  // 选择游戏模式
  selectMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setGameMode(mode);
  },

  // 设置游戏模式
  setGameMode(mode) {
    let modeText = '';
    let timeLeft = 0;
    
    switch (mode) {
      case 'practice':
        modeText = '练习模式';
        break;
      case '10':
        modeText = '闪电模式';
        timeLeft = 10;
        break;
      case '30':
        modeText = '火速模式';
        timeLeft = 30;
        break;
      case '60':
        modeText = '标准模式';
        timeLeft = 60;
        break;
      case '300':
        modeText = '学习模式';
        timeLeft = 300;
        break;
    }

    const now = Date.now();
    this.setData({
      gameMode: mode,
      modeText,
      timeLeft,
      gameStarted: true,
      startTime: now
    });

    console.log('🚀 消消乐游戏开始，记录开始时间:', new Date(now).toLocaleString());

    // 如果是测试模式（非练习模式），启用防作弊锁定
    if (mode !== 'practice') {
      this.enableExamMode(modeText);
      this.startTimer();
    }

    // 触觉反馈
    wx.vibrateShort({
      type: 'medium'
    });
  },

  // 启用考试模式
  enableExamMode(modeText) {
    console.log('启用消消乐考试模式:', modeText);
    
    // 启用完整的防作弊锁定功能
    examLock.enable({
      examName: `消消乐${modeText}`,
      onBackAttempt: (attemptCount) => {
        // 用户尝试退出时的回调
        console.log(`用户第${attemptCount}次尝试退出消消乐测试`);

        // 不暂停计时器，让游戏继续进行
        console.log('⏰ 消消乐计时器继续运行，游戏正常进行');

        wx.showToast({
          title: `检测到退出尝试(${attemptCount}/3)`,
          icon: 'none',
          duration: 1500
        });
      },
      onExitConfirm: () => {
        // 用户确认退出时的回调
        console.log('用户确认强制退出消消乐测试，游戏将继续在后台进行');

        // 记录测试被中断
        this.recordTestInterruption();

        // 显示退出提示，说明游戏继续进行
        wx.showToast({
          title: '游戏继续后台进行，将自动提交成绩',
          icon: 'none',
          duration: 3000
        });

        // 延迟执行退出逻辑
        setTimeout(() => {
          wx.navigateBack({
            delta: 1,
            fail: () => {
              wx.reLaunch({
                url: '/pages/task/create/create',
                fail: () => {
                  wx.switchTab({
                    url: '/pages/index/index'
                  });
                }
              });
            }
          });
        }, 300);
      }
    });
    
    // 设置考试状态
    this.setData({
      examMode: true
    });
    
    console.log('消消乐锁定模式已启用');
  },

  // 禁用考试模式
  disableExamMode() {
    console.log('禁用消消乐考试模式');
    
    // 禁用防作弊锁定功能
    examLock.disable();
    
    // 清除考试状态
    this.setData({
      examMode: false
    });
    
    console.log('消消乐锁定模式已禁用');
  },

  // 恢复考试模式（测试中断后恢复）
  resumeExamMode() {
    console.log('🔄 恢复消消乐考试模式');

    // 直接启用完整防作弊锁定，不显示确认对话框
    examLock.enable({
      examName: '消消乐测试',
      onBackAttempt: (attemptCount) => {
        console.log(`用户第${attemptCount}次尝试退出消消乐测试`);

        // 不暂停计时器，让游戏继续进行
        console.log('⏰ 消消乐计时器继续运行，游戏正常进行');
      },
      onExitConfirm: () => {
        console.log('用户确认退出消消乐测试，但测试继续在后台进行');
        this.recordTestInterruption();

        wx.showToast({
          title: '游戏继续后台进行，将自动提交成绩',
          icon: 'none',
          duration: 3000
        });

        setTimeout(() => {
          wx.navigateBack({
            delta: 1,
            fail: () => {
              wx.reLaunch({
                url: '/pages/task/create/create',
                fail: () => {
                  wx.switchTab({
                    url: '/pages/index/index'
                  });
                }
              });
            }
          });
        }, 300);
      }
    });

    // 设置考试状态
    this.setData({
      examMode: true
    });

    // 恢复测试状态，计算已经过去的时间
    this.resumeGameWithTimeCalculation();
  },

  // 恢复游戏状态
  restoreGameState(restoredState) {
    console.log('🔄 恢复消消乐游戏状态:', restoredState);

    // 检查是否有有效的游戏数据
    const hasValidGameData = restoredState.startTime &&
                            restoredState.gameMode &&
                            restoredState.gameStarted;

    if (hasValidGameData) {
      // 恢复游戏数据
      this.setData({
        gameStarted: restoredState.gameStarted,
        startTime: restoredState.startTime,
        gameMode: restoredState.gameMode,
        timeLeft: restoredState.timeLeft,
        score: restoredState.score || 0,
        combo: restoredState.combo || 0,
        maxCombo: restoredState.maxCombo || 0,
        matchedPairs: restoredState.matchedPairs || 0,
        totalPairs: restoredState.totalPairs || this.data.totalPairs,
        timerStartTime: restoredState.timerStartTime,
        examMode: true,
        gameCompleted: restoredState.gameCompleted || false,
        gameEnded: restoredState.gameEnded || false
      });

      console.log('🔄 游戏已开始，直接恢复游戏状态');
      console.log('🔄 恢复的数据:', {
        startTime: restoredState.startTime,
        gameMode: restoredState.gameMode,
        timeLeft: restoredState.timeLeft,
        score: restoredState.score,
        matchedPairs: restoredState.matchedPairs,
        totalPairs: restoredState.totalPairs
      });

      // 直接恢复游戏
      this.resumeExamMode();
    } else {
      console.log('🔄 没有有效的游戏数据，等待用户选择模式');
      // 游戏未开始，等待用户选择模式
    }
  },

  // 恢复游戏并计算时间
  resumeGameWithTimeCalculation() {
    const now = Date.now();
    const { startTime, gameMode, gameStarted, gameCompleted } = this.data;
    const totalGameTime = gameMode === 'easy' ? 180 : gameMode === 'medium' ? 120 : 60;

    // 如果游戏已经完成，直接显示结果
    if (gameCompleted) {
      wx.showToast({
        title: '游戏已完成',
        icon: 'success',
        duration: 2000
      });
      return;
    }

    if (startTime && gameStarted) {
      // 计算游戏总共已经过去的时间
      const totalElapsedSeconds = Math.floor((now - startTime) / 1000);
      const timeLeft = Math.max(0, totalGameTime - totalElapsedSeconds);

      console.log(`🕐 恢复消消乐时间计算:`, {
        totalElapsedSeconds,
        timeLeft,
        totalGameTime,
        gameMode
      });

      // 检查游戏是否已经结束
      if (totalElapsedSeconds >= totalGameTime) {
        // 游戏时间已到，直接结束游戏
        wx.showModal({
          title: '游戏已结束',
          content: '游戏时间已到，系统已自动结算您的成绩。',
          showCancel: false,
          confirmText: '查看结果',
          success: () => {
            this.endGame(false); // false表示时间到结束
          }
        });
        return;
      }

      // 还有时间，恢复计时器
      this.setData({
        timeLeft: timeLeft
      });

      this.startTimer(timeLeft);

      wx.showToast({
        title: `游戏已恢复，剩余${timeLeft}秒`,
        icon: 'success',
        duration: 2000
      });
    } else {
      // 没有保存的时间信息或游戏未开始，重新开始计时器
      if (gameStarted) {
        this.startTimer();
      }

      wx.showToast({
        title: '游戏已恢复',
        icon: 'success',
        duration: 2000
      });
    }

    console.log('✅ 消消乐恢复完成');
  },

  // 记录测试中断
  recordTestInterruption(skipExamLockSave = false) {
    const now = Date.now();
    const interruptionData = {
      timestamp: now,
      testType: 'puzzle',
      gameMode: this.data.gameMode,
      stage: '游戏进行中',
      currentScore: this.data.score,
      matchedPairs: this.data.matchedPairs,
      totalPairs: this.data.totalPairs,
      timeLeft: this.data.timeLeft,
      // 时间相关状态，用于恢复时计算
      timerStartTime: this.data.timerStartTime,
      startTime: this.data.startTime,
      gameStarted: this.data.gameStarted
    };

    console.log('记录消消乐测试中断:', interruptionData);

    // 只有在不是被examLock调用时才保存到examLock（避免重复保存）
    if (!skipExamLockSave && examLock && typeof examLock.saveCurrentTestState === 'function') {
      examLock.saveCurrentTestState(interruptionData);
    }

    // 保存中断数据到本地存储
    try {
      const interruptions = wx.getStorageSync('test_interruptions') || [];
      interruptions.push(interruptionData);
      wx.setStorageSync('test_interruptions', interruptions);
    } catch (error) {
      console.error('保存中断记录失败:', error);
    }

    return interruptionData;
  },

  // 开始计时器
  startTimer(remainingTime = null) {
    // 防止重复启动定时器
    if (this.timer) {
      console.log('定时器已存在，先清除旧定时器');
      clearInterval(this.timer);
      this.timer = null;
    }

    // 设置初始时间（如果有剩余时间则使用剩余时间）
    const initialTime = remainingTime !== null ? remainingTime : this.data.timeLeft;
    const gameStartTime = this.data.startTime || Date.now();
    const totalGameTime = this.data.gameMode === 'easy' ? 180 : this.data.gameMode === 'medium' ? 120 : 60;

    // 计算计时器开始的绝对时间
    const timerStartTime = gameStartTime + (totalGameTime - initialTime) * 1000;

    this.setData({
      timeLeft: initialTime,
      timerStartTime: timerStartTime // 保存计时器开始的绝对时间
    });

    console.log('启动新定时器，当前时间:', initialTime);
    this.timer = setInterval(() => {
      // 基于绝对时间计算剩余时间，确保后台时间继续
      const now = Date.now();
      const elapsedSeconds = Math.floor((now - this.data.timerStartTime) / 1000);
      const timeLeft = Math.max(0, totalGameTime - elapsedSeconds);

      this.setData({ timeLeft });

      if (timeLeft <= 0) {
        console.log('时间到，游戏结束');
        this.endGame(false); // 时间到，游戏结束
      } else if (timeLeft <= 5) {
        // 最后5秒震动提醒
        wx.vibrateShort({
          type: 'light'
        });
      }
    }, 1000);
  },

  // 选择方块
  selectBlock(e) {
    const blockId = parseInt(e.currentTarget.dataset.id);
    const blocks = [...this.data.wordBlocks];
    const clickedBlock = blocks.find(b => b.id === blockId);
    
    // 防止点击已匹配、不可见或正在处理中的方块
    if (!clickedBlock || clickedBlock.matched || !clickedBlock.visible || clickedBlock.processing) return;

    console.log('点击方块:', {
      id: clickedBlock.id,
      text: clickedBlock.text,
      type: clickedBlock.type,
      pairId: clickedBlock.pairId,
      selectedBlock: this.data.selectedBlock
    });

    // 如果已有选中的方块
    if (this.data.selectedBlock !== null) {
      const selectedBlock = blocks.find(b => b.id === this.data.selectedBlock);
      
      // 防止重复处理已经选中的方块
      if (!selectedBlock || selectedBlock.matched || !selectedBlock.visible || selectedBlock.processing) {
        console.log('清除无效的选中状态');
        // 清除无效的选中状态，重新选择当前方块
        clickedBlock.selected = true;
        this.setData({ 
          wordBlocks: blocks,
          selectedBlock: clickedBlock.id 
        });
        return;
      }
      
      if (selectedBlock.id === clickedBlock.id) {
        // 点击同一个方块，取消选择
        console.log('取消选择同一方块');
        selectedBlock.selected = false;
        this.setData({
          wordBlocks: blocks,
          selectedBlock: null
        });
        return;
      }

      // 检查是否匹配
      const isMatch = this.checkMatch(selectedBlock, clickedBlock);
      console.log('检查匹配结果:', {
        selected: { id: selectedBlock.id, text: selectedBlock.text, type: selectedBlock.type, pairId: selectedBlock.pairId },
        clicked: { id: clickedBlock.id, text: clickedBlock.text, type: clickedBlock.type, pairId: clickedBlock.pairId },
        isMatch: isMatch
      });

      if (isMatch) {
        // 立即标记为正在处理，防止重复点击
        selectedBlock.processing = true;
        clickedBlock.processing = true;
        selectedBlock.selected = false; // 立即取消选中状态
        clickedBlock.selected = false;
        
        // 立即更新状态，防止用户重复点击
        this.setData({
          wordBlocks: blocks,
          selectedBlock: null
        }, () => {
          // 在setData回调中处理匹配
          this.processMatch(selectedBlock, clickedBlock, blocks);
        });
      } else {
        // 不匹配，记录错误尝试（仅在非练习模式下）
        if (this.data.gameMode !== 'practice') {
          this.recordWrongAttempt(selectedBlock, clickedBlock);
        }

        // 添加错误匹配的反馈效果
        this.playWrongMatchEffect();

        // 重新选择
        selectedBlock.selected = false;
        clickedBlock.selected = true;
        this.setData({
          wordBlocks: blocks,
          selectedBlock: clickedBlock.id,
          combo: 0 // 重置连击
        });
      }
    } else {
      // 选择第一个方块
      console.log('选择第一个方块');
      clickedBlock.selected = true;
      this.setData({
        wordBlocks: blocks,
        selectedBlock: clickedBlock.id
      });
    }

    // 触觉反馈
    wx.vibrateShort({
      type: 'light'
    });
  },

  // 检查匹配
  checkMatch(block1, block2) {
    // 确保两个方块都存在且有效
    if (!block1 || !block2) {
      console.log('checkMatch: 方块不存在');
      return false;
    }
    
    // 确保两个方块都有必要的属性
    if (block1.pairId === undefined || block2.pairId === undefined || 
        !block1.type || !block2.type) {
      console.log('checkMatch: 缺少必要属性', {
        block1: { pairId: block1.pairId, type: block1.type },
        block2: { pairId: block2.pairId, type: block2.type }
      });
      return false;
    }
    
    // 检查匹配条件：相同的pairId且不同的type
    const isMatch = block1.pairId === block2.pairId && block1.type !== block2.type;
    console.log('checkMatch详细信息:', {
      block1: { id: block1.id, text: block1.text, pairId: block1.pairId, type: block1.type },
      block2: { id: block2.id, text: block2.text, pairId: block2.pairId, type: block2.type },
      pairIdMatch: block1.pairId === block2.pairId,
      typeDifferent: block1.type !== block2.type,
      finalResult: isMatch
    });
    
    return isMatch;
  },

  // 处理匹配
  processMatch(block1, block2, blocks) {
    console.log('开始处理匹配:', {
      block1: { id: block1.id, text: block1.text },
      block2: { id: block2.id, text: block2.text }
    });

    // 计算得分
    const combo = this.data.combo + 1;
    const basePoints = 100;
    const comboBonus = combo * 20;
    const timeBonus = this.data.gameMode !== 'practice' ? Math.max(0, this.data.timeLeft) : 0;
    const points = basePoints + comboBonus + timeBonus;

    // 在原始blocks数组中找到对应的方块并设置匹配状态
    const currentBlocks = [...this.data.wordBlocks];
    const block1Index = currentBlocks.findIndex(b => b.id === block1.id);
    const block2Index = currentBlocks.findIndex(b => b.id === block2.id);
    
    if (block1Index === -1 || block2Index === -1) {
      console.error('无法找到要匹配的方块');
      return;
    }

    // 设置匹配状态，但保持可见以显示动画效果
    currentBlocks[block1Index].matched = true;
    currentBlocks[block1Index].selected = false;
    currentBlocks[block1Index].showEffect = true;
    currentBlocks[block1Index].points = points;
    currentBlocks[block1Index].processing = true;
    
    currentBlocks[block2Index].matched = true;
    currentBlocks[block2Index].selected = false;
    currentBlocks[block2Index].showEffect = true;
    currentBlocks[block2Index].points = points;
    currentBlocks[block2Index].processing = true;

    // 更新游戏状态
    const matchedPairs = this.data.matchedPairs + 1;
    const score = this.data.score + points;
    const maxCombo = Math.max(this.data.maxCombo, combo);

    console.log('更新游戏状态:', { matchedPairs, score, combo });

    // 立即更新状态，确保UI响应
    this.setData({
      wordBlocks: currentBlocks,
      selectedBlock: null,
      score,
      combo,
      maxCombo,
      matchedPairs
    });

    // 播放匹配音效和触觉反馈
    this.playMatchEffect(combo);

    // 显示飘分特效
    this.showFloatingScore(points, combo);

    // 1秒后让方块消失（保留动画效果）
    setTimeout(() => {
      const updatedBlocks = [...this.data.wordBlocks];
      const finalBlock1Index = updatedBlocks.findIndex(b => b.id === block1.id);
      const finalBlock2Index = updatedBlocks.findIndex(b => b.id === block2.id);
      
      if (finalBlock1Index !== -1) {
        updatedBlocks[finalBlock1Index].showEffect = false;
        updatedBlocks[finalBlock1Index].visible = false;
        updatedBlocks[finalBlock1Index].processing = false;
      }
      
      if (finalBlock2Index !== -1) {
        updatedBlocks[finalBlock2Index].showEffect = false;
        updatedBlocks[finalBlock2Index].visible = false;
        updatedBlocks[finalBlock2Index].processing = false;
      }
      
      console.log('方块消失处理完成');
      this.setData({ wordBlocks: updatedBlocks });
    }, 1000);

    // 检查游戏是否完成
    if (matchedPairs === this.data.totalPairs) {
      console.log('游戏完成！');
      // 显示过关特效
      setTimeout(() => {
        this.showLevelCompleteEffect();
      }, 1200);
      
      // 延迟显示结果弹窗
      setTimeout(() => {
        this.endGame(true); // 完成所有配对，游戏胜利
      }, 2800);
    }
  },



  // 重排方块
  shuffleBlocks() {
    const blocks = [...this.data.wordBlocks];
    const visibleUnmatched = blocks.filter(b => b.visible && !b.matched);
    
    // 重新分配位置给可见的未匹配方块
    this.reassignBlockPositions(visibleUnmatched);
    
    // 清除所有选择状态
    blocks.forEach(block => {
      if (block.visible) {
        block.selected = false;
      }
    });
    
    this.setData({
      wordBlocks: blocks,
      selectedBlock: null
    });

    wx.showToast({
      title: '已重新排列',
      icon: 'success'
    });
  },

  // 显示提示
  showHint() {
    const blocks = this.data.wordBlocks;
    const visibleUnmatched = blocks.filter(b => b.visible && !b.matched);
    
    if (visibleUnmatched.length < 2) return;

    // 找到第一对匹配的方块
    for (let i = 0; i < visibleUnmatched.length; i++) {
      for (let j = i + 1; j < visibleUnmatched.length; j++) {
        if (this.checkMatch(visibleUnmatched[i], visibleUnmatched[j])) {
          // 高亮显示这一对
          const updatedBlocks = [...blocks];
          visibleUnmatched[i].selected = true;
          visibleUnmatched[j].selected = true;
          
          this.setData({ wordBlocks: updatedBlocks });
          
          // 3秒后取消高亮
          setTimeout(() => {
            visibleUnmatched[i].selected = false;
            visibleUnmatched[j].selected = false;
            this.setData({ 
              wordBlocks: updatedBlocks,
              selectedBlock: null
            });
          }, 3000);
          
          wx.showToast({
            title: '提示：这两个可以配对',
            icon: 'none'
          });
          return;
        }
      }
    }

    wx.showToast({
      title: '暂无可配对的方块',
      icon: 'none'
    });
  },

  // 暂停游戏
  pauseGame() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    
    this.setData({
      showPause: true
    });
  },

  // 恢复游戏
  resumeGame() {
    this.setData({
      showPause: false
    });
    
    if (this.data.gameMode !== 'practice' && this.data.timeLeft > 0) {
      this.startTimer();
    }
  },

  // 隐藏暂停弹窗
  hidePauseModal() {
    this.resumeGame();
  },

  // 重新开始游戏
  restartGame() {
    console.log('=== 重新开始游戏 ===');
    console.log('当前游戏模式:', { isCustomMode: this.data.isCustomMode, gameMode: this.data.gameMode });
    
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }

    // 重置游戏状态
    this.setData({
      showPause: false,
      showResult: false,
      score: 0,
      combo: 0,
      maxCombo: 0,
      matchedPairs: 0,
      selectedBlock: null,
      startTime: Date.now(),
      gameEnded: false // 重置游戏结束状态
    });

    // 如果是自定义模式，重新生成当前关卡的词汇方块
    if (this.data.isCustomMode) {
      const app = getApp();
      const gameData = app.globalData.eliminationGameData;
      
      if (gameData && gameData.words) {
        console.log('重新开始自定义游戏，词汇数量:', gameData.words.length);
        this.generateWordBlocks(gameData.words);
        
        // 如果有游戏模式，重新设置计时器
        if (this.data.gameMode && this.data.gameMode !== 'practice') {
          this.setGameMode(this.data.gameMode);
        } else {
          // 练习模式直接标记为已开始
          this.setData({ gameStarted: true });
        }
      } else {
        wx.showToast({ title: '游戏数据丢失', icon: 'error' });
        wx.navigateBack();
      }
    } else {
      // 默认模式，使用原来的逻辑
      this.setData({ gameStarted: false });
      this.initializeGame();
    }
  },

  // 结束游戏
  endGame(isWin) {
    // 防止重复结束游戏
    if (this.data.gameEnded) {
      console.log('游戏已结束，忽略重复调用');
      return;
    }
    
    console.log('开始结束游戏，获胜状态:', isWin);
    this.setData({ gameEnded: true });
    
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }

    // 如果是测试模式，先解除考试锁定
    if (this.data.gameMode !== 'practice' && this.data.examMode) {
      this.disableExamMode();
    }

    const endTime = Date.now();
    const usedTime = this.formatTime(endTime - this.data.startTime);
    
    // 如果是竞赛模式，提交竞赛结果
    if (this.data.competitionMode && this.data.competitionId) {
      this.submitCompetitionResult(isWin, usedTime);
    }
    
    // 如果是分享模式，保存测试结果
    if (this.data.isSharedMode && this.data.shareId) {
      this.saveSharedTestResult(isWin, usedTime);
    } else {
      // 自己的测试，更新学习进度
      this.updatePuzzleLearningProgress(isWin, usedTime);
    }

    // 测试模式下收集错题（未匹配的词汇对和错误尝试）
    if (this.data.gameMode !== 'practice') {
      this.collectMistakesToErrorBook(isWin);
    }
    
    // 检查是否还有下一关（普通多关卡模式）
    const hasNextLevel = this.data.isCustomMode &&
                        this.data.currentGroup < this.data.totalGroups &&
                        isWin;

    // 检查是否是多关卡竞赛
    const app = getApp();
    const gameData = app.globalData.eliminationGameData || {};
    const isLevelCompetition = gameData.masterCompetitionId;

    // 设置基本结果显示数据
    const resultData = {
      showResult: true,
      resultIcon: isWin ? '🏆' : '⏰',
      resultTitle: isLevelCompetition ? '关卡完成！' :
                   hasNextLevel ? `第${this.data.currentGroup}关完成！` :
                   (isWin ? '恭喜完成！' : '时间到！'),
      finalScore: this.data.score,
      usedTime,
      showNextLevelBtn: hasNextLevel,
      showNextCompetitionLevelBtn: false, // 先设为false，异步检查后更新
      masterCompetitionId: gameData.masterCompetitionId,
      nextLevelNumber: null
    };

    console.log('设置结果弹窗数据:', resultData);
    this.setData(resultData);

    // 如果是多关卡竞赛且获胜，异步检查是否有下一关
    if (isLevelCompetition && isWin) {
      this.checkNextCompetitionLevel().then(hasNext => {
        console.log('异步检查下一关结果:', hasNext);
        this.setData({
          showNextCompetitionLevelBtn: hasNext
        });
      }).catch(error => {
        console.error('检查下一关失败:', error);
        this.setData({
          showNextCompetitionLevelBtn: false
        });
      });
    }
  },

  // 提交竞赛结果
  async submitCompetitionResult(isWin, usedTime) {
    try {
      wx.showLoading({
        title: '提交结果...',
        mask: true
      });

      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo') || {};
      
      // 转换时间格式（从"X分X秒"或"X秒"转换为秒数）
      let durationInSeconds = 0;
      if (usedTime.includes('分')) {
        const match = usedTime.match(/(\d+)分(\d+)秒/);
        if (match) {
          durationInSeconds = parseInt(match[1]) * 60 + parseInt(match[2]);
        }
      } else {
        const match = usedTime.match(/(\d+)秒/);
        if (match) {
          durationInSeconds = parseInt(match[1]);
        }
      }
      
      // 调用云函数提交竞赛结果
      const result = await wx.cloud.callFunction({
        name: 'submitCompetitionResult',
        data: {
          competitionId: this.data.competitionId,
          mode: 'elimination',
          score: this.data.score,
          duration: durationInSeconds,
          eliminatedPairs: this.data.matchedPairs,
          totalPairs: this.data.totalPairs, // 添加总配对数
          isWin: isWin, // 添加是否获胜参数
          userName: userInfo.nickName || userInfo.username || '匿名用户'
        }
      });

      wx.hideLoading();

      if (result.result.success) {
        // 显示提交成功的提示
        if (result.result.isNewRecord) {
          wx.showToast({
            title: '新纪录！成绩已更新',
            icon: 'success',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: result.result.message || '成绩已提交',
            icon: 'success',
            duration: 2000
          });
        }
        
        // 获取云函数返回的完成状态，用于关卡解锁判定
        const isCompleted = result.result.completed !== undefined ? 
          result.result.completed : isWin;
        
        console.log('消消乐竞赛结果提交成功', {
          isWin: isWin,
          cloudCompleted: result.result.completed,
          finalCompleted: isCompleted
        });
        
        // 如果是多关卡竞赛且通过了当前关卡，通知关卡选择页面刷新状态
        if (isCompleted) {
          const app = getApp();
          const gameData = app.globalData.eliminationGameData || {};
          
          if (gameData.masterCompetitionId) {
            // 设置需要刷新关卡选择页面的标志
            app.globalData.needRefreshLevelSelect = true;
            console.log('消消乐关卡完成，已设置刷新标志');
            
            // 尝试通知当前打开的关卡选择页面立即刷新
            const pages = getCurrentPages();
            for (let i = pages.length - 1; i >= 0; i--) {
              const page = pages[i];
              if (page.route === 'pages/competition/level-select/level-select' && 
                  typeof page.refreshLevelStatus === 'function') {
                console.log('找到关卡选择页面，触发立即刷新');
                page.refreshLevelStatus();
                break;
              }
            }
          }
        }
      } else {
        throw new Error(result.result.message || '提交失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('提交竞赛结果失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 保存分享测试结果
  async saveSharedTestResult(isWin, usedTime) {
    try {
      wx.showLoading({
        title: '提交结果...',
        mask: true
      });

      const currentUser = wx.getStorageSync('userInfo') || {};
      const { shareId, currentGroup = 1 } = this.data;

      // 获取用户昵称，尝试多种可能的字段
      let nickName = '匿名用户';
      let avatarUrl = '';

      if (currentUser.nickName) {
        nickName = currentUser.nickName;
      } else if (currentUser.wechatInfo?.nickName) {
        nickName = currentUser.wechatInfo.nickName;
      } else if (currentUser.username) {
        nickName = currentUser.username;
      } else if (currentUser.phone) {
        nickName = `用户${currentUser.phone.slice(-4)}`;
      }

      if (currentUser.avatarUrl) {
        avatarUrl = currentUser.avatarUrl;
      } else if (currentUser.wechatInfo?.avatarUrl) {
        avatarUrl = currentUser.wechatInfo.avatarUrl;
      }

      console.log('消消乐用户信息调试:', {
        currentUser: currentUser,
        extractedNickName: nickName,
        extractedAvatarUrl: avatarUrl
      });

      // 收集错词数据
      const mistakes = this.collectMistakesForSave(isWin);

      // 构建测试结果数据
      const submitData = {
        shareId: shareId,
        levelId: currentGroup,
        testResult: {
          score: this.data.score,
          accuracy: parseFloat(((this.data.matchedPairs / this.data.totalPairs) * 100).toFixed(1)),
          correctCount: this.data.matchedPairs,
          wrongCount: this.data.totalPairs - this.data.matchedPairs,
          mistakes: mistakes,
          timeSpent: this.parseTimeToSeconds(usedTime),
          totalQuestions: this.data.totalPairs,
          // 消消乐特有字段
          isWin: isWin,
          matchedPairs: this.data.matchedPairs,
          maxCombo: this.data.maxCombo
        },
        userInfo: {
          nickName: nickName,
          avatarUrl: avatarUrl
        }
      };

      console.log('提交消消乐分享测试结果:', submitData);

      // 优先提交到云端
      let cloudSubmitted = false;

      try {
        const result = await wx.cloud.callFunction({
          name: 'submitShareTestResult',
          data: submitData
        });

        if (result.result.success) {
          cloudSubmitted = true;
          console.log('云端提交成功:', result.result.data);
        }
      } catch (cloudError) {
        console.log('云端提交失败，使用本地存储:', cloudError);
      }

      // 同时保存到本地存储作为备份
      const shareTests = wx.getStorageSync('shareTests') || {};
      const shareTestData = shareTests[this.data.shareId];

      if (shareTestData) {
        const result = {
          completedAt: new Date().getTime(),
          playerName: currentUser.nickName || '匿名玩家',
          isWin: isWin,
          score: this.data.score,
          usedTime: usedTime,
          matchedPairs: this.data.matchedPairs,
          totalPairs: this.data.totalPairs,
          maxCombo: this.data.maxCombo,
          gameMode: this.data.gameMode,
          mistakes: mistakes,

          // 分享测试结果的标准字段
          testMode: 'elimination',
          correctCount: this.data.matchedPairs,
          wrongCount: this.data.totalPairs - this.data.matchedPairs,
          totalQuestions: this.data.totalPairs,
          accuracy: ((this.data.matchedPairs / this.data.totalPairs) * 100).toFixed(1),
          duration: usedTime,
          shareMode: 'share',
          shareId: this.data.shareId,
          timestamp: Date.now(),

          // 参与者信息
          participantInfo: currentUser,
          participantOpenid: currentUser.openid,
          creatorOpenid: shareTestData.creatorInfo?.openid
        };

        shareTestData.results.push(result);

        // 更新被分享人的测试次数
        if (shareTestData.visitors && currentUser.openid) {
          const visitor = shareTestData.visitors.find(v => v.openid === currentUser.openid);
          if (visitor) {
            visitor.testCount = (visitor.testCount || 0) + 1;
            visitor.lastTestTime = Date.now();
            visitor.bestScore = Math.max(visitor.bestScore || 0, this.data.score);
            visitor.totalTests = (visitor.totalTests || 0) + 1;
            visitor.totalScore = (visitor.totalScore || 0) + this.data.score;
            visitor.averageScore = Math.round(visitor.totalScore / visitor.totalTests);
          }
        }

        shareTests[this.data.shareId] = shareTestData;
        wx.setStorageSync('shareTests', shareTests);

        // 同时保存到测试结果统一存储
        this.saveToTestResults(result);

        console.log('分享测试结果已保存:', result);
      }

      wx.hideLoading();

      // 显示提交状态
      if (cloudSubmitted) {
        wx.showToast({
          title: '结果已提交',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '已保存到本地',
          icon: 'none'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('保存分享测试结果失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      });
    }
  },

  // 解析时间字符串为秒数
  parseTimeToSeconds(timeStr) {
    if (typeof timeStr === 'number') return timeStr;
    if (!timeStr) return 0;

    // 解析 "MM:SS" 格式
    const parts = timeStr.split(':');
    if (parts.length === 2) {
      const minutes = parseInt(parts[0]) || 0;
      const seconds = parseInt(parts[1]) || 0;
      return minutes * 60 + seconds;
    }

    return 0;
  },

  // 收集错词数据用于保存
  collectMistakesForSave(isWin) {
    const mistakes = [];
    
    // 1. 收集错误的匹配尝试
    this.data.wrongAttempts.forEach(attempt => {
      const correctPair1 = this.findCorrectPair(attempt.block1);
      const correctPair2 = this.findCorrectPair(attempt.block2);
      
      if (correctPair1) {
        mistakes.push({
          word: attempt.block1.type === 'english' ? attempt.block1.text : correctPair1,
          meaning: attempt.block1.type === 'chinese' ? attempt.block1.text : this.findChinesePair(attempt.block1.text),
          userAnswer: attempt.attempted,
          correctAnswer: `${attempt.block1.type === 'english' ? attempt.block1.text : correctPair1} - ${attempt.block1.type === 'chinese' ? attempt.block1.text : this.findChinesePair(attempt.block1.text)}`,
          mistakeSource: 'wrong_attempt',
          english: attempt.block1.type === 'english' ? attempt.block1.text : correctPair1,
          chinese: attempt.block1.type === 'chinese' ? attempt.block1.text : this.findChinesePair(attempt.block1.text)
        });
      }
    });
    
    // 2. 如果游戏失败，收集所有未匹配的词汇对
    if (!isWin) {
      const unmatchedBlocks = this.data.wordBlocks.filter(block => 
        block.visible && !block.matched
      );
      
      const unmatchedPairs = {};
      unmatchedBlocks.forEach(block => {
        if (!unmatchedPairs[block.pairId]) {
          unmatchedPairs[block.pairId] = [];
        }
        unmatchedPairs[block.pairId].push(block);
      });
      
      Object.values(unmatchedPairs).forEach(pair => {
        if (pair.length === 2) {
          const englishBlock = pair.find(b => b.type === 'english');
          const chineseBlock = pair.find(b => b.type === 'chinese');
          
          if (englishBlock && chineseBlock) {
            mistakes.push({
              word: englishBlock.text,
              meaning: chineseBlock.text,
              userAnswer: '未完成匹配',
              correctAnswer: `${englishBlock.text} - ${chineseBlock.text}`,
              mistakeSource: 'unmatched',
              english: englishBlock.text,
              chinese: chineseBlock.text
            });
          }
        }
      });
    }
    
    return mistakes;
  },

  // 保存到测试结果统一存储
  saveToTestResults(result) {
    try {
      const allTestResults = wx.getStorageSync('testResults') || [];
      allTestResults.push(result);
      wx.setStorageSync('testResults', allTestResults);
      console.log('消消乐测试结果已保存到统一存储');
    } catch (error) {
      console.error('保存到统一测试结果失败:', error);
    }
  },

  // 更新消消乐学习进度
  updatePuzzleLearningProgress: function(isWin, usedTime) {
    const {
      libraryId,
      libraryName,
      isGrouped,
      currentGroup,
      totalGroups,
      wordsPerGroup,
      score,
      matchedPairs,
      totalPairs,
      gameMode,
      timeLimit
    } = this.data;

    if (!libraryId) {
      return;
    }

    // 构建测试配置
    const testSettings = {
      gameMode: gameMode || '60', // 游戏模式（60秒、10秒等）
      timeLimit: timeLimit || 60,
      wordsPerGroup: wordsPerGroup,
      eliminationMode: 'puzzle'
    };

    try {
      const testMode = 'elimination'; // 消消乐模式

      if (isGrouped && currentGroup && totalGroups && wordsPerGroup) {
        // 分组学习进度更新
        const progressKey = `progress_${libraryId}_${testMode}`;
        let progressData = wx.getStorageSync(progressKey) || {};

        // 初始化或更新分组进度数据
        if (!progressData.isGrouped) {
          progressData = {
            libraryId: libraryId,
            libraryName: libraryName,
            mode: testMode,
            isGrouped: true,
            groupInfo: {
              currentGroup: 1,
              totalGroups: totalGroups,
              wordsPerGroup: wordsPerGroup,
              completedGroups: []
            }
          };
        }

        // 检查当前组是否通过（消消乐需要完全匹配才算通过）
        const isPassed = isWin && (matchedPairs === totalPairs);

        if (isPassed && !progressData.groupInfo.completedGroups.includes(currentGroup)) {
          // 标记当前组为已完成
          progressData.groupInfo.completedGroups.push(currentGroup);
          progressData.groupInfo.completedGroups.sort((a, b) => a - b);
        }

        // 更新当前组和进度信息
        progressData.groupInfo.currentGroup = Math.max(currentGroup, progressData.groupInfo.currentGroup);
        progressData.lastStudyTime = Date.now();
        progressData.percentage = Math.round((progressData.groupInfo.completedGroups.length / totalGroups) * 100);
        progressData.currentIndex = progressData.groupInfo.completedGroups.length * wordsPerGroup;
        progressData.totalCount = totalGroups * wordsPerGroup;
        progressData.progressText = `第${currentGroup}/${totalGroups}组`;
        progressData.detailText = `已完成${progressData.groupInfo.completedGroups.length}组，共${totalGroups}组`;

        // 添加测试配置
        progressData.testSettings = testSettings;

        // 保存到本地存储
        wx.setStorageSync(progressKey, progressData);
        console.log('消消乐分组学习进度已更新到本地:', progressData);

        // 同时保存到云端
        this.saveProgressToCloud(progressData, testSettings);

      } else {
        // 普通学习进度更新（非分组）
        const progressKey = `progress_${libraryId}_${testMode}`;
        let progressData = wx.getStorageSync(progressKey) || {};

        // 更新基本进度信息
        progressData = {
          ...progressData,
          libraryId: libraryId,
          libraryName: libraryName,
          mode: testMode,
          lastStudyTime: Date.now(),
          isGrouped: false,
          testSettings: testSettings
        };

        // 保存到本地存储
        wx.setStorageSync(progressKey, progressData);
        console.log('消消乐学习进度已更新到本地:', progressData);

        // 同时保存到云端
        this.saveProgressToCloud(progressData, testSettings);
      }

    } catch (error) {
      console.error('更新消消乐学习进度失败:', error);
    }
  },

  // 保存学习进度到云端
  saveProgressToCloud: function(progressData, testSettings) {
    try {
      wx.cloud.callFunction({
        name: 'saveLearningProgress',
        data: {
          libraryId: progressData.libraryId,
          libraryName: progressData.libraryName,
          mode: progressData.mode,
          progressData: progressData,
          testSettings: testSettings
        }
      }).then(result => {
        if (result.result && result.result.success) {
          console.log('消消乐学习进度已保存到云端:', result.result);
        } else {
          console.error('保存消消乐学习进度到云端失败:', result.result);
        }
      }).catch(error => {
        console.error('调用云函数保存消消乐学习进度失败:', error);
      });
    } catch (error) {
      console.error('保存消消乐学习进度到云端异常:', error);
    }
  },

  // 进入下一关
  // 播放匹配特效
  playMatchEffect(combo) {
    // 播放匹配音效
    this.playMatchSound(combo);

    // 根据连击数提供不同的反馈效果
    if (combo >= 5) {
      // 5连击以上：超强反馈
      wx.vibrateShort({ type: 'heavy' });
      setTimeout(() => wx.vibrateShort({ type: 'heavy' }), 100);
      setTimeout(() => wx.vibrateShort({ type: 'medium' }), 200);

      wx.showToast({
        title: `🔥 ${combo}连击！无敌！`,
        icon: 'success',
        duration: 1200
      });
    } else if (combo >= 3) {
      // 3-4连击：强反馈
      wx.vibrateShort({ type: 'heavy' });
      setTimeout(() => wx.vibrateShort({ type: 'medium' }), 100);

      wx.showToast({
        title: `🎉 ${combo}连击！棒极了！`,
        icon: 'success',
        duration: 1000
      });
    } else if (combo === 2) {
      // 2连击：中等反馈
      wx.vibrateShort({ type: 'medium' });

      wx.showToast({
        title: `⚡ ${combo}连击！`,
        icon: 'success',
        duration: 800
      });
    } else {
      // 单次匹配：轻反馈
      wx.vibrateShort({ type: 'light' });

      wx.showToast({
        title: '✓ 配对成功',
        icon: 'success',
        duration: 600
      });
    }

    console.log(`播放匹配音效 - ${combo}连击`);
  },

  // 播放错误匹配特效
  playWrongMatchEffect() {
    // 播放错误匹配音效
    this.playWrongMatchSound();

    // 错误震动反馈 - 双重震动表示错误
    wx.vibrateShort({ type: 'heavy' });
    setTimeout(() => wx.vibrateShort({ type: 'heavy' }), 150);

    // 显示错误提示
    const errorMessages = [
      '✗ 配对错误',
      '❌ 不匹配',
      '🚫 再试试',
      '💭 仔细想想'
    ];
    const randomMessage = errorMessages[Math.floor(Math.random() * errorMessages.length)];

    wx.showToast({
      title: randomMessage,
      icon: 'error',
      duration: 1000
    });

    console.log('播放错误匹配音效');
  },

  // 显示飘分特效
  showFloatingScore(points, combo) {
    const floatingScores = [...(this.data.floatingScores || [])];
    const id = Date.now() + Math.random();
    
    floatingScores.push({
      id,
      points,
      combo,
      show: true
    });

    this.setData({ floatingScores });

    // 2秒后移除特效
    setTimeout(() => {
      const updatedScores = this.data.floatingScores.filter(score => score.id !== id);
      this.setData({ floatingScores: updatedScores });
    }, 2000);
  },

  // 显示过关特效
  showLevelCompleteEffect() {
    // 烟花特效数据
    const fireworks = [];
    for (let i = 0; i < 6; i++) {
      fireworks.push({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: i * 200
      });
    }

    this.setData({ 
      showFireworks: true,
      fireworks 
    });

    // 连续震动庆祝
    wx.vibrateShort({ type: 'heavy' });
    setTimeout(() => wx.vibrateShort({ type: 'medium' }), 200);
    setTimeout(() => wx.vibrateShort({ type: 'light' }), 400);

    // 3秒后隐藏烟花
    setTimeout(() => {
      this.setData({ showFireworks: false });
    }, 3000);
  },

  goToNextLevel() {
    const app = getApp();
    const gameData = app.globalData.eliminationGameData;
    
    if (!gameData || !gameData.allWords) {
      wx.showToast({ title: '无法加载下一关数据', icon: 'error' });
      return;
    }

    // 使用用户选择的每组词汇数量，而不是系统计算的最大值
    const MAX_WORDS_PER_GROUP = gameData.wordsPerGroup || this.calculateMaxWords();
    const nextGroup = this.data.currentGroup + 1;
    const startIndex = (nextGroup - 1) * MAX_WORDS_PER_GROUP;
    const endIndex = Math.min(startIndex + MAX_WORDS_PER_GROUP, gameData.allWords.length);
    const nextGroupWords = gameData.allWords.slice(startIndex, endIndex);

    console.log('进入下一关:', {
      nextGroup,
      MAX_WORDS_PER_GROUP,
      startIndex,
      endIndex,
      nextGroupWords: nextGroupWords.length,
      totalWords: gameData.allWords.length
    });

    // 转换词汇格式（与模式选择页面保持一致）
    const gameWords = nextGroupWords.map(word => ({
      english: word.words || word.word || word.english,
      chinese: word.meaning || (word.meanings && word.meanings[0] && word.meanings[0].definitions && word.meanings[0].definitions[0].definition) || word.chinese || '无释义'
    }));

    // 更新游戏数据
    app.globalData.eliminationGameData.words = gameWords;

    // 重置游戏状态并开始下一关
    this.setData({
      currentGroup: nextGroup,
      showResult: false,
      score: 0,
      combo: 0,
      maxCombo: 0,
      matchedPairs: 0,
      selectedBlock: null,
      totalPairs: gameWords.length,
      startTime: Date.now()
    });

    this.generateWordBlocks(gameWords);

    wx.showToast({
      title: `第${nextGroup}关开始！`,
      icon: 'success'
    });
  },
  
  // 检查是否有下一关可用（消消乐竞赛模式）
  async checkNextCompetitionLevel() {
    try {
      const app = getApp();
      const gameData = app.globalData.eliminationGameData || {};
      
      if (!gameData.masterCompetitionId) {
        return false;
      }

      // 获取分组竞赛详情，检查下一关状态
      const result = await wx.cloud.callFunction({
        name: 'getCompetitions',
        data: {
          mode: 'getGroupedDetail',
          masterCompetitionId: gameData.masterCompetitionId
        }
      });

      if (result.result.success) {
        const data = result.result.data;
        const levels = data.levels || [];
        
        // 找到当前关卡在列表中的位置
        const currentLevelIndex = levels.findIndex(level => level.id === this.data.competitionId);
        const hasNext = currentLevelIndex >= 0 && currentLevelIndex < levels.length - 1;
        
        console.log('消消乐检查下一关:', {
          currentLevelIndex,
          totalLevels: levels.length,
          hasNext,
          nextLevelId: hasNext ? levels[currentLevelIndex + 1].id : null
        });
        
        const nextLevelId = hasNext ? levels[currentLevelIndex + 1].id : null;

        this.setData({
          hasNextCompetitionLevel: hasNext,
          nextCompetitionLevelId: nextLevelId, // 保持兼容性
          nextLevelId: nextLevelId, // 与其他模式保持一致
          nextLevelNumber: hasNext ? levels[currentLevelIndex + 1].levelNumber : null
        });
        
        return hasNext;
      }
      return false;
    } catch (error) {
      console.error('检查下一关失败:', error);
      return false;
    }
  },

  // 进入下一个竞赛关卡
  goToNextCompetitionLevel() {
    const nextLevelId = this.data.nextLevelId || this.data.nextCompetitionLevelId;

    console.log('点击挑战下一关，当前数据:', {
      nextLevelId: nextLevelId,
      nextCompetitionLevelId: this.data.nextCompetitionLevelId,
      masterCompetitionId: this.data.masterCompetitionId,
      competitionId: this.data.competitionId
    });

    if (!nextLevelId) {
      wx.showModal({
        title: '无法加载下一关',
        content: '下一关信息获取失败，请返回关卡列表重新选择',
        showCancel: false,
        confirmText: '知道了'
      });
      return;
    }

    wx.showLoading({
      title: '加载下一关...',
      mask: true
    });

    // 直接跳转到下一关消消乐游戏
    wx.redirectTo({
      url: `/pages/task/puzzle/puzzle?competitionId=${nextLevelId}&mode=custom&gameMode=60&masterCompetitionId=${this.data.masterCompetitionId}`,
      success: () => {
        wx.hideLoading();
        console.log('成功跳转到下一关');
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('进入下一关失败:', error);
        wx.showModal({
          title: '跳转失败',
          content: '无法进入下一关，请检查网络连接后重试',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    });
  },

  // 查看竞赛排行榜
  viewCompetitionRanking() {
    const { competitionId, masterCompetitionId } = this.data;
    const app = getApp();
    const gameData = app.globalData.eliminationGameData || {};

    if (!competitionId) {
      wx.showToast({
        title: '竞赛信息不完整',
        icon: 'none'
      });
      return;
    }

    // 如果是多关卡竞赛，直接查看整体排行榜（不是单个关卡的排行榜）
    if (masterCompetitionId || gameData.masterCompetitionId) {
      const masterId = masterCompetitionId || gameData.masterCompetitionId;
      wx.navigateTo({
        url: `/pages/competition/ranking/ranking?competitionId=${masterId}&mode=elimination&type=master`,
        fail: (err) => {
          console.error('跳转排行榜失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 单一竞赛，直接查看排行榜
      wx.navigateTo({
        url: `/pages/competition/ranking/ranking?competitionId=${competitionId}&mode=elimination`,
        fail: (err) => {
          console.error('跳转排行榜失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 隐藏结果弹窗
  hideResultModal() {
    // 不自动隐藏，需要用户主动选择
  },

  // 返回关卡列表
  backToLevelList() {
    const app = getApp();
    const gameData = app.globalData.eliminationGameData || {};

    // 设置全局标记，通知关卡选择页面需要刷新
    app.globalData.needRefreshLevelSelect = true;

    if (gameData.masterCompetitionId) {
      // 竞赛模式
      wx.navigateTo({
        url: `/pages/competition/level-select/level-select?masterCompetitionId=${gameData.masterCompetitionId}&mode=elimination`
      });
    } else if (gameData.shareId) {
      // 分享模式
      wx.navigateBack({
        fail: () => {
          // 如果返回失败，说明没有上一页，则重新导航到关卡选择页面
          wx.redirectTo({
            url: `/pages/competition/level-select/level-select?shareId=${gameData.shareId}&mode=share&testType=elimination&refresh=true`
          });
        }
      });
    } else {
      wx.showToast({
        title: '非多关卡模式',
        icon: 'none'
      });
    }
  },

  // 返回菜单
  backToMenu() {
    const app = getApp();
    const gameData = app.globalData.eliminationGameData || {};

    // 如果是多关卡竞赛，返回到关卡选择页面
    if (gameData.masterCompetitionId) {
      wx.navigateTo({
        url: `/pages/competition/level-select/level-select?masterCompetitionId=${gameData.masterCompetitionId}&mode=elimination`
      });
    } else {
      // 普通模式或单一竞赛，检查是否需要返回主页
      const pages = getCurrentPages();
      if (pages.length <= 1) {
        wx.switchTab({
          url: '/pages/index/index'
        });
      } else {
        const prevPage = pages[pages.length - 2];
        if (prevPage && prevPage.route === 'pages/index/index') {
          wx.switchTab({
            url: '/pages/index/index'
          });
        } else {
          wx.navigateBack();
        }
      }
    }
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 防止点击弹窗内容时关闭弹窗
  },

  // 工具函数：洗牌算法
  shuffleArray(array) {
    const result = [...array];
    for (let i = result.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [result[i], result[j]] = [result[j], result[i]];
    }
    return result;
  },

  // 格式化时间
  formatTime(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}分${remainingSeconds}秒`;
    } else {
      return `${remainingSeconds}秒`;
    }
  },

  onUnload() {
    console.log('消消乐页面卸载，清理资源');
    
    // 清理定时器，防止内存泄漏
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
      console.log('定时器已清理');
    }
    
    // 禁用考试模式
    if (this.data.examMode) {
      this.disableExamMode();
      console.log('考试模式已禁用');
    }
    
    // 重置游戏状态，防止残留数据
    this.setData({ gameEnded: true });
  },



  // 记录错误的匹配尝试
  recordWrongAttempt(block1, block2) {
    const wrongAttempts = this.data.wrongAttempts;
    
    // 构建错误尝试的数据
    const wrongAttempt = {
      attempted: block1.text + ' - ' + block2.text,
      block1: {
        text: block1.text,
        type: block1.type,
        pairId: block1.pairId
      },
      block2: {
        text: block2.text,
        type: block2.type,
        pairId: block2.pairId
      },
      timestamp: Date.now()
    };
    
    wrongAttempts.push(wrongAttempt);
    this.setData({ wrongAttempts });
    
    // 给用户一个视觉反馈
    wx.vibrateShort({ type: 'medium' });
  },

  // 收集错题到错题本
  collectMistakesToErrorBook(isWin) {
    const app = getApp();
    
    // 使用更可靠的登录状态检查
    if (!app.canCollectMistakes()) {
      console.log('用户未登录或openid不可用，跳过错题收集');
      return;
    }
    
    const userId = app.getUserOpenId();

    const mistakes = [];
    
    // 1. 收集错误的匹配尝试
    this.data.wrongAttempts.forEach(attempt => {
      // 找到正确的匹配对
      const correctPair1 = this.findCorrectPair(attempt.block1);
      const correctPair2 = this.findCorrectPair(attempt.block2);
      
      if (correctPair1) {
        mistakes.push({
          word: attempt.block1.type === 'english' ? attempt.block1.text : correctPair1,
          meaning: attempt.block1.type === 'chinese' ? attempt.block1.text : this.findChinesePair(attempt.block1.text),
          userAnswer: attempt.attempted,
          correctAnswer: `${attempt.block1.type === 'english' ? attempt.block1.text : correctPair1} - ${attempt.block1.type === 'chinese' ? attempt.block1.text : this.findChinesePair(attempt.block1.text)}`,
          mistakeSource: 'wrong_attempt'
        });
      }
    });
    
    // 2. 如果游戏失败，收集所有未匹配的词汇对
    if (!isWin) {
      const unmatchedBlocks = this.data.wordBlocks.filter(block => 
        block.visible && !block.matched
      );
      
      // 按配对ID分组，收集未完成的词汇对
      const unmatchedPairs = {};
      unmatchedBlocks.forEach(block => {
        if (!unmatchedPairs[block.pairId]) {
          unmatchedPairs[block.pairId] = [];
        }
        unmatchedPairs[block.pairId].push(block);
      });
      
      // 对于完整的未匹配词汇对，添加到错题中
      Object.values(unmatchedPairs).forEach(pair => {
        if (pair.length === 2) {
          const englishBlock = pair.find(b => b.type === 'english');
          const chineseBlock = pair.find(b => b.type === 'chinese');
          
          if (englishBlock && chineseBlock) {
            mistakes.push({
              word: englishBlock.text,
              meaning: chineseBlock.text,
              userAnswer: '未完成匹配',
              correctAnswer: `${englishBlock.text} - ${chineseBlock.text}`,
              mistakeSource: 'unmatched'
            });
          }
        }
      });
    }
    
    // 消消乐不收集错词，因为必须全部答对才能过关
  },

  // 查找正确的配对
  findCorrectPair(block) {
    const allBlocks = this.data.wordBlocks;
    const correctPair = allBlocks.find(b => 
      b.pairId === block.pairId && b.type !== block.type
    );
    return correctPair ? correctPair.text : null;
  },

  // 查找中文配对（用于英文方块）
  findChinesePair(englishText) {
    const allBlocks = this.data.wordBlocks;
    const englishBlock = allBlocks.find(b => 
      b.type === 'english' && b.text === englishText
    );
    if (englishBlock) {
      const chineseBlock = allBlocks.find(b => 
        b.pairId === englishBlock.pairId && b.type === 'chinese'
      );
      return chineseBlock ? chineseBlock.text : null;
    }
    return null;
  },



  // 分享游戏结果
  shareGameResult() {
    const options = ['保存成绩图片到相册', '分享给微信好友', '分享到朋友圈'];

    wx.showActionSheet({
      itemList: options,
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.saveResultImageToAlbum();
            break;
          case 1:
            this.shareGameToWeChat();
            break;
          case 2:
            this.shareToTimeline();
            break;
        }
      }
    });
  },

  // 保存成绩图片到相册
  saveResultImageToAlbum() {
    // 检查微信版本和环境
    const systemInfo = wx.getSystemInfoSync();
    console.log('保存图片 - 系统信息:', {
      platform: systemInfo.platform,
      version: systemInfo.version,
      SDKVersion: systemInfo.SDKVersion
    });

    // 先尝试直接保存，如果失败再处理权限
    this.attemptDirectSave();
  },

  // 尝试直接保存（绕过权限检查）
  attemptDirectSave() {
    wx.showLoading({
      title: '生成图片中...',
      mask: true
    });

    // 生成成绩图片并保存到相册
    this.generateGameResultImage().then(() => {
      wx.canvasToTempFilePath({
        canvasId: 'gameResultCanvas',
        success: (res) => {
          // 直接尝试保存，不预先检查权限
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading();
              wx.showToast({
                title: '已保存到相册',
                icon: 'success'
              });
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('直接保存失败:', err);
              // 如果直接保存失败，尝试权限处理流程
              this.handleSaveFailure(err, res.tempFilePath);
            }
          });
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('生成图片失败:', err);
          this.showFallbackOptions();
        }
      }, this);
    }).catch((err) => {
      wx.hideLoading();
      console.error('生成图片失败:', err);
      this.showFallbackOptions();
    });
  },

  // 处理保存失败的情况
  handleSaveFailure(err, tempFilePath) {
    console.error('保存图片失败详情:', err);

    if (err.errMsg.includes('privacy api banned') || err.errMsg.includes('banned')) {
      // API被禁用，提供替代方案
      this.showPrivacyApiBannedDialog();
    } else if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
      // 权限问题，尝试权限流程
      this.handlePermissionFlow(tempFilePath);
    } else {
      // 其他错误，显示通用错误处理
      this.showFallbackOptions();
    }
  },

  // 显示隐私API被禁用的对话框
  showPrivacyApiBannedDialog() {
    wx.showModal({
      title: '保存功能暂不可用',
      content: '由于微信政策限制，当前版本暂时无法直接保存图片到相册。\n\n建议您：\n1. 截屏保存当前页面\n2. 等待后续版本更新\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 提示用户截屏
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },

  // 处理权限流程
  handlePermissionFlow(tempFilePath) {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启相册权限',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      // 用户开启了权限，重新尝试保存
                      this.retrySaveImage(tempFilePath);
                    }
                  }
                });
              }
            }
          });
        } else {
          // 尝试请求权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.retrySaveImage(tempFilePath);
            },
            fail: () => {
              this.showFallbackOptions();
            }
          });
        }
      },
      fail: () => {
        this.showFallbackOptions();
      }
    });
  },

  // 重新尝试保存图片
  retrySaveImage(tempFilePath) {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: () => {
        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('重试保存失败:', err);
        this.showFallbackOptions();
      }
    });
  },

  // 显示备选方案
  showFallbackOptions() {
    wx.showModal({
      title: '保存失败',
      content: '图片保存失败，您可以：\n\n1. 截屏保存当前页面\n2. 稍后重试\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '稍后重试',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },



  // 分享到朋友圈
  shareToTimeline() {
    const { finalScore, matchedPairs, totalPairs, gameMode } = this.data;
    const modeText = gameMode === 'practice' ? '练习模式' : `${gameMode}秒挑战`;

    // 设置朋友圈分享数据
    this.setData({
      currentTimelineShareData: {
        title: `我在墨词自习室消消乐${modeText}中获得了${finalScore}分！快来挑战吧！`,
        query: 'from=timeline',
        imageUrl: '/assets/icons/logo.png'
      }
    });

    // 更新分享菜单，启用朋友圈分享
    wx.updateShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        // 直接提示用户朋友圈分享已准备好
        wx.showToast({
          title: '朋友圈分享已准备好',
          icon: 'success',
          duration: 1500
        });

        // 延迟提示用户使用右上角分享
        setTimeout(() => {
          wx.showModal({
            title: '分享到朋友圈',
            content: '请点击右上角"..."按钮，选择"分享到朋友圈"来分享您的消消乐成绩！',
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1600);
      },
      fail: () => {
        wx.showToast({
          title: '分享功能暂不可用',
          icon: 'none'
        });
      }
    });
  },

  // 生成游戏结果图片
  generateGameResultImage() {
    return new Promise((resolve, reject) => {
      const { finalScore, matchedPairs, totalPairs, maxCombo, usedTime, gameMode } = this.data;
      const modeText = gameMode === 'practice' ? '练习模式' : `${gameMode}秒挑战`;

      // 创建画布
      const ctx = wx.createCanvasContext('gameResultCanvas', this);

      // 设置背景
      const gradient = ctx.createLinearGradient(0, 0, 375, 600);
      gradient.addColorStop(0, '#667eea');
      gradient.addColorStop(1, '#764ba2');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, 375, 600);

      // 设置字体样式
      ctx.setFillStyle('#ffffff');
      ctx.setFontSize(28);
      ctx.setTextAlign('center');

      // 标题
      ctx.fillText('墨词自习室 - 消消乐成绩', 187.5, 60);

      // 测试信息
      ctx.setFontSize(24);
      ctx.fillText(`${modeText}`, 187.5, 100);

      // 成绩卡片背景
      ctx.setFillStyle('rgba(255, 255, 255, 0.95)');
      ctx.fillRect(30, 130, 315, 340);

      // 成绩信息
      ctx.setFillStyle('#333333');
      ctx.setFontSize(36);
      ctx.setTextAlign('center');
      ctx.fillText(`${finalScore}分`, 187.5, 200);

      ctx.setFontSize(20);
      ctx.fillText(`最终得分`, 187.5, 230);

      // 详细统计
      ctx.setFontSize(18);
      ctx.setTextAlign('left');

      ctx.fillText(`配对成功：${matchedPairs}/${totalPairs}`, 60, 280);
      ctx.fillText(`最大连击：×${maxCombo}`, 60, 310);
      if (gameMode !== 'practice') {
        ctx.fillText(`用时：${usedTime}`, 60, 340);
      }

      // 时间信息
      const testTime = new Date().toLocaleString('zh-CN');
      ctx.setFontSize(14);
      ctx.setTextAlign('center');
      ctx.fillText(`游戏时间：${testTime}`, 187.5, 420);

      ctx.draw(false, () => {
        resolve();
      });
    });
  },

  // 分享游戏结果到微信
  shareGameToWeChat() {
    const { finalScore, matchedPairs, totalPairs, gameMode } = this.data;
    const modeText = gameMode === 'practice' ? '练习模式' : `${gameMode}秒挑战`;

    // 设置分享数据
    this.setData({
      currentShareData: {
        title: `我在墨词自习室消消乐${modeText}中获得了${finalScore}分！`,
        path: `/pages/task/puzzle/puzzle?from=share`,
        imageUrl: '/assets/icons/logo.png'
      }
    });

    // 由于无法直接调用wx.shareAppMessage，显示分享弹窗让用户使用open-type="share"
    this.setData({
      showShareModal: true
    });
  },

  // 复制游戏结果链接
  copyGameResultLink() {
    const { finalScore, matchedPairs, totalPairs, maxCombo, usedTime, gameMode } = this.data;
    const modeText = gameMode === 'practice' ? '练习模式' : `${gameMode}秒挑战`;
    
    let shareText = `🎯 墨词自习室消消乐成绩分享\n\n🎮 ${modeText}\n📊 得分：${finalScore}分\n✅ 配对：${matchedPairs}/${totalPairs}\n🔥 最大连击：×${maxCombo}`;
    
    if (gameMode !== 'practice') {
      shareText += `\n⏱️ 用时：${usedTime}`;
    }
    
    shareText += `\n\n快来挑战吧！`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 关闭分享弹窗
  closeShareModal() {
    this.setData({
      showShareModal: false
    });
  },

  // 分享按钮点击
  onShareButtonTap() {
    console.log('分享按钮被点击');
    // 关闭分享弹窗
    this.setData({
      showShareModal: false
    });
  },

  // 保存游戏成绩图片到相册（替换复制分享信息功能）
  saveGameResultToAlbum() {
    // 关闭分享弹窗
    this.setData({
      showShareModal: false
    });

    // 调用保存成绩图片到相册功能
    this.saveResultImageToAlbum();
  },

  // 页面分享回调
  onShareAppMessage() {
    const shareData = this.data.currentShareData;
    if (shareData) {
      return shareData;
    }

    // 默认分享数据
    const { finalScore, gameMode } = this.data;
    const modeText = gameMode === 'practice' ? '练习模式' : `${gameMode}秒挑战`;

    return {
      title: `我在墨词自习室消消乐${modeText}中获得了${finalScore}分！`,
      path: `/pages/task/puzzle/puzzle?from=share`,
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 朋友圈分享配置
  onShareTimeline() {
    // 优先使用专门为朋友圈设置的分享数据
    const timelineShareData = this.data.currentTimelineShareData;
    if (timelineShareData) {
      return timelineShareData;
    }

    // 其次使用普通分享数据
    const shareData = this.data.currentShareData;
    if (shareData) {
      return {
        title: shareData.title + ' 快来挑战吧！',
        query: 'from=timeline',
        imageUrl: shareData.imageUrl
      };
    }

    // 默认朋友圈分享数据
    const { finalScore, gameMode } = this.data;
    const modeText = gameMode === 'practice' ? '练习模式' : `${gameMode}秒挑战`;

    return {
      title: `我在墨词自习室消消乐${modeText}中获得了${finalScore}分！快来挑战吧！`,
      query: 'from=timeline',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // ================ 音效播放功能 ================

  // 播放匹配成功音效
  playMatchSound: function(combo) {
    try {
      const audioContext = wx.createInnerAudioContext();

      // 根据连击数选择不同的音效
      let soundData;
      if (combo >= 5) {
        soundData = this.generateComboSoundData(5); // 超级连击音效
      } else if (combo >= 3) {
        soundData = this.generateComboSoundData(3); // 连击音效
      } else if (combo === 2) {
        soundData = this.generateComboSoundData(2); // 双击音效
      } else {
        soundData = this.generateMatchSoundData(); // 普通匹配音效
      }

      if (soundData) {
        audioContext.src = soundData;
        audioContext.volume = 0.6;
        audioContext.play();

        audioContext.onEnded(() => {
          audioContext.destroy();
        });

        audioContext.onError(() => {
          audioContext.destroy();
        });
      }
    } catch (error) {
      console.log('播放匹配音效失败:', error);
      // 备用方案：使用震动模拟音效
      this.playVibrateSound('match', combo);
    }
  },

  // 播放错误匹配音效
  playWrongMatchSound: function() {
    try {
      const audioContext = wx.createInnerAudioContext();
      const soundData = this.generateWrongMatchSoundData();

      if (soundData) {
        audioContext.src = soundData;
        audioContext.volume = 0.5;
        audioContext.play();

        audioContext.onEnded(() => {
          audioContext.destroy();
        });

        audioContext.onError(() => {
          audioContext.destroy();
        });
      }
    } catch (error) {
      console.log('播放错误音效失败:', error);
      // 备用方案：使用震动模拟音效
      this.playVibrateSound('wrong');
    }
  },

  // 生成匹配成功音效数据
  generateMatchSoundData: function() {
    try {
      // 使用本地音效文件或在线资源
      return '/assets/sounds/match.wav';
    } catch (error) {
      return null;
    }
  },

  // 生成连击音效数据
  generateComboSoundData: function(comboLevel) {
    try {
      // 根据连击等级返回不同的音效
      switch (comboLevel) {
        case 2:
          return '/assets/sounds/combo2.wav';
        case 3:
          return '/assets/sounds/combo3.wav';
        case 5:
          return '/assets/sounds/combo5.wav';
        default:
          return '/assets/sounds/match.wav';
      }
    } catch (error) {
      return null;
    }
  },

  // 生成错误匹配音效数据
  generateWrongMatchSoundData: function() {
    try {
      return '/assets/sounds/wrong_match.wav';
    } catch (error) {
      return null;
    }
  },

  // 备用震动音效方案
  playVibrateSound: function(type, combo) {
    try {
      if (type === 'match') {
        if (combo >= 5) {
          // 超级连击：快速多次震动
          for (let i = 0; i < 5; i++) {
            setTimeout(() => wx.vibrateShort({ type: 'light' }), i * 50);
          }
        } else if (combo >= 3) {
          // 连击：三次震动
          wx.vibrateShort({ type: 'light' });
          setTimeout(() => wx.vibrateShort({ type: 'light' }), 80);
          setTimeout(() => wx.vibrateShort({ type: 'light' }), 160);
        } else if (combo === 2) {
          // 双击：两次震动
          wx.vibrateShort({ type: 'light' });
          setTimeout(() => wx.vibrateShort({ type: 'light' }), 100);
        } else {
          // 普通匹配：单次震动
          wx.vibrateShort({ type: 'light' });
        }
      } else if (type === 'wrong') {
        // 错误：低频长震动
        wx.vibrateShort({ type: 'heavy' });
        setTimeout(() => wx.vibrateShort({ type: 'heavy' }), 200);
      }
    } catch (error) {
      console.log('播放震动音效失败:', error);
    }
  }
});