<view class="container">
  <!-- 册列表 -->
  <view class="book-list" wx:if="{{showBookList}}">
    <view class="section-title">请选择册数</view>
    <view class="book-item" wx:for="{{books}}" wx:key="id" bindtap="onBookTap" data-book="{{item}}">
      <view class="book-info">
        <view class="book-name">{{item.name}}</view>
        <view class="book-count">{{item.count}}</view>
      </view>
      <view class="book-arrow">
        <text class="arrow-icon">›</text>
      </view>
    </view>
  </view>

  <!-- 单元列表 -->
  <view class="unit-list" wx:if="{{showUnitList}}">
    <!-- 返回按钮 -->
    <view class="back-bar">
      <view class="back-btn" bindtap="backToBookList">
        <text class="arrow-icon back-arrow">‹</text>
        <text>返回册选择</text>
      </view>
    </view>

    <!-- 全册学习选项 -->
    <view class="full-book-section">
      <view class="section-title">学习模式</view>
      <view class="full-book-item" bindtap="onFullBookTap">
        <view class="full-book-info">
          <view class="full-book-name">全册学习</view>
          <view class="full-book-desc">学习整册词汇</view>
        </view>
        <view class="full-book-arrow">
          <text class="arrow-icon">›</text>
        </view>
      </view>
    </view>

    <!-- 分单元学习 -->
    <view class="unit-section">
      <view class="section-title">按单元学习</view>
      <view class="unit-item" wx:for="{{units}}" wx:key="id" bindtap="onUnitTap" data-unit="{{item}}">
        <view class="unit-info">
          <view class="unit-name">{{item.name}}</view>
        </view>
        <view class="unit-arrow">
          <text class="arrow-icon">›</text>
        </view>
      </view>
    </view>
  </view>
</view> 