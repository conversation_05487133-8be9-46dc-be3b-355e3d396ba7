<view class="modal-mask" wx:if="{{show}}">
  <view class="modal-content">
    <view class="modal-header">
      <text>跳转到指定页</text>
    </view>
    <view class="modal-body">
      <view class="input-container">
        <input
          class="page-input"
          placeholder="{{inputFocused || inputValue ? '' : '请输入页码 (1-' + totalPages + ')'}}"
          placeholder-class="input-placeholder"
          value="{{inputValue}}"
          bindinput="onInput"
          bindfocus="onFocus"
          bindblur="onBlur"
          type="number"
        />
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn-cancel" bindtap="onCancel">取消</button>
      <button class="btn-confirm" bindtap="onConfirm">确定</button>
    </view>
  </view>
</view>
