const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { competitionId, mode } = event
    
    if (!competitionId) {
      return {
        success: false,
        message: '缺少竞赛ID'
      }
    }

    // 获取竞赛信息
    let competitionResult
    try {
      competitionResult = await db.collection('competitions').doc(competitionId).get()
    } catch (error) {
      if (error.errCode === -502005) {
        return {
          success: false,
          message: '竞赛不存在'
        }
      }
      throw error
    }
    
    if (!competitionResult.data) {
      return {
        success: false,
        message: '竞赛不存在'
      }
    }

    const competition = competitionResult.data
    
    // 检查是否是多关卡竞赛
    if (competition.type === 'master') {
      // 多关卡竞赛，需要统计所有子关卡的数据
      return await processMultiLevelRanking(competition, mode)
    } else {
      // 单一竞赛，使用原有逻辑
      return await processSingleLevelRanking(competition, mode)
    }
    
  } catch (error) {
    console.error('获取排行榜失败:', error)
    return {
      success: false,
      message: '获取排行榜失败: ' + error.message,
      data: []
    }
  }
}

// 处理多关卡竞赛排行榜
async function processMultiLevelRanking(masterCompetition, mode) {
  try {
    // 获取所有子关卡
    const levelResults = await db.collection('competitions').where({
      masterCompetitionId: masterCompetition._id,
      type: 'level',
      status: 'active'
    }).get()
    
    const levels = levelResults.data
    console.log(`找到${levels.length}个子关卡`)
    
    if (levels.length === 0) {
      return {
        success: true,
        data: [],
        total: 0,
        competitionName: masterCompetition.name
      }
    }
    
    // 统计所有用户在所有关卡中的表现
    const userStats = {}
    
    levels.forEach(level => {
      const results = level.results || []
      results.forEach(result => {
        const openId = result.openid || result.openId
        if (!openId) return
        
        if (!userStats[openId]) {
          userStats[openId] = {
            openId: openId,
            userName: result.userName || '匿名用户',
            completedLevels: 0,
            totalScore: 0,
            totalDuration: 0,
            totalCorrect: 0,
            totalQuestions: 0,
            levelResults: [],
            submitTime: null
          }
        }
        
        // 检查是否通过了这个关卡
        let isPassed = false;
        if (mode === 'elimination') {
          // 消消乐模式：只看completed状态，不看正确率
          isPassed = result.completed === true;
        } else {
          // 其他模式：completed为true或正确率≥80%
          isPassed = result.completed === true || 
                   (result.accuracy && result.accuracy >= 80) ||
                   (result.correctCount && result.totalCount && 
                    (result.correctCount / result.totalCount) >= 0.8);
        }
        
        // 只有通过的关卡才计入统计
        if (isPassed) {
          userStats[openId].completedLevels++
          userStats[openId].totalScore += result.score || 0
          userStats[openId].totalDuration += result.duration || 0
          
          // 只有非消消乐模式才统计正确率数据
          if (mode !== 'elimination') {
            userStats[openId].totalCorrect += result.correctCount || 0
            userStats[openId].totalQuestions += result.totalCount || 0
          }
        }
        
        // 记录所有关卡结果（包括未通过的，用于展示）
        userStats[openId].levelResults.push({
          levelId: level._id,
          levelNumber: level.levelNumber,
          score: result.score,
          duration: result.duration,
          accuracy: result.accuracy,
          passed: isPassed
        })
        
        // 保存最晚的提交时间
        if (!userStats[openId].submitTime || result.submitTime > userStats[openId].submitTime) {
          userStats[openId].submitTime = result.submitTime
        }
      })
    })
    
    // 计算衍生数据
    Object.values(userStats).forEach(user => {
      user.averageScore = user.completedLevels > 0 ? Math.round(user.totalScore / user.completedLevels) : 0
      // 只有非消消乐模式才计算总正确率
      if (mode === 'elimination') {
        user.overallAccuracy = 0 // 消消乐模式不显示正确率
      } else {
        user.overallAccuracy = user.totalQuestions > 0 ? Math.round((user.totalCorrect / user.totalQuestions) * 100) : 0
      }
      user.completionRate = Math.round((user.completedLevels / levels.length) * 100)
    })
    
    // 排序规则：根据模式决定是否考虑正确率
    const sortedResults = Object.values(userStats).sort((a, b) => {
      // 首先按完成关卡数降序
      if (b.completedLevels !== a.completedLevels) {
        return b.completedLevels - a.completedLevels
      }
      // 然后按总分数降序
      if (b.totalScore !== a.totalScore) {
        return b.totalScore - a.totalScore
      }
      // 消消乐模式不考虑正确率，直接按用时排序
      if (mode === 'elimination') {
        return a.totalDuration - b.totalDuration
      }
      // 其他模式按准确率降序，再按总用时升序
      if (b.overallAccuracy !== a.overallAccuracy) {
        return b.overallAccuracy - a.overallAccuracy
      }
      // 最后按总用时升序（用时越少越好）
      return a.totalDuration - b.totalDuration
    })

    // 只返回前20名
    const topResults = sortedResults.slice(0, 20)

    // 查询用户信息，获取真实昵称
    const enhancedResults = []
    for (const result of topResults) {
      let finalUserName = result.userName || '匿名用户'
      
      // 总是尝试从数据库获取最新的用户昵称
      const searchOpenid = result.openId
      if (searchOpenid) {
        try {
          console.log('查询用户昵称 - openid:', searchOpenid)
          const userQuery = await db.collection('users').where({ 
            openid: searchOpenid 
          }).limit(1).get()
          
          if (userQuery.data && userQuery.data.length > 0) {
            const userInfo = userQuery.data[0]
            // 优先使用微信昵称，其次使用用户名
            const dbUserName = userInfo.wechatInfo?.nickName || userInfo.username
            if (dbUserName && dbUserName !== '' && !dbUserName.startsWith('wx')) {
              finalUserName = dbUserName
              console.log('找到用户昵称:', finalUserName)
            }
          }
        } catch (err) {
          console.warn('查询用户信息失败:', searchOpenid, err)
        }
      }
      
      enhancedResults.push({
        ...result,
        userName: finalUserName
      })
    }

    return {
      success: true,
      data: enhancedResults,
      total: Object.keys(userStats).length,
      competitionName: masterCompetition.name,
      type: 'master',
      totalLevels: levels.length
    }
    
  } catch (error) {
    console.error('获取多关卡排行榜失败:', error)
    return {
      success: false,
      message: '获取多关卡排行榜失败: ' + error.message,
      data: []
    }
  }
}

// 处理单一竞赛排行榜
async function processSingleLevelRanking(competition, mode) {
  try {
    const results = competition.results || []

    // 根据不同模式排序
    let sortedResults = []
    
    switch (mode) {
      case 'en2zh':
      case 'zh2en':
        // 英译汉/汉译英：按分数降序，分数相同按时间升序
        sortedResults = results.sort((a, b) => {
          if (b.score !== a.score) {
            return b.score - a.score
          }
          return a.duration - b.duration
        })
        break
        
      case 'dictation':
        // 听写：按分数降序，分数相同按正确率降序，再按时间升序
        sortedResults = results.sort((a, b) => {
          if (b.score !== a.score) {
            return b.score - a.score
          }
          if (b.accuracy !== a.accuracy) {
            return b.accuracy - a.accuracy
          }
          return a.duration - b.duration
        })
        break
        
      case 'elimination':
        // 消消乐：按分数降序，分数相同按消除对数降序，再按时间升序
        sortedResults = results.sort((a, b) => {
          if (b.score !== a.score) {
            return b.score - a.score
          }
          if (b.eliminatedPairs !== a.eliminatedPairs) {
            return b.eliminatedPairs - a.eliminatedPairs
          }
          return a.duration - b.duration
        })
        break
        
      default:
        // 默认按分数排序
        sortedResults = results.sort((a, b) => b.score - a.score)
    }

    // 只返回前20名
    const topResults = sortedResults.slice(0, 20)

    // 查询用户信息，获取真实昵称
    const enhancedResults = []
    for (const result of topResults) {
      let finalUserName = result.userName || '匿名用户'
      
      // 总是尝试从数据库获取最新的用户昵称
      const searchOpenid = result.openid || result.openId || null
      if (searchOpenid) {
        try {
          console.log('查询用户昵称 - openid:', searchOpenid)
          const userQuery = await db.collection('users').where({ 
            openid: searchOpenid 
          }).limit(1).get()
          
          if (userQuery.data && userQuery.data.length > 0) {
            const userInfo = userQuery.data[0]
            // 优先使用微信昵称，其次使用用户名
            const dbUserName = userInfo.wechatInfo?.nickName || userInfo.username
            if (dbUserName && dbUserName !== '' && !dbUserName.startsWith('wx')) {
              finalUserName = dbUserName
              console.log('找到用户昵称:', finalUserName)
            }
          }
        } catch (err) {
          console.warn('查询用户信息失败:', searchOpenid, err)
        }
      }
      
      enhancedResults.push({
        ...result,
        userName: finalUserName
      })
    }

    return {
      success: true,
      data: enhancedResults,
      total: results.length,
      competitionName: competition.name,
      type: 'single'
    }
    
  } catch (error) {
    console.error('获取单一竞赛排行榜失败:', error)
    return {
      success: false,
      message: '获取单一竞赛排行榜失败: ' + error.message,
      data: []
    }
  }
} 