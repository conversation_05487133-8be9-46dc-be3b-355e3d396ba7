/* pages/profile/share-management/share-management.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #666;
}

.main-content {
  margin-bottom: 140rpx;
}

/* 分享信息头部 */
.share-header {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.share-title {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.library-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.test-mode {
  background: #007AFF;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.share-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.share-id,
.expire-time {
  font-size: 28rpx;
  color: #666;
}

.multi-level-info {
  margin-top: 15rpx;
  padding: 15rpx;
  background: #f0f9ff;
  border-radius: 10rpx;
  border-left: 4rpx solid #007AFF;
}

.level-info {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: bold;
}

/* 统计信息 */
.statistics {
  display: flex;
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #007AFF;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 25rpx;
  font-size: 30rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #007AFF;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: #007AFF;
  border-radius: 3rpx;
}

/* 分值说明 */
.score-explanation {
  background: white;
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.explanation-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.explanation-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 标签页内容 */
.tab-content {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 概览页面 */
.overview-section {
  margin-bottom: 40rpx;
}

.overview-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.recent-participants {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.participant-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  border-left: 4rpx solid #007AFF;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.participant-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.nickname {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.test-info,
.level-info {
  font-size: 24rpx;
  color: #666;
}

.participant-status {
  text-align: right;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.latest-score {
  font-size: 28rpx;
  font-weight: bold;
  color: #007AFF;
}

.test-time {
  font-size: 24rpx;
  color: #666;
}

/* 分析图表 */
.analysis-charts {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.chart-item {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.chart-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.chart-placeholder {
  height: 200rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 26rpx;
  border: 2rpx dashed #ddd;
}

/* 参与者列表 */
.participants-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.participant-card {
  background: #f8f9fa;
  padding: 25rpx;
  border-radius: 15rpx;
  border-left: 4rpx solid #007AFF;
}

.participant-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.participant-scores {
  text-align: right;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.best-score,
.average-score {
  font-size: 24rpx;
  color: #666;
}

.join-time {
  font-size: 24rpx;
  color: #666;
}

.participant-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 关卡进度 */
.level-progress {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #e0e0e0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF, #00C6FF);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 关卡概览 */
.levels-overview {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.level-card {
  background: #f8f9fa;
  padding: 25rpx;
  border-radius: 15rpx;
  border-left: 4rpx solid #007AFF;
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.level-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.level-participants {
  font-size: 24rpx;
  color: #666;
}

.level-participants-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.level-participant {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 10rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
}

.level-participant.completed {
  background: #e8f5e8;
  border-left: 4rpx solid #28a745;
}

.level-participant.attempted {
  background: #fff3cd;
  border-left: 4rpx solid #ffc107;
}

.level-participant.not-started {
  background: #f8f9fa;
  border-left: 4rpx solid #6c757d;
}

.small-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
}

.participant-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}

.participant-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.participant-status {
  font-size: 22rpx;
  color: #666;
}

.level-participant.completed .participant-status {
  color: #28a745;
}

.level-participant.attempted .participant-status {
  color: #856404;
}

.level-participant.not-started .participant-status {
  color: #6c757d;
}

.level-score {
  font-size: 26rpx;
  font-weight: bold;
  color: #007AFF;
  min-width: 60rpx;
  text-align: right;
}

.level-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 15rpx;
  border-top: 1rpx solid #e0e0e0;
}

.level-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5rpx;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.share-btn {
  background: #007AFF;
  color: white;
}

.export-btn {
  background: #34C759;
  color: white;
}

.delete-btn {
  background: #FF3B30;
  color: white;
}

/* 详情弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  width: 100%;
  max-width: 650rpx;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 36rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx 24rpx 0 0;
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: white;
}

.modal-close {
  font-size: 44rpx;
  color: white;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 36rpx;
}

.detail-section {
  margin-bottom: 36rpx;
  background: #fafafa;
  border-radius: 16rpx;
  padding: 24rpx;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.select-all-btn {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 20rpx;
  color: white;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.select-all-btn:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

.mistakes-list {
  max-height: 500rpx;
  overflow-y: auto;
  padding: 8rpx;
}

.mistake-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background: white;
  border-radius: 12rpx;
  border: 1rpx solid #e8e8e8;
  transition: all 0.3s ease;
}

.mistake-item:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.mistake-item:last-child {
  margin-bottom: 0;
}

.mistake-checkbox {
  margin-right: 24rpx;
  margin-top: 12rpx;
  transform: scale(1.3);
}

.mistake-content {
  flex: 1;
}

.mistake-word {
  font-size: 34rpx;
  font-weight: 600;
  color: #2c3e50;
  display: block;
  margin-bottom: 12rpx;
}

.mistake-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #667eea;
}

.mistake-correct,
.mistake-wrong {
  font-size: 26rpx;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  font-weight: 500;
}

.mistake-correct {
  color: #27ae60;
  background: rgba(39, 174, 96, 0.1);
}

.mistake-wrong {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
}

.mistake-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32rpx;
  padding: 24rpx;
  background: white;
  border-radius: 12rpx;
  border: 1rpx solid #e8e8e8;
}

.selected-count {
  font-size: 30rpx;
  color: #667eea;
  font-weight: 600;
}

.create-test-btn {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 24rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.create-test-btn:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

/* 关卡分数 */
.level-scores {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.level-score-item {
  display: flex;
  flex-direction: column;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
}

.level-score-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5rpx;
}

.level-name {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

.level-score {
  font-size: 26rpx;
  font-weight: bold;
  color: #007AFF;
}

.level-accuracy {
  font-size: 24rpx;
  color: #666;
}

.level-scores-container {
  margin-top: 15rpx;
}

.level-scores-scroll {
  border: 2rpx solid #f0f0f0;
  border-radius: 10rpx;
  padding: 10rpx;
}

/* 删除重复的样式定义，使用上面已定义的样式 */

.mistake-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30rpx;
  padding: 20rpx 0;
  border-top: 2rpx solid #f0f0f0;
  gap: 15rpx;
}

.selected-count {
  font-size: 28rpx;
  color: #666;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.create-test-btn {
  padding: 15rpx 40rpx;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.create-test-btn:active {
  background: #45a049;
}

.create-test-btn-compact {
  padding: 10rpx 16rpx;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  font-weight: bold;
  flex-shrink: 0;
  min-width: 120rpx;
}

.create-test-btn-compact:active {
  background: #45a049;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-hint {
  font-size: 28rpx;
  color: #999;
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  margin-top: 15rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF, #00C6FF);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 状态样式 */
.status-completed {
  color: #34C759;
  font-weight: bold;
}

.status-progress {
  color: #FF9500;
  font-weight: bold;
}