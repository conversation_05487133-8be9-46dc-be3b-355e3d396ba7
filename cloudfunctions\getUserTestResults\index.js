const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { shareId, participantOpenid, creatorOpenid } = event;

  try {
    // 验证参数
    if (!shareId || !participantOpenid) {
      return {
        success: false,
        message: '缺少必要参数'
      };
    }

    // 查询指定分享测试的用户测试结果
    const query = db.collection('shareTestResults')
      .where({
        shareId: shareId,
        participantOpenid: participantOpenid
      })
      .orderBy('submitTime', 'desc');

    const results = await query.get();

    // 处理结果数据，确保错词数据格式正确
    const processedResults = results.data.map(result => {
      // 处理错词数据
      let processedMistakes = [];
      if (result.mistakes && Array.isArray(result.mistakes)) {
        processedMistakes = result.mistakes.map(mistake => {
          // 确保错词数据是正确的格式
          let wordText = '';
          let userAnswerText = '';
          let correctAnswerText = '';

          // 处理单词字段
          if (typeof mistake.word === 'object' && mistake.word !== null) {
            wordText = mistake.word.word || mistake.word.words || mistake.word.english || '未知单词';
          } else {
            wordText = mistake.word || mistake.question || mistake.english || '未知单词';
          }

          // 处理用户答案
          if (typeof mistake.selectedAnswer === 'object' && mistake.selectedAnswer !== null) {
            userAnswerText = mistake.selectedAnswer.word || mistake.selectedAnswer.meaning || '未知答案';
          } else {
            userAnswerText = mistake.selectedAnswer || mistake.userAnswer || '未答';
          }

          // 处理正确答案
          if (typeof mistake.correctAnswer === 'object' && mistake.correctAnswer !== null) {
            correctAnswerText = mistake.correctAnswer.word || mistake.correctAnswer.meaning || '未知答案';
          } else {
            correctAnswerText = mistake.correctAnswer || mistake.meaning || mistake.chinese || '未知答案';
          }

          return {
            word: wordText,
            userAnswer: userAnswerText,
            correctAnswer: correctAnswerText,
            wordText: wordText,
            userAnswerText: userAnswerText,
            correctAnswerText: correctAnswerText
          };
        });
      }

      return {
        ...result,
        mistakes: processedMistakes
      };
    });

    return {
      success: true,
      data: processedResults
    };

  } catch (error) {
    console.error('获取用户测试结果失败:', error);
    return {
      success: false,
      message: '获取用户测试结果失败',
      error: error.message
    };
  }
};
