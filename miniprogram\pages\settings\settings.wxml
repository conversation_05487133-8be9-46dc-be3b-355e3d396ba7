<!-- 设置页面容器 -->
<view class="container">
  <!-- 学习计划 -->
  <view class="section">
    <view class="section-title">学习计划</view>
    <view class="section-content">
      <view class="setting-item">
        <text class="label">每日学习单词数</text>
        <picker bindchange="onDailyWordsChange" value="{{dailyWordsIndex}}" range="{{dailyWordsOptions}}">
          <view class="picker">
            {{dailyWordsOptions[dailyWordsIndex]}}个
          </view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 学习设置 -->
  <view class="section">
    <view class="section-title">学习设置</view>
    <view class="section-content">
      <view class="setting-item">
        <text class="label">学习提醒</text>
        <switch checked="{{reminderEnabled}}" bindchange="onReminderChange" color="#4a90e2"/>
      </view>
      <view class="setting-item" wx:if="{{reminderEnabled}}">
        <text class="label">提醒时间</text>
        <picker mode="time" value="{{reminderTime}}" bindchange="onReminderTimeChange">
          <view class="picker">
            {{reminderTime}}
          </view>
        </picker>
      </view>
      <view class="setting-item">
        <text class="label">自动播放发音</text>
        <switch checked="{{autoPlayAudio}}" bindchange="onAutoPlayAudioChange" color="#4a90e2"/>
      </view>
      <view class="setting-item">
        <text class="label">显示音标</text>
        <switch checked="{{showPhonetic}}" bindchange="onShowPhoneticChange" color="#4a90e2"/>
      </view>
      <view class="setting-item">
        <text class="label">显示例句</text>
        <switch checked="{{showExample}}" bindchange="onShowExampleChange" color="#4a90e2"/>
      </view>
    </view>
  </view>

  <!-- 其他设置 -->
  <view class="section">
    <view class="section-title">其他设置</view>
    <view class="section-content">
      <view class="setting-item" bindtap="clearLearningRecords">
        <text class="label">清除学习记录</text>
        <text class="value">></text>
      </view>
      <view class="setting-item" bindtap="goToAbout">
        <text class="label">关于我们</text>
        <text class="value">></text>
      </view>
    </view>
  </view>

  <!-- 保存按钮 -->
  <view class="save-btn" bindtap="saveSettings">保存设置</view>

  <!-- 清除记录确认弹窗 -->
  <view class="modal" wx:if="{{showClearModal}}">
    <view class="modal-content">
      <view class="modal-title">确认清除</view>
      <view class="modal-text">确定要清除所有学习记录吗？此操作不可恢复。</view>
      <view class="modal-buttons">
        <view class="modal-btn cancel" bindtap="cancelClear">取消</view>
        <view class="modal-btn confirm" bindtap="confirmClear">确定</view>
      </view>
    </view>
  </view>
</view> 