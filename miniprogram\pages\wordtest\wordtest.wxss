/* pages/wordtest/wordtest.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  padding-bottom: 180rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx 20rpx;
}

.header-content {
  color: white;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.page-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 创建竞赛提示 */
.competition-tip {
  background: rgba(255, 215, 0, 0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 20rpx 32rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid rgba(255, 215, 0, 0.4);
}

.competition-tip .tip-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.competition-tip .tip-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 词库栏 */
.library-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 24rpx 32rpx;
  margin-bottom: 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.library-info {
  flex: 1;
}

.library-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.library-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.switch-btn {
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;
}

.switch-btn:active {
  transform: scale(0.95);
}

/* 检测模式网格 */
.test-modes {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.mode-card {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
}

.mode-card:active {
  transform: scale(0.95) translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.card-content {
  padding: 40rpx 24rpx 32rpx;
  text-align: center;
  position: relative;
  background: linear-gradient(135deg, var(--mode-color-1), var(--mode-color-2));
  color: white;
  min-height: 180rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* 模式卡片颜色主题 */
.card-content.pink {
  --mode-color-1: #FF6B9D;
  --mode-color-2: #E91E63;
}

.card-content.blue {
  --mode-color-1: #4A90E2;
  --mode-color-2: #357ABD;
}

.card-content.green {
  --mode-color-1: #4CAF50;
  --mode-color-2: #388E3C;
}

.card-content.orange {
  --mode-color-1: #FF9800;
  --mode-color-2: #F57C00;
}

.card-content.purple {
  --mode-color-1: #9C27B0;
  --mode-color-2: #7B1FA2;
}

.card-content.teal {
  --mode-color-1: #26A69A;
  --mode-color-2: #00695C;
}

/* 卡片背景装饰 */
.card-content::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: modeRotate 20s linear infinite;
}

@keyframes modeRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.card-content::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0 0 24rpx 24rpx;
}

/* 模式图标 */
.mode-icon-wrapper {
  position: relative;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-glow {
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  z-index: 0;
  animation: iconGlow 3s ease-in-out infinite;
}

.mode-icon {
  font-size: 60rpx;
  display: block;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.2));
  animation: modeIconFloat 2s ease-in-out infinite;
  z-index: 1;
  position: relative;
}

@keyframes iconGlow {
  0%, 100% { 
    transform: scale(1);
    opacity: 0.2;
  }
  50% { 
    transform: scale(1.1);
    opacity: 0.3;
  }
}

@keyframes modeIconFloat {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-8rpx); }
  60% { transform: translateY(-4rpx); }
}

/* 模式标题和描述 */
.mode-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.mode-desc {
  font-size: 22rpx;
  opacity: 0.9;
  line-height: 1.3;
  margin-bottom: 16rpx;
  display: block;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 难度标签 */
.mode-level {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(6rpx);
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 点击交互效果 */
.mode-card:active .mode-icon {
  animation-play-state: paused;
  transform: scale(1.1);
}

.mode-card:active .icon-glow {
  transform: scale(1.2);
  opacity: 0.4;
}

/* 底部提示 */
.bottom-tips {
  text-align: center;
  padding: 20rpx;
}

.tip-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  line-height: 1.5;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 响应式调整 */
@media (max-width: 320px) {
  .test-modes {
    gap: 16rpx;
  }
  
  .card-content {
    padding: 32rpx 20rpx 28rpx;
    min-height: 160rpx;
  }
  
  .mode-icon {
    font-size: 48rpx;
  }
  
  .mode-title {
    font-size: 28rpx;
  }
}

.user-card {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  padding: 30rpx;
  border-radius: 20rpx;
  color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(74, 144, 226, 0.2);
}
.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}
.info {
  margin-left: 20rpx;
}
.username {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}
.streak {
  font-size: 24rpx;
  opacity: 0.9;
}

.plan-card {
  background: linear-gradient(135deg, #50E3C2, #39BFA8);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(80, 227, 194, 0.2);
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.title {
  font-size: 32rpx;
  font-weight: bold;
}
.progress {
  font-size: 28rpx;
  font-weight: bold;
}
.progress-bar {
  height: 8rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}
.progress-inner {
  height: 100%;
  background: #fff;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}
.plan-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  opacity: 0.9;
}

.stats-card {
  background: linear-gradient(135deg, #F5A623, #E08B1A);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(245, 166, 35, 0.2);
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  margin-top: 20rpx;
}
.stats-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 20rpx;
  border-radius: 16rpx;
}
.stats-item .value {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}
.stats-item .label {
  font-size: 24rpx;
  opacity: 0.9;
}

.records-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.records-list {
  max-height: 400rpx;
  overflow-y: auto;
}
.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.record-item:last-child {
  border-bottom: none;
}
.date {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}
.words {
  font-size: 26rpx;
  color: #666;
  flex: 1;
  text-align: center;
}
.time {
  font-size: 24rpx;
  color: #999;
}

.action-bar {
  position: fixed;
  bottom: 120rpx;
  left: 30rpx;
  right: 30rpx;
  display: flex;
  justify-content: space-between;
  z-index: 100;
}
.action-btn {
  flex: 1;
  margin: 0 15rpx;
  height: 100rpx;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  border: none;
  padding: 0;
}
.action-btn:nth-child(2) {
  background: linear-gradient(135deg, #7ED321, #6BB018);
}
.action-btn.review-btn {
  background: linear-gradient(135deg, #7ED321, #6BB018);
}
.action-btn .icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}
.action-btn text {
  color: #FFFFFF;
  font-size: 26rpx;
  font-weight: 500;
}
.action-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}
.action-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
  background: linear-gradient(135deg, #e0e0e0, #cccccc);
}
.action-btn.review-btn.disabled {
  background: linear-gradient(135deg, #e0e0e0, #cccccc);
}