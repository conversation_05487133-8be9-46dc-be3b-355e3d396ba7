/* 短文翻译选择页面样式 */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  box-sizing: border-box;
}

/* 页面头部 */
.page-header {
  margin-bottom: 40rpx;
}

.header-content {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.back-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-right: 20rpx;
}

.back-icon {
  font-size: 32rpx;
  color: #333;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: #666;
  display: block;
}

/* 选择卡片列表 */
.passage-list {
  margin-bottom: 40rpx;
}

.passage-card {
  margin-bottom: 24rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.passage-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.card-content {
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, var(--card-color-1), var(--card-color-2));
  color: white;
}

/* 卡片颜色主题 */
.card-content.blue {
  --card-color-1: #4A90E2;
  --card-color-2: #357ABD;
}

.card-content.green {
  --card-color-1: #7ED321;
  --card-color-2: #6CBE1A;
}

.card-content.purple {
  --card-color-1: #BD10E0;
  --card-color-2: #A00CC7;
}

.card-content.orange {
  --card-color-1: #F5A623;
  --card-color-2: #E8941B;
}

.card-icon-wrapper {
  position: relative;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-bg {
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  z-index: 0;
}

.card-icon {
  font-size: 64rpx;
  z-index: 1;
  position: relative;
}

.card-text {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.card-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.arrow-icon {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 20rpx;
}

/* 底部提示 */
.bottom-tips {
  text-align: center;
  padding: 32rpx 20rpx;
  margin-bottom: 40rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
} 