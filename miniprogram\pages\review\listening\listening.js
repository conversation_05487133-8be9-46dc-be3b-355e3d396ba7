Page({
  data: {
    mistakeIds: [], // 要复习的错题ID数组
    mistakes: [], // 错题数据数组
    currentIndex: 0, // 当前复习的错题索引
    currentMistake: null, // 当前错题数据
    total: 0, // 总错题数
    progress: 0, // 进度百分比
    knownCount: 0, // 认识的错题数
    unknownCount: 0, // 不认识的错题数
    showCompletion: false, // 是否显示完成提示
    innerAudioContext: null, // 音频播放器
    isPlaying: false, // 是否正在播放
    audioProgress: 0, // 音频播放进度
    audioDuration: 0 // 音频总时长
  },

  onLoad(options) {
    // 获取要复习的错题ID
    const mistakeIds = options.ids.split(',');
    this.setData({ 
      mistakeIds,
      total: mistakeIds.length
    });
    
    // 初始化音频播放器
    this.initAudioContext();
    
    // 加载错题数据
    this.loadMistakes();
  },

  onUnload() {
    // 页面卸载时销毁音频播放器
    if (this.innerAudioContext) {
      this.innerAudioContext.destroy();
    }
  },

  // 初始化音频播放器
  initAudioContext() {
    this.innerAudioContext = wx.createInnerAudioContext();
    
    // 监听播放进度
    this.innerAudioContext.onTimeUpdate(() => {
      const progress = (this.innerAudioContext.currentTime / this.innerAudioContext.duration) * 100;
      this.setData({ 
        audioProgress: progress,
        isPlaying: true
      });
    });

    // 监听播放结束
    this.innerAudioContext.onEnded(() => {
      this.setData({ 
        isPlaying: false,
        audioProgress: 0
      });
    });

    // 监听播放错误
    this.innerAudioContext.onError((res) => {
      console.error('音频播放错误:', res);
      wx.showToast({
        title: '音频播放失败',
        icon: 'none'
      });
      this.setData({ isPlaying: false });
    });
  },

  // 加载错题数据
  async loadMistakes() {
    const db = wx.cloud.database();
    const _ = db.command;
    
    try {
      const res = await db.collection('mistake_listening')
        .where({
          _id: _.in(this.data.mistakeIds)
        })
        .get();
      
      const mistakes = res.data;
      this.setData({ 
        mistakes,
        currentMistake: mistakes[0]
      });

      // 设置第一个错题的音频
      if (mistakes[0] && mistakes[0].audioUrl) {
        this.innerAudioContext.src = mistakes[0].audioUrl;
      }
    } catch (error) {
      console.error('加载错题失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 播放/暂停音频
  playAudio() {
    if (!this.data.currentMistake.audioUrl) return;

    if (this.data.isPlaying) {
      this.innerAudioContext.pause();
      this.setData({ isPlaying: false });
    } else {
      this.innerAudioContext.play();
      this.setData({ isPlaying: true });
    }
  },

  // 滑动进度条
  onSliderChange(e) {
    const value = e.detail.value;
    const time = (value / 100) * this.innerAudioContext.duration;
    this.innerAudioContext.seek(time);
    this.setData({ audioProgress: value });
  },

  // 标记为认识
  onMarkAsKnown() {
    this.updateProgress(true);
  },

  // 标记为不认识
  onMarkAsUnknown() {
    this.updateProgress(false);
  },

  // 更新进度
  updateProgress(isKnown) {
    const { currentIndex, mistakes, total } = this.data;
    const currentMistake = mistakes[currentIndex];
    
    // 更新统计数据
    this.setData({
      knownCount: isKnown ? this.data.knownCount + 1 : this.data.knownCount,
      unknownCount: isKnown ? this.data.unknownCount : this.data.unknownCount + 1
    });

    // 更新错题本
    this.updateMistake(currentMistake._id, isKnown);

    // 检查是否完成所有错题
    if (currentIndex === total - 1) {
      this.setData({ showCompletion: true });
      return;
    }

    // 更新当前错题和进度
    const nextIndex = currentIndex + 1;
    const progress = (nextIndex / total) * 100;
    
    // 设置下一个错题的音频
    if (mistakes[nextIndex] && mistakes[nextIndex].audioUrl) {
      this.innerAudioContext.src = mistakes[nextIndex].audioUrl;
    }
    
    this.setData({
      currentIndex: nextIndex,
      currentMistake: mistakes[nextIndex],
      progress,
      isPlaying: false,
      audioProgress: 0
    });
  },

  // 更新错题本
  async updateMistake(mistakeId, isKnown) {
    const db = wx.cloud.database();
    const openid = wx.getStorageSync('openid');
    
    try {
      if (isKnown) {
        // 如果认识，从错题本中删除
        await db.collection('mistake_listening')
          .doc(mistakeId)
          .remove();
      }
    } catch (error) {
      console.error('更新错题本失败:', error);
    }
  },

  // 完成复习
  onCompletionConfirm() {
    wx.navigateBack();
  }
}); 