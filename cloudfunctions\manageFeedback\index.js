const cloud = require('wx-server-sdk')

// 初始化云能力
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 管理员账号识别配置
const ADMIN_OPENID_PART = '5938DE76' // 微信登录账号ID部分
const ADMIN_PHONE = '15547663399' // 用户名密码登录账号

// 检查是否为管理员
function isAdmin(userOpenId, userInfo = null) {
  // 检查openid（微信登录）
  if (userOpenId && userOpenId.includes(ADMIN_OPENID_PART)) {
    return true
  }

  // 如果有额外的用户信息，检查手机号等字段
  if (userInfo) {
    if (userInfo.phone === ADMIN_PHONE || userInfo.username === ADMIN_PHONE) {
      return true
    }

    // 检查用户ID是否包含管理员标识（适用于微信登录用户）
    if (userInfo._id && userInfo._id.includes(ADMIN_OPENID_PART)) {
      return true
    }
  }

  return false
}

// 获取用户信息用于管理员验证
async function getUserInfoForAdmin(openId) {
  try {
    // 查询用户信息
    const userResult = await db.collection('users').where({ openid: openId }).get()
    if (userResult.data && userResult.data.length > 0) {
      return userResult.data[0]
    }
    return null
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

// 需要管理员权限的操作
const ADMIN_ACTIONS = ['list', 'reply', 'updateStatus', 'delete']

exports.main = async (event, context) => {
  const { action, data } = event
  const wxContext = cloud.getWXContext()
  const currentUserOpenId = wxContext.OPENID

  try {
    // 检查是否需要管理员权限
    if (ADMIN_ACTIONS.includes(action)) {
      // 获取用户信息用于管理员验证
      const userInfo = await getUserInfoForAdmin(currentUserOpenId)

      // 检查管理员权限
      const isAdminUser = isAdmin(currentUserOpenId, userInfo)

      console.log('反馈管理权限检查:', {
        action,
        currentUserOpenId,
        isAdminUser,
        userInfo: userInfo ? {
          username: userInfo.username,
          phone: userInfo.phone,
          _id: userInfo._id
        } : null
      })

      if (!isAdminUser) {
        return {
          success: false,
          message: '无权限执行此操作，仅管理员可管理反馈'
        }
      }
    }

    switch (action) {
      case 'submit':
        // 提交反馈
        const submitResult = await db.collection('feedbacks').add({
          data: {
            userId: context.OPENID,
            userNickName: data.userNickName || '匿名用户',
            problemText: data.problemText || '',
            suggestionText: data.suggestionText || '',
            deviceInfo: data.deviceInfo || {},
            timestamp: Date.now(),
            createTime: new Date(),
            status: 'pending', // pending, replied, closed
            adminReply: '',
            replyTime: null
          }
        })
        return {
          success: true,
          data: { id: submitResult._id },
          message: '反馈提交成功'
        }

      case 'list':
        // 获取所有反馈列表（管理员用）
        const listResult = await db.collection('feedbacks')
          .orderBy('createTime', 'desc')
          .limit(100)
          .get()
        return {
          success: true,
          data: listResult.data
        }

      case 'reply':
        // 管理员回复反馈
        const replyResult = await db.collection('feedbacks')
          .doc(data.id)
          .update({
            data: {
              adminReply: data.reply,
              replyTime: new Date(),
              status: 'replied'
            }
          })
        return {
          success: true,
          data: replyResult,
          message: '回复成功'
        }

      case 'updateStatus':
        // 更新反馈状态
        await db.collection('feedbacks')
          .doc(data.id)
          .update({
            data: {
              status: data.status
            }
          })
        return {
          success: true,
          message: '状态更新成功'
        }

      case 'delete':
        // 删除反馈
        await db.collection('feedbacks')
          .doc(data.id)
          .remove()
        return {
          success: true,
          message: '反馈删除成功'
        }

      case 'getUserFeedbacks':
        // 获取用户自己的反馈
        const userFeedbacks = await db.collection('feedbacks')
          .where({
            userId: context.OPENID
          })
          .orderBy('createTime', 'desc')
          .get()
        return {
          success: true,
          data: userFeedbacks.data
        }

      default:
        return {
          success: false,
          error: '无效的操作类型'
        }
    }
  } catch (error) {
    console.error('管理反馈失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
} 