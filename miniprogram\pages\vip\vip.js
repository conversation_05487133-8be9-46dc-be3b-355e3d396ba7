// pages/vip/vip.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showRedeem: false,
    redeemCode: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  showRedeemInput() {
    this.setData({ showRedeem: true })
  },

  onRedeemInput(e) {
    this.setData({ redeemCode: e.detail.value })
  },

  onRedeem() {
    const code = this.data.redeemCode.trim()
    if (!code) {
      wx.showToast({ title: '请输入兑换码', icon: 'none' })
      return
    }
    wx.showLoading({ title: '兑换中...' })
    wx.cloud.callFunction({
      name: 'redeemCode',
      data: { code },
      success: res => {
        wx.hideLoading()
        if (res.result.code === 200) {
          wx.showToast({ title: '兑换成功！', icon: 'success' })
          this.setData({ showRedeem: false, redeemCode: '' })
        } else {
          wx.showToast({ title: res.result.message, icon: 'none' })
        }
      },
      fail: err => {
        wx.hideLoading()
        wx.showToast({ title: '兑换失败', icon: 'none' })
      }
    })
  }
})