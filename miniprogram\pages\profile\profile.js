// pages/profile/profile.js
const app = getApp()
const VIP_CONFIG = require('../../utils/vip-config.js')
const util = require('../../utils/util.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    isAdmin: false,
    userInfo: null,
    isLoggedIn: false,
    showVipFeatures: VIP_CONFIG.enabled, // 控制VIP相关功能显示
    shortUserId: '未知',
    unreadFeedbackCount: 0 // 未读反馈数量
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserInfo()
    this.checkAdminStatus()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadUserInfo()
    this.checkAdminStatus() // 重新检查管理员状态，包括加载未读数量
    
    // 设置自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadUserInfo()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '墨词自习室 - 师生互动学习空间',
      path: '/pages/index/index',
      imageUrl: '/assets/icons/logo.png'
    }
  },

  goToCodeAdmin() {
    wx.navigateTo({ url: '/management/admin/codes/codes' })
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      console.log('=== Profile页面加载用户信息 ===')
      
      // 获取app实例
      const app = getApp()
      
      // 检查登录状态
      const isLoggedIn = app.isLoggedIn()
      console.log('登录状态:', isLoggedIn)
      
      const userInfo = await app.getUserInfo()
      console.log('获取到的用户信息:', userInfo)
      
      // 生成短ID
      const shortUserId = util.getShortUserId(userInfo._id)
      
      this.setData({ 
        userInfo,
        shortUserId,  // 添加短ID到数据中
        isLoggedIn
      })
      
      // 如果未登录，显示登录提示
      if (!isLoggedIn) {
        console.log('用户未登录，显示默认信息')
      }
    } catch (error) {
      console.error('加载用户信息失败：', error)
      wx.showToast({
        title: '加载用户信息失败',
        icon: 'none'
      })
    }
  },

  // 检查管理员状态
  async checkAdminStatus() {
    try {
      // 获取当前用户信息
      const app = getApp();
      const userInfo = await app.getUserInfo();
      
      // 管理员权限检测
      console.log('🔍 开始管理员权限检测...');
      console.log('用户ID:', userInfo?._id);
      console.log('用户名:', userInfo?.username);
      console.log('手机号:', userInfo?.phone);
      
      // 管理员账号标识
      const adminAccounts = [
        '5938DE76', // 微信登录的用户ID（部分）
        '***********' // 账号密码登录的用户名
      ];
      
      let isAdmin = false;
      let matchedField = '';
      
      if (userInfo) {
        // 检查用户ID（适用于微信登录）- 支持部分匹配
        if (userInfo._id) {
          const matchedAccount = adminAccounts.find(account => userInfo._id.includes(account));
          if (matchedAccount) {
            isAdmin = true;
            matchedField = `用户ID包含: ${matchedAccount}`;
            console.log('✅ 通过用户ID部分匹配检测到管理员权限:', userInfo._id, '匹配:', matchedAccount);
          }
        }
        
        // 检查完整用户ID匹配
        if (!isAdmin && userInfo._id && adminAccounts.includes(userInfo._id)) {
          isAdmin = true;
          matchedField = `用户ID: ${userInfo._id}`;
          console.log('✅ 通过用户ID完整匹配检测到管理员权限:', userInfo._id);
        }
        // 检查用户名（适用于账号密码登录）
        else if (userInfo.username && adminAccounts.includes(userInfo.username)) {
          isAdmin = true;
          matchedField = `用户名: ${userInfo.username}`;
          console.log('✅ 通过用户名检测到管理员权限:', userInfo.username);
        }
        // 检查openid（备用方案）
        else if (userInfo.openid && adminAccounts.includes(userInfo.openid)) {
          isAdmin = true;
          matchedField = `OpenID: ${userInfo.openid}`;
          console.log('✅ 通过openid检测到管理员权限:', userInfo.openid);
        }
        // 检查微信信息中的用户ID
        else if (userInfo.wechatInfo && userInfo.wechatInfo.openid && adminAccounts.includes(userInfo.wechatInfo.openid)) {
          isAdmin = true;
          matchedField = `微信OpenID: ${userInfo.wechatInfo.openid}`;
          console.log('✅ 通过微信OpenID检测到管理员权限:', userInfo.wechatInfo.openid);
        }
        // 检查是否有其他可能的字段包含管理员标识
        else {
          console.log('❌ 未匹配到管理员权限');
          console.log('待检查的管理员账号:', adminAccounts);
          
          // 遍历用户信息的所有字段，查找可能包含管理员标识的字段
          const checkAllFields = (obj, prefix = '') => {
            for (const [key, value] of Object.entries(obj)) {
              if (typeof value === 'string' && adminAccounts.includes(value)) {
                console.log(`🔍 在字段 ${prefix}${key} 中发现管理员标识: ${value}`);
                isAdmin = true;
                matchedField = `${prefix}${key}: ${value}`;
                return true;
              } else if (typeof value === 'object' && value !== null) {
                if (checkAllFields(value, `${prefix}${key}.`)) return true;
              }
            }
            return false;
          };
          
          checkAllFields(userInfo);
        }
      }
      
      console.log('🎯 权限检测结果:', isAdmin ? `✅ 管理员 (${matchedField})` : '❌ 非管理员');
      
      this.setData({ isAdmin });
      
      if (isAdmin) {
        this.loadAdminData(); // 加载管理员相关数据
      }
      
    } catch (error) {
      console.log('管理员状态检查失败:', error);
      this.setData({ isAdmin: false });
    }
  },

  // 编辑头像
  onEditAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const imagePath = res.tempFiles[0].tempFilePath;
        
        // 这里可以上传头像到云存储并更新用户信息
        wx.showToast({
          title: '头像更新功能开发中',
          icon: 'none'
        });
      },
      fail: (err) => {
        console.error('选择头像失败:', err);
      }
    });
  },

  // 跳转到个人中心
  goToPersonalCenter() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({
      url: '/pages/profile/personal-center/personal-center'
    })
  },

  // 跳转到登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 跳转到会员中心
  goToVip() {
    wx.navigateTo({
      url: '/pages/vip/vip'
    })
  },

  // 跳转到帮助页面
  goToHelp() {
    wx.navigateTo({
      url: '/pages/profile/help/help'
    })
  },

  // 跳转到关于页面
  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    })
  },

  // 跳转到我的分享
  goToMyShare() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({
      url: '/pages/profile/share/share'
    })
  },

  // 跳转到收到的分享
  goToReceivedShare() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({
      url: '/pages/profile/received/received'
    })
  },

  // 跳转到学习进度
  goToLearningProgress() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({
      url: '/management/learning-progress/learning-progress'
    })
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '需要登录',
      content: '请先登录后使用此功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.goToLogin()
        }
      }
    })
  },

  // 处理退出登录
  handleLogout() {
    if (!this.data.isLoggedIn) {
      this.goToLogin()
      return
    }

    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      confirmText: '退出',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.performLogout()
        }
      }
    })
  },

  // 执行退出登录
  async performLogout() {
    try {
      wx.showLoading({
        title: '退出中...',
        mask: true
      })

      // 清除本地存储的用户信息
      wx.removeStorageSync('userInfo')
      wx.removeStorageSync('userToken')
      wx.removeStorageSync('token')
      wx.removeStorageSync('openid')
      wx.removeStorageSync('sessionKey')
      wx.removeStorageSync('unionId')

      // 使用app的clearUserInfo方法清除全局用户信息
      const app = getApp()
      app.clearUserInfo()

      // 清除页面状态，显示未登录状态
      this.setData({
        isLoggedIn: false,
        userInfo: null,
        isAdmin: false
      })

      wx.hideLoading()

      wx.showToast({
        title: '已退出登录',
        icon: 'success',
        duration: 2000
      })

      // 不跳转页面，停留在"我的"界面，显示未登录状态
      console.log('退出登录成功，停留在我的界面')

      // 延迟刷新页面状态，确保toast显示完成
      setTimeout(() => {
        this.loadUserInfo()
        console.log('页面状态已刷新为未登录状态')
      }, 2000)

    } catch (error) {
      wx.hideLoading()
      console.error('退出登录失败:', error)
      wx.showToast({
        title: '退出失败',
        icon: 'none'
      })
    }
  },

  // 联系客服 - 跳转到专门的客服页面
  goToCustomerService() {
    console.log('点击联系客服');
    wx.navigateTo({
      url: '/pages/profile/customer-service/customer-service',
      success: () => {
        console.log('成功跳转到客服页面');
      },
      fail: (err) => {
        console.error('跳转客服页面失败:', err);
        wx.showToast({
          title: '页面加载失败',
          icon: 'none'
        });
      }
    });
  },

  // 管理员工具
  goToAdminTools() {
    wx.showActionSheet({
      itemList: ['查看反馈', '用户管理', '密码管理', '系统管理'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 查看反馈
            wx.navigateTo({
              url: '/management/admin/feedback/feedback'
            });
            break;
          case 1:
            // 用户管理
            wx.showToast({ title: '功能开发中', icon: 'none' });
            break;
          case 2:
            // 密码管理
            this.showPasswordManager();
            break;
          case 3:
            // 系统管理
            wx.showToast({ title: '功能开发中', icon: 'none' });
            break;
        }
      }
    });
  },

  // 加载管理员数据
  async loadAdminData() {
    try {
      // 获取未读反馈数量
      const result = await wx.cloud.callFunction({
        name: 'getUnreadFeedbackCount'
      });

      if (result.result && result.result.success) {
        const unreadCount = result.result.data.count || 0;
        this.setData({ unreadFeedbackCount: unreadCount });
        console.log('✅ 未读反馈数量:', unreadCount);
      }
    } catch (error) {
      console.error('⚠️ 加载管理员数据失败，使用默认值:', error.message);
      // 云函数失败时设置默认值，确保管理员功能仍可使用
      this.setData({ unreadFeedbackCount: 0 });
    }
  },

  // 管理员工具 - 更新版本
  goToAdminTools() {
    wx.navigateTo({
      url: '/management/admin/admin-tools/admin-tools'
    });
  },

  // 旧版本管理员工具（备用）
  goToOldAdminTools() {
    wx.showActionSheet({
      itemList: ['查看反馈', '用户管理', '密码管理', '系统管理'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 查看反馈
            wx.navigateTo({
              url: '/management/admin/feedback/feedback'
            });
            break;
          case 1:
            // 用户管理
            wx.showToast({ title: '功能开发中', icon: 'none' });
            break;
          case 2:
            // 密码管理
            this.showPasswordManager();
            break;
          case 3:
            // 系统管理
            wx.showToast({ title: '功能开发中', icon: 'none' });
            break;
        }
      }
    });
  },



  // 密码管理
  showPasswordManager() {
    wx.showModal({
      title: '教师权限密码管理',
      content: '当前教师权限密码修改功能',
      confirmText: '修改密码',
      success: (res) => {
        if (res.confirm) {
          // 显示密码修改页面
          wx.showToast({ title: '功能开发中', icon: 'none' });
        }
      }
    });
  },
});