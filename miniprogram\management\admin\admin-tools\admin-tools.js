Page({
  data: {
    unreadFeedbackCount: 0,
    loading: true
  },

  onLoad() {
    this.loadAdminData();
  },

  onShow() {
    this.loadAdminData();
  },

  // 加载管理员数据
  async loadAdminData() {
    wx.showLoading({ title: '加载中...' });
    
    try {
      // 获取未读反馈数量
      const result = await wx.cloud.callFunction({
        name: 'getUnreadFeedbackCount'
      });

      if (result.result && result.result.success) {
        const unreadCount = result.result.data.count || 0;
        this.setData({ 
          unreadFeedbackCount: unreadCount,
          loading: false
        });
        console.log('未读反馈数量:', unreadCount);
      } else {
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error('加载管理员数据失败:', error);
      this.setData({ loading: false });
    } finally {
      wx.hideLoading();
    }
  },

  // 跳转到通知管理
  goToNoticeManage() {
    wx.navigateTo({
      url: '/management/admin/notice/notice'
    });
  },

  // 跳转到反馈管理
  goToFeedbackManage() {
    wx.navigateTo({
      url: '/management/admin/feedback/feedback',
      success: () => {
        // 进入反馈页面后，重置角标计数
        this.setData({ unreadFeedbackCount: 0 });
      }
    });
  },



  // 刷新数据
  onPullDownRefresh() {
    this.loadAdminData();
    wx.stopPullDownRefresh();
  }
}) 