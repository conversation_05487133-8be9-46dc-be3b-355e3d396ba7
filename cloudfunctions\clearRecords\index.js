const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const learningRecordsCollection = db.collection('learning_records')
const mistakesCollection = db.collection('mistakes')
const usersCollection = db.collection('users')

exports.main = async (event, context) => {
  const { userId } = event

  try {
    // 删除学习记录
    await learningRecordsCollection.where({
      userId
    }).remove()

    // 删除错题记录
    await mistakesCollection.where({
      userId
    }).remove()

    // 重置用户统计数据
    await usersCollection.doc(userId).update({
      data: {
        stats: {
          totalTime: 0,
          totalWords: 0,
          masteredWords: 0,
          correctRate: 0,
          streakDays: 0,
          lastStudyDate: null,
          dailyStats: [],
          weeklyStats: [],
          monthlyStats: []
        }
      }
    })

    return {
      code: 200,
      message: '清除成功'
    }
  } catch (error) {
    console.error(error)
    return {
      code: 500,
      message: '服务器错误'
    }
  }
} 