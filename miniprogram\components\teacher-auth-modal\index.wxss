/* 教师权限验证弹窗样式 */
.teacher-auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
}

.modal-content {
  position: relative;
  width: 600rpx;
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 头部样式 */
.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
  color: white;
}

.header-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  animation: iconBounce 0.6s ease-out;
}

@keyframes iconBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

.header-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.4;
}

/* 密码输入区域 */
.password-section {
  padding: 40rpx 30rpx 20rpx;
}

.input-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.password-input {
  width: 100%;
  height: 88rpx;
  padding: 0 60rpx 0 24rpx;
  font-size: 28rpx;
  background: transparent;
  border: none;
  outline: none;
}

.password-input::placeholder {
  color: #adb5bd;
}

.eye-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.eye-icon:active {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-50%) scale(0.95);
}

.eye-icon .icon {
  font-size: 24rpx;
}

.password-hint {
  margin-top: 16rpx;
  padding-left: 8rpx;
}

.hint-text {
  font-size: 22rpx;
  color: #6c757d;
}

/* 按钮区域 */
.button-section {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  gap: 20rpx;
}

.auth-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.auth-btn::after {
  border: none;
}

.auth-btn:active {
  transform: scale(0.98);
}

.cancel-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #dee2e6;
}

.cancel-btn:active {
  background: #e9ecef;
}

.confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
}

.confirm-btn:active {
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.confirm-btn:disabled {
  background: #dee2e6;
  color: #adb5bd;
  box-shadow: none;
  transform: none;
}

.btn-text {
  position: relative;
  z-index: 1;
}

/* 底部提示 */
.modal-footer {
  padding: 0 30rpx 30rpx;
  text-align: center;
}

.footer-text {
  font-size: 22rpx;
  color: #6c757d;
  line-height: 1.4;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .modal-content {
    width: 540rpx;
  }
  
  .header-icon {
    font-size: 50rpx;
  }
  
  .header-title {
    font-size: 28rpx;
  }
  
  .header-subtitle {
    font-size: 22rpx;
  }
} 