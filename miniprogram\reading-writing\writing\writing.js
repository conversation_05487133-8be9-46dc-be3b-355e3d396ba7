Page({
  data: {
    // 学习模块
    learningModules: [
      {
        id: 'materials',
        title: '话题素材',
        subtitle: '丰富写作素材库',
        description: '精选优质写作素材，覆盖各类话题',
        icon: '📚',
        color: '#8B5CF6'
      },
      {
        id: 'essays',
        title: '话题范文',
        subtitle: '经典范文赏析',
        description: '优秀作文范例，提升写作水平',
        icon: '✨',
        color: '#8B5CF6'
      }
    ],

    // 通盖话题列表
    commonTopics: [
      { id: 'culture', title: '文化习俗/跨文化交流', color: '#4A90E2' },
      { id: 'school_life', title: '青少年校园生活/社会生活/成长与选择', color: '#7ED321' },
      { id: 'education', title: '教育学习', color: '#9013FE' },
      { id: 'technology', title: '科技媒体', color: '#00BCD4' },
      { id: 'environment', title: '环境保护', color: '#4CAF50' },
      { id: 'health', title: '体育健康', color: '#FF9800' },
      { id: 'art', title: '艺术', color: '#E91E63' },
      { id: 'relationships', title: '人际关系', color: '#673AB7' },
      { id: 'volunteer', title: '公益活动', color: '#F44336' },
      { id: 'travel', title: '旅行', color: '#2196F3' },
      { id: 'literature', title: '文学', color: '#9C27B0' },
      { id: 'career', title: '职业/大学专业', color: '#FF5722' }
    ],

    // 学习贴士
    learningTips: [
      '建议先学习话题素材，积累词汇和表达',
      '通过范文学习写作结构和技巧',
      '定期练习写作，巩固所学内容'
    ]
  },

  onLoad(options) {
    wx.setNavigationBarTitle({
      title: '写作积累'
    });
  },

  // 点击学习模块
  onModuleTap(e) {
    const moduleId = e.currentTarget.dataset.id;

    if (moduleId === 'materials') {
      // 跳转到话题素材页面
      wx.navigateTo({
        url: '/reading-writing/writing/topics/topics?type=materials'
      });
    } else if (moduleId === 'essays') {
      // 跳转到话题范文页面
      wx.navigateTo({
        url: '/reading-writing/writing/topics/topics?type=essays'
      });
    }
  },

  // 点击通盖话题
  onTopicTap(e) {
    const topicId = e.currentTarget.dataset.id;
    const topic = this.data.commonTopics.find(t => t.id === topicId);

    // 显示开发中提示
    wx.showModal({
      title: topic.title,
      content: '该话题内容正在完善中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    });
  }
})