Page({
  data: {
    words: [], // 错词列表
    selected: [], // 选中的单词id数组
    allSelected: false,
    loading: false
  },

  onLoad() {
    this.loadWords();
  },

  loadWords() {
    this.setData({ loading: true });
    const db = wx.cloud.database();
    const openid = wx.getStorageSync('openid');
    db.collection('mistake_words')
      .where({ openid })
      .get()
      .then(res => {
        const words = res.data.map(item => ({
          ...item,
          checked: false
        }));
        this.setData({
          words,
          selected: [],
          allSelected: false,
          loading: false
        });
      })
      .catch(() => {
        this.setData({ loading: false });
      });
  },

  onSelectAll() {
    const { words, allSelected } = this.data;
    if (allSelected) {
      // 取消全选
      words.forEach(w => w.checked = false);
      this.setData({
        words,
        selected: [],
        allSelected: false
      });
    } else {
      // 全选
      const selected = words.map(w => w._id);
      words.forEach(w => w.checked = true);
      this.setData({
        words,
        selected,
        allSelected: true
      });
    }
  },

  onInvertSelection() {
    const { words } = this.data;
    const selected = [];
    words.forEach(w => {
      w.checked = !w.checked;
      if (w.checked) selected.push(w._id);
    });
    this.setData({
      words,
      selected,
      allSelected: selected.length === words.length && words.length > 0
    });
  },

  onWordTap(e) {
    const { index } = e.currentTarget.dataset;
    const { words, selected } = this.data;
    const word = words[index];
    word.checked = !word.checked;
    let newSelected = [];
    if (word.checked) {
      newSelected = [...selected, word._id];
    } else {
      newSelected = selected.filter(id => id !== word._id);
    }
    this.setData({
      words,
      selected: newSelected,
      allSelected: newSelected.length === words.length && words.length > 0
    });
  },

  onReviewTap() {
    const { selected, words } = this.data;
    if (selected.length === 0) {
      wx.showToast({
        title: '请先选择要复习的单词',
        icon: 'none'
      });
      return;
    }
    // 跳转到复习页面，传递选中的单词id
    wx.navigateTo({
      url: `/pages/review/review?ids=${selected.join(',')}`
    });
  }
}); 