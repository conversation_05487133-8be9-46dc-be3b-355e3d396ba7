Page({
  data: {
    wordMistakeCount: 0,
    listeningMistakeCount: 0
  },

  onLoad: function() {
    this.loadMistakeCount();
  },

  onShow: function() {
    // 设置自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      });
    }
    // 每次显示页面时重新加载错词数量
    this.loadMistakeCount();
  },

  // 加载错题数量
  async loadMistakeCount() {
    try {
      const app = getApp();

      // 使用app.js中的getUserOpenId方法
      const openid = app.getUserOpenId();

      console.log('获取到的openid:', openid);

      if (!openid) {
        console.log('用户未登录，无法获取错词数量');
        return;
      }

      // 获取单词错题数量（按不同单词统计，而不是按错误次数）
      const db = wx.cloud.database();
      const _ = db.command;

      console.log('开始查询单词错词，openid:', openid);

      // 先简化查询，只查询_openid字段
      const wordRecords = await db.collection('mistakes')
        .where({
          _openid: openid,
          type: 'word'
        })
        .get();

      console.log('单词错词查询结果:', {
        total: wordRecords.data.length,
        sample: wordRecords.data.slice(0, 3)
      });

      // 统计三种模式的错词数量（与错词本内部页面保持一致）
      const statistics = {
        total: wordRecords.data.length,
        en_to_cn: 0,
        cn_to_en: 0,
        dictation: 0
      };

      wordRecords.data.forEach(record => {
        console.log('处理单词记录:', record);
        const mode = record.testMode || record.mistakeType;
        if (mode === 'en_to_cn' || mode === 'wordtest') {
          statistics.en_to_cn++;
        } else if (mode === 'cn_to_en') {
          statistics.cn_to_en++;
        } else if (mode === 'dictation') {
          statistics.dictation++;
        }
      });

      const wordCount = statistics.total;
      console.log('单词错词统计:', statistics);

      console.log('开始查询听写错词');

      // 只查询mistakes集合中type为listening的记录
      const listeningRecords1 = await db.collection('mistakes')
        .where({
          _openid: openid,
          type: 'listening'
        })
        .get();

      console.log('听写错词查询结果:', {
        mistakesCollection: listeningRecords1.data.length,
        sample1: listeningRecords1.data.slice(0, 2)
      });

      // 初始化空的第二个结果
      const listeningRecords2 = { data: [] };

      // 统计不同听写题目的数量
      const uniqueListeningItems = new Set();

      // 处理mistakes集合中的听写记录
      listeningRecords1.data.forEach(record => {
        console.log('处理听写记录1:', record);
        const itemKey = record.word || record.wordId;
        if (itemKey) {
          uniqueListeningItems.add(itemKey);
        }
      });

      // 处理mistake_listening集合中的记录
      listeningRecords2.data.forEach(record => {
        console.log('处理听写记录2:', record);
        const itemKey = record.word || record.wordId || record.question;
        if (itemKey) {
          uniqueListeningItems.add(itemKey);
        }
      });

      const listeningCount = uniqueListeningItems.size;
      console.log('听写去重后数量:', listeningCount, '去重集合:', Array.from(uniqueListeningItems));

      console.log('错词数量统计:', {
        openid: openid,
        word: wordCount,
        listening: listeningCount,
        wordRecordsTotal: wordRecords.data.length,
        listeningRecordsTotal: listeningRecords1.data.length + listeningRecords2.data.length,
        wordRecordsSample: wordRecords.data.slice(0, 2),
        listeningRecords1Sample: listeningRecords1.data.slice(0, 2),
        listeningRecords2Sample: listeningRecords2.data.slice(0, 2)
      });

      this.setData({
        wordMistakeCount: wordCount || 0,
        listeningMistakeCount: listeningCount || 0
      });

    } catch (error) {
      console.error('获取错词数量失败:', error);
      // 出错时设置为0
      this.setData({
        wordMistakeCount: 0,
        listeningMistakeCount: 0
      });
    }
  },

  onMistakeTap: function(e) {
    const type = e.currentTarget.dataset.type;
    
    // 根据错题本类型跳转到相应的详情页
    if (type === 'word') {
      wx.navigateTo({
        url: '/pages/mistakes/word/word'
      });
    } else if (type === 'listening') {
      wx.navigateTo({
        url: '/pages/mistakes/listening/listening'
      });
    }
  }
}); 