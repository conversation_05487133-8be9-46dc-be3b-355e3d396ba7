<view class="container">
  <!-- 操作按钮 -->
  <view class="action-bar">
    <button class="action-btn" bindtap="onSelectAll">全选</button>
    <button class="action-btn" bindtap="onInvertSelection">反选</button>
  </view>

  <!-- 错题列表 -->
  <view class="mistake-list">
    <view class="mistake-item {{item.checked ? 'selected' : ''}}" 
          wx:for="{{mistakes}}" 
          wx:key="_id" 
          bindtap="onMistakeTap" 
          data-index="{{index}}">
      <view class="checkbox">
        <image class="checkbox-icon" 
               src="/images/{{item.checked ? 'checkbox-checked.png' : 'checkbox.png'}}" 
               mode="aspectFit"></image>
      </view>
      <view class="mistake-content">
        <view class="mistake-text">{{item.text}}</view>
        <view class="mistake-translation">{{item.translation}}</view>
        <view class="mistake-info">
          <text class="mistake-source">来源：{{item.sourceName}}</text>
          <text class="mistake-time">{{item.createTime}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <view class="selection-info">已选择 {{selectedCount}} 个错题</view>
    <button class="next-btn {{selectedCount > 0 ? 'active' : ''}}" 
            bindtap="onReviewTap">开始复习</button>
  </view>
</view> 