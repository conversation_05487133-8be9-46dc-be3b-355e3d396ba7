const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

exports.main = async (event, context) => {
  const { code } = event
  const db = cloud.database()
  const wxContext = cloud.getWXContext()
  // 查找兑换码
  const codeRes = await db.collection('codes').where({ code, used: false }).get()
  if (codeRes.data.length === 0) {
    return { code: 400, message: '兑换码无效或已被使用' }
  }
  const codeInfo = codeRes.data[0]
  // 更新用户会员状态
  const userRes = await db.collection('users').where({ _openid: wxContext.OPENID }).get()
  if (userRes.data.length === 0) {
    return { code: 401, message: '用户不存在' }
  }
  const now = new Date()
  let expire = now
  if (userRes.data[0].vipExpire && userRes.data[0].vipExpire > now) {
    expire = new Date(userRes.data[0].vipExpire)
  }
  expire.setDate(expire.getDate() + (codeInfo.duration || 30))
  await db.collection('users').where({ _openid: wxContext.OPENID }).update({
    data: { vip: true, vipExpire: expire }
  })
  // 标记兑换码已用
  await db.collection('codes').where({ code }).update({
    data: { used: true, usedBy: wxContext.OPENID, usedAt: now }
  })
  return { code: 200, message: '兑换成功，会员已开通', vipExpire: expire }
} 