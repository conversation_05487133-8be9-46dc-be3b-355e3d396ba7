<!-- 听写模式选择页面 -->
<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">🎧 听写练习</text>
      <text class="page-subtitle">{{selectedWords.length}}个单词 · 选择学习模式</text>
    </view>
  </view>

  <!-- 模式选择卡片 -->
  <view class="mode-cards">
    <!-- 练习模式 -->
    <view class="mode-card {{selectedMode === 'practice' ? 'selected' : ''}}" 
          bindtap="onModeSelect" 
          data-mode="practice">
      <view class="card-header">
        <view class="mode-icon">{{practiceMode.icon}}</view>
        <view class="mode-info">
          <text class="mode-title">{{practiceMode.title}}</text>
          <text class="mode-desc">{{practiceMode.desc}}</text>
        </view>
      </view>
      <view class="mode-features">
        <view class="feature-item" wx:for="{{practiceMode.features}}" wx:key="*this">
          <text class="feature-icon">✓</text>
          <text class="feature-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 测试模式 -->
    <view class="mode-card {{selectedMode === 'test' ? 'selected' : ''}}" 
          bindtap="onModeSelect" 
          data-mode="test">
      <view class="card-header">
        <view class="mode-icon">{{testMode.icon}}</view>
        <view class="mode-info">
          <text class="mode-title">{{testMode.title}}</text>
          <text class="mode-desc">{{testMode.desc}}</text>
        </view>
      </view>
      <view class="mode-features">
        <view class="feature-item" wx:for="{{testMode.features}}" wx:key="*this">
          <text class="feature-icon">✓</text>
          <text class="feature-text">{{item}}</text>
        </view>
      </view>
    </view>


  </view>

  <!-- 测试模式配置 -->
  <view class="test-config" wx:if="{{selectedMode === 'test'}}">
    <!-- 播放次数设置 -->
    <view class="config-section">
      <view class="section-title">
        <text class="title">单词播放次数</text>
        <text class="subtitle">每个单词连续播放的遍数</text>
      </view>
      <view class="time-options">
        <view class="time-option {{playCount === 1 ? 'selected' : ''}}"
              bindtap="onPlayCountSelect"
              data-value="1">
          <text class="option-label">1次</text>
          <text class="option-desc">单遍播放</text>
        </view>
        <view class="time-option {{playCount === 2 ? 'selected' : ''}}"
              bindtap="onPlayCountSelect"
              data-value="2">
          <text class="option-label">2次</text>
          <text class="option-desc">重复播放</text>
        </view>
        <view class="time-option {{playCount === 3 ? 'selected' : ''}}"
              bindtap="onPlayCountSelect"
              data-value="3">
          <text class="option-label">3次</text>
          <text class="option-desc">加强记忆</text>
        </view>
      </view>
    </view>

    <!-- 停顿时间设置 -->
    <view class="config-section">
      <view class="section-title">
        <text class="title">每道题时间限制</text>
        <text class="subtitle">每个单词播放完成后的答题时间</text>
      </view>
      <view class="time-options">
        <view class="time-option {{timeLimit === 5 ? 'selected' : ''}}"
              bindtap="onTimeLimitSelect"
              data-value="5">
          <text class="option-label">5秒</text>
          <text class="option-desc">快速挑战</text>
        </view>
        <view class="time-option {{timeLimit === 10 ? 'selected' : ''}}"
              bindtap="onTimeLimitSelect"
              data-value="10">
          <text class="option-label">10秒</text>
          <text class="option-desc">标准速度</text>
        </view>
        <view class="time-option {{timeLimit === 15 ? 'selected' : ''}}"
              bindtap="onTimeLimitSelect"
              data-value="15">
          <text class="option-label">15秒</text>
          <text class="option-desc">正常节奏</text>
        </view>
        <view class="time-option {{timeLimit === 20 ? 'selected' : ''}}"
              bindtap="onTimeLimitSelect"
              data-value="20">
          <text class="option-label">20秒</text>
          <text class="option-desc">充裕时间</text>
        </view>
        <view class="time-option {{timeLimit === 30 ? 'selected' : ''}}"
              bindtap="onTimeLimitSelect"
              data-value="30">
          <text class="option-label">30秒</text>
          <text class="option-desc">慢节奏</text>
        </view>
      </view>
    </view>

    <!-- 🔧 新增：单词顺序设置 -->
    <view class="config-section">
      <view class="section-title">
        <text class="title">单词播放顺序</text>
        <text class="subtitle">选择听写时单词的播放顺序</text>
      </view>
      <view class="time-options">
        <view class="time-option {{wordOrder === 'original' ? 'selected' : ''}}"
              bindtap="onWordOrderSelect"
              data-value="original">
          <text class="option-label">保持原顺序</text>
          <text class="option-desc">按选择顺序</text>
        </view>
        <view class="time-option {{wordOrder === 'random' ? 'selected' : ''}}"
              bindtap="onWordOrderSelect"
              data-value="random">
          <text class="option-label">随机顺序</text>
          <text class="option-desc">增加挑战</text>
        </view>
      </view>
    </view>

    <!-- 🔧 新增：每组词汇数量设置 -->
    <view class="config-section">
      <view class="section-title">
        <text class="title">每组词汇数量</text>
        <text class="subtitle">选择每组听写包含的词汇数量</text>
      </view>
      <view class="time-options">
        <view class="time-option {{wordsPerGroup === 5 ? 'selected' : ''}}"
              bindtap="onWordsPerGroupSelect"
              data-value="5">
          <text class="option-label">5个/组</text>
          <text class="option-desc">快速节奏</text>
        </view>
        <view class="time-option {{wordsPerGroup === 10 ? 'selected' : ''}}"
              bindtap="onWordsPerGroupSelect"
              data-value="10">
          <text class="option-label">10个/组</text>
          <text class="option-desc">标准分组</text>
        </view>
        <view class="time-option {{wordsPerGroup === 15 ? 'selected' : ''}}"
              bindtap="onWordsPerGroupSelect"
              data-value="15">
          <text class="option-label">15个/组</text>
          <text class="option-desc">适中分组</text>
        </view>
        <view class="time-option {{wordsPerGroup === 20 ? 'selected' : ''}}"
              bindtap="onWordsPerGroupSelect"
              data-value="20">
          <text class="option-label">20个/组</text>
          <text class="option-desc">较大分组</text>
        </view>
        <view class="time-option {{wordsPerGroup === 25 ? 'selected' : ''}}"
              bindtap="onWordsPerGroupSelect"
              data-value="25">
          <text class="option-label">25个/组</text>
          <text class="option-desc">大分组</text>
        </view>
        <view class="time-option {{wordsPerGroup === 30 ? 'selected' : ''}}"
              bindtap="onWordsPerGroupSelect"
              data-value="30">
          <text class="option-label">30个/组</text>
          <text class="option-desc">最大分组</text>
        </view>
      </view>
    </view>

    <!-- 播放规则说明 -->
    <view class="config-section">
      <view class="section-title">
        <text class="title">💡 播放规则说明</text>
        <text class="subtitle">听写规则和注意事项</text>
      </view>
      <view class="tips-content">
        <view class="feature-item">
          <text class="feature-icon">✓</text>
          <text class="feature-text">相同单词不同遍数间隔固定为1秒</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">✓</text>
          <text class="feature-text">每页{{wordsPerGroup}}个单词，超过自动分页</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">✓</text>
          <text class="feature-text">播放过程为静音环境，专注听写</text>
        </view>
      </view>
    </view>

    <!-- 分享设置 -->
    <view class="config-section">
      <view class="section-title">
        <text class="title">测试对象</text>
        <text class="subtitle">选择谁来完成这个测试</text>
      </view>
      <view class="share-options">
        <view class="share-option {{shareOption === 'self' ? 'selected' : ''}}"
              bindtap="onShareOptionSelect"
              data-option="self">
          <view class="option-icon">👤</view>
          <view class="option-info">
            <text class="option-title">自己测试</text>
            <text class="option-desc">立即开始听写，错题自动记录</text>
          </view>
        </view>
        <view class="share-option {{shareOption === 'others' ? 'selected' : ''}}"
              bindtap="onShareOptionSelect"
              data-option="others">
          <view class="option-icon">👥</view>
          <view class="option-info">
            <text class="option-title">分享给他人</text>
            <text class="option-desc">创建分享链接，查看他人测试结果</text>
          </view>
        </view>
        <view class="share-option {{shareOption === 'competition' ? 'selected' : ''}}"
              bindtap="onShareOptionSelect"
              data-option="competition">
          <view class="option-icon">🏆</view>
          <view class="option-info">
            <text class="option-title">创建单词竞赛</text>
            <text class="option-desc">创建听写竞赛，与他人一起挑战</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 有效期设置（仅在分享给他人时显示） -->
    <view class="config-section" wx:if="{{shareOption === 'others'}}">
      <view class="section-title">
        <text class="title">分享有效期</text>
        <text class="subtitle">选择分享链接的有效时间</text>
      </view>
      <view class="time-options">
        <view class="time-option {{expireDays === item.value ? 'selected' : ''}}"
              wx:for="{{expireDaysOptions}}"
              wx:key="value"
              bindtap="onExpireDaysSelect"
              data-value="{{item.value}}">
          <text class="option-label">{{item.label}}</text>
          <text class="option-desc">{{item.desc}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="onBack">返回</button>
    <button class="action-btn primary {{selectedMode ? '' : 'disabled'}}" 
            bindtap="{{selectedMode === 'test' ? 'startTest' : 'startPractice'}}"
            disabled="{{!selectedMode}}">
      {{selectedMode === 'test' ? (shareOption === 'self' ? '开始测试' : shareOption === 'others' ? '创建分享' : '创建竞赛') : '开始练习'}}
    </button>
  </view>

  <!-- 自定义竞赛名称输入组件 -->
  <competition-input id="competitionInput" placeholder="请输入竞赛名称（例如：我的听写挑战）" bind:confirm="onCompetitionInputConfirm" bind:cancel="onCompetitionInputCancel" />

  <!-- 分享弹窗 -->
  <view class="share-modal" wx:if="{{showShareModal}}" bindtap="closeShareModal">
    <view class="share-content" catchtap="">
      <view class="share-header">
        <text class="share-title">分享测试</text>
        <text class="close-btn" bindtap="closeShareModal">✕</text>
      </view>
      
      <view class="share-info">
        <text class="share-desc">选择分享方式，将测试分享给朋友</text>
        <text class="share-note">朋友可以通过分享链接参与测试</text>
      </view>

      <view class="share-buttons">
        <button class="share-btn copy-btn" bindtap="copyCompleteShareInfo">
          <text class="share-btn-icon">📋</text>
          <text class="share-btn-text">复制测试信息</text>
        </button>

        <button
          class="share-btn wechat-share-btn"
          open-type="share"
          bindtap="onShareToWeChatFriends"
        >
          <text class="share-btn-icon">📤</text>
          <text class="share-btn-text">分享给微信好友</text>
        </button>

        <button class="share-btn timeline-btn" bindtap="onShareToTimeline">
          <text class="share-btn-icon">🌟</text>
          <text class="share-btn-text">分享到朋友圈</text>
        </button>
      </view>
    </view>
  </view>
</view> 