App({
  globalData: {
    userInfo: null,
    token: null,
    isLoggedIn: false,
    settings: {
      dailyWords: 20,
      soundEnabled: true,
      vibrationEnabled: true
    },
    learningData: null, // 学习数据
    customWords: null, // 自定义单词
    // 页面跳转返回标记
    returnToWordtest: false,
    returnToIndex: false,
    returnToElimination: false,
    returnTo: null
  },

  onLaunch() {
    console.log('=== 应用启动 ===');

    // 每次启动都检查小程序更新（无条件检查）
    this.checkForUpdates(false, true); // 第二个参数表示是启动检查

    // 初始化云开发
    if (wx.cloud) {
      try {
        wx.cloud.init({
          env: 'cloud1-4gh5ey7ycfac218f', // 替换为你的云开发环境ID
          traceUser: true
        });
        console.log('=== 云开发初始化成功 ===');
      } catch (error) {
        console.error('=== 云开发初始化失败 ===:', error);
      }
    } else {
      console.log('云开发不可用，使用本地数据');
    }

    // 检查登录状态
    this.checkLoginStatus();

    // 获取系统信息
    try {
      const deviceInfo = wx.getDeviceInfo();
      const windowInfo = wx.getWindowInfo();
      const appBaseInfo = wx.getAppBaseInfo();

      // 合并信息保持兼容性
      this.globalData.systemInfo = {
        ...deviceInfo,
        ...windowInfo,
        ...appBaseInfo
      };
      console.log('系统信息获取成功:', this.globalData.systemInfo);
    } catch (error) {
      console.log('系统信息获取失败:', error);
      // 降级处理
      this.globalData.systemInfo = {};
    }
  },

  onShow() {
    console.log('应用显示');

    // 应用从后台进入前台时，检查更新（降低频率）
    // 只有距离上次后台唤醒检查超过30分钟才重新检查
    // 注意：这里不影响启动时的检查，启动时总是会检查
    const lastBackgroundCheckTime = wx.getStorageSync('lastBackgroundUpdateCheckTime') || 0;
    const now = Date.now();
    const thirtyMinutes = 30 * 60 * 1000; // 30分钟

    if (now - lastBackgroundCheckTime > thirtyMinutes) {
      setTimeout(() => {
        this.checkForUpdates(true, false); // 第一个参数true表示后台唤醒，第二个参数false表示非启动
        wx.setStorageSync('lastBackgroundUpdateCheckTime', now);
      }, 2000); // 延迟2秒，避免与启动流程冲突
    }
  },

  // 检查小程序更新（静默强制更新）
  checkForUpdates(isFromBackground = false, isLaunch = false) {
    const logPrefix = isLaunch ? '🚀 启动时' : (isFromBackground ? '🔙 后台唤醒' : '🔄 手动');
    console.log(`${logPrefix}检查小程序更新...`);

    // 检查基础库版本是否支持更新API
    if (!wx.canIUse('getUpdateManager')) {
      console.log('当前基础库版本过低，无法使用更新API');
      // 对于旧版本，尝试其他方式提示用户更新微信
      if (isLaunch) {
        this.handleLegacyVersionUpdate();
      }
      return;
    }

    const updateManager = wx.getUpdateManager();

    // 检查更新
    updateManager.onCheckForUpdate((res) => {
      console.log('📱 更新检查结果:', res);
      if (res.hasUpdate) {
        console.log('🆕 发现新版本，开始静默下载...');
        // 记录发现更新的时间
        wx.setStorageSync('lastUpdateFoundTime', Date.now());
      } else {
        console.log('✅ 当前已是最新版本');
        // 对于旧版本，即使没有检测到更新，也尝试强制检查
        if (isLaunch) {
          this.checkLegacyVersionNeedsUpdate();
        }
      }
    });

    // 新版本下载完成 - 静默强制更新
    updateManager.onUpdateReady(() => {
      console.log('✅ 新版本下载完成，立即强制应用更新');

      // 清除任何待处理的更新标志
      wx.removeStorageSync('pendingUpdate');
      wx.removeStorageSync('lastUpdateFoundTime');

      // 立即应用更新并重启，无任何弹窗
      updateManager.applyUpdate();
    });

    // 新版本下载失败 - 静默处理，但会重试
    updateManager.onUpdateFailed((err) => {
      console.error('❌ 新版本下载失败:', err);

      // 静默重试机制：5分钟后重试一次
      if (isLaunch) {
        setTimeout(() => {
          console.log('🔄 下载失败，5分钟后重试更新检查...');
          this.checkForUpdates(false, false);
        }, 5 * 60 * 1000); // 5分钟后重试
      }
    });

    // 清除任何待处理的更新标志（启动时清理）
    if (isLaunch) {
      wx.removeStorageSync('pendingUpdate');
    }
  },

  // 处理旧版本更新（针对不支持getUpdateManager的版本）
  handleLegacyVersionUpdate() {
    console.log('🔄 处理旧版本更新检查...');

    // 检查是否是很久没有更新的版本
    const lastVersionCheckTime = wx.getStorageSync('lastVersionCheckTime') || 0;
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000; // 24小时

    // 如果超过24小时没有检查过版本，或者是第一次启动
    if (now - lastVersionCheckTime > oneDay || lastVersionCheckTime === 0) {
      wx.setStorageSync('lastVersionCheckTime', now);

      // 尝试通过其他方式检测是否需要更新
      // 可以通过检查本地存储的版本信息来判断
      this.checkLegacyVersionNeedsUpdate();
    }
  },

  // 检查旧版本是否需要更新
  checkLegacyVersionNeedsUpdate() {
    try {
      // 获取当前小程序版本信息
      const accountInfo = wx.getAccountInfoSync();
      const currentVersion = accountInfo.miniProgram.version;

      console.log('当前小程序版本:', currentVersion);

      // 如果没有版本号，说明是开发版或体验版，不需要处理
      if (!currentVersion) {
        console.log('开发版或体验版，跳过版本检查');
        return;
      }

      // 记录当前版本
      const lastKnownVersion = wx.getStorageSync('lastKnownVersion');
      if (lastKnownVersion !== currentVersion) {
        console.log('检测到版本变化:', lastKnownVersion, '->', currentVersion);
        wx.setStorageSync('lastKnownVersion', currentVersion);
      }

      // 对于特别旧的版本（比如没有记录的版本），强制尝试更新检查
      if (!lastKnownVersion) {
        console.log('首次记录版本信息，尝试强制更新检查');
        this.forceUpdateCheck();
      }

    } catch (error) {
      console.error('检查版本信息失败:', error);
    }
  },

  // 强制更新检查（针对旧版本）
  forceUpdateCheck() {
    console.log('🔄 强制更新检查...');

    // 尝试重新初始化更新管理器
    setTimeout(() => {
      if (wx.canIUse('getUpdateManager')) {
        const updateManager = wx.getUpdateManager();

        // 强制触发更新检查
        updateManager.onCheckForUpdate((res) => {
          if (res.hasUpdate) {
            console.log('🆕 强制检查发现新版本');
          }
        });

        updateManager.onUpdateReady(() => {
          console.log('✅ 强制检查：新版本下载完成，立即应用');
          updateManager.applyUpdate();
        });
      }
    }, 3000); // 延迟3秒后尝试
  },

  // 手动检查更新（管理员功能，保持弹窗提示）
  manualCheckUpdate() {
    console.log('🔄 管理员手动检查更新...');

    wx.showLoading({
      title: '检查更新中...',
      mask: true
    });

    if (!wx.canIUse('getUpdateManager')) {
      wx.hideLoading();
      wx.showModal({
        title: '提示',
        content: '当前微信版本过低，无法检查更新。请升级微信到最新版本。',
        showCancel: false
      });
      return;
    }

    const updateManager = wx.getUpdateManager();

    // 为手动检查创建新的事件监听器
    const manualUpdateManager = wx.getUpdateManager();

    manualUpdateManager.onCheckForUpdate((res) => {
      wx.hideLoading();

      if (res.hasUpdate) {
        wx.showModal({
          title: '发现新版本',
          content: '检测到新版本，将自动下载并更新。',
          showCancel: false,
          confirmText: '好的'
        });
      } else {
        wx.showModal({
          title: '✅ 已是最新版本',
          content: '您当前使用的已经是最新版本，无需更新。',
          showCancel: false,
          confirmText: '好的'
        });
      }
    });

    // 手动检查时，下载完成后也是强制更新
    manualUpdateManager.onUpdateReady(() => {
      console.log('✅ 手动检查：新版本下载完成，强制应用更新');
      manualUpdateManager.applyUpdate();
    });

    manualUpdateManager.onUpdateFailed((err) => {
      console.error('❌ 手动检查：新版本下载失败:', err);
      wx.showModal({
        title: '更新失败',
        content: '新版本下载失败，请检查网络连接后重试。',
        showCancel: false,
        confirmText: '确定'
      });
    });
  },

  // 检查登录状态
  checkLoginStatus() {
    try {
      // 检查多个可能的token存储键
      const token = wx.getStorageSync('token') || wx.getStorageSync('userToken');
      const userInfo = wx.getStorageSync('userInfo');
      const openid = wx.getStorageSync('openid');
      
      if (token && userInfo && userInfo._id) {
        this.globalData.token = token;
        this.globalData.userInfo = userInfo;
        this.globalData.isLoggedIn = true;
        
        // 确保openid也被设置
        if (userInfo.openid) {
          this.globalData.openid = userInfo.openid;
        } else if (openid) {
          this.globalData.openid = openid;
          // 如果userInfo没有openid，但本地存储有，更新userInfo
          userInfo.openid = openid;
          this.globalData.userInfo = userInfo;
          wx.setStorageSync('userInfo', userInfo);
        }
        
        console.log('检测到完整登录状态:', userInfo);
        console.log('用户token:', token);
        console.log('用户openid:', this.globalData.openid);
      } else if (openid) {
        // 如果只有openid，尝试自动恢复用户信息
        console.log('检测到openid，尝试自动恢复用户信息...');
        this.recoverUserInfo(openid);
      } else {
        console.log('未检测到登录信息，token:', token, 'userInfo:', userInfo, 'openid:', openid);
        this.globalData.isLoggedIn = false;
        this.globalData.userInfo = this.getDefaultUserInfo();
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
      this.globalData.isLoggedIn = false;
      this.globalData.userInfo = this.getDefaultUserInfo();
    }
  },

  // 恢复用户信息
  async recoverUserInfo(openid) {
    try {
      console.log('尝试从云端恢复用户信息...');
      
      // 调用云函数获取用户信息
      const result = await wx.cloud.callFunction({
        name: 'getUserInfo',
        data: { openid }
      });

      if (result.result && result.result.success) {
        const userInfo = result.result.data.userInfo;
        const token = result.result.data.token;
        
        // 保存恢复的用户信息
        const saveSuccess = this.setUserInfo(userInfo, token);
        
        if (saveSuccess) {
          console.log('用户信息恢复成功:', userInfo);
          wx.showToast({
            title: '欢迎回来！',
            icon: 'success',
            duration: 2000
          });
        }
      } else {
        console.log('云端用户信息不存在或已失效');
        this.globalData.isLoggedIn = false;
        this.globalData.userInfo = this.getDefaultUserInfo();
      }
    } catch (error) {
      console.error('恢复用户信息失败:', error);
      this.globalData.isLoggedIn = false;
      this.globalData.userInfo = this.getDefaultUserInfo();
    }
  },

  // 获取默认用户信息
  getDefaultUserInfo() {
    return {
      _id: null,
      username: '未登录用户',
      wechatInfo: {
        nickName: '未登录用户',
        avatarUrl: '/assets/icons/profile.png'
      },
      stats: {
        totalWords: 0,
        continuousDays: 0,
        masteredWords: 0,
        learnedWords: 0,
        reviewWords: 0,
        mistakeWords: 0,
        lastLearnDate: null
      },
      membership: {
        isVip: false,
        expireTime: null,
        type: 'free'
      },
      settings: {
        dailyWords: 20,
        reminderEnabled: false,
        reminderTime: '20:00',
        autoPlayAudio: true,
        showPhonetic: true,
        showExample: true
      }
    };
  },

  // 获取用户信息
  getUserInfo() {
    return new Promise((resolve, reject) => {
      if (this.globalData.isLoggedIn && this.globalData.userInfo) {
        resolve(this.globalData.userInfo);
      } else {
        // 检查本地存储
        try {
          const userInfo = wx.getStorageSync('userInfo');
          if (userInfo) {
            this.globalData.userInfo = userInfo;
            this.globalData.isLoggedIn = true;
            resolve(userInfo);
          } else {
            resolve(this.getDefaultUserInfo());
          }
        } catch (error) {
          console.error('获取用户信息失败:', error);
          resolve(this.getDefaultUserInfo());
        }
      }
    });
  },

  // 设置用户信息
  setUserInfo(userInfo, token) {
    try {
      this.globalData.userInfo = userInfo;
      this.globalData.token = token;
      this.globalData.isLoggedIn = true;
      
      // 同时设置openid到globalData，确保一致性
      if (userInfo && userInfo.openid) {
        this.globalData.openid = userInfo.openid;
        wx.setStorageSync('openid', userInfo.openid);
      }
      
      // 保存到本地存储
      wx.setStorageSync('userInfo', userInfo);
      wx.setStorageSync('token', token);
      
      console.log('用户信息已设置:', userInfo);
      console.log('openid已设置:', userInfo.openid);
      return true;
    } catch (error) {
      console.error('设置用户信息失败:', error);
      return false;
    }
  },

  // 清除用户信息
  clearUserInfo() {
    try {
      this.globalData.userInfo = this.getDefaultUserInfo();
      this.globalData.token = null;
      this.globalData.isLoggedIn = false;
      
      // 清除本地存储
      wx.removeStorageSync('userInfo');
      wx.removeStorageSync('token');
      
      console.log('用户信息已清除');
      return true;
    } catch (error) {
      console.error('清除用户信息失败:', error);
      return false;
    }
  },

  // 检查是否已登录
  isLoggedIn() {
    return this.globalData.isLoggedIn;
  },

  // 获取用户openid（更可靠的方式）
  getUserOpenId() {
    // 优先从globalData获取
    if (this.globalData.openid) {
      return this.globalData.openid;
    }
    
    // 从userInfo中获取
    if (this.globalData.userInfo && this.globalData.userInfo.openid) {
      // 同步到globalData
      this.globalData.openid = this.globalData.userInfo.openid;
      return this.globalData.userInfo.openid;
    }
    
    // 从本地存储获取
    try {
      const storedOpenId = wx.getStorageSync('openid');
      if (storedOpenId) {
        // 同步到globalData
        this.globalData.openid = storedOpenId;
        return storedOpenId;
      }
      
      // 从存储的userInfo中获取
      const storedUserInfo = wx.getStorageSync('userInfo');
      if (storedUserInfo && storedUserInfo.openid) {
        // 同步到globalData
        this.globalData.openid = storedUserInfo.openid;
        return storedUserInfo.openid;
      }
    } catch (error) {
      console.error('获取本地openid失败:', error);
    }
    
    return null;
  },

  // 检查是否可以收集错词（登录状态检查）
  canCollectMistakes() {
    const isLoggedIn = this.isLoggedIn();
    const openid = this.getUserOpenId();
    
    console.log('登录状态检查:', {
      isLoggedIn: isLoggedIn,
      hasOpenId: !!openid,
      openid: openid
    });
    
    return isLoggedIn && !!openid;
  },

  // 获取用户设置
  getSettings() {
    if (this.globalData.userInfo && this.globalData.userInfo.settings) {
      return this.globalData.userInfo.settings;
    }
    return this.globalData.settings;
  },

  // 更新用户设置
  updateSettings(settings) {
    return new Promise((resolve) => {
      if (this.globalData.userInfo && this.globalData.userInfo.settings) {
        this.globalData.userInfo.settings = { ...this.globalData.userInfo.settings, ...settings };
        // 更新本地存储
        wx.setStorageSync('userInfo', this.globalData.userInfo);
      } else {
        this.globalData.settings = { ...this.globalData.settings, ...settings };
      }
      console.log('设置已更新:', settings);
      resolve(true);
    });
  },

  // 全局错误处理
  onError(error) {
    console.error('=== 应用全局错误 ===:', error);
    console.error('错误堆栈:', error.stack);

    // 显示错误提示
    wx.showToast({
      title: '程序出现异常',
      icon: 'none',
      duration: 2000
    });
  },

  // 全局未捕获的Promise错误处理
  onUnhandledRejection(res) {
    console.error('=== 未处理的Promise错误 ===:', res.reason);
    console.error('Promise错误详情:', res);
  }
}); 