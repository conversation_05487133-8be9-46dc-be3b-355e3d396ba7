.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-content {
  width: 80%;
  background: #fff;
  border-radius: 10rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx 0;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  border-bottom: 1rpx solid #eee;
}

.modal-body {
  padding: 40rpx 30rpx;
}

.competition-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  box-sizing: border-box;
}

.input-placeholder {
  color: #999;
  font-size: 30rpx;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.btn-cancel, .btn-confirm {
  flex: 1;
  height: 90rpx;
  margin: 0;
  border-radius: 0;
  font-size: 32rpx;
  line-height: 90rpx;
}

.btn-cancel {
  background: #f5f5f5;
  color: #333;
}

.btn-confirm {
  background: #4a90e2;
  color: white;
} 