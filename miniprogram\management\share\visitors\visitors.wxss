.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.nav-actions {
  display: flex;
  gap: 20rpx;
}

.nav-btn {
  padding: 12rpx 24rpx;
  background-color: #007aff;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 统计信息 */
.stats-section {
  margin-bottom: 30rpx;
}

.stats-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stats-row {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 分享者进度信息 */
.creator-progress-section {
  margin-bottom: 30rpx;
}

.progress-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.progress-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.progress-library {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.progress-mode {
  display: inline-block;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  align-self: flex-start;
}

.progress-percent {
  font-size: 36rpx;
  font-weight: bold;
  color: #4facfe;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #e2e8f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-size: 26rpx;
  color: #4a5568;
  font-weight: 500;
}

.progress-time {
  font-size: 24rpx;
  color: #a0aec0;
}

/* 访问者列表 */
.visitors-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background-color: white;
  border-radius: 16rpx;
}

.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.empty-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 访问者列表 */
.visitors-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.visitor-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 访问者头部 */
.visitor-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.visitor-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.visitor-avatar image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  font-size: 32rpx;
  color: #666;
  font-weight: bold;
}

.visitor-info {
  flex: 1;
}

.visitor-name {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.name-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 12rpx;
}

.remark-text {
  font-size: 24rpx;
  color: #007aff;
  background-color: #e7f4ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.visitor-stats {
  display: flex;
  gap: 20rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #666;
}

/* 访问时间 */
.visitor-time {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.time-label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
}

.time-value {
  font-size: 26rpx;
  color: #333;
}

/* 操作按钮 */
.visitor-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 20rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 140rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  border: none;
  padding: 0;
}

.action-btn.primary {
  background-color: #007aff;
  color: white;
}

.action-btn.secondary {
  background-color: #f0f0f0;
  color: #333;
}

.action-btn.danger {
  background-color: #ff4757;
  color: white;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-body {
  padding: 30rpx;
}

.user-info {
  margin-bottom: 20rpx;
}

.user-name {
  font-size: 28rpx;
  color: #666;
}

.remark-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
  box-sizing: border-box;
}

.remark-input::placeholder {
  color: #999;
}

.input-tip {
  margin-top: 12rpx;
  text-align: right;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

.modal-actions {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border: none;
  border-radius: 0;
  background-color: white;
}

.modal-btn.cancel {
  color: #666;
}

.modal-btn.confirm {
  color: #007aff;
  border-left: 1rpx solid #f0f0f0;
} 