Page({
  data: {
    notices: [],
    loading: true,
    newNoticeContent: '',
    currentNotice: null,
    currentNoticeLoading: true
  },

  onLoad() {
    this.loadCurrentNotice();
    this.loadNotices();
  },

  onShow() {
    this.loadCurrentNotice();
    this.loadNotices();
  },

  // 格式化时间显示
  formatTime(time) {
    if (!time) return '';
    
    const date = new Date(time);
    const now = new Date();
    const diffTime = now - date;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      // 今天
      return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    } else if (diffDays === 1) {
      // 昨天
      return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    } else if (diffDays < 7) {
      // 一周内
      return `${diffDays}天前`;
    } else {
      // 超过一周，显示具体日期
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    }
  },

  // 加载当前生效的通知
  async loadCurrentNotice() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getNotice'
      });

      if (result.result && result.result.success) {
        this.setData({
          currentNotice: result.result.data,
          currentNoticeLoading: false
        });
      } else {
        this.setData({
          currentNotice: null,
          currentNoticeLoading: false
        });
      }
    } catch (error) {
      console.error('加载当前通知失败:', error);
      this.setData({
        currentNotice: null,
        currentNoticeLoading: false
      });
    }
  },

  // 加载通知列表
  async loadNotices() {
    wx.showLoading({ title: '加载中...' });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'manageNotice',
        data: {
          action: 'list'
        }
      });

      if (result.result && result.result.success) {
        this.setData({
          notices: result.result.data || [],
          loading: false
        });
      } else {
        throw new Error(result.result?.error || '加载失败');
      }
    } catch (error) {
      console.error('加载通知失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
      this.setData({ loading: false });
    } finally {
      wx.hideLoading();
    }
  },

  // 输入新通知内容
  onNewNoticeInput(e) {
    this.setData({
      newNoticeContent: e.detail.value
    });
  },

  // 添加新通知
  async addNotice() {
    const content = this.data.newNoticeContent.trim();
    if (!content) {
      wx.showToast({
        title: '请输入通知内容',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '添加中...' });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'manageNotice',
        data: {
          action: 'add',
          data: {
            content: content
          }
        }
      });

      if (result.result && result.result.success) {
        this.setData({ newNoticeContent: '' });
        this.loadCurrentNotice(); // 重新加载当前通知
        this.loadNotices(); // 重新加载列表
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
      } else {
        throw new Error(result.result?.error || '添加失败');
      }
    } catch (error) {
      console.error('添加通知失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 点击通知项
  onNoticeeTap(e) {
    const { index } = e.currentTarget.dataset;
    const notice = this.data.notices[index];
    
    const actions = notice.status === 'active' ? 
      ['编辑内容', '停用通知', '删除通知'] : 
      ['编辑内容', '启用通知', '删除通知'];
    
    wx.showActionSheet({
      itemList: actions,
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.editNotice(notice, index);
            break;
          case 1:
            this.toggleNoticeStatus(notice, index);
            break;
          case 2:
            this.deleteNotice(notice, index);
            break;
        }
      }
    });
  },

  // 编辑通知
  editNotice(notice, index) {
    wx.showModal({
      title: '编辑通知',
      content: notice.content,
      editable: true,
      success: async (res) => {
        if (res.confirm && res.content.trim() !== notice.content) {
          await this.updateNotice(notice._id, res.content.trim(), index);
        }
      }
    });
  },

  // 更新通知
  async updateNotice(noticeId, newContent, index) {
    wx.showLoading({ title: '更新中...' });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'manageNotice',
        data: {
          action: 'update',
          data: {
            id: noticeId,
            content: newContent
          }
        }
      });

      if (result.result && result.result.success) {
        const notices = [...this.data.notices];
        notices[index].content = newContent;
        notices[index].updateTime = new Date();
        this.setData({ notices });
        
        // 如果更新的是当前生效的通知，重新加载当前通知
        if (notices[index].status === 'active') {
          this.loadCurrentNotice();
        }
        
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
      } else {
        throw new Error(result.result?.error || '更新失败');
      }
    } catch (error) {
      console.error('更新通知失败:', error);
      wx.showToast({
        title: '更新失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 切换通知状态
  async toggleNoticeStatus(notice, index) {
    const newStatus = notice.status === 'active' ? 'inactive' : 'active';
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'manageNotice',
        data: {
          action: 'update',
          data: {
            id: notice._id,
            status: newStatus
          }
        }
      });

      if (result.result && result.result.success) {
        const notices = [...this.data.notices];
        notices[index].status = newStatus;
        this.setData({ notices });
        
        // 状态改变后重新加载当前通知
        this.loadCurrentNotice();
        
        wx.showToast({
          title: newStatus === 'active' ? '已启用' : '已停用',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('更新状态失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    }
  },

  // 删除通知
  deleteNotice(notice, index) {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条通知吗？',
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          await this.performDeleteNotice(notice._id, index);
        }
      }
    });
  },

  // 执行删除通知
  async performDeleteNotice(noticeId, index) {
    wx.showLoading({ title: '删除中...' });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'manageNotice',
        data: {
          action: 'delete',
          data: {
            id: noticeId
          }
        }
      });

      if (result.result && result.result.success) {
        const notices = [...this.data.notices];
        notices.splice(index, 1);
        this.setData({ notices });
        
        // 删除通知后重新加载当前通知（可能删除的就是当前通知）
        this.loadCurrentNotice();
        
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('删除通知失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 快速编辑当前通知
  editCurrentNotice() {
    if (!this.data.currentNotice) {
      wx.showToast({
        title: '当前没有生效的通知',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '编辑当前通知',
      content: this.data.currentNotice.content,
      editable: true,
      placeholderText: '请输入通知内容...',
      success: async (res) => {
        if (res.confirm && res.content.trim() !== this.data.currentNotice.content) {
          await this.updateCurrentNotice(res.content.trim());
        }
      }
    });
  },

  // 更新当前通知
  async updateCurrentNotice(newContent) {
    wx.showLoading({ title: '更新中...' });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'manageNotice',
        data: {
          action: 'update',
          data: {
            id: this.data.currentNotice._id,
            content: newContent
          }
        }
      });

      if (result.result && result.result.success) {
        this.loadCurrentNotice(); // 重新加载当前通知
        this.loadNotices(); // 重新加载通知列表
        
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
      } else {
        throw new Error(result.result?.error || '更新失败');
      }
    } catch (error) {
      console.error('更新当前通知失败:', error);
      wx.showToast({
        title: '更新失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 刷新
  onPullDownRefresh() {
    this.loadCurrentNotice();
    this.loadNotices();
    wx.stopPullDownRefresh();
  }
}) 