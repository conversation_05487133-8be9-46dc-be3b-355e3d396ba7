// 定时清理临时文件云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  console.log('开始定时清理临时文件');
  
  try {
    const now = new Date();
    
    // 查找需要删除的文件
    const { data: filesToDelete } = await db.collection('temp_files')
      .where({
        deleteTime: _.lte(now),
        status: 'pending'
      })
      .get();
    
    console.log(`找到 ${filesToDelete.length} 个需要删除的文件`);
    
    let deletedCount = 0;
    let errorCount = 0;
    
    // 逐个删除文件
    for (const file of filesToDelete) {
      try {
        // 删除云存储文件
        await cloud.deleteFile({
          fileList: [file.fileID]
        });
        
        // 更新数据库记录状态
        await db.collection('temp_files').doc(file._id).update({
          data: {
            status: 'deleted',
            deletedTime: now
          }
        });
        
        deletedCount++;
        console.log(`成功删除文件: ${file.fileID}`);
        
      } catch (error) {
        console.error(`删除文件失败: ${file.fileID}`, error);
        
        // 更新为错误状态
        await db.collection('temp_files').doc(file._id).update({
          data: {
            status: 'error',
            error: error.message,
            errorTime: now
          }
        });
        
        errorCount++;
      }
    }
    
    // 清理7天前的数据库记录
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const { stats: cleanupStats } = await db.collection('temp_files')
      .where({
        createTime: _.lte(sevenDaysAgo)
      })
      .remove();
    
    console.log(`清理了 ${cleanupStats.removed} 条过期数据库记录`);
    
    return {
      success: true,
      message: '定时清理完成',
      stats: {
        totalFiles: filesToDelete.length,
        deletedCount: deletedCount,
        errorCount: errorCount,
        cleanedRecords: cleanupStats.removed
      }
    };
    
  } catch (error) {
    console.error('定时清理失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};
