const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  
  try {
    console.log('开始测试数据库连接...');
    console.log('当前用户OPENID:', wxContext.OPENID);
    
    // 测试数据库连接
    const testData = {
      testId: 'test_' + Date.now(),
      createdBy: wxContext.OPENID,
      createTime: db.serverDate(),
      message: '数据库连接测试'
    };
    
    // 尝试插入测试数据
    const result = await db.collection('shareTests').add({
      data: testData
    });
    
    console.log('数据库插入成功:', result);
    
    // 尝试查询刚插入的数据
    const queryResult = await db.collection('shareTests')
      .where({
        testId: testData.testId
      })
      .get();
    
    console.log('数据库查询成功:', queryResult);
    
    // 删除测试数据
    if (queryResult.data.length > 0) {
      await db.collection('shareTests').doc(queryResult.data[0]._id).remove();
      console.log('测试数据已清理');
    }
    
    return {
      success: true,
      message: '数据库连接测试成功',
      data: {
        openid: wxContext.OPENID,
        insertResult: result,
        queryResult: queryResult
      }
    };
    
  } catch (error) {
    console.error('数据库测试失败:', error);
    return {
      success: false,
      message: '数据库测试失败',
      error: error.message,
      stack: error.stack
    };
  }
}; 