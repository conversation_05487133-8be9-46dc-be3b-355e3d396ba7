/* 页面容器 */
.container {
  min-height: 100vh;
  padding: 40rpx;
  background-color: #f8f9fa;
}

/* 设置区块 */
.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.section-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 0 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 设置项 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #333;
}

.value {
  font-size: 28rpx;
  color: #999;
}

/* 选择器 */
.picker {
  font-size: 28rpx;
  color: #666;
  padding: 8rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

/* 开关 */
switch {
  transform: scale(0.8);
}

/* 保存按钮 */
.save-btn {
  position: fixed;
  left: 40rpx;
  right: 40rpx;
  bottom: 40rpx;
  height: 96rpx;
  background-color: #4a90e2;
  color: #fff;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

/* 弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 32rpx;
}

.modal-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.modal-buttons {
  display: flex;
  gap: 24rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.modal-btn.cancel {
  background-color: #f0f0f0;
  color: #666;
}

.modal-btn.confirm {
  background-color: #ff4d4f;
  color: #fff;
} 