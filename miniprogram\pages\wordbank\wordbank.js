const app = getApp();
const { LIBRARY_INFO } = require('../../config/constants.js');
const VIP_CONFIG = require('../../utils/vip-config.js');

Page({
  data: {
    statusBarHeight: 0, // 状态栏高度
    // 动态生成词库数据
    wordbanks: {},
    returnTo: '', // 返回的页面标识
    showVipFeatures: VIP_CONFIG.enabled, // 控制VIP相关功能显示
    // 自定义词库相关
    customWordbanks: [], // 用户创建的自定义词库
    batchDeleteMode: false, // 批量删除模式
    showCreateModal: false, // 显示创建词库弹窗
    newWordbankName: '', // 新词库名称
    newWordbankContent: '', // 新词库内容
    inputMethod: 'text', // 输入方式：text-文本输入，excel-Excel上传
    selectedFile: null, // 选择的Excel文件
    processing: false, // 是否正在处理
    
    // Excel处理相关
    showFormatRequirements: false, // 显示格式要求详情
    removeDuplicates: true, // 自动去重
    processedWords: null, // 处理后的词汇预览
    textareaPlaceholder: `请输入单词和释义（必须包含）：
格式示例：
apple 苹果
play v. 玩耍
good adj. 好的 This is good.

每行一个单词，支持音标和例句...`
  },

  onLoad: function(options) {
    const { returnTo } = options;
    
    // 获取系统信息，设置状态栏高度
    let statusBarHeight = 0;
    try {
      const windowInfo = wx.getWindowInfo();
      statusBarHeight = windowInfo.statusBarHeight || 0;
    } catch (error) {
      console.warn('获取状态栏高度失败:', error);
    }
    
    this.setData({ 
      returnTo: returnTo || '',
      statusBarHeight: statusBarHeight
    });
    
    this.initWordbanks();
  },

  // 初始化词库数据
  initWordbanks: function() {
    const wordbanks = {
      college: {},
      gaokao: {},
      textbook: {
        renjiao: {
          name: '人教版同步',
          count: '分册学习',
          needsUpload: false,
          books: []
        },
        beishi: {
          name: '北师版同步',
          count: '分册学习',
          needsUpload: false,
          books: []
        }
      },
      phrase: {},
      special: {},
      other: {},
      zhongkao: {}
    };

    // 遍历所有词库信息
    Object.keys(LIBRARY_INFO).forEach(libraryId => {
      const info = LIBRARY_INFO[libraryId];

      if (info.type === 'zhongkao') {
        wordbanks.zhongkao[libraryId] = {
          id: libraryId,
          name: info.name,
          count: '加载中...',
          needsUpload: false
        };
      } else if (info.type === 'college') {
        wordbanks.college[libraryId] = {
          id: libraryId,
          name: info.name,
          count: '加载中...',
          needsUpload: false
        };
      } else if (info.type === 'gaokao') {
        wordbanks.gaokao[libraryId] = {
          id: libraryId,
          name: info.name,
          count: '加载中...',
          needsUpload: false
        };
      } else if (info.type === 'textbook') {
        if (info.series === 'renjiao') {
          wordbanks.textbook.renjiao.books.push({
            id: libraryId,
            name: info.name
          });
        } else if (info.series === 'beishi') {
          wordbanks.textbook.beishi.books.push({
            id: libraryId,
            name: info.name
          });
        }
      } else if (info.type === 'phrase') {
        wordbanks.phrase[libraryId] = {
          id: libraryId,
          name: info.name,
          count: '加载中...',
          needsUpload: false
        };
      } else if (info.type === 'special') {
        wordbanks.special[libraryId] = {
          id: libraryId,
          name: info.name,
          count: '加载中...',
          needsUpload: false
        };
      } else if (info.type === 'other') {
        wordbanks.other[libraryId] = {
          id: libraryId,
          name: info.name,
          count: '加载中...',
          needsUpload: false
        };
      }
    });

    this.setData({ wordbanks });

    // 异步加载词库的真实数量
    this.loadWordbanksCount();
  },

  // 加载词库的真实数量
  async loadWordbanksCount() {
    const wordbanks = this.data.wordbanks;

    // 需要加载数量的词库类型
    const typesToLoad = ['zhongkao', 'college', 'gaokao', 'phrase', 'special', 'other'];

    for (const type of typesToLoad) {
      const typeWordbanks = wordbanks[type];
      if (typeWordbanks && typeof typeWordbanks === 'object') {
        for (const libraryId of Object.keys(typeWordbanks)) {
          try {
            const count = await this.getLibraryWordCount(libraryId);
            // 更新对应词库的数量
            this.setData({
              [`wordbanks.${type}.${libraryId}.count`]: `${count}词`
            });
          } catch (error) {
            console.error(`获取词库${libraryId}数量失败:`, error);
            // 出错时显示默认值
            this.setData({
              [`wordbanks.${type}.${libraryId}.count`]: '未知'
            });
          }
        }
      }
    }
  },

  // 获取单个词库的词汇数量
  async getLibraryWordCount(libraryId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getWords',
        data: {
          libraryId: libraryId,
          getTotalCount: true,
          limit: 1 // 只需要获取总数，不需要具体词汇
        }
      });

      if (result.result.code === 200) {
        return result.result.totalCount || 0;
      } else {
        console.error('获取词汇数量失败:', result.result.message);
        return 0;
      }
    } catch (error) {
      console.error('调用getWords云函数失败:', error);
      return 0;
    }
  },

  onShow: function() {
    console.log('=== 词库页面 onShow 被调用 ===');
    
    // 设置自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
    }
    
    // 检查全局数据，确定是从哪个页面来的
    const app = getApp();
    console.log('=== 检查全局数据 ===');
    console.log('returnToWordtest:', app.globalData.returnToWordtest);
    console.log('returnToIndex:', app.globalData.returnToIndex);

    if (app.globalData.returnToWordtest) {
      console.log('=== 设置返回目标为 wordtest ===');
      this.setData({ returnTo: 'wordtest' });
      app.globalData.returnToWordtest = false;
    } else if (app.globalData.returnToIndex) {
      console.log('=== 设置返回目标为 index ===');
      this.setData({ returnTo: 'index' });
      app.globalData.returnToIndex = false;
    } else if (app.globalData.returnToElimination) {
      console.log('=== 设置返回目标为 elimination ===');
      this.setData({ returnTo: 'elimination' });
      app.globalData.returnToElimination = false;
    } else if (app.globalData.returnTo === 'dictation') {
      console.log('=== 设置返回目标为 dictation ===');
      this.setData({ returnTo: 'dictation' });
      app.globalData.returnTo = null;
    } else {
      console.log('=== 清除返回目标，正常浏览词库 ===');
      // 清除returnTo，表示是正常浏览词库
      this.setData({ returnTo: '' });
    }
    
    console.log('=== onShow 完成，最终 returnTo 值 ===:', this.data.returnTo);
    
    // 加载自定义词库
    this.loadCustomWordbanks();
  },

  onWordbankTap: function(e) {
    const { id, type } = e.currentTarget.dataset;
    console.log('=== 点击词库 ===', id, type);
    console.log('当前 returnTo:', this.data.returnTo);
    
    // 检查是否为教材系列
    if (type === 'textbook') {
      const series = e.currentTarget.dataset.series;
      // 跳转到分册选择页面
      wx.navigateTo({
        url: `/pages/wordbank/unit/unit?series=${series}&returnTo=${this.data.returnTo}`
      });
      return;
    }

    // 获取词库信息
    const wordbank = LIBRARY_INFO[id];
    if (!wordbank) {
      wx.showToast({ title: '词库不存在', icon: 'error' });
      return;
    }

    // 所有词库都已经可用，不需要上传检查

    // VIP功能已封存，移除VIP权限检查

        // 如果是从其他页面来的，处理返回逻辑
    if (this.data.returnTo === 'wordtest') {
      console.log('=== 处理返回到单词检测页面的逻辑 ===');
      // 保存到用户设置中，确保下次进入时还是这个词库
      this.saveUserLibrarySetting(id, wordbank.name);
      
      // 通过全局数据传递选中的词库信息
      const app = getApp();
      app.globalData.selectedLibrary = {
        id: id,
        name: wordbank.name
      };
      
      // 立即跳转回单词检测页面，不需要延迟
      console.log('=== 准备跳转回单词检测页面 ===');
      wx.navigateTo({
        url: '/pages/wordtest/wordtest',
        success: () => {
          console.log('=== 成功跳转到单词检测页面 ===');
          // 跳转成功后再显示提示
          wx.showToast({
            title: `已选择${wordbank.name}`,
            icon: 'success',
            duration: 2000
          });
        },
        fail: (err) => {
          console.error('=== 跳转到单词检测页面失败 ===:', err);
          // 如果跳转失败，可能是页面栈问题，使用redirectTo
          wx.redirectTo({
            url: '/pages/wordtest/wordtest',
            success: () => {
              wx.showToast({
                title: `已选择${wordbank.name}`,
                icon: 'success',
                duration: 2000
              });
            }
          });
        }
      });
      return;
    } else if (this.data.returnTo === 'index') {
      // 保存到用户设置中，确保下次进入时还是这个词库
      this.saveUserLibrarySetting(id, wordbank.name);
      
      // 通过全局数据传递选中的词库信息，用于首页更新显示
      const app = getApp();
      app.globalData.selectedLibrary = {
        id: id,
        name: wordbank.name
      };
      
      // 切换到首页
      wx.switchTab({
        url: '/pages/index/index',
        success: () => {
          wx.showToast({
            title: `已设置默认词库：${wordbank.name}`,
            icon: 'success',
            duration: 2000
          });
        }
      });
      return;

    } else if (this.data.returnTo === 'elimination') {
      // 消消乐模式，保存库信息然后跳转到词汇选择页面
      this.saveUserLibrarySetting(id, wordbank.name);
      
      // 跳转到词汇列表页面，指定为消消乐模式
      wx.navigateTo({
        url: `/pages/wordbank/wordlist/wordlist?libraryId=${id}&mode=elimination&testMode=elimination`
      });
      return;
    } else if (this.data.returnTo === 'dictation') {
      // 听写模式，保存库信息然后跳转到词汇选择页面
      this.saveUserLibrarySetting(id, wordbank.name);
      
      // 跳转到词汇列表页面，指定为听写模式
      wx.navigateTo({
        url: `/pages/wordbank/wordlist/wordlist?libraryId=${id}&mode=select&testMode=dictation`
      });
      return;
    }

    // 跳转到单词列表页面
    console.log('=== 执行默认跳转逻辑，进入单词列表页面 ===');
    console.log('当前 returnTo 值:', this.data.returnTo);
    console.log('判断条件: returnTo === "wordtest"?', this.data.returnTo === 'wordtest');
    
    wx.navigateTo({
      url: `/pages/wordbank/wordlist/wordlist?libraryId=${id}`
    });
  },

  // 保存用户词库设置
  async saveUserLibrarySetting(libraryId, libraryName) {
    try {
      // 保存到本地缓存
      wx.setStorageSync('currentLibrary', {
        id: libraryId,
        name: libraryName
      });
      
      // 如果用户已登录，也保存到云端
      const app = getApp();
      const userInfo = await app.getUserInfo();
      if (userInfo && userInfo._id) {
        // 这里可以调用云函数保存用户设置
        console.log('用户已登录，词库设置已保存到本地');
      }
    } catch (error) {
      console.error('保存词库设置失败:', error);
    }
  },

  // 返回按钮处理
  onBack: function() {
    if (this.data.returnTo === 'wordtest') {
      wx.switchTab({
        url: '/pages/index/index'
      });
    } else {
      wx.navigateBack();
    }
  },

  // 自定义词库相关方法
  
  // 加载自定义词库
  async loadCustomWordbanks() {
    try {
      const db = wx.cloud.database();
      const userInfo = await app.getUserInfo();
      
      if (!userInfo || !userInfo.openid) {
        console.log('用户未登录，跳过加载自定义词库');
        return;
      }

      const result = await db.collection('custom_wordbanks')
        .where({
          creatorOpenid: userInfo.openid
        })
        .orderBy('createTime', 'desc')
        .get();

      console.log('加载的自定义词库:', result.data);
      
      // 格式化时间
      const formattedData = result.data.map(item => ({
        ...item,
        createTimeFormatted: this.formatTime(item.createTime)
      }));

      this.setData({
        customWordbanks: formattedData
      });
    } catch (error) {
      console.error('加载自定义词库失败:', error);
      
      // 如果是集合不存在的错误，忽略它（第一次使用时正常现象）
      if (error.errCode === -502005) {
        console.log('自定义词库集合尚未创建，这是正常现象');
        this.setData({
          customWordbanks: []
        });
      }
    }
  },

  // 显示创建词库弹窗
  showCreateWordbankModal() {
    this.setData({
      showCreateModal: true,
      newWordbankName: '',
      newWordbankContent: '',
      inputMethod: 'text',
      selectedFile: null,
      processing: false
    });
  },

  // 隐藏创建词库弹窗
  hideCreateModal() {
    this.setData({
      showCreateModal: false,
      newWordbankName: '',
      newWordbankContent: '',
      inputMethod: 'text',
      selectedFile: null,
      processing: false
    });
  },

  // 切换输入方式
  switchInputMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      inputMethod: method,
      selectedFile: null,
      newWordbankContent: ''
    });
  },

  // 选择Excel文件
  chooseExcelFile() {
    // 优先使用 chooseMessageFile，如果失败则使用 wx.chooseMedia
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['xlsx', 'xls'],
      success: (res) => {
        this.handleFileSelection(res.tempFiles[0]);
      },
      fail: (error) => {
        console.log('chooseMessageFile失败，尝试其他方式:', error);
        // 如果chooseMessageFile失败，尝试使用chooseMedia
        wx.chooseMedia({
          count: 1,
          mediaType: ['file'],
          success: (res) => {
            if (res.tempFiles && res.tempFiles.length > 0) {
              this.handleFileSelection(res.tempFiles[0]);
            }
          },
          fail: (mediaError) => {
            console.error('选择文件失败:', mediaError);
            wx.showModal({
              title: '文件选择失败',
              content: '当前环境不支持文件选择功能。\n\n建议使用以下方法：\n1. 使用文本输入方式\n2. 下载Excel模板填写后重试\n3. 检查微信版本是否为最新',
              showCancel: false
            });
          }
        });
      }
    });
  },

  // 下载Excel模板
  downloadExcelTemplate() {
    // 显示模板下载说明
    wx.showModal({
      title: '📋 Excel模板说明',
      content: '由于小程序限制，请按以下格式创建Excel文件：\n\n第一行（标题行）：\n• A1: words（必需）\n• B1: phonetic（可选）\n• C1: meaning（必需）\n• D1: example（可选）\n\n数据行示例：\n• A2: apple, B2: /ˈæpl/, C2: n. 苹果, D2: I like apples.\n• A3: play, B3: /pleɪ/, C3: v. 玩耍, D3: He plays ball.\n\n注意事项：\n• 必须包含标题行\n• words和meaning列必填\n• phonetic和example列可选\n• 列顺序可调整，以列名为准\n• 最多10000个单词',
      confirmText: '我知道了',
      showCancel: false
    });
  },

  // 移除已选择的文件
  removeSelectedFile() {
    this.setData({
      selectedFile: null
    });
    wx.showToast({
      title: '文件已移除',
      icon: 'success'
    });
  },

  // 处理文件选择
  handleFileSelection(file) {
    // 检查文件类型
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
      wx.showModal({
        title: '文件格式错误',
        content: '请选择 Excel 文件（.xlsx 或 .xls 格式）\n\n当前文件：' + file.name,
        showCancel: false,
        confirmText: '重新选择'
      });
      return;
    }

    // 检查文件大小
    const maxSize = 5 * 1024 * 1024; // 5MB限制
    if (file.size > maxSize) {
      wx.showModal({
        title: '文件过大',
        content: `文件大小不能超过 5MB\n\n当前文件：${(file.size / 1024 / 1024).toFixed(2)}MB\n建议：\n• 减少词汇数量（建议少于5000个）\n• 删除不必要的格式和图片\n• 保存为较旧的 Excel 格式`,
        showCancel: false,
        confirmText: '重新选择'
      });
      return;
    }

    // 检查文件名是否包含特殊字符
    const validFileName = /^[a-zA-Z0-9\u4e00-\u9fa5._\-\s]+$/;
    if (!validFileName.test(file.name)) {
      wx.showModal({
        title: '文件名包含特殊字符',
        content: '文件名中包含特殊字符可能导致上传失败\n\n建议使用中文、英文、数字和基本符号',
        showCancel: true,
        confirmText: '继续使用',
        cancelText: '重新选择',
        success: (res) => {
          if (res.confirm) {
            this.setSelectedFile(file);
          }
        }
      });
      return;
    }

    this.setSelectedFile(file);
  },

  // 设置选中的文件
  setSelectedFile(file) {
    // 计算文件大小文本
    let sizeText = '未知大小';
    if (file.size) {
      const sizeKB = file.size / 1024;
      if (sizeKB < 1024) {
        sizeText = sizeKB.toFixed(1) + ' KB';
      } else {
        sizeText = (sizeKB / 1024).toFixed(1) + ' MB';
      }
    }

    this.setData({
      selectedFile: {
        name: file.name,
        path: file.path,
        size: file.size,
        sizeText: sizeText
      }
    });

    wx.showToast({
      title: '文件选择成功',
      icon: 'success',
      duration: 1500
    });

    // 显示文件信息提示
    setTimeout(() => {
      wx.showToast({
        title: `${file.name} (${sizeText})`,
        icon: 'none',
        duration: 2000
      });
    }, 1600);
  },

  // 移除选择的文件
  removeSelectedFile() {
    this.setData({
      selectedFile: null
    });
  },



  // 词库名称输入
  onWordbankNameInput(e) {
    this.setData({
      newWordbankName: e.detail.value
    });
  },

  // 词库内容输入
  onWordbankContentInput(e) {
    this.setData({
      newWordbankContent: e.detail.value
    });
  },

  // 解析词汇内容（支持多种格式）
  parseWordbankContent(content) {
    const lines = content.trim().split('\n');
    const words = [];
    const seenWords = new Set(); // 用于去重

    lines.forEach((line, index) => {
      line = line.trim();
      if (!line) return;

      // 支持多种格式（但必须包含释义）：
      // 1. apple 苹果
      // 2. apple /ˈæpl/ 苹果
      // 3. apple 苹果 I like apples.
      // 4. apple /ˈæpl/ 苹果 I like apples.
      const parts = line.split(/\s+/);
      if (parts.length < 2) {
        console.warn(`第${index + 1}行格式错误：必须包含单词和释义`);
        return;
      }

      const word = parts[0].toLowerCase();
      if (seenWords.has(word)) return; // 去重
      seenWords.add(word);

      let phonetic = '';
      let meaning = '';
      let example = '';

      // 查找音标
      const phoneticMatch = line.match(/\/[^\/]+\//);
      if (phoneticMatch) {
        phonetic = phoneticMatch[0];
        // 移除音标后重新解析
        const withoutPhonetic = line.replace(phoneticMatch[0], '').trim();
        const newParts = withoutPhonetic.split(/\s+/);
        meaning = newParts[1] || '';
        example = newParts.slice(2).join(' ') || '';
      } else {
        meaning = parts[1] || '';
        example = parts.slice(2).join(' ') || '';
      }

      // 验证必须有释义
      if (!meaning) {
        console.warn(`第${index + 1}行缺少释义：${word}`);
        return;
      }

      words.push({
        word: word,
        phonetic: phonetic,
        meaning: meaning,
        example: example,
        // 保持兼容性，同时保存为chinese字段
        chinese: meaning
      });
    });

    return words;
  },

  // 处理Excel文件内容
  async processExcelFile(filePath) {
    try {
      // 1. 上传文件到云存储
      wx.showLoading({
        title: '上传文件中...',
        mask: true
      });

      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: `excel/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.xlsx`,
        filePath: filePath
      });

      // 2. 调用云函数处理Excel文件
      wx.showLoading({
        title: '解析Excel文件中...',
        mask: true
      });

      const processResult = await wx.cloud.callFunction({
        name: 'processExcel',
        data: {
          fileID: uploadResult.fileID
        },
        config: {
          timeout: 120000 // 设置超时时间为120秒
        }
      });

      // 3. 删除临时上传的文件
      try {
        await wx.cloud.deleteFile({
          fileList: [uploadResult.fileID]
        });
      } catch (deleteError) {
        console.warn('删除临时文件失败:', deleteError);
      }

      // 4. 处理结果
      if (processResult.result.code === 200) {
        const { words, totalRows, validWords, errors, format } = processResult.result.data;
        
        console.log(`Excel解析完成: 总行数${totalRows}, 有效词汇${validWords}个`);
        console.log('检测到的格式:', format);
        
        // 如果有错误，显示错误信息
        if (errors && errors.length > 0) {
          const errorMsg = errors.slice(0, 5).join('\n') + (errors.length > 5 ? `\n...还有${errors.length - 5}个错误` : '');
          
          await new Promise(resolve => {
            wx.showModal({
              title: `发现 ${errors.length} 个问题`,
              content: `已成功解析 ${validWords} 个单词。\n\n错误详情：\n${errorMsg}\n\n是否继续创建词库？`,
              confirmText: '继续创建',
              cancelText: '取消',
              success: (res) => {
                if (!res.confirm) {
                  throw new Error('用户取消操作');
                }
                resolve();
              }
            });
          });
        }
        
        // 显示解析结果摘要
        wx.showToast({
          title: `成功解析${validWords}个单词`,
          icon: 'success',
          duration: 2000
        });
        
        return words;
      } else {
        throw new Error(processResult.result.message || '解析失败');
      }

    } catch (error) {
      console.error('Excel文件处理失败:', error);
      
      // 更详细的错误处理
      let errorMessage = 'Excel文件处理失败';
      
      // 检查具体错误类型
      if (error.errCode) {
        switch (error.errCode) {
          case -404002:
          case -1:
            errorMessage = 'processExcel云函数未部署，请联系管理员';
            break;
          case -504003:
            errorMessage = 'Excel文件处理超时。\n\n解决方案：\n1. 检查文件大小（建议小于2MB）\n2. 减少单词数量（建议少于1000行）\n3. 检查网络连接';
            break;
          default:
            errorMessage = `云函数调用失败 (错误码: ${error.errCode})`;
        }
      } else if (error.message) {
        if (error.message.includes('用户取消')) {
          throw error; // 重新抛出用户取消错误
        } else if (error.message.includes('not found') || error.message.includes('does not exist')) {
          errorMessage = 'processExcel云函数未部署，请联系管理员';
        } else if (error.message.includes('fileID') || error.message.includes('upload')) {
          errorMessage = '文件上传失败，请检查网络连接';
        } else if (error.message.includes('timeout') || error.message.includes('超时')) {
          errorMessage = 'Excel文件处理超时。\n\n解决方案：\n1. 检查文件大小（建议小于2MB）\n2. 减少单词数量（建议少于1000行）\n3. 检查网络连接';
        } else if (error.message.includes('Excel格式错误')) {
          errorMessage = error.message + '\n\n请参考上方的格式要求重新制作Excel文件。';
        } else {
          errorMessage = error.message;
        }
      }
      
      throw new Error(errorMessage);
    }
  },



  // 创建自定义词库
  async createCustomWordbank() {
    // 验证输入
    if (!this.data.newWordbankName) {
      wx.showToast({
        title: '请输入词库名称',
        icon: 'none'
      });
      return;
    }

    if (this.data.inputMethod === 'text' && !this.data.newWordbankContent) {
      wx.showToast({
        title: '请输入词汇内容',
        icon: 'none'
      });
      return;
    }

    if (this.data.inputMethod === 'excel' && !this.data.selectedFile) {
      wx.showToast({
        title: '请选择Excel文件',
        icon: 'none'
      });
      return;
    }

    if (this.data.processing) {
      return; // 防止重复提交
    }

    this.setData({ processing: true });

    try {
      wx.showLoading({
        title: this.data.inputMethod === 'excel' ? '处理Excel文件中...' : '解析词汇中...',
        mask: true
      });

      const userInfo = await app.getUserInfo();
      if (!userInfo || !userInfo.openid) {
        wx.hideLoading();
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        this.setData({ processing: false });
        return;
      }

      let words = [];

      // 根据输入方式处理内容
      if (this.data.inputMethod === 'text') {
        words = this.parseWordbankContent(this.data.newWordbankContent);
      } else if (this.data.inputMethod === 'excel') {
        try {
          words = await this.processExcelFile(this.data.selectedFile.path);
          
          // 显示Excel解析结果
          console.log(`Excel解析完成: 共${words.length}个单词`);
          
        } catch (error) {
          wx.hideLoading();
          console.error('Excel处理失败:', error);
          
          // 处理用户取消的情况
          if (error.message && error.message.includes('用户取消')) {
            this.setData({ processing: false });
            return;
          }
          
          let errorContent = error.message || '文件处理失败';
          
          if (error.message.includes('processExcel云函数未部署')) {
            errorContent = 'Excel处理功能需要云函数支持，请联系管理员部署processExcel云函数。\n\n临时方案：将Excel内容复制粘贴到文本输入框中。';
          }
          
          wx.showModal({
            title: 'Excel处理失败',
            content: errorContent,
            showCancel: true,
            cancelText: '知道了',
            confirmText: '切换文本输入',
            success: (res) => {
              if (res.confirm) {
                // 切换到文本输入
                this.setData({ 
                  inputMethod: 'text',
                  selectedFile: null 
                });
              }
            }
          });
          this.setData({ processing: false });
          return;
        }
      }

      if (words.length === 0) {
        wx.hideLoading();
        wx.showToast({
          title: '未识别到有效词汇',
          icon: 'none'
        });
        this.setData({ processing: false });
        return;
      }

      // 创建词库
      wx.showLoading({
        title: '创建词库中...',
        mask: true
      });

      try {
        // 使用云函数创建自定义词库
        const createResult = await wx.cloud.callFunction({
          name: 'createCustomWordbank',
          data: {
            wordbankData: {
              name: this.data.newWordbankName,
              words: words,
              creatorOpenid: userInfo.openid,
              creatorName: userInfo.nickName || '匿名用户'
            }
          }
        });

        if (createResult.result.code !== 200) {
          throw new Error(createResult.result.message || '创建失败');
        }

        console.log('云函数创建词库成功:', createResult.result);

      } catch (cloudFunctionError) {
        console.error('云函数创建失败:', cloudFunctionError);
        
        // 如果云函数调用失败，降级使用前端直接创建
        console.log('降级使用前端直接创建方式...');
        
        const db = wx.cloud.database();
        const wordbankId = 'custom_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        try {
          // 保存到云数据库
          await db.collection('custom_wordbanks').add({
            data: {
              id: wordbankId,
              name: this.data.newWordbankName,
              words: words,
              wordCount: words.length,
              creatorOpenid: userInfo.openid,
              creatorName: userInfo.nickName || '匿名用户',
              createTime: db.serverDate(),
              updateTime: db.serverDate()
            }
          });
        } catch (dbError) {
          // 如果集合不存在，尝试创建
          if (dbError.errCode === -502005) {
            console.log('数据库集合不存在，尝试直接添加数据...');
            await db.collection('custom_wordbanks').add({
              data: {
                id: wordbankId,
                name: this.data.newWordbankName,
                words: words,
                wordCount: words.length,
                creatorOpenid: userInfo.openid,
                creatorName: userInfo.nickName || '匿名用户',
                createTime: db.serverDate(),
                updateTime: db.serverDate()
              }
            });
          } else {
            throw dbError;
          }
        }
      }

      wx.hideLoading();
      
      // 显示创建结果
      const autoFoundCount = words.filter(w => w.source && w.source !== 'none').length;
      let successMessage = `创建成功！共${words.length}个词汇`;
      if (autoFoundCount > 0) {
        successMessage += `，其中${autoFoundCount}个自动查询到释义`;
      }
      
      wx.showToast({
        title: successMessage,
        icon: 'success',
        duration: 3000
      });

      // 隐藏弹窗并刷新列表
      this.hideCreateModal();
      this.loadCustomWordbanks();

    } catch (error) {
      wx.hideLoading();
      console.error('创建自定义词库失败:', error);
      
      let errorMessage = '创建失败';
      if (error.errCode === -502005) {
        errorMessage = '数据库集合不存在';
        
        // 显示详细的解决方案
        wx.showModal({
          title: '数据库初始化失败',
          content: '自定义词库功能需要初始化数据库集合。\n\n点击"自动初始化"来创建所需的数据库集合，或者联系管理员手动创建。',
          showCancel: true,
          confirmText: '自动初始化',
          cancelText: '稍后处理',
          success: async (modalRes) => {
            if (modalRes.confirm) {
              // 用户选择自动初始化
              await this.initDatabaseCollections();
            }
          }
        });
        return;
      } else if (error.message && error.message.includes('createCustomWordbank云函数未部署')) {
        errorMessage = '功能组件未部署，请联系管理员';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      wx.showToast({
        title: errorMessage,
        icon: 'error'
      });
    } finally {
      this.setData({ processing: false });
    }
  },

  // 点击自定义词库
  onCustomWordbankTap(e) {
    const wordbankId = e.currentTarget.dataset.id;
    const wordbank = this.data.customWordbanks.find(wb => wb.id === wordbankId);
    
    if (!wordbank) {
      wx.showToast({ title: '词库不存在', icon: 'error' });
      return;
    }
    
    console.log('=== 点击自定义词库 ===', wordbankId, wordbank);
    console.log('当前 returnTo:', this.data.returnTo);
    
    // 如果是从单词检测页面来的，处理返回逻辑
    if (this.data.returnTo === 'wordtest') {
      console.log('=== 处理返回到单词检测页面的逻辑 ===');
      
      // 保存到用户设置中，确保下次进入时还是这个词库
      this.saveUserLibrarySetting(wordbankId, wordbank.name);
      
      // 通过全局数据传递选中的词库信息
      const app = getApp();
      app.globalData.selectedLibrary = {
        id: wordbankId,
        name: wordbank.name,
        isCustom: true
      };
      
      // 立即跳转回单词检测页面
      console.log('=== 准备跳转回单词检测页面 ===');
      wx.navigateTo({
        url: '/pages/wordtest/wordtest',
        success: () => {
          console.log('=== 成功跳转到单词检测页面 ===');
          wx.showToast({
            title: `已选择${wordbank.name}`,
            icon: 'success',
            duration: 2000
          });
        },
        fail: (err) => {
          console.error('=== 跳转到单词检测页面失败 ===:', err);
          wx.redirectTo({
            url: '/pages/wordtest/wordtest',
            success: () => {
              wx.showToast({
                title: `已选择${wordbank.name}`,
                icon: 'success',
                duration: 2000
              });
            }
          });
        }
      });
      return;
    }
    
    // 如果是从首页来的，设置为默认词库
    if (this.data.returnTo === 'index') {
      this.saveUserLibrarySetting(wordbankId, wordbank.name);
      
      const app = getApp();
      app.globalData.selectedLibrary = {
        id: wordbankId,
        name: wordbank.name,
        isCustom: true
      };
      
      wx.switchTab({
        url: '/pages/index/index',
        success: () => {
          wx.showToast({
            title: `已设置默认词库：${wordbank.name}`,
            icon: 'success',
            duration: 2000
          });
        }
      });
      return;
    }
    
    // 其他情况，跳转到自定义词库的单词列表页面
    wx.navigateTo({
      url: `/pages/wordbank/custom-wordlist/custom-wordlist?wordbankId=${wordbankId}`
    });
  },

  // 编辑自定义词库
  editCustomWordbank(e) {
    const wordbankId = e.currentTarget.dataset.id;
    
    // 跳转到编辑页面
    wx.navigateTo({
      url: `/pages/wordbank/edit-wordbank/edit-wordbank?wordbankId=${wordbankId}`
    });
  },

  // 删除自定义词库
  async deleteCustomWordbank(e) {
    const wordbankId = e.currentTarget.dataset.id;
    const wordbank = this.data.customWordbanks.find(wb => wb.id === wordbankId);

    console.log('=== 删除词库 ===', wordbankId, wordbank);

    if (!wordbank) {
      wx.showToast({
        title: '词库不存在',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除词库"${wordbank.name}"吗？此操作不可恢复。`,
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '删除中...',
              mask: true
            });

            // 获取用户信息进行权限验证
            const userInfo = await app.getUserInfo();
            if (!userInfo || !userInfo.openid) {
              wx.hideLoading();
              wx.showToast({
                title: '请先登录',
                icon: 'none'
              });
              return;
            }
            
            console.log('词库详细信息:', {
              _id: wordbank._id,
              creatorOpenid: wordbank.creatorOpenid,
              currentUserOpenid: userInfo.openid,
              name: wordbank.name
            });

            // 使用云函数删除词库
            console.log('调用删除云函数...');
            const deleteResult = await wx.cloud.callFunction({
              name: 'deleteCustomWordbank',
              data: {
                wordbankId: wordbank._id
              }
            });

            console.log('删除结果:', deleteResult);

            wx.hideLoading();

            // 检查删除结果
            if (deleteResult.result && deleteResult.result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });

              // 立即刷新列表
              await this.loadCustomWordbanks();
            } else {
              const errorMessage = deleteResult.result ? deleteResult.result.message : '删除失败';
              wx.showToast({
                title: errorMessage,
                icon: 'none'
              });
              
              // 如果是词库不存在，也刷新列表
              if (deleteResult.result && deleteResult.result.code === 404) {
                await this.loadCustomWordbanks();
              }
            }

          } catch (error) {
            wx.hideLoading();
            console.error('删除自定义词库失败:', error);
            
            let errorMessage = '删除失败';
            if (error.message && error.message.includes('deleteCustomWordbank云函数未部署')) {
              errorMessage = '删除功能未部署，请联系管理员';
            } else if (error.message) {
              errorMessage = error.message;
            }
            
            wx.showToast({
              title: errorMessage,
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // 批量删除自定义词库
  async batchDeleteWordbanks() {
    const selectedWordbanks = this.data.customWordbanks.filter(wb => wb.selected);
    
    if (selectedWordbanks.length === 0) {
      wx.showToast({
        title: '请选择要删除的词库',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '批量删除',
      content: `确定要删除选中的 ${selectedWordbanks.length} 个词库吗？此操作不可恢复。`,
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: `删除中 (0/${selectedWordbanks.length})`,
              mask: true
            });

            const userInfo = await app.getUserInfo();
            if (!userInfo || !userInfo.openid) {
              wx.hideLoading();
              wx.showToast({
                title: '请先登录',
                icon: 'none'
              });
              return;
            }

            let successCount = 0;
            let failCount = 0;

            // 逐个删除词库
            for (let i = 0; i < selectedWordbanks.length; i++) {
              const wordbank = selectedWordbanks[i];
              
              wx.showLoading({
                title: `删除中 (${i + 1}/${selectedWordbanks.length})`,
                mask: true
              });

              try {
                // 使用云函数删除词库
                const deleteResult = await wx.cloud.callFunction({
                  name: 'deleteCustomWordbank',
                  data: {
                    wordbankId: wordbank._id
                  }
                });
                
                if (deleteResult.result && deleteResult.result.success) {
                  successCount++;
                } else {
                  failCount++;
                  console.error('删除词库失败:', wordbank.name, deleteResult.result ? deleteResult.result.message : '未知错误');
                }
              } catch (error) {
                console.error('删除词库失败:', wordbank.name, error);
                failCount++;
              }
            }

            wx.hideLoading();

            if (failCount === 0) {
              wx.showToast({
                title: '批量删除成功',
                icon: 'success'
              });
            } else {
              wx.showModal({
                title: '删除完成',
                content: `成功删除 ${successCount} 个词库，失败 ${failCount} 个`,
                showCancel: false
              });
            }

            // 刷新列表
            await this.loadCustomWordbanks();
            // 退出批量删除模式
            this.exitBatchDeleteMode();

          } catch (error) {
            wx.hideLoading();
            console.error('批量删除失败:', error);
            wx.showToast({
              title: '批量删除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // 进入批量删除模式
  enterBatchDeleteMode() {
    this.setData({
      batchDeleteMode: true,
      customWordbanks: this.data.customWordbanks.map(wb => ({
        ...wb,
        selected: false
      }))
    });
  },

  // 退出批量删除模式
  exitBatchDeleteMode() {
    this.setData({
      batchDeleteMode: false,
      customWordbanks: this.data.customWordbanks.map(wb => ({
        ...wb,
        selected: false
      }))
    });
  },

  // 格式化时间
  formatTime(time) {
    if (!time) return '';
    
    const date = new Date(time);
    const now = new Date();
    
    // 计算时间差
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / ********);
    
    if (minutes < 1) {
      return '刚刚';
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 30) {
      return `${days}天前`;
    } else {
      // 超过30天显示具体日期
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  },

  // 切换词库选择状态
  toggleWordbankSelection(e) {
    const wordbankId = e.currentTarget.dataset.id;
    const customWordbanks = this.data.customWordbanks.map(wb => {
      if (wb.id === wordbankId) {
        return { ...wb, selected: !wb.selected };
      }
      return wb;
    });
    
    this.setData({ customWordbanks });
  },

  // 全选/取消全选
  toggleSelectAll() {
    const hasUnselected = this.data.customWordbanks.some(wb => !wb.selected);
    const customWordbanks = this.data.customWordbanks.map(wb => ({
      ...wb,
      selected: hasUnselected
    }));
    
    this.setData({ customWordbanks });
  },

  // 初始化数据库集合
  async initDatabaseCollections() {
    wx.showLoading({
      title: '正在初始化数据库...',
      mask: true
    });

    try {
      console.log('开始调用数据库初始化云函数');
      
      const initResult = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: {}
      });

      wx.hideLoading();

      if (initResult.result.code === 200) {
        const { status, message } = initResult.result;
        
        if (status === 'already_exists') {
          wx.showModal({
            title: '初始化完成',
            content: '数据库集合已存在，现在可以创建自定义词库了！',
            showCancel: false,
            confirmText: '立即创建',
            success: (res) => {
              if (res.confirm) {
                // 重新尝试创建词库
                this.createCustomWordbank();
              }
            }
          });
        } else if (status === 'created') {
          wx.showModal({
            title: '初始化成功',
            content: '数据库集合创建成功！现在可以创建自定义词库了。',
            showCancel: false,
            confirmText: '立即创建',
            success: (res) => {
              if (res.confirm) {
                // 重新尝试创建词库
                this.createCustomWordbank();
              }
            }
          });
        }
      } else {
        throw new Error(initResult.result.message || '初始化失败');
      }

    } catch (error) {
      wx.hideLoading();
      console.error('数据库初始化失败:', error);
      
      let errorMsg = '数据库初始化失败';
      if (error.message && error.message.includes('initDatabase云函数未部署')) {
        errorMsg = 'initDatabase云函数未部署，请联系管理员部署云函数';
      } else if (error.message) {
        errorMsg = error.message;
      }
      
      wx.showModal({
        title: '初始化失败',
        content: errorMsg + '\n\n请联系管理员手动创建 custom_wordbanks 数据库集合。',
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },

  // ====== Excel处理相关方法 ======

  // 切换格式要求显示
  toggleFormatRequirements() {
    this.setData({
      showFormatRequirements: !this.data.showFormatRequirements
    });
  },

  // 自动查询释义选项改变
  // 自动去重选项改变
  onRemoveDuplicatesChange(e) {
    this.setData({
      removeDuplicates: e.detail.value
    });
  },

  // 处理选择的Excel文件
  async processSelectedFile() {
    if (!this.data.selectedFile) {
      wx.showToast({
        title: '请先选择文件',
        icon: 'none'
      });
      return;
    }

    if (this.data.processing) return;

    this.setData({ processing: true });

    try {
      wx.showLoading({
        title: '解析Excel文件...',
        mask: true
      });

      // 处理Excel文件
      let words = await this.processExcelFile(this.data.selectedFile.path);
      
      if (!words || words.length === 0) {
        throw new Error('未能解析到有效词汇');
      }

      // 应用处理选项
      if (this.data.removeDuplicates) {
        // 去重处理
        const wordMap = new Map();
        words.forEach(word => {
          if (!wordMap.has(word.word.toLowerCase())) {
            wordMap.set(word.word.toLowerCase(), word);
          }
        });
        words = Array.from(wordMap.values());
      }

      this.setData({ processedWords: words });

      wx.hideLoading();

      // 显示预览结果
      this.showWordsPreview(words);

    } catch (error) {
      wx.hideLoading();
      console.error('处理Excel文件失败:', error);
      
      let errorMessage = '文件处理失败';
      if (error.message.includes('processExcel云函数未部署')) {
        errorMessage = 'Excel处理功能需要云函数支持，请联系管理员';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      wx.showModal({
        title: '处理失败',
        content: errorMessage,
        showCancel: false
      });
    } finally {
      this.setData({ processing: false });
    }
  },

  // 显示词汇预览
  showWordsPreview(words) {
    const previewCount = Math.min(words.length, 10);
    const previewWords = words.slice(0, previewCount);
    const hasMoreWords = words.length > previewCount;

    let previewText = `解析成功！共识别到 ${words.length} 个词汇\n\n预览前${previewCount}个：\n`;
    previewWords.forEach((word, index) => {
      previewText += `${index + 1}. ${word.word}`;
      if (word.chinese) {
        previewText += ` - ${word.chinese}`;
      }
      previewText += '\n';
    });

    if (hasMoreWords) {
      previewText += `\n... 还有${words.length - previewCount}个词汇`;
    }

    previewText += '\n\n确认创建词库吗？';

    wx.showModal({
      title: '词汇预览',
      content: previewText,
      confirmText: '确认创建',
      cancelText: '重新处理',
      success: (res) => {
        if (res.confirm) {
          // 用户确认，直接创建词库
          this.createWordbankWithProcessedWords();
        } else {
          // 用户取消，清空处理结果
          this.setData({ processedWords: null });
        }
      }
    });
  },

  // 使用已处理的词汇创建词库
  async createWordbankWithProcessedWords() {
    if (!this.data.processedWords || !this.data.newWordbankName) {
      wx.showToast({
        title: '数据不完整',
        icon: 'none'
      });
      return;
    }

    if (this.data.processing) return;

    this.setData({ processing: true });

    try {
      wx.showLoading({
        title: '创建词库中...',
        mask: true
      });

      const userInfo = await app.getUserInfo();
      if (!userInfo || !userInfo.openid) {
        throw new Error('请先登录');
      }

      // 使用云函数创建自定义词库
      const createResult = await wx.cloud.callFunction({
        name: 'createCustomWordbank',
        data: {
          wordbankData: {
            name: this.data.newWordbankName,
            words: this.data.processedWords,
            creatorOpenid: userInfo.openid,
            creatorName: userInfo.nickName || '匿名用户'
          }
        }
      });

      if (createResult.result.code !== 200) {
        throw new Error(createResult.result.message || '创建失败');
      }

      wx.hideLoading();

      // 显示创建结果
      const autoFoundCount = this.data.processedWords.filter(w => w.source && w.source !== 'none').length;
      let successMessage = `创建成功！共${this.data.processedWords.length}个词汇`;
      if (autoFoundCount > 0) {
        successMessage += `，其中${autoFoundCount}个自动查询到释义`;
      }

      wx.showToast({
        title: successMessage,
        icon: 'success',
        duration: 3000
      });

      // 隐藏弹窗并刷新列表
      this.hideCreateModal();
      this.loadCustomWordbanks();

    } catch (error) {
      wx.hideLoading();
      console.error('创建词库失败:', error);
      
      wx.showToast({
        title: error.message || '创建失败',
        icon: 'error'
      });
    } finally {
      this.setData({ processing: false });
    }
  }
}); 