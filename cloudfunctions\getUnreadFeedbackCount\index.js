const cloud = require('wx-server-sdk')

// 初始化云能力
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    // 统计未读反馈数量
    const result = await db.collection('feedbacks')
      .where({
        status: 'pending'
      })
      .count()

    return {
      success: true,
      data: {
        count: result.total
      }
    }
  } catch (error) {
    console.error('获取未读反馈数量失败:', error)
    return {
      success: false,
      error: error.message,
      data: {
        count: 0
      }
    }
  }
} 