.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 200rpx 0;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #007AFF;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666;
}

/* 编辑内容 */
.edit-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.edit-form {
  flex: 1;
  padding: 30rpx;
  padding-bottom: 140rpx;
}

/* 表单组 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  background: white;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #007AFF;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

/* 输入方式选择 */
.input-methods {
  display: flex;
  gap: 20rpx;
  margin-top: 16rpx;
}

.method-btn {
  flex: 1;
  padding: 20rpx 0;
  font-size: 28rpx;
  background-color: #f0f0f0;
  color: #666;
  border: 2rpx solid transparent;
  border-radius: 10rpx;
  transition: all 0.3s ease;
}

.method-btn.active {
  background-color: #e3f2fd;
  color: #007AFF;
  border-color: #007AFF;
}

.method-btn[disabled] {
  opacity: 0.5;
}

/* 文本输入区域 */
.form-textarea {
  width: 100%;
  min-height: 400rpx;
  padding: 24rpx;
  font-size: 28rpx;
  line-height: 1.6;
  background: white;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-textarea:focus {
  border-color: #007AFF;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

.char-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 格式提示 */
.format-tips {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 10rpx;
  margin-top: 20rpx;
}

.tips-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.tips-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.8;
  padding-left: 20rpx;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx 0;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 12rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.cancel {
  background-color: #f0f0f0;
  color: #333;
}

.action-btn.save {
  background-color: #007AFF;
  color: white;
}

.action-btn[disabled] {
  opacity: 0.6;
} 