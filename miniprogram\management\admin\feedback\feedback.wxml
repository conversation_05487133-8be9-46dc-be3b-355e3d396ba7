<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">反馈管理</text>
      <text class="page-subtitle">查看和管理用户反馈</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{loading}}">
    <view class="loading-content">
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-section" wx:elif="{{isEmpty}}">
    <view class="empty-content">
      <text class="empty-icon">📝</text>
      <text class="empty-title">暂无反馈</text>
      <text class="empty-desc">用户还没有提交任何反馈</text>
    </view>
  </view>

  <!-- 反馈列表 -->
  <view class="feedback-list" wx:else>
    <view 
      class="feedback-item {{item.status === 'pending' ? 'unread' : 'read'}}"
      wx:for="{{feedbacks}}"
      wx:key="id"
      bindtap="onFeedbackTap"
      bindlongpress="onFeedbackLongPress"
      data-index="{{index}}"
    >
      <view class="feedback-header">
        <view class="user-info">
          <text class="user-name">{{item.userNickName}}</text>
          <text class="feedback-time">{{formatTime(item.timestamp)}}</text>
        </view>
        <view class="status-badge {{item.status}}">
          <text class="status-text">{{item.status === 'pending' ? '未读' : '已读'}}</text>
        </view>
      </view>
      
      <view class="feedback-content">
        <view class="content-section" wx:if="{{item.problemText}}">
          <text class="content-label">问题反馈:</text>
          <text class="content-text">{{item.problemText}}</text>
        </view>
        
        <view class="content-section" wx:if="{{item.suggestionText}}">
          <text class="content-label">建议内容:</text>
          <text class="content-text">{{item.suggestionText}}</text>
        </view>
      </view>
      
      <view class="feedback-actions">
        <text class="action-hint">点击查看详情 · 长按删除</text>
      </view>
    </view>
  </view>
</view> 