/* pages/reading/classic-novels/classic-novels.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.novels-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.novel-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 24rpx;
  transition: transform 0.2s ease;
}

.novel-card:active {
  transform: scale(0.98);
}

.novel-cover {
  width: 120rpx;
  height: 160rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.cover-placeholder {
  font-size: 48rpx;
  color: white;
}

.novel-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.novel-header {
  margin-bottom: 12rpx;
}

.novel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 6rpx;
  line-height: 1.3;
}

.novel-chinese {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.novel-meta {
  display: flex;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.novel-author {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
}

.novel-year {
  font-size: 24rpx;
  color: #999;
}

.novel-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: block;
}

.novel-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 24rpx;
  color: #999;
  margin-right: 8rpx;
}

.detail-value {
  font-size: 24rpx;
  color: #666;
}

.difficulty-badge {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: white;
}

.difficulty-beginner {
  background: #10B981;
}

.difficulty-intermediate {
  background: #F59E0B;
}

.difficulty-advanced {
  background: #EF4444;
}

.novel-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.novel-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.tag {
  background: #F3F4F6;
  color: #6B7280;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.novel-rating {
  margin-left: 20rpx;
}

.rating-text {
  font-size: 24rpx;
  color: #F59E0B;
  font-weight: 500;
}
