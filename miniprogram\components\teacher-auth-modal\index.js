const teacherAuth = require('../../utils/teacher-auth.js');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    password: '',
    passwordVisible: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 密码输入事件
    onPasswordInput: function(e) {
      this.setData({
        password: e.detail.value
      });
      
      // 同步到页面数据
      teacherAuth.onTeacherAuthPasswordInput(e);
    },

    // 切换密码可见性
    onTogglePasswordVisibility: function() {
      this.setData({
        passwordVisible: !this.data.passwordVisible
      });
      
      // 同步到工具类
      teacherAuth.togglePasswordVisibility();
    },

    // 确认验证
    onConfirm: function() {
      if (this.data.password.trim().length === 0) {
        wx.showToast({
          title: '请输入密码',
          icon: 'none'
        });
        return;
      }
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 调用工具类处理
      teacherAuth.onTeacherAuthConfirm();
    },

    // 取消验证
    onCancel: function() {
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 调用工具类处理
      teacherAuth.onTeacherAuthCancel();
    },

    // 点击遮罩层
    onMaskTap: function() {
      // 点击遮罩层时不关闭弹窗，需要用户主动选择
      wx.showToast({
        title: '请选择操作',
        icon: 'none',
        duration: 1000
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      console.log('教师权限验证弹窗组件已加载');
    },
    
    detached: function() {
      console.log('教师权限验证弹窗组件已卸载');
    }
  },

  /**
   * 在组件实例进入页面节点树时执行
   */
  pageLifetimes: {
    show: function() {
      // 页面显示时重置数据
      this.setData({
        password: '',
        passwordVisible: false
      });
    }
  }
}); 