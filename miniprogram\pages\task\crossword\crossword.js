// pages/task/crossword/crossword.js
const app = getApp();

Page({
  data: {
    // 游戏数据
    gameData: {
      difficulty: '简单',
      grid: [], // 7x7网格
    },
    
    // 示例游戏配置
    sampleGame: {
      // 横向单词
      acrossWords: [
        { id: 'a1', number: 1, word: 'CAT', startRow: 0, startCol: 1, clue: '一种常见的宠物动物，会抓老鼠' },
        { id: 'a2', number: 4, word: 'DOG', startRow: 2, startCol: 0, clue: '人类最忠诚的朋友，会看家' },
        { id: 'a3', number: 6, word: 'SUN', startRow: 4, startCol: 2, clue: '天空中发光发热的恒星' },
        { id: 'a4', number: 8, word: 'BOOK', startRow: 6, startCol: 1, clue: '装订成册的纸张，用来阅读' }
      ],
      // 纵向单词
      downWords: [
        { id: 'd1', number: 1, word: 'COLD', startRow: 0, startCol: 1, clue: '温度很低的状态，与热相反' },
        { id: 'd2', number: 2, word: 'APPLE', startRow: 0, startCol: 2, clue: '红色或绿色的水果，营养丰富' },
        { id: 'd3', number: 3, word: 'TEA', startRow: 0, startCol: 3, clue: '中国传统饮品，用茶叶泡制' },
        { id: 'd4', number: 5, word: 'GO', startRow: 2, startCol: 2, clue: '移动，行走，出发' },
        { id: 'd5', number: 7, word: 'NO', startRow: 4, startCol: 4, clue: '否定词，表示不同意' }
      ]
    },
    
    // 当前状态
    currentTab: 'across', // across 或 down
    selectedCell: -1,
    selectedWord: '',
    completedWords: 0,
    totalWords: 0,
    
    // 线索列表
    acrossClues: [],
    downClues: [],
    
    // 游戏状态
    startTime: 0,
    gameTime: '00:00',
    accuracy: 100,
    showSuccess: false,
    hintsUsed: 0
  },

  onLoad(options) {
    this.initGame();
    this.startTimer();
  },

  // 初始化游戏
  initGame() {
    const { sampleGame } = this.data;
    
    // 创建7x7空网格
    const grid = Array(49).fill(null).map(() => ({
      type: 'black',
      letter: '',
      number: null,
      locked: false,
      wordIds: []
    }));

    // 放置横向单词
    sampleGame.acrossWords.forEach(word => {
      for (let i = 0; i < word.word.length; i++) {
        const cellIndex = (word.startRow * 7) + word.startCol + i;
        if (cellIndex < 49) {
          grid[cellIndex].type = 'white';
          grid[cellIndex].wordIds.push(word.id);
          if (i === 0) {
            grid[cellIndex].number = word.number;
          }
        }
      }
    });

    // 放置纵向单词
    sampleGame.downWords.forEach(word => {
      for (let i = 0; i < word.word.length; i++) {
        const cellIndex = ((word.startRow + i) * 7) + word.startCol;
        if (cellIndex < 49) {
          grid[cellIndex].type = 'white';
          grid[cellIndex].wordIds.push(word.id);
          if (i === 0 && !grid[cellIndex].number) {
            grid[cellIndex].number = word.number;
          }
        }
      }
    });

    // 准备线索列表
    const acrossClues = sampleGame.acrossWords.map(word => ({
      id: word.id,
      number: word.number,
      clue: word.clue,
      length: word.word.length,
      completed: false
    }));

    const downClues = sampleGame.downWords.map(word => ({
      id: word.id,
      number: word.number,
      clue: word.clue,
      length: word.word.length,
      completed: false
    }));

    this.setData({
      'gameData.grid': grid,
      acrossClues,
      downClues,
      totalWords: acrossClues.length + downClues.length,
      startTime: Date.now()
    });
  },

  // 点击格子
  onCellTap(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const cell = this.data.gameData.grid[index];
    
    if (cell.type === 'white') {
      this.setData({
        selectedCell: index
      });
      
      // 自动聚焦到输入框
      this.selectComponent(`.grid-cell[data-index="${index}"] .cell-input`);
    }
  },

  // 输入字母
  onLetterInput(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const letter = e.detail.value.toUpperCase();
    
    const grid = [...this.data.gameData.grid];
    grid[index].letter = letter;
    
    this.setData({
      'gameData.grid': grid
    });

    // 检查单词完成状态
    this.checkWordCompletion();
    
    // 自动移动到下一个格子
    if (letter && this.data.selectedWord) {
      this.moveToNextCell(index);
    }
  },

  // 移动到下一个格子
  moveToNextCell(currentIndex) {
    const { selectedWord, gameData, sampleGame } = this.data;
    
    // 找到当前选中的单词
    let wordInfo = null;
    if (selectedWord.startsWith('a')) {
      wordInfo = sampleGame.acrossWords.find(w => w.id === selectedWord);
    } else {
      wordInfo = sampleGame.downWords.find(w => w.id === selectedWord);
    }
    
    if (!wordInfo) return;
    
    // 计算下一个格子位置
    let nextIndex = -1;
    if (selectedWord.startsWith('a')) {
      // 横向单词，移动到右边
      const currentCol = currentIndex % 7;
      const targetCol = currentCol + 1;
      if (targetCol < wordInfo.startCol + wordInfo.word.length) {
        nextIndex = currentIndex + 1;
      }
    } else {
      // 纵向单词，移动到下面
      const currentRow = Math.floor(currentIndex / 7);
      const targetRow = currentRow + 1;
      if (targetRow < wordInfo.startRow + wordInfo.word.length) {
        nextIndex = currentIndex + 7;
      }
    }
    
    if (nextIndex >= 0 && nextIndex < 49 && gameData.grid[nextIndex].type === 'white') {
      this.setData({
        selectedCell: nextIndex
      });
    }
  },

  // 检查单词完成状态
  checkWordCompletion() {
    const { gameData, sampleGame } = this.data;
    let completedCount = 0;
    
    // 检查横向单词
    const acrossClues = this.data.acrossClues.map(clue => {
      const wordInfo = sampleGame.acrossWords.find(w => w.id === clue.id);
      let isCompleted = true;
      
      for (let i = 0; i < wordInfo.word.length; i++) {
        const cellIndex = (wordInfo.startRow * 7) + wordInfo.startCol + i;
        const expectedLetter = wordInfo.word[i];
        const actualLetter = gameData.grid[cellIndex].letter;
        
        if (actualLetter !== expectedLetter) {
          isCompleted = false;
          break;
        }
      }
      
      if (isCompleted && !clue.completed) {
        completedCount++;
      }
      
      return { ...clue, completed: isCompleted };
    });

    // 检查纵向单词
    const downClues = this.data.downClues.map(clue => {
      const wordInfo = sampleGame.downWords.find(w => w.id === clue.id);
      let isCompleted = true;
      
      for (let i = 0; i < wordInfo.word.length; i++) {
        const cellIndex = ((wordInfo.startRow + i) * 7) + wordInfo.startCol;
        const expectedLetter = wordInfo.word[i];
        const actualLetter = gameData.grid[cellIndex].letter;
        
        if (actualLetter !== expectedLetter) {
          isCompleted = false;
          break;
        }
      }
      
      if (isCompleted && !clue.completed) {
        completedCount++;
      }
      
      return { ...clue, completed: isCompleted };
    });

    const newCompletedWords = acrossClues.filter(c => c.completed).length + downClues.filter(c => c.completed).length;
    
    this.setData({
      acrossClues,
      downClues,
      completedWords: newCompletedWords
    });

    // 检查游戏是否完成
    if (newCompletedWords === this.data.totalWords) {
      this.completeGame();
    }
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  // 选择单词
  selectWord(e) {
    const wordId = e.currentTarget.dataset.wordId;
    const { sampleGame } = this.data;
    
    this.setData({
      selectedWord: wordId
    });

    // 高亮显示单词的第一个格子
    let wordInfo = null;
    if (wordId.startsWith('a')) {
      wordInfo = sampleGame.acrossWords.find(w => w.id === wordId);
    } else {
      wordInfo = sampleGame.downWords.find(w => w.id === wordId);
    }
    
    if (wordInfo) {
      const firstCellIndex = (wordInfo.startRow * 7) + wordInfo.startCol;
      this.setData({
        selectedCell: firstCellIndex
      });
    }
  },

  // 获取提示
  getHint() {
    const { selectedWord, sampleGame, gameData } = this.data;
    
    if (!selectedWord) {
      wx.showToast({
        title: '请先选择一个单词',
        icon: 'none'
      });
      return;
    }

    // 找到单词信息
    let wordInfo = null;
    if (selectedWord.startsWith('a')) {
      wordInfo = sampleGame.acrossWords.find(w => w.id === selectedWord);
    } else {
      wordInfo = sampleGame.downWords.find(w => w.id === selectedWord);
    }

    if (!wordInfo) return;

    // 找到第一个空格子并填入正确字母
    const grid = [...gameData.grid];
    let hintGiven = false;

    for (let i = 0; i < wordInfo.word.length; i++) {
      let cellIndex;
      if (selectedWord.startsWith('a')) {
        cellIndex = (wordInfo.startRow * 7) + wordInfo.startCol + i;
      } else {
        cellIndex = ((wordInfo.startRow + i) * 7) + wordInfo.startCol;
      }

      if (!grid[cellIndex].letter) {
        grid[cellIndex].letter = wordInfo.word[i];
        grid[cellIndex].locked = true;
        hintGiven = true;
        break;
      }
    }

    if (hintGiven) {
      this.setData({
        'gameData.grid': grid,
        hintsUsed: this.data.hintsUsed + 1
      });
      
      this.checkWordCompletion();
      
      wx.showToast({
        title: '提示已给出',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '该单词已完成',
        icon: 'none'
      });
    }
  },

  // 检查答案
  checkAnswers() {
    const { gameData, sampleGame } = this.data;
    let correctCount = 0;
    let totalCount = 0;

    // 检查所有填入的字母
    sampleGame.acrossWords.forEach(wordInfo => {
      for (let i = 0; i < wordInfo.word.length; i++) {
        const cellIndex = (wordInfo.startRow * 7) + wordInfo.startCol + i;
        const expectedLetter = wordInfo.word[i];
        const actualLetter = gameData.grid[cellIndex].letter;
        
        if (actualLetter) {
          totalCount++;
          if (actualLetter === expectedLetter) {
            correctCount++;
          }
        }
      }
    });

    const accuracy = totalCount > 0 ? Math.round((correctCount / totalCount) * 100) : 0;
    
    wx.showModal({
      title: '检查结果',
      content: `当前正确率：${accuracy}%\n正确：${correctCount}/${totalCount}`,
      showCancel: false
    });
  },

  // 清空网格
  clearGrid() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有已填入的字母吗？',
      success: (res) => {
        if (res.confirm) {
          const grid = this.data.gameData.grid.map(cell => ({
            ...cell,
            letter: cell.locked ? cell.letter : '',
          }));
          
          this.setData({
            'gameData.grid': grid,
            completedWords: 0
          });
          
          this.checkWordCompletion();
        }
      }
    });
  },

  // 显示答案
  showAnswers() {
    wx.showModal({
      title: '显示答案',
      content: '确定要显示所有答案吗？这将结束当前游戏。',
      success: (res) => {
        if (res.confirm) {
          const { sampleGame } = this.data;
          const grid = [...this.data.gameData.grid];

          // 填入所有正确答案
          sampleGame.acrossWords.forEach(wordInfo => {
            for (let i = 0; i < wordInfo.word.length; i++) {
              const cellIndex = (wordInfo.startRow * 7) + wordInfo.startCol + i;
              grid[cellIndex].letter = wordInfo.word[i];
              grid[cellIndex].locked = true;
            }
          });

          sampleGame.downWords.forEach(wordInfo => {
            for (let i = 0; i < wordInfo.word.length; i++) {
              const cellIndex = ((wordInfo.startRow + i) * 7) + wordInfo.startCol;
              grid[cellIndex].letter = wordInfo.word[i];
              grid[cellIndex].locked = true;
            }
          });

          this.setData({
            'gameData.grid': grid
          });

          this.checkWordCompletion();
        }
      }
    });
  },

  // 完成游戏
  completeGame() {
    const endTime = Date.now();
    const gameTime = this.formatTime(endTime - this.data.startTime);
    const accuracy = this.calculateAccuracy();

    this.setData({
      gameTime,
      accuracy,
      showSuccess: true
    });

    // 触觉反馈
    wx.vibrateShort({
      type: 'heavy'
    });
  },

  // 计算准确率
  calculateAccuracy() {
    const hintsUsed = this.data.hintsUsed;
    const totalLetters = this.data.sampleGame.acrossWords.reduce((sum, word) => sum + word.word.length, 0) +
                        this.data.sampleGame.downWords.reduce((sum, word) => sum + word.word.length, 0);
    
    // 考虑交叉点重复计算的问题
    const uniqueLetters = new Set();
    this.data.sampleGame.acrossWords.forEach(word => {
      for (let i = 0; i < word.word.length; i++) {
        const cellIndex = (word.startRow * 7) + word.startCol + i;
        uniqueLetters.add(cellIndex);
      }
    });
    this.data.sampleGame.downWords.forEach(word => {
      for (let i = 0; i < word.word.length; i++) {
        const cellIndex = ((word.startRow + i) * 7) + word.startCol;
        uniqueLetters.add(cellIndex);
      }
    });

    const actualTotalLetters = uniqueLetters.size;
    const penalty = hintsUsed * 5; // 每个提示扣5分
    const accuracy = Math.max(0, 100 - penalty);
    
    return accuracy;
  },

  // 格式化时间
  formatTime(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  },

  // 开始计时器
  startTimer() {
    this.timer = setInterval(() => {
      if (!this.data.showSuccess) {
        const currentTime = Date.now();
        const gameTime = this.formatTime(currentTime - this.data.startTime);
        this.setData({ gameTime });
      }
    }, 1000);
  },

  // 隐藏成功弹窗
  hideSuccessModal() {
    this.setData({
      showSuccess: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击模态框内容时关闭弹窗
  },

  // 重新开始游戏
  restartGame() {
    this.setData({
      showSuccess: false,
      completedWords: 0,
      hintsUsed: 0,
      selectedCell: -1,
      selectedWord: '',
      startTime: Date.now()
    });
    
    this.initGame();
  },

  // 返回主页
  backToMenu() {
    // 检查页面栈，如果只有一个页面或者前一个页面是主页，则使用switchTab
    const pages = getCurrentPages();
    if (pages.length <= 1) {
      wx.switchTab({
        url: '/pages/index/index'
      });
    } else {
      const prevPage = pages[pages.length - 2];
      if (prevPage && prevPage.route === 'pages/index/index') {
        wx.switchTab({
          url: '/pages/index/index'
        });
      } else {
        wx.navigateBack();
      }
    }
  },

  onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
}); 