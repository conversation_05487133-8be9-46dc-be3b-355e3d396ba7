// pages/profile/report/report.js
const app = getApp();
// const TeacherAuth = require('../../utils/teacher-auth.js'); // 暂时注释掉避免模块依赖问题

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 当前步骤 (0: 基本信息, 1: 选择题型, 2: 题型分析, 3: 报告预览)
    currentStep: 0,
    
    // 柱状图数据
    chartData: [],
    
    // 选中的题型索引
    selectedSection: 0,
    
    // 当前题型索引（用于逐个填写）
    currentSectionIndex: 0,
    
    // 已启用的题型数量
    enabledSectionsCount: 0,
    
    // 已选题型总分
    totalSelectedScore: 0,
    // 失分总数
    totalLostScore: 0,
    // 得分总数
    totalGainedScore: 0,
    // 正确率
    correctRate: 0,
    
    // 听口考试整体选择状态
    listeningExamSelected: false,
    
    // 样式选择
    selectedStyle: 0, // 0: 经典蓝, 1: 清新绿, 2: 温暖橙
    styles: [
      {
        id: 0,
        name: '经典蓝',
        primary: '#667eea',
        secondary: '#764ba2',
        accent: '#4c63d2'
      },
      {
        id: 1,
        name: '清新绿',
        primary: '#11998e',
        secondary: '#38ef7d',
        accent: '#0891b2'
      },
      {
        id: 2,
        name: '温暖橙',
        primary: '#f093fb',
        secondary: '#f5576c',
        accent: '#d63384'
      }
    ],
    
    // 基本信息
    basicInfo: {
      studentName: '',
      grade: '',
      examType: '',
      totalScore: '',
      analystName: ''
    },
    
    // 报告配置
    reportConfig: {
      showCourseplan: false  // 改为默认不显示，用户可以选择是否填写
    },
    
    // 课程规划配置
    coursePlanHeaders: {
      lesson: '课次',
      content: '学习内容'
    },
    
    // 课程规划数据
    coursePlanData: [
      { id: 1, lesson: 1, content: '' },
      { id: 2, lesson: 2, content: '' },
      { id: 3, lesson: 3, content: '' },
      { id: 4, lesson: 4, content: '' },
      { id: 5, lesson: 5, content: '' },
      { id: 6, lesson: 6, content: '' },
      { id: 7, lesson: 7, content: '' },
      { id: 8, lesson: 8, content: '' },
      { id: 9, lesson: 9, content: '' },
      { id: 10, lesson: 10, content: '' },
      { id: 11, lesson: 11, content: '' },
      { id: 12, lesson: 12, content: '' }
    ],
    
    // 题型配置
    sections: [
      {
        id: 'listening_choice',
        name: '听后选择',
        totalScore: 21,
        lostScore: 0,
        enabled: false,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '词汇量不够', selected: false },
          { text: '听力理解能力不足', selected: false },
          { text: '注意力不集中', selected: false },
          { text: '没有预读题干和选项', selected: false },
          { text: '听力熟练度不够/听的太少', selected: false }
        ],
        suggestions: [
          { text: '扩大词汇量', selected: false },
          { text: '精听练习', selected: false },
          { text: '多听多练增加/保持熟练度', selected: false },
          { text: '增加预读环节', selected: false },
          { text: '薄弱类型专项练习', selected: false },
          { text: '训练注意力集中', selected: false }
        ]
      },
      {
        id: 'listening_record',
        name: '听后记录',
        totalScore: 6,
        lostScore: 0,
        enabled: false,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '单词拼写记忆不足', selected: false },
          { text: '没画路标词导致漏掉答案词汇发音', selected: false },
          { text: '没有预判/不会预判', selected: false }
        ],
        suggestions: [
          { text: '背高频词的拼写', selected: false },
          { text: '学习预判方法', selected: false },
          { text: '练习单词听写', selected: false },
          { text: '加入画路标词环节', selected: false }
        ]
      },
      {
        id: 'listening_retell',
        name: '听后转述',
        totalScore: 9,
        lostScore: 0,
        enabled: false,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '词汇量过少导致听不懂', selected: false },
          { text: '听力理解能力相对薄弱', selected: false },
          { text: '转述的句子语法错误过多', selected: false },
          { text: '缺乏速记能力/写字比较慢', selected: false },
          { text: '口语练习过少', selected: false }
        ],
        suggestions: [
          { text: '背单词增加词汇量', selected: false },
          { text: '多做精听练习', selected: false },
          { text: '学习句子结构语法知识/纠正常犯的语法错误', selected: false },
          { text: '学习训练速记速写', selected: false },
          { text: '加强口语表达训练', selected: false }
        ]
      },
      {
        id: 'listening_reading',
        name: '朗读',
        totalScore: 8,
        lostScore: 0,
        enabled: false,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '单词发音不准确', selected: false },
          { text: '语调不自然', selected: false },
          { text: '口语练习过少', selected: false },
          { text: '单词不熟练导致卡顿', selected: false },
          { text: '断句不合理', selected: false }
        ],
        suggestions: [
          { text: '纠正发音', selected: false },
          { text: '学习训练断句方法', selected: false },
          { text: '增加练习量提高熟练度', selected: false },
          { text: '练习语调变化', selected: false },
          { text: '学习朗读技巧', selected: false }
        ]
      },
      {
        id: 'listening_answer',
        name: '回答问题',
        totalScore: 6,
        lostScore: 0,
        enabled: false,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '快速定位答案的能力较薄弱', selected: false },
          { text: '题意理解不准确', selected: false },
          { text: '回答所用的句式结构语法错误较多', selected: false },
          { text: '不会速记/记答案时间不够', selected: false },
          { text: '回答不够全面', selected: false }
        ],
        suggestions: [
          { text: '学习快速找答案的方法技巧', selected: false },
          { text: '在练习中提高理解和回答能力', selected: false },
          { text: '学习句子结构语法知识/纠正常犯的语法错误', selected: false },
          { text: '加强速记能力的练习', selected: false },
          { text: '强化分散性答案的查找方法', selected: false }
        ]
      },
      {
        id: 'cloze',
        name: '完形填空',
        totalScore: 15,
        lostScore: 0,
        enabled: true,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '词汇量不足', selected: false },
          { text: '主观臆断较多/缺乏查找原文依据的意识', selected: false },
          { text: '缺乏做题方法技巧', selected: false },
          { text: '上下文句子理解能力薄弱', selected: false },
          { text: '固定搭配短语积累不足', selected: false }
        ],
        suggestions: [
          { text: '扩大词汇量', selected: false },
          { text: '练习精读翻译能力', selected: false },
          { text: '建立原文依据的意识', selected: false },
          { text: '训练做题思维与技巧', selected: false },
          { text: '加强上下文理解', selected: false },
          { text: '强化固定搭配和短语的背诵', selected: false }
        ]
      },
      {
        id: 'grammar',
        name: '语法填空',
        totalScore: 15,
        lostScore: 0,
        enabled: true,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '词汇量不足', selected: false },
          { text: '句子翻译/长难句分析能力不足导致没有正确理解句意', selected: false },
          { text: '语法题熟练度不足/经验不足/练习过少', selected: false },
          { text: '粗心大意', selected: false },
          { text: '时态知识漏洞', selected: false },
          { text: '语态知识漏洞', selected: false },
          { text: '主谓一致知识漏洞', selected: false },
          { text: '非谓语动词知识漏洞', selected: false },
          { text: '定语从句知识漏洞', selected: false },
          { text: '名词性从句知识漏洞', selected: false },
          { text: '状语从句知识漏洞', selected: false },
          { text: '介词知识漏洞', selected: false }
        ],
        suggestions: [
          { text: '背单词', selected: false },
          { text: '训练句子翻译与长难句分析能力', selected: false },
          { text: '多刷题，在题目中强化实战能力', selected: false },
          { text: '平时注重培养细心和耐心', selected: false },
          { text: '学习薄弱语法知识', selected: false }
        ]
      },
      {
        id: 'reading',
        name: '阅读理解',
        totalScore: 28,
        lostScore: 0,
        enabled: true,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '词汇量不足', selected: false },
          { text: '长难句分析能力较薄弱', selected: false },
          { text: '定位答案的方法不熟/不全', selected: false },
          { text: '主观臆断较多/缺乏查找原文依据的意识', selected: false },
          { text: '逻辑分析与推理能力较薄弱', selected: false },
          { text: '缺乏做题方法技巧', selected: false },
          { text: '文章结构分析与应用能力有待加强', selected: false }
        ],
        suggestions: [
          { text: '强化3500大纲词与阅读词汇的背诵', selected: false },
          { text: '训练长难句分析能力', selected: false },
          { text: '梳理强化定位答案的方法', selected: false },
          { text: '建立原文依据的意识原则', selected: false },
          { text: '训练逻辑分析与推理能力（尤其抽象话题文章）', selected: false },
          { text: '训练做题思维技巧', selected: false },
          { text: '学习训练文章结构的总结与应用能力', selected: false }
        ]
      },
      {
        id: 'seven',
        name: '七选五',
        totalScore: 10,
        lostScore: 0,
        enabled: true,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '词汇量不足', selected: false },
          { text: '句子翻译/长难句分析能力较薄弱', selected: false },
          { text: '上下文逻辑推理能力较薄弱', selected: false },
          { text: '缺乏七选五解题方法技巧', selected: false },
          { text: '刷题少/做题经验不足/熟练度不够', selected: false }
        ],
        suggestions: [
          { text: '强化词汇的背诵', selected: false },
          { text: '精读翻译练习/训练长难句分析能力', selected: false },
          { text: '在题目分析中强化逻辑推理能力', selected: false },
          { text: '学习并训练做题思维与方法技巧', selected: false },
          { text: '保质保量的刷题积累经验与熟练度', selected: false }
        ]
      },
      {
        id: 'expression',
        name: '阅读表达',
        totalScore: 12,
        lostScore: 0,
        enabled: true,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '词汇量不足', selected: false },
          { text: '句子翻译/长难句分析能力较薄弱', selected: false },
          { text: '定位答案的方法不熟/不全', selected: false },
          { text: '总结概括/语言简化/句子合并能力不足', selected: false },
          { text: '对阅表题型特征与答题方法不熟悉', selected: false },
          { text: '语言表达能力不足', selected: false },
          { text: '练的太少/熟练度不够', selected: false },
          { text: '粗心大意', selected: false }
        ],
        suggestions: [
          { text: '强化3500大纲词与阅读词汇的背诵', selected: false },
          { text: '精读翻译练习/训练长难句分析能力', selected: false },
          { text: '梳理强化定位答案的方法', selected: false },
          { text: '训练总结概括/语言简化/句子合并能力', selected: false },
          { text: '学习每个小题的答题方法与注意事项', selected: false },
          { text: '加强表达能力', selected: false },
          { text: '保质保量的刷题与纠错', selected: false },
          { text: '平时多培养细心与耐心', selected: false }
        ]
      },
      {
        id: 'writing',
        name: '应用文写作',
        totalScore: 20,
        lostScore: 0,
        enabled: true,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '写作词汇/短语/句式/素材/范文积累不足', selected: false },
          { text: '基础汉译英翻译能力不足', selected: false },
          { text: '文章语法错误较多', selected: false },
          { text: '用词不准确/语句表达不够地道/不得体', selected: false },
          { text: '高级句式较少', selected: false },
          { text: '内容不充实/不丰富', selected: false },
          { text: '段落间/段落内结构不合理', selected: false },
          { text: '写跑题', selected: false },
          { text: '考场时间安排不合理导致写作时间不足', selected: false },
          { text: '平时写的太少导致写作熟练度不足', selected: false }
        ],
        suggestions: [
          { text: '积累背诵各话题写作词汇/短语/句式/素材/范文', selected: false },
          { text: '学习汉译英翻译理论基础', selected: false },
          { text: '梳理查缺补漏语法知识', selected: false },
          { text: '多写多练多纠错', selected: false },
          { text: '强化常用高级句式的应用练习', selected: false },
          { text: '梳理各话题内容写法/强化素材积累与范文赏析/限时内容构思训练', selected: false },
          { text: '了解训练常用的应用文结构/做范文赏析', selected: false },
          { text: '训练审题能力', selected: false },
          { text: '合理规划考场各题型作答时间', selected: false }
        ]
      },
      {
        id: 'wordChoice',
        name: '选词填空',
        totalScore: 10,
        lostScore: 0,
        enabled: false,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '词汇量不足', selected: false },
          { text: '句子翻译能力薄弱', selected: false },
          { text: '词性辨别能力不足', selected: false },
          { text: '语法变形能力不足', selected: false },
          { text: '上下文串联理解能力/逻辑推理能力不足', selected: false }
        ],
        suggestions: [
          { text: '扩大词汇量', selected: false },
          { text: '精读翻译训练', selected: false },
          { text: '学习词性辨别', selected: false },
          { text: '梳理强化语法知识的掌握', selected: false },
          { text: '训练上下文分析与逻辑推理能力', selected: false }
        ]
      },
      {
        id: 'translation',
        name: '完成句子/翻译句子',
        totalScore: 10,
        lostScore: 0,
        enabled: false,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '词汇量不足', selected: false },
          { text: '短语/句型结构掌握不足', selected: false },
          { text: '不了解中英文的差异性/中式英语较多', selected: false }
        ],
        suggestions: [
          { text: '强化单词的背诵', selected: false },
          { text: '积累背诵常见的短语/句型结构', selected: false },
          { text: '学习汉译英翻译理论基础', selected: false }
        ]
      },
      {
        id: 'spelling',
        name: '单词拼写',
        totalScore: 10,
        lostScore: 0,
        enabled: false,
        customReason: '',
        customSuggestion: '',
        reasons: [
          { text: '汉译英单词拼写掌握量不足', selected: false },
          { text: '刷题较少/词汇应用较少导致遗忘', selected: false },
          { text: '单词没有反复复习', selected: false }
        ],
        suggestions: [
          { text: '加强常考词汇的拼写背诵', selected: false },
          { text: '多刷题多应用强化记忆效果', selected: false },
          { text: '增加练习次数与刷题数量', selected: false }
        ]
      }
    ],
    
    // 当前选中的题型
    currentSection: null,
    
    // 已启用的题型列表
    enabledSections: [],
    
    // 报告数据
    reportData: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // 直接初始化页面（权限已在教师工具箱验证过）
    this.initializePage();

    // 定期清理过期文件（每次进入页面时检查，但限制频率）
    this.scheduleCleanup();
  },

  // 初始化页面数据
  initializePage: function() {
    // 检查会员状态
    const app = getApp();
    if (app.globalData.userInfo && app.globalData.userInfo.username === 'gengruihuan') {
      app.globalData.userInfo.isVip = true;
      app.globalData.userInfo.vipExpiry = '2025-12-31';
    }
    
    // 初始化统计数据
    this.updateSectionStats();
    this.updateListeningExamStatus();
    
    // 初始化当前题型
    const enabledSections = this.data.sections.filter(section => section.enabled);
    if (enabledSections.length > 0) {
      this.setData({
        currentSection: enabledSections[0],
        selectedSection: 0
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    return {
      title: `${this.data.basicInfo.studentName}的英语能力分析报告`,
      path: `/pages/profile/report/report?shared=1`,
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 基本信息输入处理
  onStudentNameChange: function(e) {
    this.setData({
      'basicInfo.studentName': e.detail.value
    });
  },

  onGradeChange: function(e) {
    this.setData({
      'basicInfo.grade': e.detail.value
    });
  },

  onExamTypeChange: function(e) {
    this.setData({
      'basicInfo.examType': e.detail.value
    });
  },

  onAnalystNameChange: function(e) {
    this.setData({
      'basicInfo.analystName': e.detail.value
    });
  },

  // 切换题型启用状态
  toggleSection: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const sections = [...this.data.sections];
    sections[index].enabled = !sections[index].enabled;
    
    this.setData({
      sections
    });
    
    this.updateSectionStats();
    this.updateListeningExamStatus();
  },

  // 听口考试整体勾选
  toggleListeningExam: function() {
    const sections = [...this.data.sections];
    const listeningIds = ['listening_choice', 'listening_record', 'listening_retell', 'listening_reading', 'listening_answer'];
    const newStatus = !this.data.listeningExamSelected;

    // 设置所有听口题型的状态
    listeningIds.forEach(id => {
      const index = sections.findIndex(section => section.id === id);
      if (index !== -1) {
        sections[index].enabled = newStatus;
      }
    });

    this.setData({
      sections,
      listeningExamSelected: newStatus
    });

    this.updateSectionStats();
  },

  // 更新听口考试整体状态
  updateListeningExamStatus: function() {
    const listeningIds = ['listening_choice', 'listening_record', 'listening_retell', 'listening_reading', 'listening_answer'];
    const listeningEnabledCount = listeningIds.reduce((count, id) => {
      const section = this.data.sections.find(s => s.id === id);
      return count + (section && section.enabled ? 1 : 0);
    }, 0);
    
    // 如果所有听口题型都选中，则整体选中；否则不选中
    const listeningExamSelected = listeningEnabledCount === listeningIds.length;
    
    this.setData({
      listeningExamSelected
    });
  },

  // 更新题型统计
  updateSectionStats: function() {
    const enabledSections = this.data.sections.filter(section => section.enabled);
    const enabledSectionsCount = enabledSections.length;

    // 计算总分时确保正确处理数字类型
    const totalSelectedScore = enabledSections.reduce((sum, section) => {
      let totalScore = section.totalScore;

      // 处理总分的数据类型转换
      if (typeof totalScore === 'string') {
        const cleanValue = totalScore.toString().trim();
        if (cleanValue === '' || cleanValue === '.') {
          totalScore = 0;
        } else {
          totalScore = parseFloat(cleanValue);
          if (isNaN(totalScore)) {
            totalScore = 0;
          }
        }
      } else if (typeof totalScore !== 'number') {
        totalScore = 0;
      }

      return sum + totalScore;
    }, 0);

    // 计算失分总数 - 确保正确处理小数和字符串
    const totalLostScore = enabledSections.reduce((sum, section) => {
      const lostScore = section.lostScore;
      // 处理空值、字符串和数字
      if (lostScore === '' || lostScore === null || lostScore === undefined || lostScore === '.') {
        return sum;
      }

      // 将输入值转换为数字，处理字符串情况
      let numValue;
      if (typeof lostScore === 'string') {
        // 如果是字符串，先清理并转换
        const cleanValue = lostScore.toString().trim();
        if (cleanValue === '' || cleanValue === '.') {
          return sum;
        }
        numValue = parseFloat(cleanValue);
      } else {
        numValue = parseFloat(lostScore);
      }

      // 检查是否为有效数字
      if (isNaN(numValue)) {
        return sum;
      }

      return sum + numValue;
    }, 0);

    // 计算得分总数
    const totalGainedScore = totalSelectedScore - totalLostScore;

    // 计算正确率
    const correctRate = totalSelectedScore > 0 ? Math.round((totalGainedScore / totalSelectedScore) * 100) : 0;

    // 使用更精确的小数处理方式
    const formatNumber = (num) => {
      // 检查是否为有效数字
      if (isNaN(num) || !isFinite(num)) {
        return 0;
      }
      // 四舍五入到两位小数
      const rounded = Math.round(num * 100) / 100;
      // 如果是整数，显示整数；否则显示最多两位小数
      return rounded % 1 === 0 ? Math.floor(rounded) : rounded;
    };

    this.setData({
      enabledSections,
      enabledSectionsCount,
      totalSelectedScore: formatNumber(totalSelectedScore),
      totalLostScore: formatNumber(totalLostScore),
      totalGainedScore: formatNumber(totalGainedScore),
      correctRate
    });
  },

  // 选择题型
  selectSection(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const enabledSections = this.data.enabledSections;
    this.setData({
      selectedSection: index,
      currentSection: enabledSections[index]
    });
  },

  // 验证数字输入（支持小数）
  validateNumberInput(value) {
    if (!value || value === '') return '';
    
    // 移除非数字和小数点的字符
    const cleanValue = value.replace(/[^0-9.]/g, '');
    
    // 确保只有一个小数点
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      return parts[0] + '.' + parts.slice(1).join('');
    }
    
    return cleanValue;
  },

  // 输入框聚焦处理
  onScoreFocus(e) {
    const field = e.currentTarget.dataset.field;
    if (field === 'lostScore') {
      const sections = [...this.data.sections];
      const enabledSections = this.data.enabledSections;
      const currentEnabledIndex = this.data.currentSectionIndex;
      const currentEnabledSection = enabledSections[currentEnabledIndex];
      const originalIndex = sections.findIndex(section => section.id === currentEnabledSection.id);
      
      // 如果当前值是0，清空输入框
      if (sections[originalIndex][field] === 0) {
        sections[originalIndex][field] = '';
        this.setData({
          sections,
          currentSection: this.data.enabledSections[currentEnabledIndex]
        });
      }
    }
  },

  // 分数输入处理
  onScoreChange(e) {
    const field = e.currentTarget.dataset.field;
    let inputValue = e.detail.value;

    const sections = [...this.data.sections];
    const enabledSections = this.data.enabledSections;
    const currentEnabledIndex = this.data.currentSectionIndex;
    const currentEnabledSection = enabledSections[currentEnabledIndex];
    const originalIndex = sections.findIndex(section => section.id === currentEnabledSection.id);

    // 处理空值情况
    if (inputValue === '' || inputValue === null || inputValue === undefined) {
      sections[originalIndex][field] = '';
      this.updateSectionStats();
      this.setData({
        sections,
        currentSection: this.data.enabledSections[currentEnabledIndex]
      });
      return;
    }

    // 允许输入中间状态（如 "3.", "0.5", ".5" 等）
    // 修复正则表达式以正确支持小数点输入
    if (inputValue === '.' || /^(\d*\.?\d*|\.\d*)$/.test(inputValue)) {
      // 先设置输入值，允许中间状态显示
      sections[originalIndex][field] = inputValue;

      // 如果是完整的数字，进行验证和处理
      const numValue = parseFloat(inputValue);
      if (!isNaN(numValue) && isFinite(numValue)) {
        if (field === 'totalScore') {
          if (numValue < 0) {
            sections[originalIndex][field] = 0;
          } else {
            // 如果输入以小数点结尾（如"3."），保持字符串格式以允许继续输入
            if (inputValue.endsWith('.')) {
              sections[originalIndex][field] = inputValue;
            } else {
              // 确保总分为数字类型
              sections[originalIndex][field] = numValue;
            }

            // 如果失分大于新的总分，则调整失分
            const currentLostScore = sections[originalIndex].lostScore;
            if (currentLostScore !== '' && currentLostScore !== null && currentLostScore !== undefined) {
              const lostScoreNum = parseFloat(currentLostScore);
              if (!isNaN(lostScoreNum) && lostScoreNum > numValue) {
                sections[originalIndex].lostScore = numValue;
                wx.showToast({
                  title: `失分已调整为${numValue}分`,
                  icon: 'none',
                  duration: 1500
                });
              }
            }
          }
        } else if (field === 'lostScore') {
          // 获取当前总分
          let totalScore = sections[originalIndex].totalScore;
          if (typeof totalScore === 'string') {
            totalScore = parseFloat(totalScore);
          }
          if (isNaN(totalScore) || totalScore === null || totalScore === undefined) {
            totalScore = 0;
          }

          if (numValue > totalScore) {
            wx.showToast({
              title: `扣分不能超过总分${totalScore}分`,
              icon: 'none',
              duration: 2000
            });
            sections[originalIndex][field] = totalScore;
          } else if (numValue < 0) {
            sections[originalIndex][field] = 0;
          } else {
            // 如果输入以小数点结尾（如"3."），保持字符串格式以允许继续输入
            if (inputValue.endsWith('.')) {
              sections[originalIndex][field] = inputValue;
            } else {
              // 确保失分为数字类型
              sections[originalIndex][field] = numValue;
            }
          }
        }

        // 更新统计数据
        this.updateSectionStats();
      }

      this.setData({
        sections,
        currentSection: this.data.enabledSections[currentEnabledIndex]
      });
      return;
    }

    // 如果输入不符合数字格式，忽略此次输入
    return;
  },

  // 选项切换
  toggleOption(e) {
    const type = e.currentTarget.dataset.type;
    const index = parseInt(e.currentTarget.dataset.index);
    const sections = [...this.data.sections];
    const enabledSections = this.data.enabledSections;
    const currentEnabledIndex = this.data.currentSectionIndex;
    const currentEnabledSection = enabledSections[currentEnabledIndex];
    
    // 找到在原sections数组中的索引
    const originalIndex = sections.findIndex(section => section.id === currentEnabledSection.id);
    
    sections[originalIndex][type][index].selected = !sections[originalIndex][type][index].selected;
    
    // 更新启用的题型列表
    this.updateSectionStats();
    
    this.setData({
      sections,
      currentSection: this.data.enabledSections[currentEnabledIndex]
    });
  },

  // 自定义输入处理
  onCustomChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    const sections = [...this.data.sections];
    const enabledSections = this.data.enabledSections;
    const currentEnabledIndex = this.data.currentSectionIndex;
    const currentEnabledSection = enabledSections[currentEnabledIndex];
    
    // 找到在原sections数组中的索引
    const originalIndex = sections.findIndex(section => section.id === currentEnabledSection.id);
    
    sections[originalIndex][field] = value;
    
    // 更新启用的题型列表
    this.updateSectionStats();
    
    this.setData({
      sections,
      currentSection: this.data.enabledSections[currentEnabledIndex]
    });
  },

  // 步骤控制
  nextStep() {
    if (this.data.currentStep === 0) {
      // 验证基本信息
      if (!this.validateBasicInfo()) {
        return;
      }
    } else if (this.data.currentStep === 1) {
      // 验证题型选择
      if (this.data.enabledSectionsCount === 0) {
        wx.showToast({
          title: '请至少选择一个题型',
          icon: 'none'
        });
        return;
      }
      // 初始化第一个启用的题型
      if (this.data.enabledSections.length > 0) {
        this.setData({
          selectedSection: 0,
          currentSectionIndex: 0,
          currentSection: this.data.enabledSections[0]
        });
      }
    } else if (this.data.currentStep === 2) {
      // 在题型分析步骤中，处理下一题型或进入课程规划选择
      if (this.data.currentSectionIndex < this.data.enabledSections.length - 1) {
        // 还有下一个题型，跳转到下一个
        const nextIndex = this.data.currentSectionIndex + 1;
        this.setData({
          currentSectionIndex: nextIndex,
          selectedSection: nextIndex,
          currentSection: this.data.enabledSections[nextIndex]
        });
        return; // 不增加步骤，留在当前步骤
      } else {
        // 最后一个题型完成，进入课程规划选择步骤
        // 不在这里生成报告，等到课程规划步骤完成后再生成
      }
    } else if (this.data.currentStep === 3) {
      // 课程规划选择步骤完成，生成报告
      this.generateReport();
    }
    
    this.setData({
      currentStep: this.data.currentStep + 1
    });
  },

  // 跳过课程规划
  skipCoursePlan() {
    this.setData({
      'reportConfig.showCourseplan': false
    });
    this.generateReport();
    this.setData({
      currentStep: 4 // 直接进入报告预览步骤
    });
  },

  // 确认使用课程规划
  confirmCoursePlan() {
    this.setData({
      'reportConfig.showCourseplan': true
    });
    this.generateReport();
    this.setData({
      currentStep: 4 // 进入报告预览步骤
    });
  },

  prevStep() {
    if (this.data.currentStep === 2 && this.data.currentSectionIndex > 0) {
      // 在题型分析步骤中，如果不是第一个题型，返回上一个题型
      const prevIndex = this.data.currentSectionIndex - 1;
      this.setData({
        currentSectionIndex: prevIndex,
        selectedSection: prevIndex,
        currentSection: this.data.enabledSections[prevIndex]
      });
    } else if (this.data.currentStep === 4) {
      // 从报告预览步骤返回到课程规划选择步骤
      this.setData({
        currentStep: 3
      });
    } else {
      // 否则返回上一步
      this.setData({
        currentStep: this.data.currentStep - 1
      });
      
      // 如果回到题型分析步骤，重置为第一个题型
      if (this.data.currentStep === 2) {
        this.setData({
          currentSectionIndex: 0,
          selectedSection: 0,
          currentSection: this.data.enabledSections[0]
        });
      }
    }
  },

  // 验证基本信息
  validateBasicInfo() {
    const { studentName, grade, examType } = this.data.basicInfo;
    
    if (!studentName.trim()) {
      wx.showToast({
        title: '请输入学员姓名',
        icon: 'none'
      });
      return false;
    }
    
    if (!grade.trim()) {
      wx.showToast({
        title: '请输入年级',
        icon: 'none'
      });
      return false;
    }
    
    if (!examType.trim()) {
      wx.showToast({
        title: '请输入考试类型',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  // 生成报告
  generateReport() {
    console.log('开始生成报告，当前启用的题型:', this.data.enabledSections.length);

    // 处理启用的题型数据，提取选中的原因和建议
    const processedSections = this.data.enabledSections.map(section => {
      const selectedReasons = section.reasons
        ? section.reasons.filter(reason => reason.selected).map(reason => reason.text)
        : [];
      
      const selectedSuggestions = section.suggestions
        ? section.suggestions.filter(suggestion => suggestion.selected).map(suggestion => suggestion.text)
        : [];
      
      return {
        ...section,
        selectedReasons,
        selectedSuggestions
      };
    });
    
    // 使用相同的数字格式化逻辑
    const formatNumber = (num) => {
      // 四舍五入到两位小数
      const rounded = Math.round(num * 100) / 100;
      // 如果是整数，显示整数；否则显示最多两位小数
      return rounded % 1 === 0 ? Math.floor(rounded) : rounded;
    };
    
    // 计算统计数据 - 使用与updateSectionStats相同的逻辑
    // 重新计算总分，确保数据一致性
    const totalSelectedScore = processedSections.reduce((sum, section) => {
      let totalScore = section.totalScore;

      // 处理总分的数据类型转换
      if (typeof totalScore === 'string') {
        const cleanValue = totalScore.toString().trim();
        if (cleanValue === '' || cleanValue === '.') {
          totalScore = 0;
        } else {
          totalScore = parseFloat(cleanValue);
          if (isNaN(totalScore)) {
            totalScore = 0;
          }
        }
      } else if (typeof totalScore !== 'number') {
        totalScore = 0;
      }

      return sum + totalScore;
    }, 0);

    const totalLostScore = processedSections.reduce((sum, section) => {
      const lostScore = section.lostScore;
      if (lostScore === '' || lostScore === null || lostScore === undefined || lostScore === '.') {
        return sum;
      }

      let numValue;
      if (typeof lostScore === 'string') {
        const cleanValue = lostScore.toString().trim();
        if (cleanValue === '' || cleanValue === '.') {
          return sum;
        }
        numValue = parseFloat(cleanValue);
      } else {
        numValue = parseFloat(lostScore);
      }

      if (isNaN(numValue) || !isFinite(numValue)) {
        return sum;
      }

      return sum + numValue;
    }, 0);

    const totalGainedScore = totalSelectedScore - totalLostScore;
    const correctRate = totalSelectedScore > 0 ? Math.round((totalGainedScore / totalSelectedScore) * 100) : 0;
    
    // 计算各题型得分率数据用于柱状图
    const chartData = processedSections.map((section, index) => {
      const lostScore = section.lostScore || 0;
      const gainedScore = section.totalScore - lostScore;
      const rate = section.totalScore > 0 ? Math.round((gainedScore / section.totalScore) * 100) : 0;
      
      return {
        id: index,
        name: section.name,
        rate: rate,
        totalScore: section.totalScore,
        gainedScore: gainedScore,
        lostScore: lostScore
      };
    });
    
    const reportData = {
      basicInfo: this.data.basicInfo,
      sections: processedSections,
      generatedAt: new Date().toLocaleDateString('zh-CN')
    };
    
    console.log('设置报告数据，chartData:', chartData.length, '个题型');
    console.log('chartData详情:', chartData);

    this.setData({
      reportData,
      chartData,
      totalSelectedScore,
      totalLostScore: formatNumber(totalLostScore),
      totalGainedScore: formatNumber(totalGainedScore),
      correctRate
    });
  },

  // 生成HTML报告内容
  generateHTMLReport(data) {
    const { basicInfo, sections } = data;
    
    let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生英语能力分析报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 16px;
            color: #666;
        }
        .basic-info {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .info-item {
            display: flex;
            align-items: center;
        }
        .info-label {
            font-weight: bold;
            color: #333;
            margin-right: 10px;
        }
        .info-value {
            color: #667eea;
            font-weight: 600;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
        }
        .section-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 18px;
        }
        .section-content {
            padding: 20px;
        }
        .score-summary {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        .score-item {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .score-item.total {
            background: #f8f9fa;
        }
        .score-item.lost {
            background: #fff5f5;
            border-color: #feb2b2;
        }
        .score-item.gained {
            background: #f0fff4;
            border-color: #9ae6b4;
        }
        .score-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .score-value {
            font-size: 20px;
            font-weight: bold;
        }
        .score-value.total {
            color: #333;
        }
        .score-value.lost {
            color: #e53e3e;
        }
        .score-value.gained {
            color: #38a169;
        }
        .analysis-group {
            margin-bottom: 20px;
        }
        .group-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .option-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .option-item {
            background: #f8f9fa;
            margin-bottom: 5px;
            padding: 8px 12px;
            border-radius: 6px;
            border-left: 3px solid #667eea;
        }
        .custom-item {
            background: #fff5f5;
            border-left-color: #e53e3e;
            font-style: italic;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #666;
            font-size: 14px;
        }
        @media print {
            body {
                background: white;
            }
            .container {
                box-shadow: none;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">${mainTitle}</h1>
            <p class="subtitle">专业英语学习诊断分析</p>
        </div>
        
        <div class="basic-info">
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">学员姓名：</span>
                    <span class="info-value">${basicInfo.studentName}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">年级：</span>
                    <span class="info-value">${basicInfo.grade}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">考试类型：</span>
                    <span class="info-value">${basicInfo.examType}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">总分：</span>
                    <span class="info-value">${basicInfo.totalScore}分</span>
                </div>
            </div>
        </div>
`;

    // 生成各题型分析
    sections.forEach(section => {
      const lostScore = section.lostScore || 0;
      const gainedScore = section.totalScore - lostScore;
      
      html += `
        <div class="section">
            <div class="section-header">${section.name}</div>
            <div class="section-content">
                <div class="score-summary">
                    <div class="score-item total">
                        <div class="score-label">总分</div>
                        <div class="score-value total">${section.totalScore}</div>
                    </div>
                    <div class="score-item lost">
                        <div class="score-label">失分</div>
                        <div class="score-value lost">${lostScore}</div>
                    </div>
                    <div class="score-item gained">
                        <div class="score-label">得分</div>
                        <div class="score-value gained">${gainedScore}</div>
                    </div>
                </div>
                
                <div class="analysis-group">
                    <div class="group-title">原因分析</div>
                    <ul class="option-list">
`;
      
      // 添加选中的原因
      section.selectedReasons.forEach(reason => {
        html += `<li class="option-item">${reason}</li>`;
      });
      
      // 添加自定义原因
      if (section.customReason) {
        html += `<li class="option-item custom-item">${section.customReason}</li>`;
      }
      
      html += `
                    </ul>
                </div>
                
                <div class="analysis-group">
                    <div class="group-title">学习建议</div>
                    <ul class="option-list">
`;
      
      // 添加选中的建议
      section.selectedSuggestions.forEach(suggestion => {
        html += `<li class="option-item">${suggestion}</li>`;
      });
      
      // 添加自定义建议
      if (section.customSuggestion) {
        html += `<li class="option-item custom-item">${section.customSuggestion}</li>`;
      }
      
      html += `
                    </ul>
                </div>
            </div>
        </div>
`;
    });

    html += `
        <div class="footer">
            <p>报告生成时间：${new Date().toLocaleString('zh-CN')}</p>
        </div>
    </div>
</body>
</html>
`;

    return html;
  },

  // 保存报告到本地（实际项目中应该上传到云存储）
  saveReportToLocal(htmlContent) {
    // 在实际项目中，这里应该：
    // 1. 将HTML内容上传到云存储
    // 2. 返回云存储的访问URL
    // 
    // 目前返回一个模拟的URL
    return 'data:text/html;charset=utf-8,' + encodeURIComponent(htmlContent);
  },

  // 导出分析报告到相册
  exportAnalysisReport() {
    // 先检查和请求保存到相册的权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限，引导用户手动开启
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启相册权限',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      // 用户开启了权限，继续保存
                      this.doExportAnalysisReport();
                    }
                  }
                });
              }
            }
          });
        } else if (res.authSetting['scope.writePhotosAlbum'] === undefined) {
          // 还没有请求过权限，先请求权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              // 授权成功，继续保存
              this.doExportAnalysisReport();
            },
            fail: () => {
              // 授权失败
              wx.showModal({
                title: '保存失败',
                content: '需要相册权限才能保存图片',
                showCancel: false,
                confirmText: '知道了'
              });
            }
          });
        } else {
          // 已经有权限，直接保存
          this.doExportAnalysisReport();
        }
      },
      fail: () => {
        // 获取设置失败，尝试直接保存
        this.doExportAnalysisReport();
      }
    });
  },

  // 执行导出分析报告
  doExportAnalysisReport() {
    wx.showLoading({
      title: '正在生成分析报告...',
      mask: true
    });

    this.generateCanvasImage('analysis').then((tempFilePath) => {
      wx.hideLoading();
      wx.saveImageToPhotosAlbum({
        filePath: tempFilePath,
        success() {
          wx.showToast({
            title: '分析报告已保存到相册',
            icon: 'success'
          });
        },
        fail(err) {
          console.error('保存失败:', err);
          if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
            wx.showModal({
              title: '保存失败',
              content: '需要相册权限才能保存图片，请在设置中开启相册权限',
              confirmText: '去设置',
              cancelText: '取消',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting();
                }
              }
            });
          } else if (err.errMsg.includes('privacy api banned')) {
            wx.showModal({
              title: '保存失败',
              content: '当前小程序版本暂不支持保存到相册功能，请联系开发者或等待版本更新',
              showCancel: false,
              confirmText: '知道了'
            });
          } else {
            // 应急方案：提供其他保存方式
            wx.showModal({
              title: '保存失败',
              content: `保存失败：${err.errMsg || '未知错误'}\n\n您可以尝试截屏保存或联系客服`,
              confirmText: '联系客服',
              cancelText: '知道了',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.navigateTo({
                    url: '/pages/profile/customer-service/customer-service'
                  });
                }
              }
            });
          }
        }
      });
    }).catch((err) => {
      wx.hideLoading();
      console.error('生成图片失败:', err);
      wx.showModal({
        title: '生成失败',
        content: `生成图片失败：${err.message || '未知错误'}`,
        showCancel: false,
        confirmText: '知道了'
      });
    });
  },

  // 导出课程规划到相册
  exportCoursePlan() {
    if (!this.data.reportConfig.showCourseplan) {
      wx.showToast({
        title: '未填写课程规划',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '正在生成课程规划...',
      mask: true
    });

    // 直接执行导出，让微信处理权限
    this.doExportCoursePlan();
  },

  // 执行导出课程规划
  doExportCoursePlan() {
    this.generateCanvasImage('courseplan').then((tempFilePath) => {
      wx.hideLoading();
      // 直接尝试保存，不预先检查权限
      wx.saveImageToPhotosAlbum({
        filePath: tempFilePath,
        success() {
          wx.showToast({
            title: '课程规划已保存到相册',
            icon: 'success'
          });
        },
        fail: (err) => {
          console.error('直接保存失败:', err);
          // 如果直接保存失败，尝试权限处理流程
          this.handleSaveFailure(err, tempFilePath);
        }
      });
    }).catch((err) => {
      wx.hideLoading();
      console.error('生成图片失败:', err);
      wx.showModal({
        title: '生成失败',
        content: `生成图片失败：${err.message || '未知错误'}`,
        showCancel: false,
        confirmText: '知道了'
      });
    });
  },

  // 导出报告（原有功能，现在作为综合导出选项）
  exportReport() {
    const itemList = ['导出分析报告', '分享给朋友', '复制链接'];
    if (this.data.reportConfig.showCourseplan) {
      itemList.splice(1, 0, '导出课程规划');
    }
    
    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        if (res.tapIndex === 0) {
          this.exportAnalysisReport();
        } else if (res.tapIndex === 1 && this.data.reportConfig.showCourseplan) {
          this.exportCoursePlan();
        } else if ((res.tapIndex === 1 && !this.data.reportConfig.showCourseplan) || res.tapIndex === 2) {
          this.shareToFriend();
        } else if (res.tapIndex === 3) {
          this.copyLink();
        }
      }
    });
  },

  // 显示分享选项
  showShareOptions: function(e) {
    const type = e.currentTarget.dataset.type;
    console.log('showShareOptions被调用，type参数:', type);

    wx.showActionSheet({
      itemList: ['分享给朋友', '保存到相册', '生成并复制HTML链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 分享给朋友
            this.shareToFriend(type);
            break;
          case 1:
            // 保存到相册
            this.saveToAlbum(type);
            break;
          case 2:
            // 生成并复制HTML链接
            this.generateAndCopyHtmlLink(type);
            break;
        }
      }
    });
  },

  // 分享给朋友
  shareToFriend(type) {
    wx.showToast({
      title: '可通过右上角分享按钮分享',
      icon: 'none',
      duration: 2000
    });
  },

  // 保存到相册
  saveToAlbum: function(type) {
    console.log('saveToAlbum被调用，type参数:', type);
    // 直接使用传入的type，不需要转换
    let imageType = type;

    wx.showLoading({
      title: '正在生成图片...',
      mask: true
    });

    this.generateCanvasImage(imageType).then((tempFilePath) => {
      wx.hideLoading();
      this.saveImageToAlbum(tempFilePath);
    }).catch((err) => {
      wx.hideLoading();
      console.error('生成图片失败:', err);
      wx.showToast({
        title: '生成图片失败',
        icon: 'none'
      });
    });
  },

  // 生成并复制HTML链接
  generateAndCopyHtmlLink: function(type) {
    wx.showLoading({
      title: '正在清理存储空间...',
      mask: true
    });

    // 先清理存储空间，然后生成HTML链接
    wx.showLoading({
      title: '正在清理存储空间...',
      mask: true
    });

    // 先清理存储空间
    this.cleanupOldReportFiles().then(() => {
      wx.showLoading({
        title: '正在生成HTML链接...',
        mask: true
      });

      // 生成HTML内容
      const htmlContent = this.generateHtmlContent(type);

      // 上传到云存储并获取链接
      this.uploadHtmlToCloud(htmlContent, type).then((shareUrl) => {
      wx.hideLoading();

      // 显示成功提示和使用说明
      wx.showModal({
        title: '✅ HTML链接生成成功',
        content: `链接已复制到剪贴板！\n\n📋 使用说明：\n• 链接7天内有效，请及时使用\n• 在浏览器中打开查看完整报告\n• 支持打印和保存为PDF\n• 可分享给任何人查看`,
        confirmText: '知道了',
        showCancel: false,
        success: () => {
          // 复制链接到剪贴板
          try {
            wx.setClipboardData({
              data: shareUrl,
              success: () => {
                console.log('HTML链接已复制:', shareUrl);
              },
              fail: (err) => {
                console.error('复制链接失败:', err);
                wx.showToast({
                  title: '复制失败，请重试',
                  icon: 'none'
                });
              }
            });
          } catch (error) {
            console.error('复制链接异常:', error);
            wx.showToast({
              title: '复制失败，请重试',
              icon: 'none'
            });
          }
        }
      });
      }).catch((err) => {
        wx.hideLoading();
        console.error('生成HTML链接失败:', err);

        // 如果是存储空间不足，提供更详细的提示
        if (err.errMsg && err.errMsg.includes('storage limit')) {
          wx.showModal({
            title: '存储空间不足',
            content: '小程序本地存储空间已满，请清理后重试：\n\n1. 长按小程序图标\n2. 选择"删除"\n3. 重新打开小程序\n\n或者等待几分钟后重试。',
            confirmText: '知道了',
            showCancel: false
          });
        } else {
          wx.showToast({
            title: '生成链接失败',
            icon: 'none'
          });
        }
      });
    }).catch((cleanupErr) => {
      wx.hideLoading();
      console.error('清理存储空间失败:', cleanupErr);
      wx.showModal({
        title: '存储空间不足',
        content: '存储空间已满且清理失败，请手动清理小程序缓存：\n\n1. 长按小程序图标\n2. 选择"删除"\n3. 重新打开小程序',
        confirmText: '知道了',
        showCancel: false
      });
    });
  },



  // 直接保存HTML文件
  directSaveHTML(htmlData) {
    // 由于微信小程序的限制，我们提供更实用的方案
    wx.showActionSheet({
      itemList: ['复制HTML代码', '查看使用说明'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 复制HTML代码到剪贴板
          wx.setClipboardData({
            data: htmlData.content,
            success: () => {
              wx.showModal({
                title: '✅ HTML代码已复制',
                content: '完整的HTML代码已复制到剪贴板！\n\n使用方法：\n1. 新建一个文本文件\n2. 粘贴HTML代码\n3. 保存为 .html 文件\n4. 用浏览器打开\n5. 打印为PDF\n\n文件名建议：' + htmlData.fileName,
                showCancel: false,
                confirmText: '知道了'
              });
            },
            fail: () => {
              wx.showToast({
                title: '复制失败',
                icon: 'none'
              });
            }
          });
        } else {
          // 显示详细使用说明
          this.showHTMLUsageGuide(htmlData.fileName);
        }
      }
    });
  },

  // 显示HTML使用指南
  showHTMLUsageGuide(fileName) {
    wx.showModal({
      title: '📋 HTML报告使用指南',
      content: `由于小程序限制，我们提供以下方案：\n\n方案一：复制代码法\n1. 点击"复制HTML代码"\n2. 新建文本文件并粘贴\n3. 保存为 ${fileName}\n4. 浏览器打开并打印为PDF\n\n方案二：在线工具\n1. 复制HTML代码\n2. 使用在线HTML转PDF工具\n3. 直接获得PDF文件\n\n推荐浏览器：Chrome、Edge`,
      confirmText: '复制代码',
      cancelText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 用户选择复制代码
          this.generateHTMLContent('complete').then((htmlData) => {
            wx.setClipboardData({
              data: htmlData.content,
              success: () => {
                wx.showToast({
                  title: 'HTML代码已复制',
                  icon: 'success'
                });
              }
            });
          }).catch(() => {
            wx.showToast({
              title: '生成失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 生成HTML内容
  generateHTMLContent: function(type) {
    return new Promise((resolve, reject) => {
      try {
        // 获取报告数据
        const reportData = this.data.reportData;
        const reportConfig = this.data.reportConfig;

        if (!reportData) {
          reject(new Error('报告数据不存在'));
          return;
        }

        // 生成HTML内容
        let htmlContent = this.generateReportHTML(type, reportData, reportConfig);

        // 生成HTML文件名
        const htmlFileName = this.getHTMLFileName(type);

        resolve({
          content: htmlContent,
          fileName: htmlFileName
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  // 生成报告HTML内容
  generateReportHTML(type, reportData, reportConfig) {
    const styles = this.data.styles[this.data.selectedStyle];

    // 根据type生成不同的标题
    const pageTitle = type === 'courseplan' ?
      `${reportData.basicInfo.studentName}同学课程规划` :
      `${reportData.basicInfo.studentName}同学${reportData.basicInfo.grade}英语${reportData.basicInfo.examType}试卷分析报告`;

    let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle} - 墨词自习室</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }

        /* 打印样式 */
        @media print {
            body { margin: 0; padding: 15px; }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .report-header {
            text-align: center;
            margin-bottom: 0;
            padding: 30px 20px;
            background: linear-gradient(135deg, ${styles.primary}, ${styles.secondary});
            color: white;
        }
        .report-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .report-subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-style: italic;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            color: ${styles.primary};
            border-bottom: 3px solid ${styles.primary};
            padding-bottom: 8px;
            display: flex;
            align-items: center;
        }
        .section-title::before {
            content: '📊';
            margin-right: 10px;
            font-size: 24px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid ${styles.primary};
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .info-item:hover {
            transform: translateY(-2px);
        }
        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .info-value {
            font-size: 18px;
            color: #333;
            font-weight: bold;
        }

        .analysis-item {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid ${styles.secondary};
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .question-type {
            font-weight: bold;
            color: ${styles.primary};
            margin-bottom: 8px;
            font-size: 16px;
        }
        .score-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
            padding: 5px 10px;
            background: #f8f9fa;
            border-radius: 4px;
            display: inline-block;
        }
        .reason-analysis {
            background: linear-gradient(135deg, #e3f2fd, #f0f8ff);
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 10px;
            border-left: 3px solid #2196f3;
        }
        .reason-analysis strong {
            color: #1976d2;
        }
        .study-suggestion {
            background: linear-gradient(135deg, #e8f5e8, #f0fff0);
            padding: 12px;
            border-radius: 6px;
            border-left: 3px solid #4caf50;
        }
        .study-suggestion strong {
            color: #388e3c;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #f5f5f5, #e9ecef);
            border-radius: 10px;
            font-size: 14px;
            color: #666;
            border-top: 3px solid ${styles.primary};
        }
        .footer .logo {
            font-size: 18px;
            font-weight: bold;
            color: ${styles.primary};
            margin-bottom: 5px;
        }

        /* 使用说明 */
        .usage-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        .usage-note h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        .usage-note ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">`;

    // 添加报告头部
    htmlContent += `
        <div class="report-header">
            <div class="report-title">${reportData.basicInfo.studentName}同学${reportData.basicInfo.grade}英语${reportData.basicInfo.examType}试卷分析报告</div>
            <div class="report-subtitle">Professional English Assessment Report</div>
        </div>

        <div class="content">
            <div class="usage-note no-print">
                <h4>📋 使用说明</h4>
                <ul>
                    <li>在浏览器中打开此文件可查看完整报告</li>
                    <li>使用浏览器的"打印"功能可保存为PDF</li>
                    <li>建议使用Chrome、Edge等现代浏览器获得最佳效果</li>
                    <li>打印时建议选择"更多设置" → "背景图形"以保持完整样式</li>
                </ul>
            </div>`;

    // 根据类型添加不同的内容
    if (type === 'analysis' || type === 'complete') {
      // 添加基本信息
      htmlContent += `
    <div class="section">
        <div class="section-title">基本信息</div>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">学生姓名</div>
                <div class="info-value">${reportData.basicInfo.studentName}</div>
            </div>
            <div class="info-item">
                <div class="info-label">年级</div>
                <div class="info-value">${reportData.basicInfo.grade}</div>
            </div>
            <div class="info-item">
                <div class="info-label">考试类型</div>
                <div class="info-value">${reportData.basicInfo.examType}</div>
            </div>
            <div class="info-item">
                <div class="info-label">总分</div>
                <div class="info-value">${this.data.totalGainedScore}/${this.data.totalSelectedScore}</div>
            </div>
            <div class="info-item">
                <div class="info-label">正确率</div>
                <div class="info-value">${this.data.correctRate}%</div>
            </div>
        </div>
    </div>`;

      // 添加题型分析
      htmlContent += `
    <div class="section">
        <div class="section-title">题型分析</div>`;

      this.data.enabledSections.forEach(section => {
        const sectionData = reportData.sections[section.key];
        if (sectionData && sectionData.questions && sectionData.questions.length > 0) {
          htmlContent += `
        <div class="analysis-item">
            <div class="question-type">${section.name}</div>
            <div class="score-info">得分: ${sectionData.gainedScore || 0}/${sectionData.totalScore || 0}</div>
            <div class="reason-analysis">
                <strong>失分原因:</strong> ${sectionData.reasonAnalysis || '暂无分析'}
            </div>
            <div class="study-suggestion">
                <strong>学习建议:</strong> ${sectionData.studySuggestion || '暂无建议'}
            </div>
        </div>`;
        }
      });

      htmlContent += `
    </div>`;
    }

    // 如果是学习规划类型，添加基本信息（但不包含题型分析）
    if (type === 'courseplan') {
      htmlContent += `
    <div class="section">
        <div class="section-title">基本信息</div>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">学生姓名</div>
                <div class="info-value">${reportData.basicInfo.studentName}</div>
            </div>
            <div class="info-item">
                <div class="info-label">年级</div>
                <div class="info-value">${reportData.basicInfo.grade}</div>
            </div>
            <div class="info-item">
                <div class="info-label">考试类型</div>
                <div class="info-value">${reportData.basicInfo.examType}</div>
            </div>
            <div class="info-item">
                <div class="info-label">总分</div>
                <div class="info-value">${this.data.totalGainedScore}/${this.data.totalSelectedScore}</div>
            </div>
            <div class="info-item">
                <div class="info-label">正确率</div>
                <div class="info-value">${this.data.correctRate}%</div>
            </div>
        </div>
    </div>`;
    }

    // 如果是学习规划或完整内容，添加学习规划
    if ((type === 'courseplan' || type === 'complete') && reportConfig.showCourseplan) {
      htmlContent += `
    <div class="section">
        <div class="section-title">学习规划</div>
        <div style="background: white; padding: 15px; border-radius: 5px; white-space: pre-line;">
            ${reportConfig.courseplan || '暂无学习规划'}
        </div>
    </div>`;
    }

    // 添加页脚
    htmlContent += `
            <div class="footer">
                <div class="logo">📚 墨词自习室</div>
                <div>专业的英语学习分析工具</div>
                <div>生成时间: ${new Date().toLocaleString()}</div>
                <div style="margin-top: 10px; font-size: 12px; opacity: 0.8;">
                    此报告由墨词自习室智能生成，仅供学习参考使用
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;

    return htmlContent;
  },













  // 显示文件位置信息
  showFileLocation(filePath) {
    wx.showModal({
      title: '📁 文件位置信息',
      content: `文件已保存到：\n${filePath}\n\n💡 文件已直接保存到小程序本地存储中，类似于保存图片到相册的方式。\n\n📱 访问方式：\n• 通过"分享给朋友"发送到其他应用\n• 使用文件管理器搜索文件名\n• 发送到微信文件传输助手`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 复制文件路径
  copyFilePath(filePath) {
    wx.setClipboardData({
      data: filePath,
      success: () => {
        wx.showToast({
          title: '文件路径已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 清理旧的报告文件
  cleanupOldReportFiles: function() {
    return new Promise((resolve) => {
      try {
        const fs = wx.getFileSystemManager();
        fs.readdir({
          dirPath: wx.env.USER_DATA_PATH,
          success: (res) => {
            // 正常清理策略：保持HTML文件3天有效期，保护功能性文件
            const filesToDelete = res.files.filter(file => {
              // 保护系统文件
              if (file === 'miniprogramLog') return false;

              // 保护最近的错词本文件（1小时内）
              if (file.includes('错词本_') && !this.isOldTempFile(file)) {
                console.log('保护最近的错词本文件:', file);
                return false;
              }

              return (
                // HTML报告文件（超过3天）
                (file.includes('分析报告') || file.includes('学习规划') || file.includes('完整报告') || file.startsWith('report_')) &&
                file.endsWith('.html') && this.isOldFile(file, 3)
              ) || (
                // 其他大文件（PDF、XLS等，超过7天）
                (file.endsWith('.pdf') || file.endsWith('.xls') || file.endsWith('.csv')) && this.isOldFile(file, 7)
              ) || (
                // 音频文件（超过1天）
                file.endsWith('.mp3') && this.isOldFile(file, 1)
              ) || (
                // 图片文件（超过3天，但保护Canvas临时图片）
                file.endsWith('.png') && this.isOldFile(file, 3) && !file.includes('canvas')
              ) || (
                // 临时文件（超过1小时）
                (file.includes('temp_') || file.includes('tmp_') || file.includes('canvas_')) &&
                this.isOldTempFile(file)
              ) || (
                // 旧的错词本文件（超过1天）
                file.includes('错词本_') && this.isOldFile(file, 1)
              );
            });

            console.log(`常规清理：准备删除 ${filesToDelete.length} 个过期文件:`, filesToDelete);

            // 如果没有过期文件可清理，尝试按大小清理
            if (filesToDelete.length === 0) {
              console.log('没有过期文件，尝试按文件大小清理...');
              this.cleanupBySize().then(resolve).catch(resolve);
              return;
            }

            // 如果过期文件很少，也尝试按大小清理更多文件
            if (filesToDelete.length < 3) {
              console.log('过期文件较少，同时按大小清理更多文件...');
              this.deleteFiles(filesToDelete).then(() => {
                this.cleanupBySize().then(resolve).catch(resolve);
              }).catch(resolve);
              return;
            }

            // 删除过期文件
            this.deleteFiles(filesToDelete).then(() => {
              console.log(`常规清理完成，共删除 ${filesToDelete.length} 个文件`);
              resolve();
            }).catch(resolve);
          },
          fail: (err) => {
            console.log('读取目录失败:', err);
            resolve();
          }
        });
      } catch (error) {
        console.log('清理文件异常:', error);
        resolve();
      }
    });
  },

  // 按文件大小清理（空间不足时使用）
  cleanupBySize: function() {
    return new Promise((resolve) => {
      try {
        const fs = wx.getFileSystemManager();
        fs.readdir({
          dirPath: wx.env.USER_DATA_PATH,
          success: (res) => {
            console.log('开始按文件大小清理...');

            // 获取所有文件的大小信息
            this.getFileSizes(res.files).then((fileDetails) => {
              // 按大小排序，最大的在前面
              const sortedFiles = fileDetails.sort((a, b) => b.size - a.size);

              console.log('文件大小排序（前10个）:');
              sortedFiles.slice(0, 10).forEach((file, index) => {
                console.log(`${index + 1}. ${file.name}: ${file.sizeKB}KB`);
              });

              // 优先清理大文件，但保护重要功能文件
              const filesToDelete = sortedFiles.filter(file => {
                // 保护系统文件
                if (file.name === 'miniprogramLog') return false;

                // 保护3天内的HTML报告文件
                if ((file.name.includes('分析报告') || file.name.includes('学习规划') ||
                     file.name.includes('完整报告') || file.name.startsWith('report_')) &&
                    file.name.endsWith('.html') && !this.isOldFile(file.name, 3)) {
                  console.log('保护3天内的HTML文件:', file.name);
                  return false;
                }

                // 保护最近的错词本文件（1小时内）
                if (file.name.includes('错词本_') && !this.isOldTempFile(file.name)) {
                  console.log('保护最近的错词本文件:', file.name);
                  return false;
                }

                // 保护Canvas临时图片（可能正在使用）
                if (file.name.includes('canvas') && file.name.endsWith('.png') && !this.isOldTempFile(file.name)) {
                  console.log('保护Canvas临时图片:', file.name);
                  return false;
                }

                return true;
              }).slice(0, 10); // 清理最大的10个文件，确保释放足够空间

              console.log(`按大小清理：准备删除最大的 ${filesToDelete.length} 个文件:`,
                         filesToDelete.map(f => `${f.name}(${f.sizeKB}KB)`));

              if (filesToDelete.length === 0) {
                console.log('没有可清理的大文件，尝试激进清理...');
                this.aggressiveCleanup().then(resolve).catch(resolve);
                return;
              }

              // 删除选中的大文件
              const fileNames = filesToDelete.map(f => f.name);
              this.deleteFiles(fileNames).then(() => {
                const totalSizeKB = filesToDelete.reduce((sum, f) => sum + f.sizeKB, 0);
                console.log(`按大小清理完成，共删除 ${filesToDelete.length} 个文件，释放 ${totalSizeKB.toFixed(1)}KB 空间`);
                resolve();
              }).catch(resolve);
            }).catch(resolve);
          },
          fail: (err) => {
            console.log('按大小清理读取目录失败:', err);
            resolve();
          }
        });
      } catch (error) {
        console.log('按大小清理异常:', error);
        resolve();
      }
    });
  },

  // 获取文件大小信息
  getFileSizes: function(fileNames) {
    return new Promise((resolve) => {
      const fs = wx.getFileSystemManager();
      const fileDetails = [];
      let processedCount = 0;

      if (fileNames.length === 0) {
        resolve([]);
        return;
      }

      fileNames.forEach(fileName => {
        const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;
        fs.stat({
          path: filePath,
          success: (statRes) => {
            const fileSize = statRes.size || 0;
            fileDetails.push({
              name: fileName,
              size: fileSize,
              sizeKB: Math.round(fileSize / 1024 * 100) / 100
            });
          },
          fail: (err) => {
            console.log(`获取文件大小失败: ${fileName}`, err);
            // 即使失败也要记录，避免卡住
            fileDetails.push({
              name: fileName,
              size: 0,
              sizeKB: 0,
              error: true
            });
          },
          complete: () => {
            processedCount++;
            if (processedCount === fileNames.length) {
              resolve(fileDetails);
            }
          }
        });
      });
    });
  },

  // 删除文件列表
  deleteFiles: function(fileNames) {
    return new Promise((resolve) => {
      if (fileNames.length === 0) {
        resolve();
        return;
      }

      const fs = wx.getFileSystemManager();
      let deleteCount = 0;

      fileNames.forEach(fileName => {
        fs.unlink({
          filePath: `${wx.env.USER_DATA_PATH}/${fileName}`,
          success: () => {
            console.log('✅ 成功删除文件:', fileName);
          },
          fail: (err) => {
            console.log('❌ 删除文件失败:', fileName, err);
          },
          complete: () => {
            deleteCount++;
            if (deleteCount === fileNames.length) {
              resolve();
            }
          }
        });
      });
    });
  },

  // 激进清理 - 清理所有非系统文件（保留1天内的HTML）
  aggressiveCleanup: function() {
    return new Promise((resolve) => {
      try {
        const fs = wx.getFileSystemManager();
        fs.readdir({
          dirPath: wx.env.USER_DATA_PATH,
          success: (res) => {
            console.log('🚨 激进清理：清理所有非必要文件...');

            const filesToDelete = res.files.filter(file => {
              // 保留系统文件
              if (file === 'miniprogramLog') return false;

              // 保留1天内的HTML报告文件
              if ((file.includes('分析报告') || file.includes('学习规划') ||
                   file.includes('完整报告') || file.startsWith('report_')) &&
                  file.endsWith('.html') && !this.isOldFile(file, 1)) {
                console.log('保护1天内的HTML文件:', file);
                return false;
              }

              // 清理其他所有文件
              return true;
            });

            console.log(`🚨 激进清理：准备删除 ${filesToDelete.length} 个文件:`, filesToDelete);

            if (filesToDelete.length === 0) {
              console.log('激进清理：没有文件可清理');
              resolve();
              return;
            }

            this.deleteFiles(filesToDelete).then(() => {
              console.log(`🚨 激进清理完成，共删除 ${filesToDelete.length} 个文件`);
              resolve();
            }).catch(resolve);
          },
          fail: (err) => {
            console.log('激进清理读取目录失败:', err);
            resolve();
          }
        });
      } catch (error) {
        console.log('激进清理异常:', error);
        resolve();
      }
    });
  },

  // 判断是否为超过1小时的临时文件
  isOldTempFile: function(fileName) {
    try {
      // 尝试从文件名中提取时间戳
      const timestampMatch = fileName.match(/_(\d{13})_/);
      if (timestampMatch) {
        const fileTimestamp = parseInt(timestampMatch[1]);
        const now = Date.now();
        const oneHour = 60 * 60 * 1000;
        return (now - fileTimestamp) > oneHour;
      }
      return false;
    } catch (error) {
      return false;
    }
  },

  // 判断文件是否过期（根据文件名中的日期）
  isOldFile: function(fileName, days) {
    try {
      // 尝试匹配日期格式 YYYYMMDDTHHMMSS
      const dateMatch = fileName.match(/(\d{8}T\d{6})/);
      if (dateMatch) {
        const dateStr = dateMatch[1];
        const year = parseInt(dateStr.substring(0, 4));
        const month = parseInt(dateStr.substring(4, 6)) - 1; // 月份从0开始
        const day = parseInt(dateStr.substring(6, 8));
        const hour = parseInt(dateStr.substring(9, 11));
        const minute = parseInt(dateStr.substring(11, 13));
        const second = parseInt(dateStr.substring(13, 15));

        const fileDate = new Date(year, month, day, hour, minute, second);
        const now = new Date();
        const diffTime = now.getTime() - fileDate.getTime();
        const diffDays = diffTime / (1000 * 60 * 60 * 24);

        return diffDays > days;
      }

      // 如果没有找到日期格式，尝试时间戳格式
      const timestampMatch = fileName.match(/_(\d{13})/);
      if (timestampMatch) {
        const fileTimestamp = parseInt(timestampMatch[1]);
        const now = Date.now();
        const diffTime = now - fileTimestamp;
        const diffDays = diffTime / (1000 * 60 * 60 * 24);
        return diffDays > days;
      }

      return false;
    } catch (error) {
      console.log('判断文件日期失败:', fileName, error);
      return false;
    }
  },

  // 检查存储空间信息
  checkStorageInfo: function() {
    return new Promise((resolve) => {
      try {
        const fs = wx.getFileSystemManager();

        // 获取存储目录信息
        fs.readdir({
          dirPath: wx.env.USER_DATA_PATH,
          success: (res) => {
            console.log('存储目录文件列表:', res.files);

            // 计算文件总大小
            let totalSize = 0;
            let fileCount = res.files.length;
            let processedCount = 0;

            if (fileCount === 0) {
              resolve({
                fileCount: 0,
                totalSizeKB: 0,
                files: []
              });
              return;
            }

            const fileDetails = [];

            res.files.forEach(fileName => {
              const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;
              fs.stat({
                path: filePath,
                success: (statRes) => {
                  const fileSize = statRes.size || 0;
                  totalSize += fileSize;
                  fileDetails.push({
                    name: fileName,
                    size: fileSize,
                    sizeKB: Math.round(fileSize / 1024 * 100) / 100
                  });

                  processedCount++;
                  if (processedCount === fileCount) {
                    resolve({
                      fileCount: fileCount,
                      totalSizeKB: Math.round(totalSize / 1024 * 100) / 100,
                      files: fileDetails.sort((a, b) => b.size - a.size) // 按大小排序
                    });
                  }
                },
                fail: (err) => {
                  console.log(`获取文件 ${fileName} 信息失败:`, err);
                  // 即使失败也要计数，避免卡住
                  fileDetails.push({
                    name: fileName,
                    size: 0,
                    sizeKB: 0,
                    error: true
                  });

                  processedCount++;
                  if (processedCount === fileCount) {
                    resolve({
                      fileCount: fileCount,
                      totalSizeKB: Math.round(totalSize / 1024 * 100) / 100,
                      files: fileDetails.sort((a, b) => b.size - a.size)
                    });
                  }
                }
              });
            });
          },
          fail: (err) => {
            console.log('读取存储目录失败:', err);
            resolve({
              error: err,
              fileCount: 0,
              totalSizeKB: 0,
              files: []
            });
          }
        });
      } catch (error) {
        console.log('检查存储空间异常:', error);
        resolve({
          error: error,
          fileCount: 0,
          totalSizeKB: 0,
          files: []
        });
      }
    });
  },

  // 检测存储限制
  detectStorageLimit: function() {
    console.log('🔍 开始检测微信小程序存储限制...');

    // 微信小程序本地存储的一些已知限制
    console.log('📋 微信小程序存储限制说明:');
    console.log('- 本地存储路径: wx.env.USER_DATA_PATH');
    console.log('- 理论限制: 通常为10MB左右（不同设备可能不同）');
    console.log('- 实际可用: 可能因系统和其他因素而更小');
    console.log('- 注意: 这与云开发存储是完全不同的存储空间');

    // 尝试获取更多系统信息
    wx.getSystemInfo({
      success: (sysInfo) => {
        console.log('📱 设备信息:');
        console.log('- 平台:', sysInfo.platform);
        console.log('- 系统:', sysInfo.system);
        console.log('- 微信版本:', sysInfo.version);
        console.log('- 基础库版本:', sysInfo.SDKVersion);
        console.log('- 设备内存:', sysInfo.memorySize, 'MB');

        // 根据平台给出建议
        if (sysInfo.platform === 'devtools') {
          console.log('💡 当前在开发者工具中，存储限制可能与真机不同');
        } else {
          console.log('💡 真机环境，存储限制更严格');
        }
      }
    });

    // 建议清理策略
    console.log('🧹 建议的清理策略:');
    console.log('1. 清理所有临时文件');
    console.log('2. 清理旧的PDF/XLS文件');
    console.log('3. 清理音频和图片文件');
    console.log('4. 如果仍不够，建议用户清理小程序缓存');
  },

  // 测试存储容量
  testStorageCapacity: function() {
    console.log('🧪 开始测试存储容量...');

    const fs = wx.getFileSystemManager();
    const testData = 'A'.repeat(1024); // 1KB的测试数据
    let testSize = 1; // 从1KB开始
    const maxTestSize = 50; // 最多测试50KB

    const testWrite = (size) => {
      const testFileName = `capacity_test_${size}kb.txt`;
      const testFilePath = `${wx.env.USER_DATA_PATH}/${testFileName}`;
      const data = 'A'.repeat(size * 1024);

      fs.writeFile({
        filePath: testFilePath,
        data: data,
        encoding: 'utf8',
        success: () => {
          console.log(`✅ 成功写入 ${size}KB 测试文件`);

          // 立即删除测试文件
          fs.unlink({
            filePath: testFilePath,
            success: () => {
              if (size < maxTestSize) {
                // 继续测试更大的文件
                setTimeout(() => testWrite(size + 5), 100);
              } else {
                console.log(`🎯 存储容量测试完成，至少可以写入 ${size}KB`);
              }
            },
            fail: (e) => console.log(`删除测试文件失败 (${size}KB):`, e)
          });
        },
        fail: (err) => {
          console.log(`❌ 写入 ${size}KB 测试文件失败:`, err);
          if (size > 1) {
            console.log(`💡 推测可用存储空间约为 ${size - 5}KB 到 ${size}KB 之间`);
          } else {
            console.log('💡 存储空间严重不足，连1KB都无法写入');
          }
        }
      });
    };

    // 开始测试
    testWrite(testSize);
  },



  // 分享报告
  shareReport() {
    // 首先生成一张完整的报告图片
    wx.showLoading({
      title: '正在准备分享...',
      mask: true
    });

    this.generateCanvasImage('complete').then((tempFilePath) => {
      wx.hideLoading();

      // 使用生成的图片进行分享
      wx.showActionSheet({
        itemList: ['分享给朋友', '保存到相册'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 分享给朋友
            wx.showToast({
              title: '可通过右上角分享按钮分享',
              icon: 'none',
              duration: 2000
            });
          } else if (res.tapIndex === 1) {
            // 保存到相册
            this.saveImageToAlbum(tempFilePath);
          }
        }
      });
    }).catch((err) => {
      wx.hideLoading();
      console.error('生成分享图片失败:', err);
      wx.showToast({
        title: '生成分享图片失败',
        icon: 'none'
      });
    });
  },

  // 选择样式
  selectStyle(e) {
    const styleId = e.currentTarget.dataset.index;
    this.setData({
      selectedStyle: styleId
    });
  },

  // 生成HTML内容
  generateHtmlContent: function(type) {
    const { reportData, basicInfo, styles, selectedStyle, coursePlanData, coursePlanHeaders, reportConfig } = this.data;
    const currentStyle = styles[selectedStyle];

    // 根据type生成不同的标题
    let pageTitle, mainTitle, metaDescription;
    if (type === 'courseplan') {
      pageTitle = `${reportData.basicInfo.studentName}同学课程规划`;
      mainTitle = `${reportData.basicInfo.studentName}同学课程规划`;
      metaDescription = `${reportData.basicInfo.studentName}同学英语学习课程规划`;
    } else {
      pageTitle = `${reportData.basicInfo.studentName}同学${reportData.basicInfo.grade}英语${reportData.basicInfo.examType}试卷分析报告`;
      mainTitle = `${reportData.basicInfo.studentName}同学${reportData.basicInfo.grade}英语${reportData.basicInfo.examType}试卷分析报告`;
      metaDescription = `${reportData.basicInfo.studentName}同学${reportData.basicInfo.grade}英语${reportData.basicInfo.examType}试卷分析报告`;
    }

    // 计算统计数据
    const totalSelectedScore = reportData.sections.reduce((sum, section) => sum + section.totalScore, 0);
    const totalLostScore = reportData.sections.reduce((sum, section) => sum + (section.lostScore || 0), 0);
    const totalGainedScore = totalSelectedScore - totalLostScore;
    const correctRate = totalSelectedScore > 0 ? Math.round((totalGainedScore / totalSelectedScore) * 100) : 0;

    // 生成图表数据
    const chartData = reportData.sections.map(section => {
      const rate = section.totalScore > 0 ? Math.round(((section.totalScore - (section.lostScore || 0)) / section.totalScore) * 100) : 0;
      return {
        name: section.name,
        rate: rate,
        totalScore: section.totalScore,
        lostScore: section.lostScore || 0,
        gainedScore: section.totalScore - (section.lostScore || 0)
      };
    });

    let htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <meta name="description" content="${metaDescription}">
    <meta name="keywords" content="试卷分析,英语学习,成绩报告,学习建议,课程规划">
    <meta name="author" content="${basicInfo.analystName || '墨词自习室'}">
    <title>${pageTitle}</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, ${currentStyle.primary} 0%, ${currentStyle.secondary} 50%, ${currentStyle.accent} 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .header .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .summary {
            padding: 30px;
            background: #fafafa;
        }

        .summary h2 {
            font-size: 20px;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
        }

        .summary h2::before {
            content: '📈';
            margin-right: 8px;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .summary-card .number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .summary-card .label {
            font-size: 14px;
            color: #666;
        }

        .total-card .number { color: #2196F3; }
        .lost-card .number { color: #f44336; }
        .gained-card .number { color: #4CAF50; }
        .rate-card .number { color: ${currentStyle.primary}; }

        .chart-section {
            padding: 30px;
        }

        .chart-section h2 {
            font-size: 20px;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
        }

        .chart-section h2::before {
            content: '📊';
            margin-right: 8px;
        }

        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chart-bar {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 6px;
        }

        .chart-bar:last-child {
            margin-bottom: 0;
        }

        .bar-label {
            width: 100px;
            font-size: 14px;
            font-weight: 500;
            flex-shrink: 0;
        }

        .bar-fill-container {
            flex: 1;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            margin: 0 15px;
            position: relative;
            overflow: hidden;
        }

        .bar-fill {
            height: 100%;
            background: linear-gradient(90deg, ${currentStyle.primary}, ${currentStyle.secondary});
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .bar-value {
            font-size: 14px;
            font-weight: bold;
            color: ${currentStyle.primary};
            min-width: 40px;
            text-align: right;
        }

        .analysis-table {
            padding: 30px;
        }

        .analysis-table h2 {
            font-size: 20px;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
        }

        .analysis-table h2::before {
            content: '📋';
            margin-right: 8px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .table th {
            background: ${currentStyle.primary};
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            vertical-align: middle;
        }

        .table td {
            padding: 15px 10px;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
            text-align: center;
        }

        .table tr:last-child td {
            border-bottom: none;
        }

        .table tr:nth-child(even) {
            background: #f9f9f9;
        }

        .subject-name {
            font-weight: bold;
            margin-bottom: 5px;
            text-align: center;
        }

        .subject-score {
            font-size: 12px;
            color: #666;
            text-align: center;
        }

        .score-number {
            font-weight: bold;
            font-size: 16px;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
            min-width: 32px;
            text-align: center;
        }

        .score-number.total {
            color: white;
            background: linear-gradient(135deg, #2196F3, #1976D2);
            box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
        }

        .score-number.lost {
            color: white;
            background: linear-gradient(135deg, #f44336, #d32f2f);
            box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
        }

        .content-list {
            font-size: 13px;
            line-height: 1.8;
            text-align: left;
        }

        .content-list .item {
            display: block;
            margin-bottom: 8px;
            padding-left: 15px;
            position: relative;
            line-height: 1.6;
        }

        .content-list .item:before {
            content: "•";
            position: absolute;
            left: 0;
            top: 0;
        }

        .content-list .item:last-child {
            margin-bottom: 0;
        }

        .footer {
            padding: 20px 30px;
            background: #f5f5f5;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
        }

        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${currentStyle.primary};
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 1000;
        }

        .print-btn:hover {
            background: ${currentStyle.secondary};
        }

        @media print {
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                padding: 20px !important;
                margin: 0 !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                font-size: 14px !important;
            }

            .container {
                box-shadow: none !important;
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .print-btn {
                display: none !important;
            }

            .header {
                background: linear-gradient(135deg, ${currentStyle.primary} 0%, ${currentStyle.secondary} 50%, ${currentStyle.accent} 100%) !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                color: white !important;
                padding: 30px !important;
            }

            .header h1 {
                color: white !important;
                font-size: 28px !important;
                margin: 0 0 10px 0 !important;
            }

            .header .subtitle {
                color: rgba(255,255,255,0.9) !important;
                font-size: 16px !important;
            }

            /* 基本信息区域打印样式 */
            .basic-info {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)) !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                padding: 20px !important;
                border-radius: 12px !important;
                margin-bottom: 30px !important;
            }

            /* 题型区域头部打印样式 */
            .section-header {
                background: linear-gradient(135deg, #667eea, #764ba2) !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                color: white !important;
                padding: 15px 20px !important;
                font-weight: bold !important;
                font-size: 18px !important;
            }

            /* 成绩统计打印样式 */
            .summary h2 {
                color: ${currentStyle.primary} !important;
                font-size: 20px !important;
                margin: 20px 0 15px 0 !important;
                border-bottom: 2px solid ${currentStyle.primary} !important;
                padding-bottom: 5px !important;
            }

            .summary-cards {
                display: flex !important;
                justify-content: space-around !important;
                margin: 20px 0 !important;
            }

            .summary-card {
                background: white !important;
                border: 2px solid #e9ecef !important;
                padding: 15px !important;
                text-align: center !important;
                border-radius: 8px !important;
                min-width: 120px !important;
            }

            .summary-card .number {
                font-size: 24px !important;
                font-weight: bold !important;
                margin-bottom: 5px !important;
            }

            .summary-card.total-card .number {
                color: ${currentStyle.primary} !important;
            }

            .summary-card.lost-card .number {
                color: #e53e3e !important;
            }

            .summary-card.gained-card .number {
                color: #38a169 !important;
            }

            .summary-card.rate-card .number {
                color: ${currentStyle.secondary} !important;
            }

            /* 各题型得分率打印样式 */
            .chart-section h2 {
                color: ${currentStyle.primary} !important;
                font-size: 20px !important;
                margin: 20px 0 15px 0 !important;
                border-bottom: 2px solid ${currentStyle.primary} !important;
                padding-bottom: 5px !important;
            }

            .chart-container {
                background: white !important;
                padding: 15px !important;
                border: 1px solid #e9ecef !important;
                border-radius: 8px !important;
            }

            .chart-bar {
                display: flex !important;
                align-items: center !important;
                margin-bottom: 12px !important;
                padding: 8px !important;
                background: #f8f9fa !important;
                border-radius: 4px !important;
            }

            .bar-label {
                min-width: 80px !important;
                font-weight: bold !important;
                color: #333 !important;
                font-size: 14px !important;
            }

            .bar-fill-container {
                flex: 1 !important;
                height: 20px !important;
                background: #e9ecef !important;
                border-radius: 10px !important;
                margin: 0 10px !important;
                position: relative !important;
                overflow: hidden !important;
            }

            .bar-fill {
                height: 100% !important;
                background: linear-gradient(135deg, ${currentStyle.primary}, ${currentStyle.secondary}) !important;
                border-radius: 10px !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .bar-value {
                min-width: 45px !important;
                text-align: right !important;
                font-weight: bold !important;
                color: ${currentStyle.primary} !important;
                font-size: 14px !important;
            }

            /* 表格打印样式 */
            .analysis-table h2 {
                color: ${currentStyle.primary} !important;
                font-size: 20px !important;
                margin: 20px 0 15px 0 !important;
                border-bottom: 2px solid ${currentStyle.primary} !important;
                padding-bottom: 5px !important;
            }

            .table {
                width: 100% !important;
                border-collapse: collapse !important;
                background: white !important;
                border: 2px solid #e9ecef !important;
                border-radius: 8px !important;
                overflow: visible !important;
                page-break-inside: auto !important;
            }

            /* 表格标题在换页时保持在一起 */
            .analysis-table h2 {
                page-break-after: avoid !important;
                break-after: avoid !important;
            }

            /* 整个分析表格区域的换页控制 */
            .analysis-table {
                page-break-inside: auto !important;
                break-inside: auto !important;
            }

            .table th {
                background: ${currentStyle.primary} !important;
                color: white !important;
                padding: 12px 8px !important;
                text-align: center !important;
                font-weight: bold !important;
                font-size: 14px !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            /* 表格头部行不被分割，且不在每页重复 */
            .table thead {
                display: table-header-group !important;
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }

            .table thead tr {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }

            /* 明确禁止表头在换页时重复显示 */
            @media print {
                .table thead {
                    display: table-row-group !important;
                }

                /* 确保表头只在第一页显示 */
                .table {
                    page-break-inside: auto !important;
                }

                /* 防止浏览器自动重复表头 */
                thead {
                    display: table-row-group !important;
                }

                th {
                    display: table-cell !important;
                }
            }

            .table td {
                padding: 12px 8px !important;
                border-bottom: 1px solid #e9ecef !important;
                vertical-align: middle !important;
                font-size: 13px !important;
            }

            /* 防止表格行在换页时被分割 */
            .table-row-item {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }

            /* 确保题型名称容器正常显示 */
            .section-name-container {
                display: block !important;
                position: static !important;
                width: 100% !important;
                text-align: center !important;
            }

            .score-number {
                font-weight: bold !important;
                font-size: 14px !important;
                padding: 3px 6px !important;
                border-radius: 3px !important;
                display: inline-block !important;
                min-width: 28px !important;
                text-align: center !important;
            }

            .score-number.total {
                background: linear-gradient(135deg, #2196F3, #1976D2) !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .score-number.lost {
                background: linear-gradient(135deg, #f44336, #d32f2f) !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            /* 原因分析区域打印样式 */
            .reason-analysis {
                background: linear-gradient(135deg, #e3f2fd, #f0f8ff) !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                padding: 12px !important;
                border-radius: 6px !important;
                margin-bottom: 10px !important;
                border-left: 3px solid #2196f3 !important;
            }

            /* 学习建议区域打印样式 */
            .study-suggestion {
                background: linear-gradient(135deg, #e8f5e8, #f0fff0) !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                padding: 12px !important;
                border-radius: 6px !important;
                border-left: 3px solid #4caf50 !important;
            }

            /* 页脚区域打印样式 */
            .footer {
                background: linear-gradient(135deg, #f5f5f5, #e9ecef) !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                text-align: center !important;
                margin-top: 40px !important;
                padding: 20px !important;
                border-radius: 10px !important;
                font-size: 14px !important;
                color: #666 !important;
                border-top: 3px solid ${currentStyle.primary} !important;
            }

            .content-list {
                font-size: 12px !important;
                line-height: 1.6 !important;
            }

            .content-list .item {
                display: block !important;
                margin-bottom: 6px !important;
                page-break-inside: avoid !important;
                break-inside: avoid !important;
                padding-left: 12px !important;
                position: relative !important;
                line-height: 1.5 !important;
            }

            .content-list .item:before {
                content: "•" !important;
                position: absolute !important;
                left: 0 !important;
                top: 0 !important;
                color: ${currentStyle.primary} !important;
            }

            /* 页脚打印样式 */
            .footer {
                padding: 15px 20px !important;
                background: #f5f5f5 !important;
                text-align: center !important;
                font-size: 11px !important;
                color: #666 !important;
                border-top: 1px solid #e9ecef !important;
                margin-top: 20px !important;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .summary-cards {
                grid-template-columns: repeat(2, 1fr);
            }

            .print-btn {
                position: static;
                width: 100%;
                margin-bottom: 20px;
            }

            .table {
                font-size: 12px;
            }

            .table th,
            .table td {
                padding: 8px 5px;
            }
        }

        /* 确保在微信浏览器中正常显示 */
        @media screen and (-webkit-min-device-pixel-ratio: 0) {
            .container {
                -webkit-font-smoothing: antialiased;
            }
        }

        /* 企业微信浏览器优化 */
        .wxwork-browser .container {
            margin: 0;
            border-radius: 0;
        }
    </style>
</head>
<body>
    <button class="print-btn" onclick="window.print()">🖨️ 打印报告</button>

    <div class="container">
        <div class="header">
            <h1>${pageTitle}</h1>
            <div class="subtitle">Professional English Assessment Report</div>
        </div>

        ${type !== 'courseplan' ? `
        <div class="summary">
            <h2>成绩统计</h2>
            <div class="summary-cards">
                <div class="summary-card total-card">
                    <div class="number">${totalSelectedScore}</div>
                    <div class="label">总分</div>
                </div>
                <div class="summary-card lost-card">
                    <div class="number">${totalLostScore}</div>
                    <div class="label">失分</div>
                </div>
                <div class="summary-card gained-card">
                    <div class="number">${totalGainedScore}</div>
                    <div class="label">得分</div>
                </div>
                <div class="summary-card rate-card">
                    <div class="number">${correctRate}%</div>
                    <div class="label">正确率</div>
                </div>
            </div>
        </div>

        <div class="chart-section">
            <h2>各题型得分率</h2>
            <div class="chart-container">
                ${chartData.map(item => `
                    <div class="chart-bar">
                        <div class="bar-label">${item.name}</div>
                        <div class="bar-fill-container">
                            <div class="bar-fill" style="width: ${item.rate}%"></div>
                        </div>
                        <div class="bar-value">${item.rate}%</div>
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}

        ${type === 'analysis' || type === 'complete' ? `
        <div class="analysis-table">
            <h2>详细分析表</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th style="width: 15%">题型</th>
                        <th style="width: 10%">总分</th>
                        <th style="width: 10%">扣分</th>
                        <th style="width: 32.5%">原因分析</th>
                        <th style="width: 32.5%">学习建议</th>
                    </tr>
                </thead>
                <tbody>
                    ${reportData.sections.map(section => `
                        <tr class="table-row-item">
                            <td style="text-align: center; vertical-align: middle; padding: 15px 8px;">
                                <div class="section-name-container">
                                    <div style="font-weight: bold; margin-bottom: 5px; line-height: 1.3;">${section.name}</div>
                                    <div style="font-size: 12px; color: #666; line-height: 1.2;">${section.totalScore - (section.lostScore || 0)}/${section.totalScore}</div>
                                </div>
                            </td>
                            <td style="text-align: center;">
                                <span class="score-number total">${section.totalScore}</span>
                            </td>
                            <td style="text-align: center;">
                                <span class="score-number lost">${section.lostScore || 0}</span>
                            </td>
                            <td>
                                <div class="content-list">
                                    ${(section.selectedReasons || []).map(reason => `<span class="item">${reason}</span>`).join('')}
                                    ${section.customReason ? `<span class="item">${section.customReason}</span>` : ''}
                                    ${(!section.selectedReasons || section.selectedReasons.length === 0) && !section.customReason ? '<span class="item" style="color: #999;">暂无分析</span>' : ''}
                                </div>
                            </td>
                            <td>
                                <div class="content-list">
                                    ${(section.selectedSuggestions || []).map(suggestion => `<span class="item">${suggestion}</span>`).join('')}
                                    ${section.customSuggestion ? `<span class="item">${section.customSuggestion}</span>` : ''}
                                    ${(!section.selectedSuggestions || section.selectedSuggestions.length === 0) && !section.customSuggestion ? '<span class="item" style="color: #999;">暂无建议</span>' : ''}
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        ` : ''}

        ${(type === 'courseplan' || type === 'complete') && reportConfig.showCourseplan ? `
        <div class="analysis-table">
            <h2>课程规划</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th style="width: 20%">${coursePlanHeaders.lesson || '课次'}</th>
                        <th style="width: 80%">${coursePlanHeaders.content || '学习内容'}</th>
                    </tr>
                </thead>
                <tbody>
                    ${coursePlanData.map(item => `
                        <tr>
                            <td style="text-align: center;">第${item.lesson}讲</td>
                            <td>${item.content || '待安排'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        ` : ''}

        <div class="footer">
            <div>生成时间：${reportData.generatedAt}${basicInfo.analystName ? '　　分析人：' + basicInfo.analystName : ''}</div>
            <div style="margin-top: 5px;">Powered by AI Technology</div>
        </div>
    </div>

    <script>
        // 检测浏览器环境
        function detectBrowser() {
            const ua = navigator.userAgent.toLowerCase();
            if (ua.indexOf('wxwork') > -1) {
                document.body.classList.add('wxwork-browser');
            } else if (ua.indexOf('micromessenger') > -1) {
                document.body.classList.add('wechat-browser');
            }
        }

        // 添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 检测浏览器环境
            detectBrowser();

            // 为图表条添加动画效果
            const bars = document.querySelectorAll('.bar-fill');
            bars.forEach((bar, index) => {
                const originalWidth = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = originalWidth;
                }, index * 100 + 300);
            });

            // 添加表格行悬停效果（仅在非触摸设备上）
            if (!('ontouchstart' in window)) {
                const tableRows = document.querySelectorAll('.table tbody tr');
                tableRows.forEach(row => {
                    row.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#f0f8ff';
                    });
                    row.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = '';
                    });
                });
            }

            // 添加打印按钮点击统计
            const printBtn = document.querySelector('.print-btn');
            if (printBtn) {
                printBtn.addEventListener('click', function() {
                    // 可以在这里添加打印统计
                    console.log('用户点击了打印按钮');
                });
            }

            // 优化移动端体验
            if (window.innerWidth <= 768) {
                // 移动端优化
                const tables = document.querySelectorAll('.table');
                tables.forEach(table => {
                    table.style.fontSize = '12px';
                });
            }
        });

        // 防止页面被嵌入iframe（安全考虑）
        if (window.top !== window.self) {
            window.top.location = window.self.location;
        }
    </script>
</body>
</html>`;

    return htmlContent;
  },

  // 汉字转拼音函数
  chineseToPinyin: function(chinese) {
    // 简单的汉字转拼音映射表（可以根据需要扩展）
    const pinyinMap = {
      '李': 'li', '王': 'wang', '张': 'zhang', '刘': 'liu', '陈': 'chen', '杨': 'yang', '赵': 'zhao', '黄': 'huang', '周': 'zhou', '吴': 'wu',
      '徐': 'xu', '孙': 'sun', '胡': 'hu', '朱': 'zhu', '高': 'gao', '林': 'lin', '何': 'he', '郭': 'guo', '马': 'ma', '罗': 'luo',
      '梁': 'liang', '宋': 'song', '郑': 'zheng', '谢': 'xie', '韩': 'han', '唐': 'tang', '冯': 'feng', '于': 'yu', '董': 'dong', '萧': 'xiao',
      '程': 'cheng', '曹': 'cao', '袁': 'yuan', '邓': 'deng', '许': 'xu', '傅': 'fu', '沈': 'shen', '曾': 'zeng', '彭': 'peng', '吕': 'lv',
      '苏': 'su', '卢': 'lu', '蒋': 'jiang', '蔡': 'cai', '贾': 'jia', '丁': 'ding', '魏': 'wei', '薛': 'xue', '叶': 'ye', '阎': 'yan',
      '余': 'yu', '潘': 'pan', '杜': 'du', '戴': 'dai', '夏': 'xia', '钟': 'zhong', '汪': 'wang', '田': 'tian', '任': 'ren', '姜': 'jiang',
      '范': 'fan', '方': 'fang', '石': 'shi', '姚': 'yao', '谭': 'tan', '廖': 'liao', '邹': 'zou', '熊': 'xiong', '金': 'jin', '陆': 'lu',
      '郝': 'hao', '孔': 'kong', '白': 'bai', '崔': 'cui', '康': 'kang', '毛': 'mao', '邱': 'qiu', '秦': 'qin', '江': 'jiang', '史': 'shi',
      '顾': 'gu', '侯': 'hou', '邵': 'shao', '孟': 'meng', '龙': 'long', '万': 'wan', '段': 'duan', '漕': 'cao', '钱': 'qian', '汤': 'tang',
      '尹': 'yin', '黎': 'li', '易': 'yi', '常': 'chang', '武': 'wu', '乔': 'qiao', '贺': 'he', '赖': 'lai', '龚': 'gong', '文': 'wen',
      // 常见名字
      '静': 'jing', '敏': 'min', '芳': 'fang', '丽': 'li', '娟': 'juan', '秀': 'xiu', '红': 'hong', '霞': 'xia', '玲': 'ling', '梅': 'mei',
      '华': 'hua', '燕': 'yan', '兰': 'lan', '英': 'ying', '莉': 'li', '萍': 'ping', '艳': 'yan', '琴': 'qin', '凤': 'feng', '洁': 'jie',
      '明': 'ming', '强': 'qiang', '军': 'jun', '伟': 'wei', '勇': 'yong', '磊': 'lei', '超': 'chao', '鹏': 'peng', '涛': 'tao', '松': 'song',
      '浩': 'hao', '亮': 'liang', '杰': 'jie', '峰': 'feng', '辉': 'hui', '刚': 'gang', '健': 'jian', '斌': 'bin', '飞': 'fei', '宇': 'yu',
      // 考试类型
      '期': 'qi', '末': 'mo', '中': 'zhong', '考': 'kao', '试': 'shi', '月': 'yue', '单': 'dan', '元': 'yuan', '测': 'ce', '验': 'yan',
      '模': 'mo', '拟': 'ni', '练': 'lian', '习': 'xi', '复': 'fu', '诊': 'zhen', '断': 'duan', '检': 'jian', '查': 'cha', '评': 'ping',
      '估': 'gu', '分': 'fen', '析': 'xi', '总': 'zong', '结': 'jie', '期': 'qi', '初': 'chu', '高': 'gao', '一': 'yi', '二': 'er',
      '三': 'san', '四': 'si', '五': 'wu', '六': 'liu', '七': 'qi', '八': 'ba', '九': 'jiu', '十': 'shi', '年': 'nian', '级': 'ji'
    };

    let result = '';
    for (let i = 0; i < chinese.length; i++) {
      const char = chinese[i];
      if (pinyinMap[char]) {
        result += pinyinMap[char];
      } else if (/[\u4e00-\u9fa5]/.test(char)) {
        // 如果是汉字但不在映射表中，使用字符编码
        result += 'char' + char.charCodeAt(0);
      } else {
        // 非汉字字符直接保留
        result += char;
      }
    }
    return result;
  },

  // 上传HTML到云存储（直接上传，不使用临时文件）
  uploadHtmlToCloud: function(htmlContent, type) {
    return new Promise((resolve, reject) => {
      const { reportData, basicInfo } = this.data;

      // 生成唯一的文件名，将汉字转换为拼音
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substring(2, 8);
      const studentNamePinyin = this.chineseToPinyin(basicInfo.studentName);
      const examTypePinyin = this.chineseToPinyin(reportData.basicInfo.examType);
      const fileName = `report_${studentNamePinyin}_${examTypePinyin}_${type}_${timestamp}_${randomStr}.html`;

      console.log('🚀 直接上传HTML到云存储（跳过临时文件）:', {
        fileName: fileName,
        contentLength: htmlContent.length,
        contentSizeKB: Math.round(htmlContent.length / 1024 * 100) / 100
      });

      // 创建临时文件用于上传（但立即删除，避免长期占用存储）
      const fs = wx.getFileSystemManager();
      const tempFilePath = `${wx.env.USER_DATA_PATH}/temp_${Date.now()}.html`;

      // 写入临时文件
      fs.writeFile({
        filePath: tempFilePath,
        data: htmlContent,
        encoding: 'utf8',
        success: () => {
          // 立即上传到云存储
          wx.cloud.uploadFile({
            cloudPath: `reports/${fileName}`,
            filePath: tempFilePath,
            success: (uploadRes) => {
              console.log('✅ HTML文件上传成功:', uploadRes);

              // 立即删除临时文件
              fs.unlink({
                filePath: tempFilePath,
                success: () => console.log('临时文件已删除'),
                fail: (e) => console.log('删除临时文件失败:', e)
              });

              // 获取7天有效的下载链接
              wx.cloud.getTempFileURL({
                fileList: [{
                  fileID: uploadRes.fileID,
                  maxAge: 7 * 24 * 60 * 60 // 7天有效期（秒）
                }],
                success: (urlRes) => {
                  console.log('getTempFileURL 响应:', urlRes);

                  // 安全检查 urlRes 和 fileList
                  if (urlRes && urlRes.fileList && Array.isArray(urlRes.fileList) && urlRes.fileList.length > 0) {
                    const fileInfo = urlRes.fileList[0];

                    // 检查文件信息是否完整
                    if (fileInfo && fileInfo.tempFileURL) {
                      const tempFileURL = fileInfo.tempFileURL;

                      // 设置7天后过期
                      const expirationTime = new Date();
                      expirationTime.setDate(expirationTime.getDate() + 7);

                      // 保存分享记录到数据库
                      this.saveShareRecord(uploadRes.fileID, tempFileURL, type, expirationTime);

                      console.log('🎉 HTML链接生成成功！');

                      // 返回7天有效的链接
                      resolve(tempFileURL);
                    } else {
                      console.error('文件信息不完整:', fileInfo);
                      reject(new Error('文件信息不完整'));
                    }
                  } else {
                    console.error('获取下载链接失败，响应格式异常:', urlRes);
                    reject(new Error('获取下载链接失败'));
                  }
                },
                fail: (err) => {
                  console.error('获取下载链接失败:', err);
                  reject(err);
                }
              });
            },
            fail: (err) => {
              console.error('❌ HTML文件上传失败:', err);

              // 删除临时文件
              fs.unlink({
                filePath: tempFilePath,
                success: () => console.log('临时文件已删除'),
                fail: (e) => console.log('删除临时文件失败:', e)
              });

              console.error('上传失败详情:', {
                errMsg: err.errMsg,
                errCode: err.errCode,
                fileName: fileName,
                contentLength: htmlContent.length,
                contentSizeKB: Math.round(htmlContent.length / 1024 * 100) / 100
              });

              reject(err);
            }
          });
        },
        fail: (writeErr) => {
          console.error('❌ 写入临时文件失败:', writeErr);
          reject(writeErr);
        }
      });
    });
  },



  // 保存分享记录（可选）
  saveShareRecord: function(fileId, shareUrl, type, expirationTime) {
    const { reportData, basicInfo } = this.data;

    wx.cloud.database().collection('report_shares').add({
      data: {
        fileId: fileId,
        shareUrl: shareUrl,
        type: type,
        studentName: basicInfo.studentName,
        examType: reportData.basicInfo.examType,
        grade: reportData.basicInfo.grade,
        analystName: basicInfo.analystName,
        createdAt: new Date(),
        expirationTime: expirationTime,
        openId: wx.getStorageSync('openId') || ''
      },
      success: (res) => {
        console.log('分享记录保存成功:', res);
      },
      fail: (err) => {
        // 如果是集合不存在的错误，静默处理
        if (err.errCode === -502005) {
          console.log('分享记录集合不存在，跳过保存');
        } else {
          console.error('分享记录保存失败:', err);
        }
      }
    });
  },

  // 分享HTML链接
  shareHtmlLink(shareUrl, type) {
    const { reportData, basicInfo } = this.data;

    // 设置分享数据
    const shareTitle = type === 'courseplan' ?
      `${reportData.basicInfo.studentName}同学课程规划` :
      `${reportData.basicInfo.studentName}同学${reportData.basicInfo.grade}英语${reportData.basicInfo.examType}试卷分析报告`;
    const shareDesc = type === 'analysis' ? '试卷分析报告' :
                     type === 'courseplan' ? '学习规划' : '完整报告';

    this.setData({
      currentShareData: {
        title: shareTitle,
        desc: shareDesc,
        link: shareUrl,
        imgUrl: '/assets/icons/logo.png'
      }
    });

    // 显示分享选项
    wx.showActionSheet({
      itemList: ['复制HTML链接', '分享给微信好友', '发送到企业微信'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 复制HTML链接（直接访问）
            wx.setClipboardData({
              data: shareUrl,
              success: () => {
                wx.showModal({
                  title: '链接已复制',
                  content: 'HTML链接已复制到剪贴板。\n\n此链接可直接在浏览器中打开，支持打印功能，适合发送给家长查看。',
                  showCancel: false,
                  confirmText: '知道了'
                });
              }
            });
            break;
          case 1:
            // 分享给微信好友（小程序内分享）
            wx.shareAppMessage({
              title: shareTitle,
              desc: shareDesc,
              path: `/pages/webview/webview?url=${encodeURIComponent(shareUrl)}`,
              imageUrl: '/assets/icons/logo.png',
              success: () => {
                wx.showToast({ title: '分享成功', icon: 'success' });
              },
              fail: () => {
                wx.showToast({ title: '分享取消', icon: 'none' });
              }
            });
            break;
          case 2:
            // 发送到企业微信
            wx.setClipboardData({
              data: shareUrl,
              success: () => {
                wx.showModal({
                  title: '链接已复制',
                  content: 'HTML链接已复制到剪贴板。\n\n请打开企业微信，粘贴此链接发送给家长。家长点击链接后可直接在浏览器中查看报告。',
                  showCancel: false,
                  confirmText: '去企业微信',
                  success: (res) => {
                    if (res.confirm) {
                      // 尝试打开企业微信（如果安装了的话）
                      wx.navigateToMiniProgram({
                        appId: 'wxwork', // 企业微信的appId
                        fail: () => {
                          wx.showToast({
                            title: '请手动打开企业微信',
                            icon: 'none'
                          });
                        }
                      });
                    }
                  }
                });
              }
            });
            break;
        }
      }
    });
  },

  // 定期清理过期文件
  scheduleCleanup: function() {
    const lastCleanup = wx.getStorageSync('lastReportCleanup') || 0;
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000; // 24小时

    // 如果距离上次清理超过24小时，则执行清理
    if (now - lastCleanup > oneDay) {
      console.log('开始清理过期的HTML报告文件...');

      wx.cloud.callFunction({
        name: 'cleanExpiredReports',
        success: (res) => {
          console.log('清理结果:', res.result);
          wx.setStorageSync('lastReportCleanup', now);
        },
        fail: (err) => {
          // 如果是集合不存在的错误，静默处理
          if (err.errCode === -502005) {
            console.log('清理报告集合不存在，跳过清理');
            wx.setStorageSync('lastReportCleanup', now);
          } else {
            console.error('清理失败:', err);
          }
        }
      });
    }
  },





  // 统一的保存图片到相册方法
  saveImageToAlbum(tempFilePath) {
    // 直接执行保存，让微信处理权限
    this.doSaveImageToAlbum(tempFilePath);
  },

  // 处理保存失败的情况
  handleSaveFailure(err, tempFilePath) {
    console.error('保存图片失败详情:', err);

    if (err.errMsg.includes('privacy api banned') || err.errMsg.includes('banned')) {
      // API被禁用，提供替代方案
      this.showPrivacyApiBannedDialog();
    } else if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
      // 权限问题，尝试权限流程
      this.handlePermissionFlow(tempFilePath);
    } else {
      // 其他错误，显示通用错误处理
      this.showFallbackOptions();
    }
  },

  // 显示隐私API被禁用的对话框
  showPrivacyApiBannedDialog() {
    wx.showModal({
      title: '保存功能暂不可用',
      content: '由于微信政策限制，当前版本暂时无法直接保存图片到相册。\n\n建议您：\n1. 截屏保存当前页面\n2. 等待后续版本更新\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 提示用户截屏
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },

  // 处理权限流程
  handlePermissionFlow(tempFilePath) {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启相册权限',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      // 用户开启了权限，重新尝试保存
                      this.retrySaveImage(tempFilePath);
                    }
                  }
                });
              }
            }
          });
        } else {
          // 尝试请求权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.retrySaveImage(tempFilePath);
            },
            fail: () => {
              this.showFallbackOptions();
            }
          });
        }
      },
      fail: () => {
        this.showFallbackOptions();
      }
    });
  },

  // 重新尝试保存图片
  retrySaveImage(tempFilePath) {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: () => {
        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('重试保存失败:', err);
        this.showFallbackOptions();
      }
    });
  },

  // 显示备选方案
  showFallbackOptions() {
    wx.showModal({
      title: '保存失败',
      content: '图片保存失败，您可以：\n\n1. 截屏保存当前页面\n2. 稍后重试\n3. 联系客服反馈问题',
      confirmText: '截屏保存',
      cancelText: '稍后重试',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '截屏保存',
            content: '请使用手机的截屏功能保存当前页面：\n\n• iPhone：同时按住电源键和音量上键\n• Android：同时按住电源键和音量下键',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    });
  },

  // 执行保存图片到相册
  doSaveImageToAlbum(tempFilePath) {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success() {
        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('直接保存失败:', err);
        // 如果直接保存失败，尝试权限处理流程
        this.handleSaveFailure(err, tempFilePath);
      }
    });
  },



  // 生成Canvas图片
  generateCanvasImage(type = 'complete') {
    return new Promise((resolve, reject) => {
      try {
        console.log('Canvas绘制开始，type参数:', type);

        // 数据验证
        if (!this.data.reportData || !this.data.reportData.sections) {
          reject(new Error('报告数据不完整'));
          return;
        }

        const ctx = wx.createCanvasContext('reportCanvas', this);
        if (!ctx) {
          reject(new Error('无法创建Canvas上下文'));
          return;
        }

        // 根据内容需求动态调整Canvas宽度
        const chartData = this.data.chartData || [];
        const baseCanvasWidth = 1125;

        let canvasWidth = baseCanvasWidth;

        // 计算柱状图需要的宽度 - 确保所有柱子都能显示且间距足够
        let chartRequiredWidth = baseCanvasWidth;
        if ((type === 'complete' || type === 'analysis') && chartData.length > 0) {
          // 为每个柱子分配合适空间：柱子宽度 + 间距，增加间距解决题型名称重叠
          const minWidthPerBar = 75; // 增加每个柱子的宽度需求，为题型名称留更多空间
          const baseWidth = 280; // 增加基础宽度
          chartRequiredWidth = baseWidth + chartData.length * minWidthPerBar;

          console.log('柱状图宽度需求计算:', {
            题型数量: chartData.length,
            每柱最小宽度: minWidthPerBar,
            基础宽度: baseWidth,
            计算需求宽度: chartRequiredWidth
          });
        }

        // 计算表格内容需要的最小宽度 - 根据实际内容动态计算
        let tableRequiredWidth = baseCanvasWidth;
        if (type === 'complete' || type === 'analysis') {
          // 根据实际内容计算所需宽度
          const reportSections = this.data.reportData.sections || [];
          let maxReasonWidth = 0;
          let maxSuggestionWidth = 0;

          // 临时创建canvas上下文来测量文本宽度
          const tempCtx = wx.createCanvasContext('reportCanvas', this);
          tempCtx.font = '18px Microsoft YaHei';

          reportSections.forEach(section => {
            // 计算原因分析列所需的最大宽度
            if (section.selectedReasons && section.selectedReasons.length > 0) {
              section.selectedReasons.forEach(reason => {
                const reasonWidth = tempCtx.measureText(reason).width;
                maxReasonWidth = Math.max(maxReasonWidth, reasonWidth);
              });
            }
            if (section.customReason) {
              const customReasonWidth = tempCtx.measureText(section.customReason).width;
              maxReasonWidth = Math.max(maxReasonWidth, customReasonWidth);
            }

            // 计算学习建议列所需的最大宽度
            if (section.selectedSuggestions && section.selectedSuggestions.length > 0) {
              section.selectedSuggestions.forEach(suggestion => {
                const suggestionWidth = tempCtx.measureText(suggestion).width;
                maxSuggestionWidth = Math.max(maxSuggestionWidth, suggestionWidth);
              });
            }
            if (section.customSuggestion) {
              const customSuggestionWidth = tempCtx.measureText(section.customSuggestion).width;
              maxSuggestionWidth = Math.max(maxSuggestionWidth, customSuggestionWidth);
            }
          });

          console.log('文本宽度测量结果:', {
            最大原因分析宽度: maxReasonWidth,
            最大学习建议宽度: maxSuggestionWidth
          });

          // 基础列宽：题型(120) + 总分(80) + 扣分(80) + 边距(40)
          const baseColumnsWidth = 120 + 80 + 80 + 40;
          // 内容列最小宽度，确保能容纳内容
          const minReasonColumnWidth = 300;
          const minSuggestionColumnWidth = 350; // 学习建议列需要更宽
          const reasonColumnWidth = Math.max(minReasonColumnWidth, maxReasonWidth + 60);
          const suggestionColumnWidth = Math.max(minSuggestionColumnWidth, maxSuggestionWidth + 80); // 增加更多边距

          tableRequiredWidth = baseColumnsWidth + reasonColumnWidth + suggestionColumnWidth;
          console.log('动态计算表格宽度:', {
            基础列宽: baseColumnsWidth,
            原因分析列宽: reasonColumnWidth,
            学习建议列宽: suggestionColumnWidth,
            总宽度: tableRequiredWidth
          });

          // 保存计算好的列宽信息供后续使用
          this.calculatedColumnWidths = {
            reasonWidth: reasonColumnWidth,
            suggestionWidth: suggestionColumnWidth
          };
        }

        // 取最大值，确保内容完整显示
        const requiredWidth = Math.max(chartRequiredWidth, tableRequiredWidth);
        canvasWidth = Math.min(requiredWidth, 2500); // 合理的最大宽度限制

        console.log('Canvas宽度计算:', {
          柱状图需求: chartRequiredWidth,
          表格需求: tableRequiredWidth,
          最终宽度: canvasWidth
        });

        // 检查Canvas尺寸限制
        const deviceInfo = wx.getDeviceInfo();
        const maxCanvasSize = deviceInfo.platform === 'ios' ? 4096 : 8192; // iOS限制更严格

        // 确保不超过系统限制，但优先保证内容完整显示
        if (canvasWidth > maxCanvasSize) {
          console.warn('Canvas宽度超出系统限制，将进行适配:', canvasWidth, '->', maxCanvasSize);
          canvasWidth = maxCanvasSize;
        }
        console.log('Canvas尺寸计算完成:', {
          系统限制: maxCanvasSize,
          计算宽度: canvasWidth,
          题型数量: chartData.length,
          表格需求宽度: tableRequiredWidth
        });

        // Canvas上下文已在上面创建，直接使用
        // 设置Canvas尺寸（使用传统方式）
        // 先计算内容高度，然后设置Canvas高度
        let canvasHeight = 1000; // 初始高度，后面会重新计算

        const currentStyle = this.data.styles[this.data.selectedStyle] || this.data.styles[0];

        // 文本换行辅助函数 - 优化长文本显示，增大默认字体
        const wrapText = (text, maxWidth, fontSize = 22) => { // 默认字体从18px增大到22px
          ctx.font = `${fontSize}px Microsoft YaHei`;
          const lines = [];
          let currentLine = '';

          // 先按句号、分号等标点符号分割，保持语义完整
          const segments = text.split(/([。；;、，,])/);

          for (let i = 0; i < segments.length; i++) {
            const segment = segments[i];
            if (!segment) continue;

            // 如果是标点符号，直接加到当前行
            if (/[。；;、，,]/.test(segment)) {
              const testLine = currentLine + segment;
              const testWidth = ctx.measureText(testLine).width;
              if (testWidth <= maxWidth) {
                currentLine = testLine;
                continue;
              }
            }

            // 按字符逐个添加
            for (let j = 0; j < segment.length; j++) {
              const char = segment[j];
              const testLine = currentLine + char;
              const testWidth = ctx.measureText(testLine).width;

              if (testWidth > maxWidth && currentLine !== '') {
                lines.push(currentLine);
                currentLine = char;
              } else {
                currentLine = testLine;
              }
            }
          }

          if (currentLine) {
            lines.push(currentLine);
          }

          return lines;
        };

        // 精确计算内容高度
        let contentHeight = 600; // 头部区域
        contentHeight += 200; // 统计卡片区域

        // 添加柱状图区域高度
        if ((type === 'complete' || type === 'analysis') && this.data.chartData && this.data.chartData.length > 0) {
          contentHeight += 80; // 柱状图标题
          contentHeight += 220; // 柱状图主体高度

          // 根据题型名称长度动态计算底部空间
          let maxNameHeight = 60; // 增加基础高度
          this.data.chartData.forEach(item => {
            if (item.name === '完成句子/翻译句子') {
              maxNameHeight = Math.max(maxNameHeight, 90); // 换行需要更多空间
            } else if (item.name && item.name.length > 8) {
            maxNameHeight = Math.max(maxNameHeight, 90); // 长名称需要更多空间
          }
        });

        contentHeight += maxNameHeight; // 柱状图底部题型名称区域
        contentHeight += 140; // 增加柱状图后间距，与实际绘制保持一致
      }

      // 只有在需要显示详细分析表时才添加高度
      if (type === 'complete' || type === 'analysis') {
        contentHeight += 120; // 详细分析表标题区域
        contentHeight += 75; // 表头
      }

      // 动态计算表格列宽 - 根据内容需求智能分配
      const tableMargin = 20;
      const tableWidth = canvasWidth - tableMargin;

      // 智能列宽分配 - 根据实际内容需求分配
      let colWidths;

      // 如果有预计算的列宽信息，使用它们
      if (this.calculatedColumnWidths) {
        // 固定列采用紧凑设计 - 调整题型列宽度并重新计算其他列宽
        const typeColWidth = 180;   // 题型列固定宽度
        const totalColWidth = 80;   // 总分列固定宽度
        const deductColWidth = 80;  // 扣分列固定宽度

        // 重新计算剩余宽度，确保学习建议列有足够空间
        const usedWidth = typeColWidth + totalColWidth + deductColWidth;
        const remainingWidth = tableWidth - usedWidth;
        const reasonWidth = Math.floor(remainingWidth * 0.4);  // 原因分析列占剩余宽度的40%
        const suggestionWidth = remainingWidth - reasonWidth;   // 学习建议列占剩余宽度的60%

        colWidths = [
          typeColWidth,
          totalColWidth,
          deductColWidth,
          reasonWidth,      // 重新计算的原因分析列宽
          suggestionWidth   // 重新计算的学习建议列宽
        ];
      } else {
        // 回退到默认分配方案 - 调整题型列宽度并重新计算其他列宽
        const typeColWidth = Math.max(160, Math.floor(tableWidth * 0.16));   // 题型列：调整为16%
        const totalColWidth = Math.max(60, Math.floor(tableWidth * 0.08));   // 总分列：8%
        const deductColWidth = Math.max(60, Math.floor(tableWidth * 0.08));  // 扣分列：8%

        // 计算内容列可用宽度
        const fixedColumnsWidth = typeColWidth + totalColWidth + deductColWidth;
        const remainingWidth = tableWidth - fixedColumnsWidth;

        // 内容列平均分配剩余宽度，确保最小宽度
        const minContentWidth = 280; // 内容列最小宽度
        const contentColumnWidth = Math.max(minContentWidth, Math.floor(remainingWidth / 2));

        colWidths = [
          typeColWidth,        // 题型列
          totalColWidth,       // 总分列
          deductColWidth,      // 扣分列
          contentColumnWidth,  // 原因分析列
          contentColumnWidth   // 学习建议列
        ];
      }

      console.log('表格列宽分配:', {
        Canvas宽度: canvasWidth,
        表格宽度: tableWidth,
        使用预计算宽度: !!this.calculatedColumnWidths,
        题型列: colWidths[0],
        总分列: colWidths[1],
        扣分列: colWidths[2],
        原因分析列: colWidths[3],
        学习建议列: colWidths[4],
        总宽度: colWidths.reduce((sum, width) => sum + width, 0)
      });

      // 动态计算每行的实际高度（只有在显示详细分析表时才计算）
      if (type === 'complete' || type === 'analysis') {
        const reportSections = this.data.reportData.sections || [];
        reportSections.forEach(section => {
        let reasonContentHeight = 0;
        let suggestionContentHeight = 0;

        // 精确计算原因分析内容高度
        const hasReasonsInCalc = (section.selectedReasons && section.selectedReasons.length > 0) || section.customReason;
        if (hasReasonsInCalc) {
          reasonContentHeight = 25; // 顶部边距

          if (section.selectedReasons && section.selectedReasons.length > 0) {
            section.selectedReasons.forEach(reason => {
              const reasonLines = wrapText(reason, colWidths[3] - 20, 22); // 增大字体到22px
              reasonContentHeight += reasonLines.length * 36; // 增加行高
              reasonContentHeight += 10; // 增加项目间距
            });
          }

          if (section.customReason) {
            const customLines = wrapText(section.customReason, colWidths[3] - 20, 22); // 增大字体到22px
            reasonContentHeight += customLines.length * 36;
          }

          reasonContentHeight += 25; // 底部边距
        } else {
          reasonContentHeight = 90; // 空内容最小高度
        }

        // 精确计算学习建议内容高度
        const hasSuggestionsInCalc = (section.selectedSuggestions && section.selectedSuggestions.length > 0) || section.customSuggestion;
        if (hasSuggestionsInCalc) {
          suggestionContentHeight = 25; // 顶部边距

          if (section.selectedSuggestions && section.selectedSuggestions.length > 0) {
            section.selectedSuggestions.forEach(suggestion => {
              const suggestionLines = wrapText(suggestion, colWidths[4] - 20, 22); // 增大字体到22px
              suggestionContentHeight += suggestionLines.length * 36; // 增加行高
              suggestionContentHeight += 10; // 增加项目间距
            });
          }

          if (section.customSuggestion) {
            const customLines = wrapText(section.customSuggestion, colWidths[4] - 20, 22); // 增大字体到22px
            suggestionContentHeight += customLines.length * 36;
          }

          suggestionContentHeight += 25; // 底部边距
        } else {
          suggestionContentHeight = 90; // 空内容最小高度
        }

        // 计算题型列的高度需求（考虑换行和听口题型的特殊处理）
        let titleNameHeight = 80; // 增加默认高度

        // 特殊处理长题型名称
        if (section.name === '完成句子/翻译句子') {
          titleNameHeight = 110; // 换行后需要更多高度
        } else if (section.name && section.name.length > 8) {
          // 对于较长的题型名称（如听口题型），增加高度
          titleNameHeight = 100;
        }

        // 听口题型通常内容较多，需要额外空间
        const isListeningSection = section.id && section.id.includes('listening');
        if (isListeningSection) {
          reasonContentHeight = Math.max(reasonContentHeight, 120);
          suggestionContentHeight = Math.max(suggestionContentHeight, 120);
        }

          // 取最大高度，确保内容完全填满
          const rowHeight = Math.max(titleNameHeight, reasonContentHeight, suggestionContentHeight);
          contentHeight += rowHeight;
        });
      }
      
      // 精确计算课程规划高度
      const planData = this.data.coursePlanData || [];
      if ((type === 'complete' || type === 'courseplan') && this.data.reportConfig.showCourseplan && planData.length > 0) {
        contentHeight += 100; // 课程规划标题
        contentHeight += 75; // 课程规划表头

        planData.forEach(item => {
          const contentText = item.content || '待填写';
          const planContentWidth = (canvasWidth - tableMargin) - 300; // 学习内容列宽，与实际绘制保持一致
          const contentLines = wrapText(contentText, planContentWidth - 40, 22); // 增大字体到22px
          const rowHeight = Math.max(85, contentLines.length * 38 + 35); // 增加行高
          contentHeight += rowHeight;
        });
      }

      // 底部信息高度 - 增加更多空间确保显示完整
      contentHeight += 60; // 增加报告生成时间前的间距
      contentHeight += 35; // 分隔线高度
      contentHeight += 40; // 报告生成时间行高
      contentHeight += 50; // 增加最终底部间距

      // 确保内容高度足够，添加最小安全边距
      // 根据题型数量动态调整最小高度，特别考虑听口题型
      const reportSections = this.data.reportData.sections || [];
      const sectionCount = reportSections.length;
      const hasListeningSections = reportSections.some(section =>
        section.id && section.id.includes('listening')
      );

      // 如果包含听口题型，增加额外高度
      const baseMinHeight = hasListeningSections ? 2000 : 1500;
      const heightPerSection = hasListeningSections ? 200 : 150;
      const minHeightByCount = Math.max(baseMinHeight, sectionCount * heightPerSection + 1000);

      // 增加安全边距，确保内容完全显示
      let safeContentHeight = Math.max(contentHeight + 200, minHeightByCount);

      // 检查高度限制，防止超出Canvas限制
      if (safeContentHeight > maxCanvasSize) {
        console.warn('Canvas高度超出限制，进行压缩:', safeContentHeight, '->', maxCanvasSize);
        safeContentHeight = maxCanvasSize;
      }

      console.log('最终Canvas尺寸:', canvasWidth, 'x', safeContentHeight);

      // 设置Canvas高度
      canvasHeight = safeContentHeight;

      // 将Canvas宽度设置到data中，供WXML使用
      this.setData({
        canvasWidth: canvasWidth
      });

      // 清空画布并设置尺寸
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);
      
      // 绘制背景渐变
      const gradient = ctx.createLinearGradient(0, 0, 0, 600);
      gradient.addColorStop(0, currentStyle.primary);
      gradient.addColorStop(0.5, currentStyle.secondary);
      gradient.addColorStop(1, currentStyle.accent);
      
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvasWidth, 600);
      
      // 绘制白色背景区域
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 600, canvasWidth, canvasHeight - 600);
      
      // 绘制装饰线 - 根据Canvas宽度动态居中和调整间距
      ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
      const lineWidth = Math.min(180, canvasWidth * 0.15); // 线条宽度不超过Canvas宽度的15%
      const minGap = 200;
      const maxGap = 300;
      const lineGap = Math.min(maxGap, Math.max(minGap, canvasWidth * 0.2)); // 间距根据Canvas宽度调整
      const totalLineWidth = lineWidth * 2 + lineGap;
      const lineStartX = (canvasWidth - totalLineWidth) / 2;

      ctx.fillRect(lineStartX, 105, lineWidth, 6);
      ctx.fillRect(lineStartX + lineWidth + lineGap, 105, lineWidth, 6);
      
      // 绘制装饰图标
      ctx.fillStyle = '#ffffff';
      ctx.font = '60px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('📊', canvasWidth / 2, 150);
      
      // 绘制标题（根据type调整标题内容）
      const { basicInfo } = this.data.reportData;
      let titleText;
      if (type === 'courseplan') {
        titleText = `${basicInfo.studentName || ''}同学课程规划`;
      } else {
        titleText = `${basicInfo.studentName || ''}同学${basicInfo.grade || ''}英语${basicInfo.examType || ''}试卷分析报告`;
      }
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 42px Microsoft YaHei';
      ctx.textAlign = 'center';
      
      // 自动调整字体大小 - 大幅增大主标题字体作为总的大标题
      const maxTitleWidth = canvasWidth - 120;
      // 大幅增大主标题字体大小，突出标题重要性
      let titleFontSize = 60; // 从48px大幅增大到60px，作为总的大标题
      ctx.font = `bold ${titleFontSize}px Microsoft YaHei`;

      while (ctx.measureText(titleText).width > maxTitleWidth && titleFontSize > 40) {
        titleFontSize -= 1;
        ctx.font = `bold ${titleFontSize}px Microsoft YaHei`;
      }

      ctx.fillText(titleText, canvasWidth / 2, 255);

      // 绘制副标题 - 增大字体大小
      const subtitleSize = 28; // 从22px增大到28px
      ctx.font = `${subtitleSize}px Microsoft YaHei`;
      ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
      ctx.fillText('Professional Assessment Report', canvasWidth / 2, 315);
      
      let yPos = 420; // 减少间距，让成绩统计更靠近标题
      
      // 根据type调整起始位置
      if (type === 'courseplan') {
        // 如果只绘制课程规划，跳过成绩统计和柱状图，直接跳到课程规划绘制
        // 不在这里设置yPos，让它在课程规划绘制时设置
      } else {
        // 绘制成绩统计标题
        ctx.fillStyle = currentStyle.primary;
        const sectionTitleSize = 36; // 从28px增大到36px
        ctx.font = `bold ${sectionTitleSize}px Microsoft YaHei`;
        ctx.textAlign = 'center';
        ctx.fillText('📈 成绩统计', canvasWidth / 2, yPos);
      }
      
      if (type !== 'courseplan') {
        yPos += 75;
        
        // 绘制统计卡片 - 根据Canvas宽度动态调整卡片大小
        const minCardWidth = 180;
        const maxCardWidth = 250;
        const cardGap = 30;
        const totalGapWidth = cardGap * 3; // 4个卡片之间有3个间距
        const availableWidth = canvasWidth - 100; // 留出左右边距
        const calculatedCardWidth = Math.min(maxCardWidth, Math.max(minCardWidth, (availableWidth - totalGapWidth) / 4));

        const cardWidth = calculatedCardWidth;
        const cardHeight = 120;
        const startX = (canvasWidth - (cardWidth * 4 + cardGap * 3)) / 2;
      
      const stats = [
        { label: '总分', value: this.data.totalSelectedScore || 0, color: '#6c757d', bgColor: '#f8f9fa' },
        { label: '失分', value: this.data.totalLostScore || 0, color: '#e53e3e', bgColor: '#fff5f5' },
        { label: '得分', value: this.data.totalGainedScore || 0, color: '#38a169', bgColor: '#f0fff4' },
        { label: '正确率', value: `${this.data.correctRate || 0}%`, color: currentStyle.primary, bgColor: '#e6f3ff' }
      ];
      
      stats.forEach((stat, index) => {
        const x = startX + index * (cardWidth + cardGap);
        
        // 绘制卡片背景
        ctx.fillStyle = stat.bgColor;
        ctx.fillRect(x, yPos, cardWidth, cardHeight);
        
        // 绘制卡片边框
        ctx.strokeStyle = stat.color;
        ctx.lineWidth = 2;
        ctx.strokeRect(x, yPos, cardWidth, cardHeight);
        
        // 绘制数值（加粗）
        ctx.fillStyle = stat.color;
        const statValueSize = 38; // 从30px增大到38px
        ctx.font = `bold ${statValueSize}px Microsoft YaHei`;
        ctx.textAlign = 'center';
        ctx.fillText(stat.value.toString(), x + cardWidth / 2, yPos + 48);

        // 绘制标签
        const statLabelSize = 24; // 从18px增大到24px
        ctx.font = `${statLabelSize}px Microsoft YaHei`;
        ctx.fillStyle = '#666666';
        ctx.fillText(stat.label, x + cardWidth / 2, yPos + 84);
      });
      
        yPos += 220; // 继续增加统计卡片后的间距，使详细分析表标题更靠近表格
      } // 结束统计卡片绘制的条件判断
      
      // 绘制各题型得分率柱状图（根据type参数控制）
      console.log('柱状图绘制检查:', {
        类型: type,
        条件: 'type === complete || type === analysis',
        匹配结果: type === 'complete' || type === 'analysis'
      });

      if (type === 'complete' || type === 'analysis') {
        const chartData = this.data.chartData || [];
        console.log('柱状图绘制检查:', {
          类型: type,
          数据长度: chartData.length,
          数据存在: !!chartData,
          前3项数据: chartData.slice(0, 3)
        });

        if (chartData.length > 0) {
          // 绘制柱状图标题
          ctx.fillStyle = currentStyle.primary;
          ctx.font = 'bold 39px Microsoft YaHei';
          ctx.textAlign = 'center';
          ctx.fillText('📊 各题型得分率', canvasWidth / 2, yPos);

          yPos += 80;
          
          // 柱状图配置 - 紧凑合理的布局
          const chartHeight = 220; // 柱状图主体高度，题型名称显示在下方
          const chartMargin = 20; // 减小边距

          // 动态计算柱子参数，让柱子占满横向空间
          const yAxisWidth = 60; // Y轴宽度
          const chartWidth = canvasWidth - chartMargin;
          const chartStartX = chartMargin / 2;
          const chartStartY = yPos;

          // 重新设计柱子宽度计算 - 让柱子占满大部分横向空间
          const totalBars = chartData.length;

          // 设置边距，减少两边空白
          const leftMargin = 30;  // 左边距
          const rightMargin = 30; // 右边距
          const totalMargin = leftMargin + rightMargin;

          // 可用于柱子的宽度
          const availableWidth = chartWidth - yAxisWidth - rightMargin;

          // 计算实际可用于柱子和间距的宽度
          const usableWidth = availableWidth - totalMargin;

          // 设置最小间距，确保柱子不会挤在一起
          const minSpacing = Math.max(8, Math.floor(usableWidth / (totalBars * 8))); // 动态最小间距
          const maxSpacing = Math.min(25, Math.floor(usableWidth / (totalBars * 3))); // 动态最大间距

          // 计算间距总宽度
          const totalSpacingWidth = (totalBars - 1) * minSpacing;

          // 剩余宽度全部分配给柱子
          const totalBarWidth = usableWidth - totalSpacingWidth;
          let barWidth = Math.floor(totalBarWidth / totalBars);

          // 如果柱子太宽，适当增加间距
          let barSpacing = minSpacing;
          if (barWidth > 100) {
            // 柱子太宽时，增加间距让布局更美观
            const excessWidth = totalBarWidth - (totalBars * 80); // 理想柱子宽度80px
            if (excessWidth > 0) {
              const additionalSpacing = Math.floor(excessWidth / (totalBars - 1));
              barSpacing = Math.min(maxSpacing, minSpacing + additionalSpacing);

              // 重新计算柱子宽度（考虑调整后的间距）
              const finalTotalSpacingWidth = (totalBars - 1) * barSpacing;
              barWidth = Math.floor((usableWidth - finalTotalSpacingWidth) / totalBars);
            }
          }

          console.log('柱状图宽度优化计算:', {
            可用总宽度: availableWidth,
            边距: totalMargin,
            实际可用宽度: usableWidth,
            题型数量: totalBars,
            最终柱子宽度: barWidth,
            柱子间距: barSpacing,
            柱子总宽度: barWidth * totalBars,
            间距总宽度: (totalBars - 1) * barSpacing,
            实际占用宽度: barWidth * totalBars + (totalBars - 1) * barSpacing,
            空间利用率: `${Math.round((barWidth * totalBars + (totalBars - 1) * barSpacing) / usableWidth * 100)}%`
          });

          console.log('动态柱子布局:', {
            Canvas宽度: canvasWidth,
            可用宽度: availableWidth,
            题型数量: chartData.length,
            计算柱子宽度: barWidth,
            计算间距: barSpacing
          });

          // 绘制柱状图背景
          ctx.fillStyle = '#f8f9fa';
          ctx.fillRect(chartStartX, chartStartY, chartWidth, chartHeight);

          // 绘制边框
          ctx.strokeStyle = '#e9ecef';
          ctx.lineWidth = 2;
          ctx.strokeRect(chartStartX, chartStartY, chartWidth, chartHeight);

          // 绘制Y轴网格线和标签
          const gridHeight = chartHeight - 40; // 减少底部空间，题型名称显示在柱状图外
          const gridStartY = chartStartY + 20;
          
          ctx.strokeStyle = '#e9ecef';
          ctx.lineWidth = 1;
          ctx.fillStyle = '#666666';
          ctx.font = '20px Microsoft YaHei';
          ctx.textAlign = 'right';
          
          for (let i = 0; i <= 5; i++) {
            const percentage = i * 20;
            const gridY = gridStartY + (5 - i) * (gridHeight / 5);
            
            // 绘制网格线
            ctx.beginPath();
            ctx.moveTo(chartStartX + yAxisWidth, gridY);
            ctx.lineTo(chartStartX + chartWidth - 20, gridY);
            ctx.stroke();
            
            // 绘制Y轴标签
            ctx.fillText(percentage + '%', chartStartX + yAxisWidth - 10, gridY + 6);
          }
          
          // 绘制柱子 - 使用优化后的布局算法
          const barAreaStartX = chartStartX + yAxisWidth;

          // 使用之前计算的边距设置（leftMargin已在上面声明）
          const finalStartX = barAreaStartX + leftMargin;

          console.log('柱状图优化布局:', {
            Canvas宽度: canvasWidth,
            柱状图起始X: barAreaStartX,
            左边距: leftMargin,
            最终起始位置: finalStartX,
            柱子宽度: barWidth,
            柱子间距: barSpacing,
            题型数量: chartData.length,
            预计总宽度: chartData.length * barWidth + (chartData.length - 1) * barSpacing
          });

          chartData.forEach((item, index) => {
            const barX = finalStartX + index * (barWidth + barSpacing);
            const barHeight = Math.max(5, (item.rate / 100) * gridHeight); // 确保最小高度
            const barY = gridStartY + gridHeight - barHeight;

            // 创建渐变
            const gradient = ctx.createLinearGradient(0, barY, 0, barY + barHeight);
            gradient.addColorStop(0, currentStyle.primary);
            gradient.addColorStop(1, currentStyle.secondary);

            // 绘制柱子
            ctx.fillStyle = gradient;
            
            // 绘制圆角矩形柱子
            const cornerRadius = 6;
            ctx.beginPath();
            ctx.moveTo(barX + cornerRadius, barY);
            ctx.lineTo(barX + barWidth - cornerRadius, barY);
            ctx.quadraticCurveTo(barX + barWidth, barY, barX + barWidth, barY + cornerRadius);
            ctx.lineTo(barX + barWidth, barY + barHeight);
            ctx.lineTo(barX, barY + barHeight);
            ctx.lineTo(barX, barY + cornerRadius);
            ctx.quadraticCurveTo(barX, barY, barX + cornerRadius, barY);
            ctx.closePath();
            ctx.fill();
            
            // 添加柱子阴影效果
            ctx.shadowColor = 'rgba(102, 126, 234, 0.3)';
            ctx.shadowBlur = 8;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 4;
            ctx.fill();
            
            // 重置阴影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            
            // 绘制数值标签 - 优化位置和字体
            ctx.fillStyle = '#333333';
            ctx.font = 'bold 22px Microsoft YaHei'; // 稍微减小字体
            ctx.textAlign = 'center';

            // 百分比标签始终显示在柱子上方，确保不重叠
            const labelY = barY - 15;
            ctx.fillText(item.rate + '%', barX + barWidth / 2, labelY);

            // 绘制题型名称 - 优化间距和字体大小
            ctx.fillStyle = '#333333'; // 改为更深的颜色，提高可读性
            ctx.font = '14px Microsoft YaHei'; // 减小字体避免重叠
            ctx.textAlign = 'center';

            // 智能处理题型名称显示 - 根据新的布局优化
            let sectionName = item.name;
            // 计算每个题型名称的可用宽度（柱子宽度 + 部分间距）
            const nameMaxWidth = barWidth + Math.min(barSpacing, 30);

            // 根据柱子宽度调整字体大小 - 减小字体避免重叠
            let nameFontSize = 16; // 从24px减小到16px
            if (barWidth < 40) {
              nameFontSize = 14; // 从20px减小到14px
            } else if (barWidth > 60) {
              nameFontSize = 18; // 从28px减小到18px
            }

            ctx.font = `${nameFontSize}px Microsoft YaHei`;
            const textWidth = ctx.measureText(sectionName).width;

            if (textWidth > nameMaxWidth || sectionName.length > 8) {
              // 需要换行处理
              if (sectionName === '完成句子/翻译句子') {
                // 特殊处理这个长名称
                ctx.fillText('完成句子', barX + barWidth / 2, chartStartY + chartHeight + 25);
                ctx.fillText('翻译句子', barX + barWidth / 2, chartStartY + chartHeight + 50);
              } else if (sectionName.length > 6) {
                // 长名称智能换行
                const midPoint = Math.ceil(sectionName.length / 2);
                let firstLine = sectionName.substring(0, midPoint);
                let secondLine = sectionName.substring(midPoint);

                // 尝试在合适的位置断开
                if (sectionName.includes('听') && sectionName.includes('口')) {
                  const hearIndex = sectionName.indexOf('听');
                  const speakIndex = sectionName.indexOf('口');
                  if (speakIndex > hearIndex) {
                    firstLine = sectionName.substring(0, speakIndex);
                    secondLine = sectionName.substring(speakIndex);
                  }
                }

                ctx.fillText(firstLine, barX + barWidth / 2, chartStartY + chartHeight + 25);
                ctx.fillText(secondLine, barX + barWidth / 2, chartStartY + chartHeight + 50);
              } else {
                // 短名称单行显示
                ctx.fillText(sectionName, barX + barWidth / 2, chartStartY + chartHeight + 35);
              }
            } else {
              // 名称适合单行显示
              ctx.fillText(sectionName, barX + barWidth / 2, chartStartY + chartHeight + 35);
            }
          });
          
          yPos += chartHeight + 140; // 进一步增加柱状图后的间距，为题型名称留出充足空间
        }
      }
      
      // 绘制详细分析表格标题（根据type参数控制）
      console.log('详细分析表绘制检查:', {
        类型: type,
        条件: 'type === complete || type === analysis',
        匹配结果: type === 'complete' || type === 'analysis'
      });

      if (type === 'complete' || type === 'analysis') {
        ctx.fillStyle = currentStyle.primary;
        ctx.font = 'bold 39px Microsoft YaHei';
        ctx.textAlign = 'center';
        ctx.fillText('📋 详细分析表', canvasWidth / 2, yPos);

        yPos += 60; // 减少标题后的间距，让标题更靠近表格
      }

      // 绘制表格（根据type参数控制）
      if (type === 'complete' || type === 'analysis') {
        const tableX = tableMargin / 2;
      
      // 绘制表头
      ctx.fillStyle = currentStyle.primary;
      ctx.fillRect(tableX, yPos, tableWidth, 68);
      
      // 绘制表头边框
      ctx.strokeStyle = '#e9ecef';
      ctx.lineWidth = 2;
      ctx.strokeRect(tableX, yPos, tableWidth, 68);
      
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 27px Microsoft YaHei';
      ctx.textAlign = 'center';
      
      const headers = ['题型', '总分', '扣分', '原因分析', '学习建议'];
      let currentX = tableX;
      
      headers.forEach((header, index) => {
        ctx.fillText(header, currentX + colWidths[index] / 2, yPos + 42);
        currentX += colWidths[index];
      });
      
      yPos += 68;
      
            // 绘制表格数据行
      const drawingSections = this.data.reportData.sections || [];
      drawingSections.forEach((section, index) => {
        // 重新计算行高，减少空白
        let reasonContentHeight = 0;
        let suggestionContentHeight = 0;
        
        // 精确计算原因分析内容高度
        const hasReasons = (section.selectedReasons && section.selectedReasons.length > 0) || section.customReason;
        if (hasReasons) {
          reasonContentHeight = 30; // 增加顶部边距
          
          if (section.selectedReasons && section.selectedReasons.length > 0) {
            section.selectedReasons.forEach(reason => {
              const reasonLines = wrapText(reason, colWidths[3] - 50, 26); // 与实际绘制时保持一致
              reasonContentHeight += reasonLines.length * 32; // 与实际绘制时保持一致
              reasonContentHeight += 8; // 项目间距
            });
          }

          if (section.customReason) {
            const customLines = wrapText(section.customReason, colWidths[3] - 50, 26); // 与实际绘制时保持一致
            reasonContentHeight += customLines.length * 32; // 与实际绘制时保持一致
          }
          
          reasonContentHeight += 30; // 增加底部边距
        } else {
          reasonContentHeight = 80; // 增加默认高度
        }

        // 精确计算学习建议内容高度
        const hasSuggestions = (section.selectedSuggestions && section.selectedSuggestions.length > 0) || section.customSuggestion;
        if (hasSuggestions) {
          suggestionContentHeight = 30; // 增加顶部边距
          
          if (section.selectedSuggestions && section.selectedSuggestions.length > 0) {
            section.selectedSuggestions.forEach(suggestion => {
              const suggestionLines = wrapText(suggestion, colWidths[4] - 40, 26); // 与实际绘制时保持一致
              suggestionContentHeight += suggestionLines.length * 32; // 与实际绘制时保持一致
              suggestionContentHeight += 8; // 项目间距
            });
          }

          if (section.customSuggestion) {
            const customLines = wrapText(section.customSuggestion, colWidths[4] - 40, 26); // 与实际绘制时保持一致
            suggestionContentHeight += customLines.length * 32; // 与实际绘制时保持一致
          }
          
          suggestionContentHeight += 30; // 增加底部边距
        } else {
          suggestionContentHeight = 80; // 增加默认高度
        }
        
        // 计算题型列的高度需求（与预计算保持一致）
        let titleNameHeight = 80;

        // 特殊处理长题型名称
        if (section.name === '完成句子/翻译句子') {
          titleNameHeight = 110;
        } else if (section.name && section.name.length > 8) {
          // 对于较长的题型名称（如听口题型），增加高度
          titleNameHeight = 100;
        }

        // 听口题型和长题型名称通常内容较多，需要额外空间
        const isListeningSection = section.id && section.id.includes('listening');
        const isLongNameSection = section.name && section.name.length > 8;
        const hasMultipleReasons = section.selectedReasons && section.selectedReasons.length > 2;
        const hasMultipleSuggestions = section.selectedSuggestions && section.selectedSuggestions.length > 2;

        if (isListeningSection || isLongNameSection || hasMultipleReasons || hasMultipleSuggestions) {
          reasonContentHeight = Math.max(reasonContentHeight, 140); // 增加最小空间要求
          suggestionContentHeight = Math.max(suggestionContentHeight, 140);
          titleNameHeight = Math.max(titleNameHeight, 110); // 增加题型名称空间要求
        }

        // 确保所有内容都有最小高度
        reasonContentHeight = Math.max(reasonContentHeight, 100);
        suggestionContentHeight = Math.max(suggestionContentHeight, 100);

        // 取最大高度作为行高
        const rowHeight = Math.max(titleNameHeight, reasonContentHeight, suggestionContentHeight);
        // 绘制行背景
        const rowColor = index % 2 === 0 ? '#f8f9fa' : '#ffffff';
        ctx.fillStyle = rowColor;
        ctx.fillRect(tableX, yPos, tableWidth, rowHeight);
        
        // 绘制边框
        ctx.strokeStyle = '#e9ecef';
        ctx.lineWidth = 2;
        ctx.strokeRect(tableX, yPos, tableWidth, rowHeight);
        
        // 绘制垂直分割线
        currentX = tableX;
        for (let i = 0; i < colWidths.length - 1; i++) {
          currentX += colWidths[i];
          ctx.beginPath();
          ctx.moveTo(currentX, yPos);
          ctx.lineTo(currentX, yPos + rowHeight);
          ctx.stroke();
        }
        
        // 绘制单元格内容
        currentX = tableX;
        
        // 题型列 - 垂直居中显示
        ctx.fillStyle = '#333333';
        const tableHeaderSize = 28; // 从22px增大到28px
        ctx.font = `bold ${tableHeaderSize}px Microsoft YaHei`;
        ctx.textAlign = 'center';
        
        // 处理长题型名称的换行
        let sectionName = section.name;
        let sectionLines = [];
        if (sectionName === '完成句子/翻译句子') {
          sectionLines = ['完成句子', '翻译句子'];
        } else {
          sectionLines = [sectionName];
        }
        
        // 计算题型名称的真正垂直居中位置
        const lineHeight = 32;
        const lineSpacing = 6;
        const titleTextHeight = sectionLines.length * lineHeight + (sectionLines.length - 1) * lineSpacing;

        // 计算题型名称在行中的垂直居中位置
        const rowCenterY = yPos + rowHeight / 2;
        const titleStartY = rowCenterY - (titleTextHeight / 2);

        // 绘制题型名称 - 与总分/扣分数字水平对齐
        ctx.fillStyle = '#333333';
        ctx.font = `bold ${tableHeaderSize}px Microsoft YaHei`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // 题型名称与总分/扣分数字在同一水平线上
        const titleY = rowCenterY - 8; // 稍微向上偏移，为下方分值留空间

        sectionLines.forEach((line, index) => {
          const lineY = titleY + (index - (sectionLines.length - 1) / 2) * (lineHeight + lineSpacing);
          ctx.fillText(line, currentX + colWidths[0] / 2, lineY);
        });

        // 绘制得分信息 - 在题型名称下方，有适当间距
        const scoreInfoSize = 18;
        ctx.font = `${scoreInfoSize}px Microsoft YaHei`;
        ctx.fillStyle = '#666666';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'top';
        const titleBottomY = titleY + (sectionLines.length - 1) / 2 * (lineHeight + lineSpacing) + lineHeight / 2;
        const scoreY = titleBottomY + 8; // 增加间距到8px
        ctx.fillText(`${section.totalScore - (section.lostScore || 0)}/${section.totalScore}`, currentX + colWidths[0] / 2, scoreY);
        
        // 总分列（方形圆角背景样式）
        currentX += colWidths[0];
        const totalScoreX = currentX + colWidths[1] / 2;
        const totalScoreY = yPos + rowHeight / 2;
        
        // 绘制方形圆角背景 - 加大尺寸，增强视觉效果
        const rectWidth = 60;
        const rectHeight = 42;
        const cornerRadius = 10;
        
        // 添加阴影效果
        ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
        ctx.shadowBlur = 4;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;
        
        ctx.fillStyle = '#e6f3ff';
        // 手动绘制圆角矩形（兼容性更好）
        const x = totalScoreX - rectWidth/2;
        const y = totalScoreY - rectHeight/2;
        ctx.beginPath();
        ctx.moveTo(x + cornerRadius, y);
        ctx.lineTo(x + rectWidth - cornerRadius, y);
        ctx.quadraticCurveTo(x + rectWidth, y, x + rectWidth, y + cornerRadius);
        ctx.lineTo(x + rectWidth, y + rectHeight - cornerRadius);
        ctx.quadraticCurveTo(x + rectWidth, y + rectHeight, x + rectWidth - cornerRadius, y + rectHeight);
        ctx.lineTo(x + cornerRadius, y + rectHeight);
        ctx.quadraticCurveTo(x, y + rectHeight, x, y + rectHeight - cornerRadius);
        ctx.lineTo(x, y + cornerRadius);
        ctx.quadraticCurveTo(x, y, x + cornerRadius, y);
        ctx.closePath();
        ctx.fill();
        
        // 重置阴影
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        
        // 绘制总分数字（加黑加粗，增大字体）
        ctx.fillStyle = '#1a365d'; // 深蓝色，更醒目
        const tableScoreSize = 28; // 从22px增大到28px
        ctx.font = `bold ${tableScoreSize}px Microsoft YaHei`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle'; // 使用middle基线确保垂直居中
        ctx.fillText(section.totalScore.toString(), totalScoreX, totalScoreY);
        
        // 扣分列（方形圆角背景样式）
        currentX += colWidths[1];
        const lostScoreX = currentX + colWidths[2] / 2;
        const lostScoreY = yPos + rowHeight / 2;
        
        // 绘制方形圆角背景 - 增强视觉效果
        // 添加阴影效果
        ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
        ctx.shadowBlur = 4;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;
        
        ctx.fillStyle = '#fff5f5';
        // 手动绘制圆角矩形（兼容性更好）
        const x2 = lostScoreX - rectWidth/2;
        const y2 = lostScoreY - rectHeight/2;
        ctx.beginPath();
        ctx.moveTo(x2 + cornerRadius, y2);
        ctx.lineTo(x2 + rectWidth - cornerRadius, y2);
        ctx.quadraticCurveTo(x2 + rectWidth, y2, x2 + rectWidth, y2 + cornerRadius);
        ctx.lineTo(x2 + rectWidth, y2 + rectHeight - cornerRadius);
        ctx.quadraticCurveTo(x2 + rectWidth, y2 + rectHeight, x2 + rectWidth - cornerRadius, y2 + rectHeight);
        ctx.lineTo(x2 + cornerRadius, y2 + rectHeight);
        ctx.quadraticCurveTo(x2, y2 + rectHeight, x2, y2 + rectHeight - cornerRadius);
        ctx.lineTo(x2, y2 + cornerRadius);
        ctx.quadraticCurveTo(x2, y2, x2 + cornerRadius, y2);
        ctx.closePath();
        ctx.fill();
        
        // 重置阴影
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        
        // 绘制扣分数字（加黑加粗，增大字体）
        ctx.fillStyle = '#c53030'; // 深红色，更醒目
        const tableErrorSize = 28; // 从22px增大到28px
        ctx.font = `bold ${tableErrorSize}px Microsoft YaHei`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle'; // 使用middle基线确保垂直居中
        ctx.fillText((section.lostScore || 0).toString(), lostScoreX, lostScoreY);
        
        // 原因分析列 - 修正垂直居中显示
        currentX += colWidths[2];
        ctx.fillStyle = '#333333';
        const tableContentSize = 26; // 从22px增大到26px，比题型名称(28px)稍小一点
        ctx.font = `${tableContentSize}px Microsoft YaHei`;
        ctx.textAlign = 'left';

        // 计算所有原因分析内容的总行数
        let totalReasonLines = 0;
        if (section.selectedReasons && section.selectedReasons.length > 0) {
          section.selectedReasons.forEach(reason => {
            const reasonLines = wrapText(reason, colWidths[3] - 50, 26); // 增加更多边距避免重叠
            totalReasonLines += reasonLines.length;
          });
        }
        if (section.customReason) {
          const customLines = wrapText(section.customReason, colWidths[3] - 50, 26); // 增加更多边距避免重叠
          totalReasonLines += customLines.length;
        }
        
        // 计算实际内容总高度（包括间距）
        const itemCount = section.selectedReasons.length + (section.customReason ? 1 : 0);
        const actualReasonHeight = totalReasonLines * 32 + Math.max(0, itemCount - 1) * 8; // 适当增加行高以适应更大字体

        // 修正垂直居中计算：确保内容不贴到边界线，增加边距
        const reasonCenterY = yPos + rowHeight / 2;
        const reasonStartY = Math.max(yPos + 20, reasonCenterY - actualReasonHeight / 2); // 增加上边距到20px
        let reasonYPos = reasonStartY;

        if (section.selectedReasons && section.selectedReasons.length > 0) {
          ctx.textBaseline = 'middle'; // 设置文本垂直居中
          section.selectedReasons.forEach(reason => {
            const reasonLines = wrapText(reason, colWidths[3] - 50, 26); // 增加更多边距避免重叠
            ctx.fillText('• ', currentX + 12, reasonYPos + 16); // 调整bullet点位置
            reasonLines.forEach((line, lineIndex) => {
              const xOffset = lineIndex === 0 ? 30 : 18;
              ctx.fillText(line, currentX + xOffset, reasonYPos + 16); // 调整文本位置
              reasonYPos += 32; // 适当增加行高以适应更大字体
            });
            reasonYPos += 8; // 减小项目间距
          });
        }

        if (section.customReason) {
          ctx.textBaseline = 'middle'; // 设置文本垂直居中
          const customLines = wrapText(section.customReason, colWidths[3] - 50, 26); // 增加更多边距避免重叠
          ctx.fillText('• ', currentX + 12, reasonYPos + 16); // 调整bullet点位置
          customLines.forEach((line, lineIndex) => {
            const xOffset = lineIndex === 0 ? 30 : 18;
            ctx.fillText(line, currentX + xOffset, reasonYPos + 16); // 调整文本位置
            reasonYPos += 32; // 适当增加行高以适应更大字体
          });
        }
        
        // 如果没有任何原因分析内容，显示提示文字
        if ((!section.selectedReasons || section.selectedReasons.length === 0) && !section.customReason) {
          ctx.fillStyle = '#999999';
          ctx.font = `${tableContentSize}px Microsoft YaHei`;
          ctx.fillText('暂无分析', currentX + 12, yPos + rowHeight / 2);
          ctx.fillStyle = '#333333'; // 恢复颜色
          ctx.font = `${tableContentSize}px Microsoft YaHei`; // 恢复字体
        }

        // 学习建议列 - 修正垂直居中显示
        currentX += colWidths[3]; // 移动到学习建议列的起始位置
        const suggestionColStartX = currentX; // 保存学习建议列的起始X坐标
        ctx.fillStyle = '#333333';
        ctx.font = `${tableContentSize}px Microsoft YaHei`; // 与wrapText函数保持一致
        ctx.textAlign = 'left';

        // 计算所有学习建议内容的总行数
        let totalSuggestionLines = 0;
        if (section.selectedSuggestions && section.selectedSuggestions.length > 0) {
          section.selectedSuggestions.forEach(suggestion => {
            const suggestionLines = wrapText(suggestion, colWidths[4] - 40, 26); // 增大字体到26px
            totalSuggestionLines += suggestionLines.length;
          });
        }
        if (section.customSuggestion) {
          const customLines = wrapText(section.customSuggestion, colWidths[4] - 40, 26); // 增大字体到26px
          totalSuggestionLines += customLines.length;
        }
        
        // 计算实际内容总高度（包括间距）- 与高度计算阶段保持一致
        const suggestionItemCount = (section.selectedSuggestions?.length || 0) + (section.customSuggestion ? 1 : 0);
        let actualSuggestionHeight = 0;

        // 重新计算实际高度，与高度计算阶段保持完全一致
        if (section.selectedSuggestions && section.selectedSuggestions.length > 0) {
          section.selectedSuggestions.forEach(suggestion => {
            const suggestionLines = wrapText(suggestion, colWidths[4] - 40, 26); // 增大字体到26px
            actualSuggestionHeight += suggestionLines.length * 32; // 适当增加行高以适应更大字体
            actualSuggestionHeight += 8; // 项目间距
          });
        }

        if (section.customSuggestion) {
          const customLines = wrapText(section.customSuggestion, colWidths[4] - 40, 26); // 增大字体到26px
          actualSuggestionHeight += customLines.length * 32; // 适当增加行高以适应更大字体
        }

        actualSuggestionHeight += 20; // 底部边距，与高度计算阶段一致

        // 修正垂直居中计算：确保内容不贴到边界线，增加边距
        const suggestionCenterY = yPos + rowHeight / 2;
        const suggestionStartY = Math.max(yPos + 20, suggestionCenterY - actualSuggestionHeight / 2); // 增加上边距到20px
        let suggestionYPos = suggestionStartY;

        if (section.selectedSuggestions && section.selectedSuggestions.length > 0) {
          ctx.textBaseline = 'middle'; // 设置文本垂直居中
          section.selectedSuggestions.forEach((suggestion, suggestionIndex) => {
            const suggestionLines = wrapText(suggestion, colWidths[4] - 40, 26); // 增大字体到26px

            // 调试信息
            if (suggestionIndex === 0) {
              console.log(`学习建议文本换行调试 - ${section.name}:`, {
                原始文本: suggestion,
                列宽: colWidths[4],
                可用宽度: colWidths[4] - 40,
                换行结果: suggestionLines,
                行数: suggestionLines.length
              });
            }

            ctx.fillText('• ', suggestionColStartX + 12, suggestionYPos + 16); // 调整bullet点位置
            suggestionLines.forEach((line, lineIndex) => {
              const xOffset = lineIndex === 0 ? 30 : 18;
              ctx.fillText(line, suggestionColStartX + xOffset, suggestionYPos + 16); // 调整文本位置
              suggestionYPos += 32; // 适当增加行高以适应更大字体
            });
            suggestionYPos += 8; // 减小项目间距
          });
        }

        if (section.customSuggestion) {
          ctx.textBaseline = 'middle'; // 设置文本垂直居中
          const customLines = wrapText(section.customSuggestion, colWidths[4] - 40, 26); // 增大字体到26px
          ctx.fillText('• ', suggestionColStartX + 12, suggestionYPos + 16); // 调整bullet点位置
          customLines.forEach((line, lineIndex) => {
            const xOffset = lineIndex === 0 ? 30 : 18;
            ctx.fillText(line, suggestionColStartX + xOffset, suggestionYPos + 16); // 调整文本位置
            suggestionYPos += 32; // 适当增加行高以适应更大字体
          });
        }

        // 如果没有任何学习建议内容，显示提示文字
        if ((!section.selectedSuggestions || section.selectedSuggestions.length === 0) && !section.customSuggestion) {
          ctx.fillStyle = '#999999';
          ctx.font = `${tableContentSize}px Microsoft YaHei`;
          ctx.fillText('暂无建议', suggestionColStartX + 12, yPos + rowHeight / 2);
          ctx.fillStyle = '#333333'; // 恢复颜色
          ctx.font = `${tableContentSize}px Microsoft YaHei`; // 恢复字体
        }
        
        yPos += rowHeight;
      });
      } // 结束详细分析表绘制的条件判断
      
      // 绘制课程规划表格（根据type参数控制）
      const coursePlanData = this.data.coursePlanData || [];
      console.log('课程规划绘制检查:', {
        类型: type,
        条件: 'type === complete || type === courseplan',
        匹配结果: type === 'complete' || type === 'courseplan',
        showCourseplan: this.data.reportConfig.showCourseplan,
        数据长度: coursePlanData.length
      });

      if ((type === 'complete' || type === 'courseplan') && this.data.reportConfig.showCourseplan && coursePlanData.length > 0) {
        // 如果是只绘制课程规划，从标题下方开始
        if (type === 'courseplan') {
          yPos = 400; // 从标题下方开始
        } else {
          yPos += 80; // 增加课程规划标题与详细分析表的间距
        }
        ctx.fillStyle = currentStyle.primary;
        ctx.font = 'bold 39px Microsoft YaHei';
        ctx.textAlign = 'center';
        ctx.fillText('📅 课程规划', canvasWidth / 2, yPos);
        
        yPos += 60;
        
        // 动态计算课程规划表格列宽 - 与详细分析表格保持一致的边距
        const planTableWidth = canvasWidth - tableMargin;
        const planColWidths = [300, planTableWidth - 300];
        
        // 绘制课程规划表格头部
        const planTableX = tableMargin / 2; // 与详细分析表格对齐
        ctx.fillStyle = currentStyle.primary;
        ctx.fillRect(planTableX, yPos, planTableWidth, 68);

        // 绘制表头边框
        ctx.strokeStyle = '#e9ecef';
        ctx.lineWidth = 2;
        ctx.strokeRect(planTableX, yPos, planTableWidth, 68);
        
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 27px Microsoft YaHei';
        ctx.textAlign = 'center';
        
        let planColX = planTableX;

        ctx.fillText(this.data.coursePlanHeaders.lesson || '课次', planColX + planColWidths[0] / 2, yPos + 42);
        planColX += planColWidths[0];
        ctx.fillText(this.data.coursePlanHeaders.content || '学习内容', planColX + planColWidths[1] / 2, yPos + 42);

        // 绘制表头垂直分割线
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(planTableX + planColWidths[0], yPos);
        ctx.lineTo(planTableX + planColWidths[0], yPos + 68);
        ctx.stroke();
        
        yPos += 68;
        
        // 绘制课程规划数据行
        coursePlanData.forEach((item, i) => {
          // 动态计算内容实际需要的高度
          const contentText = item.content || '待填写';
          const contentLines = wrapText(contentText, planColWidths[1] - 40, 22); // 增大字体到22px
          const rowHeight = Math.max(85, contentLines.length * 38 + 35); // 增加行高
          
          // 绘制行背景
          const rowColor = i % 2 === 0 ? '#f8f9fa' : '#ffffff';
          ctx.fillStyle = rowColor;
          ctx.fillRect(planTableX, yPos, planTableWidth, rowHeight);

          // 绘制边框
          ctx.strokeStyle = '#e9ecef';
          ctx.lineWidth = 2;
          ctx.strokeRect(planTableX, yPos, planTableWidth, rowHeight);

          // 绘制垂直分割线
          ctx.strokeStyle = '#e9ecef';
          ctx.beginPath();
          ctx.moveTo(planTableX + planColWidths[0], yPos);
          ctx.lineTo(planTableX + planColWidths[0], yPos + rowHeight);
          ctx.stroke();

          // 绘制单元格内容
          planColX = planTableX;
          
          // 课次
          ctx.fillStyle = currentStyle.primary;
          ctx.font = 'bold 27px Microsoft YaHei';
          ctx.textAlign = 'center';
          ctx.fillText(`第${item.lesson}讲`, planColX + planColWidths[0] / 2, yPos + rowHeight / 2 + 9);
          
          // 学习内容 - 修正垂直居中显示
          ctx.fillStyle = '#333333';
          ctx.font = '27px Microsoft YaHei';
          ctx.textAlign = 'left';
          planColX += planColWidths[0];
          
          // 计算实际内容总高度
          const actualContentHeight = contentLines.length * 38; // 增加行高
          
          // 修正垂直居中计算：单元格中心 - 内容高度的一半 + 字体基线偏移
          const contentCenterY = yPos + rowHeight / 2;
          const contentStartY = contentCenterY - actualContentHeight / 2 + 30; // 调整偏移
          let contentY = contentStartY;
          contentLines.forEach(line => {
            ctx.fillText(line, planColX + 30, contentY);
            contentY += 38; // 增加行高
          });
          
          yPos += rowHeight;
        });
      }
      
      // 绘制底部信息 - 优化间距和美化样式
      yPos += 60; // 调整课程规划与底部时间的间距
      
      // 绘制分隔线
      ctx.strokeStyle = '#e9ecef';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(60, yPos);
      ctx.lineTo(canvasWidth - 60, yPos);
      ctx.stroke();
      
      yPos += 35; // 分隔线后的间距
      
      // 报告生成时间和分析人信息（个性化艺术字体风格）
      const timeText = `生成时间：${this.data.reportData.generatedAt || new Date().toLocaleString('zh-CN')}`;
      let displayText = timeText;

      // 如果有分析人信息，用适当间距分隔
      if (this.data.basicInfo.analystName) {
        const analystText = `分析人：${this.data.basicInfo.analystName}`;
        displayText = `${timeText}　　${analystText}`;
      }
      
      // 普通黑体斜体
      ctx.font = 'italic 28px "Microsoft YaHei", "黑体", sans-serif';
      ctx.fillStyle = '#333333';
      ctx.textAlign = 'center';
      ctx.fillText(displayText, canvasWidth / 2, yPos);
      
      // 计算精确的结束位置（文字底部 + 充足边距）
      const finalYPos = yPos + 50; // 增加底部边距，确保完整显示
      
      // 确保使用计算出的准确高度
      const finalHeight = finalYPos;
      
      console.log('Canvas高度信息:', {
        contentHeight,
        yPos,
        finalYPos,
        finalHeight,
        sectionCount: reportSections.length,
        type: type
      });
      
      // 绘制完成，保存图片
      ctx.draw(false, () => {
        setTimeout(() => {
          // 计算合适的输出分辨率，提高图片清晰度
          const pixelRatio = 2; // 提高像素密度
          const outputWidth = canvasWidth * pixelRatio;
          const outputHeight = finalHeight * pixelRatio;
          
          wx.canvasToTempFilePath({
            x: 0,
            y: 0,
            width: canvasWidth,
            height: finalHeight,
            destWidth: outputWidth,   // 提高输出分辨率
            destHeight: outputHeight, // 提高输出分辨率
            canvasId: 'reportCanvas',
            quality: 1.0, // 最高质量
            fileType: 'png',
            success: (res) => {
              console.log('Canvas生成高清图片成功:', res.tempFilePath);
              resolve(res.tempFilePath);
            },
            fail: (error) => {
              console.error('Canvas生成高清图片失败:', error);
              // 降级处理：如果高分辨率失败，尝试标准分辨率
              setTimeout(() => {
                wx.canvasToTempFilePath({
                  x: 0,
                  y: 0,
                  width: canvasWidth,
                  height: finalHeight,
                  destWidth: canvasWidth,
                  destHeight: finalHeight,
                  canvasId: 'reportCanvas',
                  quality: 0.9, // 高质量
                  fileType: 'png',
                  success: (res) => {
                    console.log('Canvas生成标准图片成功:', res.tempFilePath);
                    resolve(res.tempFilePath);
                  },
                  fail: (fallbackError) => {
                    console.error('Canvas生成标准图片失败:', fallbackError);
                    // 最后降级：低质量jpg
                    setTimeout(() => {
                      wx.canvasToTempFilePath({
                        x: 0,
                        y: 0,
                        width: canvasWidth,
                        height: finalHeight,
                        destWidth: canvasWidth,
                        destHeight: finalHeight,
                        canvasId: 'reportCanvas',
                        quality: 0.8,
                        fileType: 'jpg',
                        success: (res) => {
                          console.log('Canvas生成备用图片成功:', res.tempFilePath);
                          resolve(res.tempFilePath);
                        },
                        fail: (finalError) => {
                          console.error('Canvas生成备用图片失败:', finalError);
                          reject(new Error(`图片生成失败: ${finalError.errMsg || finalError.message || '未知错误'}`));
                        }
                      }, this);
                    }, 300);
                  }
                }, this);
              }, 500);
            }
          }, this);
        }, 2000); // 增加等待时间确保绘制完成，手机端需要更长时间
      });
      } catch (error) {
        console.error('Canvas绘制过程出错:', error);
        let errorMessage = '生成图片失败';
        if (error.message) {
          errorMessage = error.message;
        } else if (typeof error === 'string') {
          errorMessage = error;
        }
        reject(new Error(errorMessage));
      }
    });
  },

  // 分享给朋友
  shareToFriend() {
    wx.showToast({
      title: '可通过右上角分享',
      icon: 'none'
    });
  },

  // 复制链接
  copyLink() {
    if (this.data.reportUrl) {
      wx.setClipboardData({
        data: this.data.reportUrl,
        success() {
          wx.showToast({
            title: '链接已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  // 课程规划表头修改
  onCoursePlanHeaderChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`coursePlanHeaders.${field}`]: value
    });
  },

  // 课程规划内容修改
  onCoursePlanContentChange(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value;
    
    const coursePlanData = [...this.data.coursePlanData];
    coursePlanData[index].content = value;
    
    this.setData({
      coursePlanData
    });
  },

  // 课程规划管理
  addCoursePlanRow() {
    const coursePlanData = [...this.data.coursePlanData];
    const newId = coursePlanData.length > 0 ? Math.max(...coursePlanData.map(item => item.id)) + 1 : 1;
    const newLesson = coursePlanData.length + 1;
    
    coursePlanData.push({
      id: newId,
      lesson: newLesson,
      content: ''
    });
    
    this.setData({
      coursePlanData
    });
  },
  
  removeCoursePlanRow(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    let coursePlanData = [...this.data.coursePlanData];
    
    if (coursePlanData.length <= 1) {
      wx.showToast({
        title: '至少保留一行',
        icon: 'none'
      });
      return;
    }
    
    coursePlanData.splice(index, 1);
    
    // 重新编号课次
    coursePlanData.forEach((item, i) => {
      item.lesson = i + 1;
    });
    
    this.setData({
      coursePlanData
    });
  },

  toggleCoursePlan() {
    this.setData({
      'reportConfig.showCourseplan': !this.data.reportConfig.showCourseplan
    });
  },



















});