<view class="container">
  <view class="form-group">
    <text>生成数量：</text>
    <input type="number" value="{{count}}" bindinput="onCountInput" />
  </view>
  <view class="form-group">
    <text>会员类型：</text>
    <picker range="{{typeOptions}}" value="{{typeIndex}}" bindchange="onTypeChange">
      <view class="picker">{{typeOptions[typeIndex]}}</view>
    </picker>
  </view>
  <view class="form-group">
    <text>时长（天）：</text>
    <input type="number" value="{{duration}}" bindinput="onDurationInput" />
  </view>
  <button class="gen-btn" bindtap="onGenerate">生成兑换码</button>

  <view class="result-section" wx:if="{{codes.length > 0}}">
    <view class="result-title">最新生成的兑换码：</view>
    <view class="code-list">
      <view class="code-item" wx:for="{{codes}}" wx:key="index">
        <text selectable="true">{{item}}</text>
      </view>
    </view>
  </view>
</view> 