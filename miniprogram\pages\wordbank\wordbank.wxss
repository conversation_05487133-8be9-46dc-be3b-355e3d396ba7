/* 词库页面样式 */
.container {
  padding: 32rpx 32rpx 200rpx 32rpx;
  background: #f8fafc;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 自定义导航栏 */
.custom-nav {
  padding-top: var(--status-bar-height, 48rpx);
  margin-bottom: 24rpx;
}

.nav-back {
  display: flex;
  align-items: center;
  color: #333;
  font-size: 32rpx;
}

.back-icon {
  font-size: 48rpx;
  font-weight: bold;
  margin-right: 8rpx;
}

.back-text {
  font-size: 32rpx;
}

/* 分组样式 */
.section {
  margin-bottom: 40rpx;
}

/* 最后一个分组的额外底部间距 */
.section:last-child {
  margin-bottom: 80rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-left: 16rpx;
  border-left: 8rpx solid #3B82F6;
}

/* 词库网格布局 */
.wordbank-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

/* 词库卡片基础样式 */
.wordbank-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.wordbank-card:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}

/* 大学词汇卡片 */
.college-card {
  background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 100%);
  color: white;
}

/* 高考大纲词汇卡片 */
.gaokao-card {
  background: linear-gradient(135deg, #34D399 0%, #10B981 100%);
  color: white;
}

/* 教材词汇卡片 */
.textbook-card {
  background: linear-gradient(135deg, #A78BFA 0%, #8B5CF6 100%);
  color: white;
}

/* 常用短语卡片 */
.phrase-card {
  background: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);
  color: white;
}

/* 题型专项词汇卡片 */
.special-card {
  background: linear-gradient(135deg, #F87171 0%, #EF4444 100%);
  color: white;
}

/* 其他词汇卡片 */
.other-card {
  background: linear-gradient(135deg, #FBBF24 0%, #F59E0B 100%);
  color: white;
}

/* 中考词汇卡片 */
.zhongkao-card {
  background: linear-gradient(135deg, #818CF8 0%, #6366F1 100%);
  color: white;
}

/* VIP 词库样式 */
.wordbank-card.vip {
  position: relative;
}

.wordbank-card.vip::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-left: 40rpx solid transparent;
  border-top: 40rpx solid rgba(255, 215, 0, 0.8);
}

.wordbank-card.vip::after {
  content: '👑';
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  font-size: 24rpx;
  z-index: 1;
}

/* 词库图标 */
.wordbank-icon {
  text-align: center;
  margin-bottom: 20rpx;
  position: relative;
}

.icon-text {
  font-size: 48rpx;
  display: block;
}

/* 上传中标签 */
.upload-tag {
  position: absolute;
  top: -8rpx;
  right: -20rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
}

/* 词库信息 */
.wordbank-info {
  text-align: center;
}

.wordbank-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.wordbank-count {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 响应式布局 */
@media (max-width: 600rpx) {
  .wordbank-grid {
    grid-template-columns: 1fr;
  }
  
  .wordbank-card {
    display: flex;
    align-items: center;
    padding: 24rpx;
  }
  
  .wordbank-icon {
    margin-right: 24rpx;
    margin-bottom: 0;
  }
  
  .wordbank-info {
    text-align: left;
    flex: 1;
  }
  
  .icon-text {
    font-size: 40rpx;
  }
}

/* 批量操作工具栏 */
.batch-toolbar {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.toolbar-normal {
  display: flex;
  justify-content: flex-end;
}

.toolbar-batch {
  display: flex;
  gap: 20rpx;
}

.batch-btn {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  border-radius: 8rpx;
  border: none;
  line-height: 1.4;
}

.batch-btn.cancel {
  background-color: #f0f0f0;
  color: #666;
}

.batch-btn.select-all {
  background-color: #007AFF;
  color: white;
}

.batch-btn.delete {
  background-color: #ff4757;
  color: white;
}

.batch-btn:not(.cancel):not(.select-all):not(.delete) {
  background-color: #007AFF;
  color: white;
}

/* 自定义词库列表样式 */
.custom-wordbank-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  padding: 0 10rpx;
}

.custom-wordbank-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20rpx 16rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  position: relative;
  height: auto;
}

.custom-wordbank-item.selected {
  border-color: #007AFF;
  background-color: #f0f8ff;
}

.custom-wordbank-item:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

.selection-checkbox {
  margin-right: 12rpx;
  flex-shrink: 0;
}

/* 隐藏不需要的样式 */
.custom-icon {
  display: none;
}

.icon-emoji {
  display: none;
}

.custom-info {
  display: none;
}

.custom-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
  padding-left: 12rpx;
}

.custom-meta {
  display: none;
}

.custom-count {
  font-size: 22rpx;
  color: #007AFF;
  font-weight: 500;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.custom-time {
  display: none;
}

.custom-actions {
  display: flex;
  gap: 6rpx;
  flex-shrink: 0;
  margin-left: 8rpx;
}

.action-btn {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-btn.edit {
  border: 2rpx solid #007AFF;
}

.action-btn.delete {
  border: 2rpx solid #ff4757;
}

.action-btn:hover {
  transform: scale(1.1);
}

.action-icon {
  font-size: 16rpx;
}

.custom-arrow {
  display: none; /* 不显示箭头，因为是卡片式布局 */
}

/* 自定义词库卡片（老样式，保留兼容） */
.custom-card {
  background: linear-gradient(135deg, #FF9A8B 0%, #A8E6CF 100%);
  color: white;
  position: relative;
}

/* 创建词库卡片 */
.create-wordbank-card {
  background: rgba(255, 255, 255, 0.95);
  border: 2rpx dashed #007bff;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  transition: all 0.3s ease;
}

.create-wordbank-card:active {
  transform: scale(0.95);
  background: rgba(0, 123, 255, 0.1);
}

.create-icon {
  font-size: 48rpx;
  color: #007bff;
  margin-right: 24rpx;
}

.create-text {
  flex: 1;
}

.create-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.create-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 词库操作按钮 */
.wordbank-actions {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  gap: 8rpx;
}

.action-btn {
  width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.4);
  transform: scale(0.9);
}

.action-icon {
  font-size: 16rpx;
}

/* 格式说明 */
.format-tips {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 24rpx;
}

.tips-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.tips-item {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

/* 创建词库弹窗 */
.create-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background: #ffffff;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
  cursor: pointer;
}

.modal-body {
  padding: 32rpx;
  overflow-y: auto;
  flex: 1;
  -webkit-overflow-scrolling: touch;
}

/* 确保内容可以滚动 */
.modal-body::-webkit-scrollbar {
  width: 4rpx;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2rpx;
}

.modal-footer {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 16rpx;
  background: #fff;
  flex-shrink: 0;
}

/* 新的表单样式 */
.form-group {
  margin-bottom: 24rpx;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 72rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #007bff;
  background: white;
  box-shadow: 0 0 0 3rpx rgba(0, 123, 255, 0.1);
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 16rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 26rpx;
  background: #fafafa;
  box-sizing: border-box;
  resize: none;
  line-height: 1.5;
  transition: all 0.3s ease;
}

.form-textarea:focus {
  border-color: #007bff;
  background: white;
  box-shadow: 0 0 0 3rpx rgba(0, 123, 255, 0.1);
}

/* 输入方式选项卡 */
.input-method-tabs {
  display: flex;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 4rpx;
  gap: 4rpx;
}

.method-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 12rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.method-tab.active {
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-icon {
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.tab-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

.method-tab.active .tab-text {
  color: #007bff;
}

/* Excel上传区域 */
.excel-upload-section {
  margin-bottom: 0;
}

.file-upload-area {
  border: 2rpx dashed #d0d7de;
  border-radius: 12rpx;
  padding: 40rpx 20rpx;
  text-align: center;
  background: #f6f8fa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: #007bff;
  background: rgba(0, 123, 255, 0.05);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  color: #6c757d;
}

.upload-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.upload-subtitle {
  font-size: 22rpx;
  color: #666;
}

/* 文件信息卡片 */
.file-info-card {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.file-preview {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.file-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
  color: #007bff;
}

.file-details {
  flex: 1;
}

.file-name {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.file-size {
  display: block;
  font-size: 22rpx;
  color: #666;
}

.file-actions {
  display: flex;
  gap: 12rpx;
}

.file-action-btn {
  flex: 1;
  padding: 12rpx 16rpx;
  border: 2rpx solid #dee2e6;
  border-radius: 8rpx;
  background: white;
  color: #666;
  font-size: 22rpx;
  transition: all 0.3s ease;
}

.file-action-btn:active {
  transform: scale(0.95);
}

.file-action-btn.danger {
  color: #dc3545;
  border-color: #dc3545;
}

/* 处理选项 */
.process-options {
  margin-bottom: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  gap: 12rpx;
}

.option-label {
  font-size: 24rpx;
  color: #333;
}

/* 格式帮助 */
.format-help {
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  background: #f8f9fa;
}

.help-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  cursor: pointer;
}

.help-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
}

.help-title {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.help-toggle {
  font-size: 22rpx;
  color: #007bff;
}

.help-content {
  padding: 0 20rpx 20rpx;
  border-top: 1rpx solid #e9ecef;
  background: white;
}

.help-section {
  margin-bottom: 16rpx;
}

.help-subtitle {
  display: block;
  font-size: 22rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.help-text {
  display: block;
  font-size: 20rpx;
  color: #666;
  margin-bottom: 4rpx;
  line-height: 1.4;
}

.example-table {
  border: 1rpx solid #dee2e6;
  border-radius: 6rpx;
  overflow: hidden;
  margin-top: 8rpx;
}

.table-row {
  display: flex;
}

.table-row.header {
  background: #f1f3f4;
}

.table-cell {
  flex: 1;
  padding: 8rpx 6rpx;
  font-size: 18rpx;
  border-right: 1rpx solid #dee2e6;
  text-align: center;
  min-width: 0;
  word-break: break-all;
}

/* 针对4列表格的特殊布局 */
.excel-example-table .table-cell:nth-child(1) {
  flex: 1.2; /* words列稍宽 */
}

.excel-example-table .table-cell:nth-child(2) {
  flex: 1.5; /* phonetic列最宽 */
}

.excel-example-table .table-cell:nth-child(3) {
  flex: 1.2; /* meaning列稍宽 */
}

.excel-example-table .table-cell:nth-child(4) {
  flex: 2; /* example列最宽 */
}

.table-cell:last-child {
  border-right: none;
}

.table-row.header .table-cell {
  font-weight: 500;
  color: #333;
}

/* 弹窗按钮 */
.modal-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
}

.modal-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #dee2e6;
}

.modal-btn.secondary:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.modal-btn.primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(0, 123, 255, 0.3);
}

.modal-btn.primary:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 123, 255, 0.2);
}

.modal-btn.primary:disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
  cursor: not-allowed;
}

.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input-field {
  width: 100%;
  height: 80rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
  line-height: 1.4;
}

.input-field:focus {
  border-color: #007bff;
  background: white;
}

.textarea-field {
  width: 100%;
  min-height: 300rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 26rpx;
  background: #fafafa;
  box-sizing: border-box;
  resize: none;
}

.textarea-field:focus {
  border-color: #007bff;
  background: white;
}

.char-count {
  display: block;
  text-align: right;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

.btn {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140rpx;
}

.btn.primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(0, 123, 255, 0.3);
}

.btn.primary:disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
}

.btn.primary:not(:disabled):active {
  background: linear-gradient(135deg, #0056b3 0%, #004494 100%);
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 123, 255, 0.2);
}

.btn.secondary {
  background: white;
  color: #666;
  border: 2rpx solid #dee2e6;
}

.btn.secondary:active {
  background: #f0f0f0;
  transform: translateY(2rpx);
}

/* 输入方式选择 */
.input-methods {
  display: flex;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.method-btn {
  flex: 1;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #fafafa;
  color: #666;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.method-btn.active {
  border-color: #007bff;
  background: #007bff;
  color: white;
}

.method-btn:active {
  transform: scale(0.95);
}

/* Excel上传区域 */
.excel-upload-area {
  border: 2rpx dashed #007bff;
  border-radius: 12rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  background: rgba(0, 123, 255, 0.05);
  transition: all 0.3s ease;
}

.excel-upload-area:active {
  background: rgba(0, 123, 255, 0.1);
  transform: scale(0.98);
}

.upload-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
}

.upload-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.upload-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 已选择文件 */
.selected-file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin: 16rpx 0;
}

.file-name {
  flex: 1;
  font-size: 24rpx;
  color: #333;
}

.remove-file-btn {
  padding: 8rpx 16rpx;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 20rpx;
}

.remove-file-btn:active {
  background: #c82333;
}

/* Excel格式说明 */
.excel-format-tips {
  background: rgba(255, 193, 7, 0.1);
  border-left: 4rpx solid #ffc107;
  padding: 20rpx;
  margin-top: 16rpx;
  border-radius: 8rpx;
}

/* Excel格式要求说明 */
.excel-format-requirements {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 2rpx solid #e3f2fd;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.requirement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 4rpx 0;
}

.requirement-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1976d2;
}

.toggle-icon {
  font-size: 20rpx;
  color: #1976d2;
  transition: transform 0.3s ease;
}

.requirement-content {
  margin-top: 16rpx;
  border-top: 1rpx solid #e3f2fd;
  padding-top: 16rpx;
}

.requirement-list {
  margin-bottom: 20rpx;
}

.requirement-item {
  display: block;
  font-size: 24rpx;
  color: #444;
  line-height: 1.6;
  margin-bottom: 8rpx;
  padding-left: 12rpx;
}

.highlight {
  background: #fff3cd;
  color: #856404;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
  font-weight: 600;
}

/* Excel示例表格 */
.excel-example-table {
  margin: 12rpx 0 0 0;
}

.example-title {
  display: block;
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.table-container {
  border: 2rpx solid #dee2e6;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #dee2e6;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row.header {
  background: linear-gradient(135deg, #007AFF 0%, #0056b3 100%);
}

.table-row.header .table-cell {
  color: white;
  font-weight: 600;
}

.table-cell {
  flex: 1;
  padding: 16rpx 12rpx;
  font-size: 22rpx;
  color: #333;
  text-align: center;
  border-right: 1rpx solid #dee2e6;
  word-break: break-all;
}

.table-cell:last-child {
  border-right: none;
}

.table-row:nth-child(even):not(.header) {
  background-color: #f8f9fa;
}

/* 模板下载按钮 */
.template-download {
  margin-top: 12rpx;
  text-align: center;
}

.template-btn {
  display: inline-flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
}

.template-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(40, 167, 69, 0.4);
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 22rpx;
}

.example-cell {
  flex: 1;
  padding: 15rpx 20rpx;
  font-size: 24rpx;
  text-align: center;
  border-right: 1rpx solid #dee2e6;
  color: #333;
}

.example-cell:last-child {
  border-right: none;
}

/* ====== 新的Excel处理界面样式 ====== */

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  padding: 24rpx 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.step.active {
  opacity: 1;
  color: #007bff;
}

.step.completed {
  opacity: 1;
  color: #28a745;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  background: #e9ecef;
  color: #6c757d;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: #007bff;
  color: white;
}

.step.completed .step-number {
  background: #28a745;
  color: white;
}

.step-text {
  font-size: 22rpx;
  font-weight: 500;
}

.step-line {
  width: 60rpx;
  height: 4rpx;
  background: #e9ecef;
  margin: 0 20rpx;
  border-radius: 2rpx;
  transition: all 0.3s ease;
}

.step-line.completed {
  background: #28a745;
}

/* 文件已选择容器 */
.file-selected-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 已选择文件卡片 */
.selected-file-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2rpx solid #dee2e6;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.file-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.file-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
  color: #007bff;
}

.file-details {
  flex: 1;
}

.file-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.file-size {
  display: block;
  font-size: 22rpx;
  color: #6c757d;
}

.file-actions {
  display: flex;
  gap: 12rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  border: none;
  transition: all 0.2s ease;
}

.change-btn {
  background: #007bff;
  color: white;
}

.change-btn:active {
  background: #0056b3;
  transform: translateY(1rpx);
}

.remove-btn {
  background: #dc3545;
  color: white;
}

.remove-btn:active {
  background: #c82333;
  transform: translateY(1rpx);
}

/* 处理选项 */
.process-options {
  background: white;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.option-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.option-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.option-item:last-child {
  margin-bottom: 0;
}

.option-item checkbox {
  margin-right: 12rpx;
  margin-top: 2rpx;
}

.option-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.option-desc {
  font-size: 22rpx;
  color: #6c757d;
  line-height: 1.4;
}

/* 处理操作 */
.process-action {
  text-align: center;
}

.process-btn {
  width: 100%;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
  margin-bottom: 16rpx;
}

.process-btn:disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
}

.process-btn:not(:disabled):active {
  background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.2);
}

.process-note {
  font-size: 22rpx;
  color: #6c757d;
  line-height: 1.4;
}

/* Excel帮助（可折叠） */
.excel-help {
  margin-top: 24rpx;
}

.help-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: rgba(13, 110, 253, 0.1);
  border: 1rpx solid rgba(13, 110, 253, 0.2);
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.help-toggle:active {
  background: rgba(13, 110, 253, 0.15);
}

.help-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.help-text {
  flex: 1;
  font-size: 26rpx;
  font-weight: 500;
  color: #0d6efd;
}

.help-arrow {
  font-size: 24rpx;
  color: #0d6efd;
  transition: transform 0.2s ease;
}

.help-content {
  margin-top: 16rpx;
  background: white;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  animation: expandIn 0.3s ease-out;
}

@keyframes expandIn {
  from {
    opacity: 0;
    max-height: 0;
    padding: 0 24rpx;
  }
  to {
    opacity: 1;
    max-height: 1000rpx;
    padding: 24rpx;
  }
}

.help-requirements {
  margin-bottom: 24rpx;
}

.help-title {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.help-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.help-example .example-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
} 