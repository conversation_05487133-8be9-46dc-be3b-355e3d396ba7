const app = getApp()

Page({
  data: {
    // 今日学习统计
    todayGoal: 0,
    learnedCount: 0,
    remainingCount: 0,
    progress: 0,
    
    // 学习计划
    isEditing: false,
    dailyGoal: 20,
    studyTime: '30',
    reminderTime: '20:00',
    
    // 学习记录
    records: [],
    
    // 时间选择器
    timeArray: Array.from({length: 24}, (_, i) => i.toString().padStart(2, '0')),
    minuteArray: Array.from({length: 60}, (_, i) => i.toString().padStart(2, '0')),
    timeIndex: [20, 0]
  },

  onLoad: function() {
    this.loadUserPlan()
    this.loadTodayStats()
    this.loadLearningRecords()
  },

  // 加载用户学习计划
  loadUserPlan: function() {
    const db = wx.cloud.database()
    db.collection('user_plans').where({
      _openid: app.globalData.openid
    }).get().then(res => {
      if (res.data.length > 0) {
        const plan = res.data[0]
        this.setData({
          dailyGoal: plan.dailyGoal,
          studyTime: plan.studyTime,
          reminderTime: plan.reminderTime
        })
      }
    })
  },

  // 加载今日学习统计
  loadTodayStats: function() {
    const db = wx.cloud.database()
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    db.collection('learning_records').where({
      _openid: app.globalData.openid,
      date: db.command.gte(today)
    }).get().then(res => {
      const learnedCount = res.data.length
      const remainingCount = Math.max(0, this.data.dailyGoal - learnedCount)
      const progress = this.data.dailyGoal > 0 ? (learnedCount / this.data.dailyGoal) * 100 : 0
      
      this.setData({
        todayGoal: this.data.dailyGoal,
        learnedCount,
        remainingCount,
        progress
      })
    })
  },

  // 加载学习记录
  loadLearningRecords: function() {
    const db = wx.cloud.database()
    db.collection('learning_records')
      .where({
        _openid: app.globalData.openid
      })
      .orderBy('date', 'desc')
      .limit(7)
      .get().then(res => {
        const records = res.data.map(record => ({
          ...record,
          date: this.formatDate(record.date),
          week: this.getWeekDay(record.date)
        }))
        
        this.setData({ records })
      })
  },

  // 切换编辑状态
  toggleEdit: function() {
    this.setData({
      isEditing: !this.data.isEditing
    })
  },

  // 保存学习计划
  savePlan: function() {
    const db = wx.cloud.database()
    const plan = {
      dailyGoal: this.data.dailyGoal,
      studyTime: this.data.studyTime,
      reminderTime: this.data.reminderTime,
      updateTime: new Date()
    }

    db.collection('user_plans').where({
      _openid: app.globalData.openid
    }).get().then(res => {
      if (res.data.length > 0) {
        // 更新现有计划
        db.collection('user_plans').doc(res.data[0]._id).update({
          data: plan
        }).then(() => {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          })
          this.setData({ isEditing: false })
          this.loadTodayStats()
        })
      } else {
        // 创建新计划
        db.collection('user_plans').add({
          data: plan
        }).then(() => {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          })
          this.setData({ isEditing: false })
          this.loadTodayStats()
        })
      }
    })
  },

  // 每日目标输入处理
  onDailyGoalInput: function(e) {
    this.setData({
      dailyGoal: parseInt(e.detail.value) || 0
    })
  },

  // 学习时间输入处理
  onStudyTimeInput: function(e) {
    this.setData({
      studyTime: e.detail.value
    })
  },

  // 提醒时间选择处理
  onReminderTimeChange: function(e) {
    const [hour, minute] = e.detail.value
    this.setData({
      reminderTime: `${hour}:${minute}`,
      timeIndex: e.detail.value
    })
  },

  // 开始学习
  startLearning: function() {
    wx.navigateTo({
      url: '/pages/learning/learning'
    })
  },

  // 复习今日单词
  reviewToday: function() {
    wx.navigateTo({
      url: '/pages/review/review'
    })
  },

  // 格式化日期
  formatDate: function(date) {
    date = new Date(date)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 获取星期几
  getWeekDay: function(date) {
    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return weekDays[new Date(date).getDay()]
  }
}) 