<!--pages/profile/received/received.wxml-->
<view class="received-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">📨 收到的分享</text>
      <text class="page-subtitle">管理您参与的分享测试</text>
    </view>
    <view class="header-stats" wx:if="{{statistics.totalShares > 0}}">
      <view class="stat-item">
        <text class="stat-number">{{statistics.totalShares}}</text>
        <text class="stat-label">个分享</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.totalTests}}</text>
        <text class="stat-label">次测试</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.averageScore}}</text>
        <text class="stat-label">平均分</text>
      </view>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-row">
      <view class="input-container">
        <input
          class="share-id-input"
          placeholder="请输入测试ID"
          value="{{shareId}}"
          bindinput="onShareIdInput"
          maxlength="50"
        />
        <button
          class="clear-btn"
          wx:if="{{shareId}}"
          bindtap="clearInput"
        >
          ×
        </button>
      </view>

      <button
        class="join-btn"
        bindtap="joinShareTest"
        loading="{{loading}}"
        disabled="{{!shareId || loading}}"
      >
        {{loading ? '检查中...' : '参与测试'}}
      </button>
    </view>
  </view>

  <!-- 分享测试按模式分组列表 -->
  <view class="shares-section" wx:if="{{modeGroups.length > 0}}">
    <!-- 工具栏 -->
    <view class="toolbar" style="padding: 16rpx 20rpx; background: white; display: flex; justify-content: flex-start; align-items: center; border-radius: 12rpx; margin-bottom: 16rpx; box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06); border: 1rpx solid #f0f0f0; gap: 12rpx;">
      <!-- 全选按钮 -->
      <button class="toolbar-btn" bindtap="toggleSelectAll" style="background: #f8f9fa; color: #333; border: 1rpx solid #e0e0e0; border-radius: 8rpx; padding: 8rpx 12rpx; font-size: 24rpx; font-weight: 500; line-height: 1.2; white-space: nowrap; text-align: center; min-width: 70rpx; height: 50rpx; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
        {{allSelected ? '取消全选' : '全选'}}
      </button>

      <!-- 分享数量文字 -->
      <text class="shares-count" style="font-size: 24rpx; color: #666; font-weight: 500; background: #f8f9fa; padding: 6rpx 12rpx; border-radius: 12rpx; border: 1rpx solid #e9ecef; white-space: nowrap; height: 36rpx; line-height: 36rpx; flex-shrink: 0;">共{{statistics.totalShares}}个分享</text>

      <!-- 弹性空间 -->
      <view style="flex: 1;"></view>

      <!-- 删除按钮 -->
      <button class="toolbar-btn danger" bindtap="batchDeleteShares" style="background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%); color: white; border: none; border-radius: 8rpx; padding: 8rpx 10rpx; font-size: 20rpx; min-width: 80rpx; box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3); line-height: 1.2; white-space: nowrap; text-align: center; height: 50rpx; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
        删除选中
      </button>
    </view>

    <!-- 模式分组列表 -->
    <view class="mode-groups">
      <view
        class="mode-group"
        wx:for="{{modeGroups}}"
        wx:key="mode"
        wx:for-item="modeGroup"
      >
        <!-- 模式标题栏 -->
        <view
          class="mode-header"
          bindtap="toggleModeExpand"
          data-mode="{{modeGroup.mode}}"
        >
          <view class="mode-info">
            <text class="mode-icon">{{modeGroup.icon}}</text>
            <text class="mode-title">{{modeGroup.name}}</text>
            <text class="mode-count">({{modeGroup.stats.totalShares}})</text>
          </view>
          <view class="mode-stats" wx:if="{{modeGroup.expanded}}">
            <text class="stat">{{modeGroup.stats.totalTests}}次测试</text>
            <text class="stat">平均{{modeGroup.stats.averageScore}}分</text>
          </view>
          <view class="mode-actions">
            <text class="expand-icon {{modeGroup.expanded ? 'expanded' : ''}}">▼</text>
          </view>
        </view>

        <!-- 分享列表 -->
        <view
          class="shares-list {{modeGroup.expanded ? 'expanded' : ''}}"
          wx:if="{{modeGroup.expanded && modeGroup.shares.length > 0}}"
        >
          <view
            class="share-item {{item.isExpired ? 'expired' : ''}}"
            wx:for="{{modeGroup.shares}}"
            wx:key="shareId"
          >
        <!-- 选择框和内容的容器 -->
        <view class="share-main">
          <!-- 选择框 -->
          <view class="share-checkbox" bindtap="toggleSelectShare" data-mode="{{modeGroup.mode}}" data-index="{{index}}">
            <view class="checkbox {{item.isSelected ? 'checked' : ''}}">
              <text wx:if="{{item.isSelected}}" class="checkbox-icon">✓</text>
            </view>
          </view>

          <!-- 分享信息 -->
          <view class="share-content" bindtap="viewShareDetail" data-mode="{{modeGroup.mode}}" data-index="{{index}}">
            <!-- 标题和状态 -->
            <view class="share-header">
              <view class="title-row">
                <text class="share-title">{{item.shareTitle}}</text>
                <text class="share-status {{item.isExpired ? 'expired' : 'active'}}">
                  {{item.isExpired ? '已过期' : '进行中'}}
                </text>
              </view>
              <view class="share-badges">
                <text class="test-mode-badge">{{getTestModeText(item.testMode)}}</text>
              </view>
            </view>

            <!-- 分享人信息 -->
            <view class="creator-info">
              <image
                class="creator-avatar"
                src="{{item.creatorInfo.avatarUrl || 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'}}"
                mode="aspectFill"
                binderror="onAvatarError"
                data-index="{{index}}"
              ></image>
              <text class="creator-name">{{item.displayCreatorName}} 分享</text>
              <text class="create-time">{{item.createTimeText}}</text>
            </view>

            <!-- 分享ID和多关卡标识 -->
            <view class="share-meta">
              <view class="share-id-row">
                <text class="share-id">ID: {{item.shareId}}</text>
                <button class="copy-id-btn" catchtap="copyShareId" data-share-id="{{item.shareId}}">
                  <text class="copy-icon">📋</text>
                </button>
              </view>
              <view wx:if="{{item.isMultiLevel}}" class="multi-level-info">
                <text class="multi-level-tag">多关卡</text>
              </view>
            </view>

            <!-- 多关卡进度 -->
            <view wx:if="{{item.isMultiLevel}}" class="level-progress">
              <text class="progress-text">{{item.levelProgressText}}</text>
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{(item.myInfo.progress && item.myInfo.progress.completedLevels ? item.myInfo.progress.completedLevels.length : 0) / (item.totalLevels || 1) * 100}}%;"></view>
              </view>
            </view>

            <!-- 测试数据 -->
            <view class="test-stats">
              <view class="stat-row">
                <view class="stat-item">
                  <text class="stat-value">{{item.myInfo.testCount || 0}}</text>
                  <text class="stat-label">次测试</text>
                </view>
                <view class="stat-item">
                  <text class="stat-value">{{item.myInfo.bestScore || 0}}</text>
                  <text class="stat-label">最高分</text>
                </view>
                <view class="stat-item">
                  <text class="stat-value">{{item.myInfo.latestScore || 0}}</text>
                  <text class="stat-label">最新分</text>
                </view>
                <!-- 消消乐模式不显示正确率 -->
                <view wx:if="{{item.testMode !== 'elimination'}}" class="stat-item">
                  <text class="stat-value">{{item.myInfo.latestAccuracy || 0}}%</text>
                  <text class="stat-label">正确率</text>
                </view>
                <!-- 消消乐模式显示用时（如果有的话） -->
                <view wx:if="{{item.testMode === 'elimination' && item.myInfo.latestTime}}" class="stat-item">
                  <text class="stat-value">{{item.myInfo.latestTime}}</text>
                  <text class="stat-label">用时</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="share-actions">
          <!-- 主要操作按钮 -->
          <button
            wx:if="{{item.isMultiLevel && !item.isExpired}}"
            class="action-btn primary"
            bindtap="selectLevelToStart"
            data-mode="{{modeGroup.mode}}"
            data-index="{{index}}"
          >
            选择关卡开始
          </button>
          <button
            wx:elif="{{!item.isMultiLevel && !item.isExpired}}"
            class="action-btn primary"
            bindtap="startSingleTest"
            data-mode="{{modeGroup.mode}}"
            data-index="{{index}}"
          >
            {{(item.myInfo && item.myInfo.testCount > 0) ? '重新测试' : '开始测试'}}
          </button>
          <button
            wx:else
            class="action-btn disabled"
            disabled="true"
          >
            已过期
          </button>

          <!-- 查看详情按钮 -->
          <button
            class="action-btn secondary"
            bindtap="viewShareDetail"
            data-mode="{{modeGroup.mode}}"
            data-index="{{index}}"
          >
            查看详情
          </button>
        </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-section" wx:if="{{modeGroups.length === 0 && !loading}}">
    <view class="empty-icon">📝</view>
    <text class="empty-text">还没有参与过分享测试</text>
    <text class="empty-desc">输入朋友分享的测试ID开始第一次测试吧</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>


</view>

<!-- 详情弹窗 -->
<view wx:if="{{showDetailModal}}" class="modal-overlay" bindtap="closeDetailModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">测试详情</text>
      <text class="modal-close" bindtap="closeDetailModal">×</text>
    </view>
    
    <view class="modal-body">
      <view class="detail-section">
        <view class="detail-row">
          <text class="detail-label">测试名称:</text>
          <text class="detail-value">{{currentDetail.shareTitle}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">测试类型:</text>
          <text class="detail-value">{{getTestModeText(currentDetail.testType)}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">测试ID:</text>
          <text class="detail-value">{{currentDetail.shareId}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">首次参与:</text>
          <text class="detail-value">{{currentDetail.firstVisitTimeText}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">最近测试:</text>
          <text class="detail-value">{{currentDetail.lastTestTimeText}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">测试次数:</text>
          <text class="detail-value">{{currentDetail.myInfo.testCount || 0}}次</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">最高分:</text>
          <text class="detail-value">{{currentDetail.myInfo.bestScore || 0}}分</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">平均分:</text>
          <text class="detail-value">{{currentDetail.averageScore}}分</text>
        </view>
        
        <!-- 多关卡任务详情 -->
        <view wx:if="{{currentDetail.isMultiLevel}}" class="level-detail">
          <view class="detail-row">
            <text class="detail-label">总关卡数:</text>
            <text class="detail-value">{{currentDetail.totalLevels}}关</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">当前关卡:</text>
            <text class="detail-value">第{{currentDetail.myInfo.progress.currentLevel || 1}}关</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">完成关卡:</text>
            <text class="detail-value">{{currentDetail.myInfo.progress.completedLevels.length || 0}}关</text>
          </view>
          
          <!-- 关卡数据详情 -->
          <view class="level-scores">
            <view class="section-title">关卡数据</view>
            <view class="level-score-list">
              <view
                wx:for="{{currentDetail.levelDataWithAccuracy}}"
                wx:key="levelIndex"
                class="level-score-item"
              >
                <view class="level-header">
                  <text class="level-name">第{{item.levelIndex + 1}}关</text>
                  <view class="level-stats">
                    <text class="level-score">{{item.score}}分</text>
                    <!-- 消消乐模式不显示正确率，其他模式只有在有正确率数据时才显示 -->
                    <text wx:if="{{!item.isEliminationMode && item.accuracy !== null}}" class="level-accuracy">正确率{{item.accuracy}}%</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>