<!--pages/teacher-tools/article-analysis/article-analysis.wxml-->
<view class="container">
  <!-- 输入文章区域 -->
  <view class="input-section" wx:if="{{currentStep === 0}}">
    <view class="section-header">
      <text class="section-title">📝 文章词汇识别标记</text>
      <text class="section-subtitle">输入英语文章内容，点击词库按钮即可标记对应词汇</text>
    </view>

    <!-- 词库多选区域 -->
    <view class="vocab-selection-area">
      <text class="selection-title">选择词库（可多选）：</text>
      <view class="vocab-checkboxes">
        <view
          class="vocab-checkbox {{gaokaoSelected ? 'selected' : ''}}"
          data-type="gaokao"
          bindtap="toggleVocabType"
        >
          <view class="checkbox-icon">
            <text wx:if="{{gaokaoSelected}}">✓</text>
          </view>
          <text class="checkbox-label">高考</text>
        </view>
        <view
          class="vocab-checkbox {{cet4Selected ? 'selected' : ''}}"
          data-type="cet4"
          bindtap="toggleVocabType"
        >
          <view class="checkbox-icon">
            <text wx:if="{{cet4Selected}}">✓</text>
          </view>
          <text class="checkbox-label">四级</text>
        </view>
        <view
          class="vocab-checkbox {{cet6Selected ? 'selected' : ''}}"
          data-type="cet6"
          bindtap="toggleVocabType"
        >
          <view class="checkbox-icon">
            <text wx:if="{{cet6Selected}}">✓</text>
          </view>
          <text class="checkbox-label">六级</text>
        </view>
      </view>

      <!-- 开始分析按钮 -->
      <button
        class="start-analysis-btn {{articleText.length > 0 && selectedVocabTypes.length > 0 ? 'enabled' : 'disabled'}}"
        bindtap="startAnalysis"
        disabled="{{articleText.length === 0 || selectedVocabTypes.length === 0 || loading}}"
      >
        {{loading ? '分析中...' : '开始分析'}}
      </button>
    </view>

    <view class="input-area">
      <textarea
        class="article-input"
        placeholder="请输入英语文章内容..."
        value="{{articleText}}"
        bindinput="onArticleInput"
        auto-height
        maxlength="10000"
        show-confirm-bar="{{false}}"
      ></textarea>

      <view class="input-actions">
        <view class="char-count">{{articleText.length}}/10000</view>
        <view class="action-buttons-row">
          <button class="action-btn secondary" bindtap="pasteFromClipboard">
            📋 粘贴
          </button>
          <button class="action-btn secondary" bindtap="loadSampleArticle">
            📖 导入示例
          </button>
        </view>
        <button class="action-btn danger" bindtap="clearArticle" wx:if="{{articleText}}">
          🗑️ 清空
        </button>
      </view>
    </view>
  </view>

  <!-- 标注结果 -->
  <view class="result-section" wx:if="{{currentStep === 1}}">
    <!-- 结果头部 -->
    <view class="result-header">
      <view class="result-title">
        <text class="title-text">📄 标注结果</text>
        <view class="vocab-indicator">
          <text class="current-vocab">
            多词库同时标注
          </text>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="stats-info">
        <view class="stat-item">
          <text class="stat-value">{{stats.totalWords}}</text>
          <text class="stat-label">总词数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{stats.markedWords}}</text>
          <text class="stat-label">标注词数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{stats.uniqueWords}}</text>
          <text class="stat-label">去重词数</text>
        </view>
        <view class="stat-item highlight">
          <text class="stat-value">{{stats.coverage}}%</text>
          <text class="stat-label">覆盖率</text>
        </view>
      </view>

      <!-- 各词库详细统计 -->
      <view class="detailed-stats" wx:if="{{selectedVocabTypes.length > 1}}">
        <view class="detailed-stat-item" wx:if="{{selectedVocabTypes.indexOf('gaokao') !== -1}}">
          <text class="stat-label-small">高考词汇：</text>
          <text class="stat-value-small" style="color: #F44336;">{{stats.gaokaoWords}}</text>
        </view>
        <view class="detailed-stat-item" wx:if="{{selectedVocabTypes.indexOf('cet4') !== -1}}">
          <text class="stat-label-small">四级词汇：</text>
          <text class="stat-value-small" style="color: #2196F3;">{{stats.cet4Words}}</text>
        </view>
        <view class="detailed-stat-item" wx:if="{{selectedVocabTypes.indexOf('cet6') !== -1}}">
          <text class="stat-label-small">六级词汇：</text>
          <text class="stat-value-small" style="color: #4CAF50;">{{stats.cet6Words}}</text>
        </view>
      </view>
    </view>

    <!-- 词库选择管理 -->
    <view class="vocab-management-bar">
      <text class="management-label">词库选择：</text>
      <view class="vocab-toggles">
        <view
          class="vocab-toggle gaokao {{selectedVocabTypes.indexOf('gaokao') !== -1 ? 'active' : ''}}"
          data-type="gaokao"
          bindtap="toggleVocabType"
          disabled="{{loading}}"
        >
          <text class="toggle-text">高考</text>
          <text class="toggle-icon" wx:if="{{selectedVocabTypes.indexOf('gaokao') !== -1}}">✓</text>
        </view>
        <view
          class="vocab-toggle cet4 {{selectedVocabTypes.indexOf('cet4') !== -1 ? 'active' : ''}}"
          data-type="cet4"
          bindtap="toggleVocabType"
          disabled="{{loading}}"
        >
          <text class="toggle-text">四级</text>
          <text class="toggle-icon" wx:if="{{selectedVocabTypes.indexOf('cet4') !== -1}}">✓</text>
        </view>
        <view
          class="vocab-toggle cet6 {{selectedVocabTypes.indexOf('cet6') !== -1 ? 'active' : ''}}"
          data-type="cet6"
          bindtap="toggleVocabType"
          disabled="{{loading}}"
        >
          <text class="toggle-text">六级</text>
          <text class="toggle-icon" wx:if="{{selectedVocabTypes.indexOf('cet6') !== -1}}">✓</text>
        </view>
      </view>
    </view>
    
    <!-- 标注文章内容 -->
    <view class="marked-content">
      <view class="marked-text-container">
        <rich-text 
          class="marked-text" 
          nodes="{{analyzedArticle.markedText}}"
        ></rich-text>
      </view>
    </view>

    <!-- 底部操作 -->
    <view class="result-actions">
      <button class="action-btn secondary" bindtap="backToEdit">
        ← 返回编辑
      </button>
      <button class="action-btn secondary" bindtap="copyResult">
        📋 复制文本
      </button>
      <button class="action-btn primary" bindtap="saveToAlbum">
        📷 保存到相册
      </button>
    </view>
  </view>

  <!-- 加载遮罩 -->
  <view class="loading-mask" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在分析文章...</text>
    </view>
  </view>

  <!-- 隐藏的Canvas用于生成PDF -->
  <canvas
    canvas-id="articlePdfCanvas"
    style="position: fixed; top: -9999px; left: -9999px; width: 750px; height: {{canvasHeight || 6000}}px;"
  ></canvas>
</view>