/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B9D 0%, #E91E63 100%);
  padding: 20rpx;
  box-sizing: border-box;
}

/* 游戏标题栏 */
.game-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 24rpx 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.game-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.mode-text, .timer, .score {
  font-size: 24rpx;
  color: #666;
}

.timer {
  color: #FF6B9D;
  font-weight: 600;
}

.score {
  color: #4CAF50;
  font-weight: 600;
}

/* 模式选择器 */
.mode-selector {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.mode-selector.hidden {
  opacity: 0;
  transform: translateY(-20rpx);
  pointer-events: none;
}

.mode-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.mode-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.mode-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 3rpx solid transparent;
}

.mode-card:active {
  transform: scale(0.95);
}

.mode-card.practice {
  border-color: #4CAF50;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.8), rgba(56, 142, 60, 0.8));
  backdrop-filter: blur(10rpx);
}

.mode-card.challenge {
  border-color: #FF9800;
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.8), rgba(245, 124, 0, 0.8));
  backdrop-filter: blur(10rpx);
}

.mode-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  display: block;
}

.mode-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.mode-desc {
  font-size: 22rpx;
  color: #666;
  display: block;
  line-height: 1.3;
}

/* 游戏区域 */
.game-area {
  transition: all 0.3s ease;
}

.game-area.hidden {
  opacity: 0;
  transform: translateY(20rpx);
  pointer-events: none;
}

/* 游戏统计 */
.game-stats {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.stat-value.combo {
  color: #FF6B9D;
}

.stat-value.time {
  color: #FF9800;
}

/* 游戏网格 */
.game-grid {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  min-height: 1200rpx; /* 增加高度以适应10行布局 */
}

.blocks-container {
  width: 100%;
  height: 1200rpx; /* 增加高度以适应10行布局 */
  position: relative;
  overflow: hidden;
}

.word-block {
  /* 移除固定尺寸，使用动态计算的尺寸 */
  position: absolute;
  transition: all 0.2s ease;
  z-index: 1;
}



.block-content {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.word-block.english .block-content {
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  color: white;
}

.word-block.chinese .block-content {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  color: white;
}

.word-block.selected .block-content {
  transform: scale(1.1);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 157, 0.4);
  border: 3rpx solid #FF6B9D;
}

.word-block.matched .block-content {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  animation: matchSuccess 1s ease-in-out;
}

@keyframes matchSuccess {
  0% { 
    transform: scale(1);
    opacity: 1;
    filter: brightness(1);
  }
  20% { 
    transform: scale(1.15);
    opacity: 1;
    filter: brightness(1.3);
    box-shadow: 0 0 30rpx rgba(255, 152, 0, 0.8);
  }
  50% { 
    transform: scale(1.1);
    opacity: 1;
    filter: brightness(1.2);
    box-shadow: 0 0 40rpx rgba(255, 152, 0, 0.6);
  }
  100% { 
    transform: scale(0.8);
    opacity: 0;
    filter: brightness(2);
    box-shadow: 0 0 20rpx rgba(255, 152, 0, 0.3);
  }
}

.block-text {
  /* 字体大小由内联样式动态设置 */
  font-weight: bold;
  text-align: center;
  line-height: 1.2;
  padding: 8rpx;
  word-break: break-all;
}

.match-effect {
  position: absolute;
  top: -20rpx;
  right: -10rpx;
  background: #FF6B9D;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  opacity: 0;
  transform: translateY(10rpx);
  transition: all 0.5s ease;
}

.match-effect.show {
  opacity: 1;
  transform: translateY(-10rpx);
  animation: effectFade 1s ease-in-out;
}

@keyframes effectFade {
  0% { opacity: 0; transform: translateY(10rpx); }
  50% { opacity: 1; transform: translateY(-10rpx); }
  100% { opacity: 0; transform: translateY(-30rpx); }
}

/* 游戏控制按钮 */
.game-controls {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.control-btn {
  padding: 20rpx 16rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.control-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.shuffle-btn {
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
  color: white;
}

.hint-btn {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
}

.pause-btn {
  background: linear-gradient(135deg, #607D8B, #455A64);
  color: white;
}

.restart-btn {
  background: linear-gradient(135deg, #F44336, #D32F2F);
  color: white;
}

/* 调试控制按钮 */
.debug-controls {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.debug-btn {
  background: linear-gradient(135deg, #9E9E9E, #616161);
  color: white;
  font-size: 20rpx;
}

/* 弹窗样式 */
.pause-modal, .result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.pause-modal.show, .result-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx 40rpx;
  margin: 40rpx;
  text-align: center;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
  transform: scale(0.8);
  transition: transform 0.3s ease;
  max-width: 500rpx;
}

.pause-modal.show .modal-content,
.result-modal.show .modal-content {
  transform: scale(1);
}

.pause-icon, .result-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20rpx); }
  60% { transform: translateY(-10rpx); }
}

.pause-title, .result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 32rpx;
}

.result-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.result-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.result-label {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.result-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #FF6B9D;
  display: block;
}

.modal-buttons {
  display: flex;
  gap: 20rpx;
}

/* 竞赛模式下的四个按钮网格布局 */
.modal-btn-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  width: 100%;
}

/* 非竞赛模式下的正常按钮布局 */
.modal-buttons-normal {
  display: flex;
  gap: 20rpx;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.modal-btn.primary {
  background: #FF6B9D;
  color: white;
}

.modal-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.modal-btn.share-result {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.modal-btn.next-level {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.modal-btn.disabled {
  background: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}

.modal-btn:active {
  transform: scale(0.95);
}

.modal-btn.disabled:active {
  transform: none;
}

/* 响应式调整 */
@media (max-width: 320px) {
  .mode-options {
    grid-template-columns: 1fr;
  }
  
  .game-controls {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .word-block {
    width: 120rpx;
    height: 90rpx;
  }
  
  .block-text {
    font-size: 22rpx;
  }
}

/* 飘分特效 */
.floating-scores {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1001;
}

.floating-score {
  position: absolute;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.5s ease;
}

.floating-score.show {
  opacity: 1;
  transform: translateY(-100rpx);
  animation: floatUp 2s ease-out forwards;
}

@keyframes floatUp {
  0% { 
    opacity: 1; 
    transform: translateY(0) scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: translateY(-60rpx) scale(1.2); 
  }
  100% { 
    opacity: 0; 
    transform: translateY(-120rpx) scale(0.8); 
  }
}

.score-text {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #FF6B9D;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
  margin-bottom: 8rpx;
}

.combo-text {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 烟花特效 */
.fireworks-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1002;
}

.firework {
  position: absolute;
  width: 10rpx;
  height: 10rpx;
}

.firework-spark {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: #FFD700;
  border-radius: 50%;
  animation: fireworkExplode 1.5s ease-out forwards;
}

.spark1 { animation-delay: 0ms; }
.spark2 { animation-delay: 50ms; }
.spark3 { animation-delay: 100ms; }
.spark4 { animation-delay: 150ms; }
.spark5 { animation-delay: 200ms; }
.spark6 { animation-delay: 250ms; }

@keyframes fireworkExplode {
  0% {
    opacity: 1;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.5) rotate(360deg);
  }
}

.spark1 { 
  background: #FF6B9D;
  animation-name: fireworkExplode1;
}
.spark2 { 
  background: #4FACFE;
  animation-name: fireworkExplode2;
}
.spark3 { 
  background: #43E97B;
  animation-name: fireworkExplode3;
}
.spark4 { 
  background: #FA709A;
  animation-name: fireworkExplode4;
}
.spark5 { 
  background: #FFECD2;
  animation-name: fireworkExplode5;
}
.spark6 { 
  background: #A8EDEA;
  animation-name: fireworkExplode6;
}

@keyframes fireworkExplode1 {
  0% { opacity: 1; transform: translate(0, 0) scale(0); }
  100% { opacity: 0; transform: translate(100rpx, -100rpx) scale(1); }
}

@keyframes fireworkExplode2 {
  0% { opacity: 1; transform: translate(0, 0) scale(0); }
  100% { opacity: 0; transform: translate(-100rpx, -100rpx) scale(1); }
}

@keyframes fireworkExplode3 {
  0% { opacity: 1; transform: translate(0, 0) scale(0); }
  100% { opacity: 0; transform: translate(100rpx, 100rpx) scale(1); }
}

@keyframes fireworkExplode4 {
  0% { opacity: 1; transform: translate(0, 0) scale(0); }
  100% { opacity: 0; transform: translate(-100rpx, 100rpx) scale(1); }
}

@keyframes fireworkExplode5 {
  0% { opacity: 1; transform: translate(0, 0) scale(0); }
  100% { opacity: 0; transform: translate(0, -120rpx) scale(1); }
}

@keyframes fireworkExplode6 {
  0% { opacity: 1; transform: translate(0, 0) scale(0); }
  100% { opacity: 0; transform: translate(0, 120rpx) scale(1); }
}

/* ================ 分享弹窗样式 ================ */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.share-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: calc(100% - 80rpx);
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.share-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-info {
  margin-bottom: 40rpx;
}

.share-desc {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.share-note {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.share-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.wechat-share-btn, .copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 32rpx;
  border: none;
  gap: 15rpx;
}

.wechat-share-btn {
  background: linear-gradient(135deg, #07c160, #00ae47);
  color: white;
}

.copy-btn {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #e0e0e0;
}

.share-btn-icon, .copy-btn-icon {
  font-size: 36rpx;
}

.share-btn-text, .copy-btn-text {
  font-size: 32rpx;
  font-weight: bold;
} 