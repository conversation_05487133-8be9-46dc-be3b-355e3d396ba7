const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { wordbankData } = event
  
  if (!wordbankData) {
    return { 
      code: 400, 
      message: '缺少词库数据' 
    }
  }

  try {
    const { name, words, creatorOpenid, creatorName } = wordbankData
    
    // 验证必要数据
    if (!name || !words || !Array.isArray(words) || words.length === 0) {
      return {
        code: 400,
        message: '词库数据格式错误'
      }
    }
    
    if (!creatorOpenid) {
      return {
        code: 400,
        message: '用户信息不完整'
      }
    }

    // 验证和标准化词汇数据
    const processedWords = words.map((wordItem, index) => {
      if (!wordItem || typeof wordItem !== 'object') {
        throw new Error(`第${index + 1}个词汇数据格式错误`)
      }

      const word = wordItem.word
      const meaning = wordItem.meaning || wordItem.chinese // 兼容旧格式
      const example = wordItem.example || ''

      if (!word || typeof word !== 'string') {
        throw new Error(`第${index + 1}个词汇缺少单词信息`)
      }

      if (!meaning || typeof meaning !== 'string') {
        throw new Error(`第${index + 1}个词汇缺少释义信息`)
      }

      return {
        word: word.trim().toLowerCase(),
        meaning: meaning.trim(),
        example: example.trim(),
        // 保留旧字段以兼容现有功能
        chinese: meaning.trim()
      }
    })

    // 生成唯一的词库ID
    const wordbankId = 'custom_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    
    // 准备词库数据
    const customWordbankData = {
      id: wordbankId,
      name: name,
      words: processedWords,
      wordCount: processedWords.length,
      creatorOpenid: creatorOpenid,
      creatorName: creatorName || '匿名用户',
      createTime: db.serverDate(),
      updateTime: db.serverDate(),
      // 添加格式版本信息
      formatVersion: '2.0'
    }

    console.log('准备创建自定义词库:', {
      id: wordbankId,
      name: name,
      wordCount: processedWords.length,
      creatorOpenid: creatorOpenid
    })

    try {
      // 尝试直接添加数据
      const result = await db.collection('custom_wordbanks').add({
        data: customWordbankData
      })

      console.log('自定义词库创建成功:', result)

      return {
        code: 200,
        message: '创建成功',
        data: {
          _id: result._id,
          wordbankId: wordbankId,
          name: name,
          wordCount: processedWords.length
        }
      }

    } catch (addError) {
      console.log('直接添加失败，错误代码:', addError.errCode)
      
      // 如果是集合不存在的错误
      if (addError.errCode === -502005) {
        console.log('检测到集合不存在，尝试创建集合...')
        
        try {
          // 通过云函数创建集合的方法：先尝试查询，触发集合创建
          await db.collection('custom_wordbanks').limit(1).get()
          console.log('集合查询完成，集合应该已创建')
          
          // 等待一下，然后重试添加数据
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          const retryResult = await db.collection('custom_wordbanks').add({
            data: customWordbankData
          })

          console.log('重试创建自定义词库成功:', retryResult)

          return {
            code: 200,
            message: '创建成功（已自动初始化数据库）',
            data: {
              _id: retryResult._id,
              wordbankId: wordbankId,
              name: name,
              wordCount: processedWords.length
            }
          }

        } catch (retryError) {
          console.error('重试创建失败:', retryError)
          
          // 如果重试还是失败，尝试使用云函数的管理权限
          try {
            // 使用云函数的管理员权限创建集合
            const adminDB = cloud.database()
            
            // 先创建一个临时文档来初始化集合
            const tempResult = await adminDB.collection('custom_wordbanks').add({
              data: {
                _temp: true,
                createTime: adminDB.serverDate()
              }
            })
            
            // 删除临时文档
            await adminDB.collection('custom_wordbanks').doc(tempResult._id).remove()
            
            // 现在添加真正的词库数据
            const finalResult = await adminDB.collection('custom_wordbanks').add({
              data: customWordbankData
            })

            console.log('使用管理员权限创建成功:', finalResult)

            return {
              code: 200,
              message: '创建成功（已初始化数据库集合）',
              data: {
                _id: finalResult._id,
                wordbankId: wordbankId,
                name: name,
                wordCount: processedWords.length
              }
            }

          } catch (adminError) {
            console.error('管理员权限创建也失败:', adminError)
            return {
              code: 500,
              message: '数据库集合创建失败：' + adminError.message,
              details: {
                errCode: adminError.errCode,
                errMsg: adminError.errMsg
              }
            }
          }
        }

      } else {
        // 其他类型的错误
        console.error('添加数据失败:', addError)
        return {
          code: 500,
          message: '创建失败：' + addError.message,
          details: {
            errCode: addError.errCode,
            errMsg: addError.errMsg
          }
        }
      }
    }

  } catch (error) {
    console.error('创建自定义词库失败:', error)
    return {
      code: 500,
      message: '服务器错误：' + error.message
    }
  }
} 