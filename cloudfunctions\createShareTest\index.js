const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 使用种子的随机数生成器
function seededRandom(seed) {
  let x = Math.sin(seed) * 10000;
  return x - Math.floor(x);
}

// 使用种子打乱数组
function shuffleArrayWithSeed(array, seed) {
  const shuffled = [...array];
  let currentSeed = seed;

  for (let i = shuffled.length - 1; i > 0; i--) {
    currentSeed = (currentSeed * 9301 + 49297) % 233280; // 线性同余生成器
    const j = Math.floor(seededRandom(currentSeed) * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }

  return shuffled;
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();

  console.log('=== 创建分享测试云函数开始 ===');
  console.log('用户OpenID:', wxContext.OPENID);
  console.log('接收到的参数:', event);

  try {
    const {
      testType,
      words,
      libraryId,
      libraryName,
      settings,
      expireDays = 7,  // 有效期天数，默认7天
      wordsPerGroup = 20,  // 每组单词数量，默认20
      shareId,  // 使用前端传递的shareId
      isRandomOrder = false,  // 是否乱序
      creatorInfo  // 分享人信息
    } = event;

    console.log('创建分享测试参数:', {
      testType,
      libraryId,
      libraryName,
      shareId,
      wordsCount: words?.length,
      isRandomOrder,
      wordsPerGroup,
      expireDays
    });

    // 获取分享人信息
    let finalCreatorInfo = creatorInfo;
    if (!finalCreatorInfo) {
      try {
        // 从用户数据库中获取分享人信息
        const userResult = await db.collection('users')
          .where({ openid: wxContext.OPENID })
          .get();

        if (userResult.data && userResult.data.length > 0) {
          const userData = userResult.data[0];

          // 根据注册类型选择昵称
          let displayName = '微信用户';
          if (userData.wechatInfo && userData.wechatInfo.nickName && userData.wechatInfo.nickName.trim()) {
            displayName = userData.wechatInfo.nickName;
          } else if (userData.username && userData.username.trim()) {
            displayName = userData.username;
          }

          // 获取头像URL
          let avatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0';
          if (userData.wechatInfo && userData.wechatInfo.avatarUrl && userData.wechatInfo.avatarUrl.trim()) {
            avatarUrl = userData.wechatInfo.avatarUrl;
          }

          finalCreatorInfo = {
            openid: wxContext.OPENID,
            nickName: displayName,
            avatarUrl: avatarUrl
          };
        } else {
          finalCreatorInfo = {
            openid: wxContext.OPENID,
            nickName: '微信用户',
            avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
          };
        }
      } catch (error) {
        console.log('获取分享人信息失败，使用默认信息:', error);
        finalCreatorInfo = {
          openid: wxContext.OPENID,
          nickName: '微信用户',
          avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
        };
      }
    }



    // 使用前端传递的shareId，如果没有则生成新的
    const finalShareId = shareId || (testType + '_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9));
    
    // 先检测数据格式以获取实际词汇数量
    const isIndexBasedRequest = words.length === 1 && words[0].totalCount;
    const actualWordsCount = isIndexBasedRequest ? words[0].totalCount : words.length;

    // 计算是否需要多关卡（优化：延迟生成关卡数据）
    const needMultiLevel = actualWordsCount > wordsPerGroup;
    const totalLevels = needMultiLevel ? Math.ceil(actualWordsCount / wordsPerGroup) : 1;

    console.log(`词汇数量: ${actualWordsCount}, 每组: ${wordsPerGroup}, 总关卡: ${totalLevels}`);

    // 优化：不在创建时生成所有关卡数据，而是在获取时动态生成
    // 这样可以大大减少创建时的处理时间，避免超时
    let levels = null;

    // 只有小型测试且使用直接存储策略才预生成关卡数据
    if ((!needMultiLevel || totalLevels <= 10) && !isIndexBasedRequest) {
      levels = [];
      if (needMultiLevel) {
        for (let i = 0; i < totalLevels; i++) {
          const start = i * wordsPerGroup;
          const end = Math.min(start + wordsPerGroup, actualWordsCount);
          levels.push({
            levelId: i + 1,
            words: words.slice(start, end),
            isUnlocked: i === 0 // 只有第一关默认解锁
          });
        }
      } else {
        levels.push({
          levelId: 1,
          words: words,
          isUnlocked: true
        });
      }
      console.log(`预生成了 ${levels.length} 个关卡数据`);
    } else {
      console.log(`大型测试(${totalLevels}关卡)或索引存储，使用延迟生成策略`);
    }
    
    // 超级优化存储策略：基于索引的分组存储
    console.log('数据格式检测:', {
      isIndexBasedRequest,
      receivedWordsCount: words.length,
      actualWordsCount,
      firstWordSample: words[0]?.words || 'N/A'
    });
    const hasLibraryId = libraryId && !libraryId.includes('custom') && !libraryId.includes('mistake');
    const isSystemLibrary = hasLibraryId && actualWordsCount >= 100; // 系统词库且词汇量大

    let storageStrategy = 'direct'; // direct, index_based
    let wordSelection = null;

    if (isIndexBasedRequest && isSystemLibrary) {
      // 使用基于索引的超级优化存储
      storageStrategy = 'index_based';

      // 生成词汇索引序列（不需要完整词汇数据）
      let wordIndexes = Array.from({length: actualWordsCount}, (_, i) => i);
      let seed = null;

      if (isRandomOrder) {
        // 乱序：生成打乱的索引序列
        seed = Date.now();
        wordIndexes = shuffleArrayWithSeed(wordIndexes, seed);
        console.log('生成乱序索引序列，种子:', seed, '词汇数量:', actualWordsCount);
      } else {
        console.log('生成顺序索引序列，词汇数量:', actualWordsCount);
      }

      wordSelection = {
        selectionType: isRandomOrder ? 'random' : 'sequential',
        libraryId: libraryId,
        wordIndexes: wordIndexes, // 只存储索引，不存储完整词汇
        totalWords: actualWordsCount,
        randomSeed: seed
      };

      console.log('使用索引存储策略，索引数组大小:', wordIndexes.length);
    }

    // 创建分享测试数据
    const shareTestData = {
      shareId: finalShareId,
      testType: testType,
      // 超级优化：根据存储策略决定数据结构
      words: storageStrategy === 'direct' ? words : null, // 只有小型测试存储完整词汇
      wordsCount: actualWordsCount, // 使用实际词汇数量
      libraryId: libraryId,
      libraryName: libraryName,
      // 词汇选择信息（索引存储或动态加载）
      wordSelection: wordSelection,
      storageStrategy: storageStrategy,
      settings: settings,
      createdBy: wxContext.OPENID,
      creatorInfo: finalCreatorInfo,
      createTime: db.serverDate(),
      // 根据选择的天数设置过期时间
      expireTime: db.serverDate({
        offset: expireDays * 24 * 60 * 60 * 1000
      }),
      expireDays: expireDays,
      // 多关卡支持（超级优化：基于索引的分组）
      isMultiLevel: needMultiLevel,
      totalLevels: totalLevels,
      wordsPerGroup: wordsPerGroup,
      levels: levels, // 小型测试有预生成数据，大型测试使用索引动态生成
      // 标记是否需要延迟生成关卡
      needsLazyLevelGeneration: levels === null && needMultiLevel,
      // 参与者记录
      visitors: [],
      results: [],
      // 关卡进度记录
      levelProgress: {}, // 格式: { [userId]: { currentLevel: 1, completedLevels: [1], scores: {1: 95} } }
      // 存储优化标记
      isOptimizedStorage: storageStrategy !== 'direct'
    };
    
    // 保存到数据库
    console.log('准备保存到数据库，shareId:', finalShareId);
    console.log('数据结构:', {
      shareId: shareTestData.shareId,
      testType: shareTestData.testType,
      storageStrategy: shareTestData.storageStrategy,
      wordsCount: shareTestData.wordsCount,
      isMultiLevel: shareTestData.isMultiLevel
    });

    const dbResult = await db.collection('shareTests').add({
      data: shareTestData
    });

    console.log('数据库保存结果:', dbResult);
    console.log('分享测试创建成功，shareId:', finalShareId);

    return {
      success: true,
      data: {
        shareId: finalShareId,
        isMultiLevel: needMultiLevel,
        totalLevels: totalLevels,
        expireDays: expireDays
      }
    };
    
  } catch (error) {
    console.error('创建分享测试失败:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });

    return {
      success: false,
      message: `创建分享测试失败: ${error.message || '未知错误'}`,
      error: {
        message: error.message,
        code: error.code,
        type: error.constructor.name
      }
    };
  }
}; 