const app = getApp()

Page({
  data: {
    currentIndex: 0,
    totalWords: 0,
    progress: 0,
    currentWord: null,
    words: [],
    showPhonetic: false,
    showMeaning: false,
    showExample: false,
    showAudio: true,
    showCompleteModal: false,
    learningTime: 0,
    startTime: null,

    // 短语测试相关
    mode: null, // phrase_en2zh, phrase_zh2en
    library: null,
    options: [],
    correctOption: -1,
    selectedOption: -1,
    isAnswered: false,
    showAnswer: false,
    score: 0,
    correctCount: 0,
    wrongCount: 0,
    showResult: false
  },

  onLoad(options) {
    console.log('学习页面参数:', options);

    // 检查是否为短语测试模式
    if (options.mode && options.mode.startsWith('phrase_')) {
      this.initPhraseTest(options);
    } else {
      this.loadWords();
      this.startTime = new Date();
    }
  },

  onUnload() {
    // 计算学习时间
    if (this.startTime) {
      const endTime = new Date()
      this.setData({
        learningTime: endTime - this.startTime
      })
    }
  },

  // 初始化短语测试
  async initPhraseTest(options) {
    const { mode, library } = options;

    this.setData({
      mode: mode,
      library: library,
      startTime: new Date()
    });

    wx.setNavigationBarTitle({
      title: mode === 'phrase_en2zh' ? '短语英译汉' : '短语汉译英'
    });

    // 加载短语数据
    await this.loadPhrases(library);
  },

  // 加载短语数据
  async loadPhrases(libraryId) {
    try {
      wx.showLoading({ title: '加载短语中...' });

      // 首先尝试从全局数据获取已选择的短语
      const app = getApp();
const optionGenerator = require('../../utils/option-generator');
      const learningData = app.globalData.learningData;

      let phrases = [];

      if (learningData && learningData.words && learningData.words.length > 0) {
        // 使用全局数据中的短语
        phrases = learningData.words;
        console.log('从全局数据加载短语:', phrases.length, '个');
      } else {
        // 如果没有全局数据，从云函数加载
        console.log('从云函数加载短语数据');
        const result = await wx.cloud.callFunction({
          name: 'getWords',
          data: {
            libraryId: libraryId,
            limit: 50 // 限制数量
          }
        });

        if (result.result && result.result.code === 200) {
          phrases = result.result.data;
        } else {
          throw new Error(result.result?.message || '加载短语失败');
        }
      }

      wx.hideLoading();

      if (phrases.length === 0) {
        wx.showModal({
          title: '提示',
          content: '该短语库暂无数据',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }

      // 打乱短语顺序
      const shuffledPhrases = phrases.sort(() => Math.random() - 0.5);

      this.setData({
        words: shuffledPhrases,
        totalWords: shuffledPhrases.length,
        currentWord: shuffledPhrases[0]
      });

      // 生成第一题
      this.generateQuestion();

    } catch (error) {
      wx.hideLoading();
      console.error('加载短语失败:', error);
      wx.showModal({
        title: '加载失败',
        content: '短语数据加载失败，请重试',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 生成题目
  generateQuestion() {
    const { words, currentIndex, mode } = this.data;
    const currentPhrase = words[currentIndex];

    if (!currentPhrase) {
      this.showResult();
      return;
    }

    let correctAnswer;
    let optionField;

    if (mode === 'phrase_en2zh') {
      // 英译汉：显示英文短语，选择中文含义
      correctAnswer = currentPhrase.chinese || currentPhrase.meaning;
      optionField = 'chinese';
    } else {
      // 汉译英：显示中文含义，选择英文短语
      correctAnswer = currentPhrase.word || currentPhrase.phrase;
      optionField = 'word';
    }

    if (!correctAnswer) {
      console.error('短语数据缺失:', currentPhrase);
      this.nextQuestion();
      return;
    }

    // 生成选项
    const options = this.generateOptions(words, optionField, correctAnswer);
    const correctIndex = options.indexOf(correctAnswer);

    this.setData({
      currentWord: currentPhrase,
      options: options,
      correctOption: correctIndex,
      selectedOption: -1,
      isAnswered: false,
      showAnswer: false,
      progress: ((currentIndex + 1) / this.data.totalWords) * 100
    });
  },

  // 生成选项
  generateOptions(phrases, field, correctAnswer) {
    // 使用新的选项生成工具
    return optionGenerator.generatePhraseOptions(phrases, field, correctAnswer, 4);
  },

  // 选择答案
  onOptionSelect(e) {
    if (this.data.isAnswered) return;

    const selectedIndex = e.currentTarget.dataset.index;
    const isCorrect = selectedIndex === this.data.correctOption;

    this.setData({
      selectedOption: selectedIndex,
      isAnswered: true,
      showAnswer: true,
      score: this.data.score + (isCorrect ? 10 : 0),
      correctCount: this.data.correctCount + (isCorrect ? 1 : 0),
      wrongCount: this.data.wrongCount + (isCorrect ? 0 : 1)
    });

    // 播放反馈
    if (isCorrect) {
      wx.vibrateShort({ type: 'light' });
    } else {
      wx.vibrateShort({ type: 'heavy' });
    }

    // 自动进入下一题
    setTimeout(() => {
      this.nextQuestion();
    }, 1500);
  },

  // 下一题
  nextQuestion() {
    const nextIndex = this.data.currentIndex + 1;

    if (nextIndex < this.data.totalWords) {
      this.setData({
        currentIndex: nextIndex
      });
      this.generateQuestion();
    } else {
      this.showResult();
    }
  },

  // 显示结果
  showResult() {
    const { correctCount, wrongCount, score, totalWords } = this.data;
    const accuracy = totalWords > 0 ? Math.round((correctCount / totalWords) * 100) : 0;

    this.setData({
      showResult: true,
      showCompleteModal: true,
      accuracy: accuracy
    });
  },

  // 重新测试
  restartTest() {
    this.setData({
      currentIndex: 0,
      score: 0,
      correctCount: 0,
      wrongCount: 0,
      showResult: false,
      showCompleteModal: false,
      selectedOption: -1,
      isAnswered: false,
      showAnswer: false
    });

    // 重新打乱短语顺序
    const shuffledPhrases = this.data.words.sort(() => Math.random() - 0.5);
    this.setData({
      words: shuffledPhrases,
      currentWord: shuffledPhrases[0]
    });

    this.generateQuestion();
  },

  // 加载单词
  async loadWords() {
    try {
      const db = wx.cloud.database()
      const userInfo = await app.getUserInfo()
      
      // 获取用户设置
      const settingsRes = await db.collection('users').doc(userInfo._id).get()
      const settings = settingsRes.data.settings
      
      // 获取今日已学单词
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const learnedRes = await db.collection('learning_records')
        .where({
          userId: userInfo._id,
          learnTime: db.command.gte(today)
        })
        .get()
      
      const learnedWordIds = learnedRes.data.map(record => record.wordId)
      
      // 获取新单词
      const wordsRes = await db.collection('words')
        .where({
          _id: db.command.nin(learnedWordIds)
        })
        .limit(settings.dailyWords - learnedWordIds.length)
        .get()
      
      const words = wordsRes.data
      
      this.setData({
        words,
        totalWords: words.length,
        currentWord: words[0]
      })
    } catch (error) {
      console.error('加载单词失败：', error)
      wx.showToast({
        title: '加载单词失败',
        icon: 'none'
      })
    }
  },

  // 播放发音
  playAudio() {
    if (this.data.currentWord && this.data.currentWord.audioUrl) {
      const innerAudioContext = wx.createInnerAudioContext()
      innerAudioContext.src = this.data.currentWord.audioUrl
      innerAudioContext.play()
    }
  },

  // 切换音标显示
  togglePhonetic() {
    this.setData({
      showPhonetic: !this.data.showPhonetic
    })
  },

  // 切换释义显示
  toggleMeaning() {
    this.setData({
      showMeaning: !this.data.showMeaning
    })
  },

  // 切换例句显示
  toggleExample() {
    this.setData({
      showExample: !this.data.showExample
    })
  },

  // 标记为困难
  async markAsDifficult() {
    await this.saveLearningRecord(1)
    this.nextWord()
  },

  // 标记为一般
  async markAsNormal() {
    await this.saveLearningRecord(2)
    this.nextWord()
  },

  // 标记为简单
  async markAsEasy() {
    await this.saveLearningRecord(3)
    this.nextWord()
  },

  // 保存学习记录
  async saveLearningRecord(masteryLevel) {
    try {
      const db = wx.cloud.database()
      const userInfo = await app.getUserInfo()
      
      // 计算下次复习时间
      const nextReviewTime = this.calculateNextReviewTime(masteryLevel)
      
      await db.collection('learning_records').add({
        data: {
          userId: userInfo._id,
          wordId: this.data.currentWord._id,
          status: 'learned',
          learnTime: new Date(),
          reviewTimes: 0,
          lastReviewTime: null,
          nextReviewTime,
          masteryLevel,
          notes: ''
        }
      })
    } catch (error) {
      console.error('保存学习记录失败：', error)
      wx.showToast({
        title: '保存学习记录失败',
        icon: 'none'
      })
    }
  },

  // 计算下次复习时间
  calculateNextReviewTime(masteryLevel) {
    const now = new Date()
    let days = 0
    
    switch (masteryLevel) {
      case 1: // 困难
        days = 1
        break
      case 2: // 一般
        days = 3
        break
      case 3: // 简单
        days = 7
        break
    }
    
    now.setDate(now.getDate() + days)
    return now
  },

  // 下一个单词
  nextWord() {
    const nextIndex = this.data.currentIndex + 1
    
    if (nextIndex < this.data.totalWords) {
      this.setData({
        currentIndex: nextIndex,
        currentWord: this.data.words[nextIndex],
        progress: (nextIndex / this.data.totalWords) * 100,
        showPhonetic: false,
        showMeaning: false,
        showExample: false
      })
    } else {
      this.setData({
        showCompleteModal: true
      })
    }
  },

  // 格式化时间
  formatTime(ms) {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}分${remainingSeconds}秒`
  },

  // 跳转到复习页面
  goToReview() {
    wx.redirectTo({
      url: '/pages/review/review'
    })
  },

  // 返回首页
  goToHome() {
    wx.redirectTo({
      url: '/pages/index/index'
    })
  }
}) 