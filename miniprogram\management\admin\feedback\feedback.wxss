/* 管理员反馈页面样式 */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.header-content {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.page-subtitle {
  font-size: 24rpx;
  color: #666;
}

/* 加载状态 */
.loading-section {
  text-align: center;
  padding: 100rpx 0;
}

.loading-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-section {
  text-align: center;
  padding: 100rpx 0;
}

.empty-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.empty-desc {
  font-size: 24rpx;
  color: #666;
}

/* 反馈列表 */
.feedback-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feedback-item {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.feedback-item.unread {
  border-left: 6rpx solid #ff6b6b;
}

.feedback-item.read {
  border-left: 6rpx solid #51cf66;
}

.feedback-item:active {
  transform: scale(0.98);
}

/* 反馈头部 */
.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.user-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.feedback-time {
  font-size: 22rpx;
  color: #999;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.status-badge.pending {
  background: #ffe0e1;
  color: #ff6b6b;
}

.status-badge.replied {
  background: #d3f9d8;
  color: #51cf66;
}

/* 反馈内容 */
.feedback-content {
  margin-bottom: 16rpx;
}

.content-section {
  margin-bottom: 12rpx;
}

.content-label {
  font-size: 22rpx;
  color: #666;
  font-weight: bold;
  display: block;
  margin-bottom: 6rpx;
}

.content-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 反馈操作 */
.feedback-actions {
  border-top: 2rpx solid #f5f5f5;
  padding-top: 12rpx;
  text-align: center;
}

.action-hint {
  font-size: 20rpx;
  color: #999;
} 