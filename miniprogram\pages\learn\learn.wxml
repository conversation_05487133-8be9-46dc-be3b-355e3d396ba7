<view class="container">
  <!-- 短语测试模式 -->
  <view wx:if="{{mode && mode.startsWith('phrase_')}}" class="phrase-test-container">
    <!-- 顶部进度 -->
    <view class="progress-section">
      <view class="progress-bar">
        <view class="progress-inner" style="width: {{progress}}%"></view>
      </view>
      <view class="progress-text">{{currentIndex + 1}}/{{totalWords}}</view>
      <view class="score-text">得分: {{score}}</view>
    </view>

    <!-- 题目区域 -->
    <view class="question-section" wx:if="{{!showResult}}">
      <!-- 英译汉模式 -->
      <view wx:if="{{mode === 'phrase_en2zh'}}" class="question-content">
        <view class="question-title">请选择正确的中文含义</view>
        <view class="phrase-display">
          <text class="phrase-text">{{currentWord.word || currentWord.phrase}}</text>
          <text class="phonetic" wx:if="{{currentWord.phonetic}}">[{{currentWord.phonetic}}]</text>
        </view>
      </view>

      <!-- 汉译英模式 -->
      <view wx:elif="{{mode === 'phrase_zh2en'}}" class="question-content">
        <view class="question-title">请选择正确的英文短语</view>
        <view class="meaning-display">
          <text class="meaning-text">{{currentWord.chinese || currentWord.meaning}}</text>
        </view>
      </view>

      <!-- 选项区域 -->
      <view class="options-section">
        <view
          class="option-item {{selectedOption === index ? (index === correctOption ? 'correct' : 'wrong') : ''}} {{showAnswer && index === correctOption ? 'correct' : ''}}"
          wx:for="{{options}}"
          wx:key="index"
          data-index="{{index}}"
          bindtap="onOptionSelect"
        >
          <view class="option-label">{{String.fromCharCode(65 + index)}}</view>
          <view class="option-text">{{item}}</view>
        </view>
      </view>
    </view>

    <!-- 结果页面 -->
    <view wx:if="{{showResult}}" class="result-section">
      <view class="result-header">
        <view class="result-title">测试完成！</view>
        <view class="result-score">{{score}}分</view>
      </view>

      <view class="result-stats">
        <view class="stat-item">
          <view class="stat-number">{{correctCount}}</view>
          <view class="stat-label">答对</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{wrongCount}}</view>
          <view class="stat-label">答错</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{accuracy}}%</view>
          <view class="stat-label">正确率</view>
        </view>
      </view>

      <view class="result-actions">
        <button class="action-btn secondary" bindtap="goToHome">返回首页</button>
        <button class="action-btn primary" bindtap="restartTest">再测一次</button>
      </view>
    </view>
  </view>

  <!-- 原有单词学习模式 -->
  <view wx:else class="word-learning-container">
    <!-- 顶部进度 -->
    <view class="progress-bar">
      <view class="progress-inner" style="width: {{progress}}%"></view>
    </view>
    <view class="progress-text">{{currentIndex + 1}}/{{totalWords}}</view>

    <!-- 单词卡片 -->
    <view class="word-card">
      <view class="word">{{currentWord.word}}</view>
      <view class="phonetic" wx:if="{{showPhonetic}}">[{{currentWord.phonetic}}]</view>
      <view class="meaning" wx:if="{{showMeaning}}">
        <view class="meaning-item" wx:for="{{currentWord.meanings}}" wx:key="index">
          <text class="part-of-speech">{{item.partOfSpeech}}.</text>
          <text class="definition">{{item.definition}}</text>
        </view>
      </view>
      <view class="example" wx:if="{{showExample}}">
        <view class="example-item" wx:for="{{currentWord.examples}}" wx:key="index">
          <text class="example-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-bar">
      <view class="action-btn" bindtap="playAudio" wx:if="{{showAudio}}">
        <image class="icon" src="/images/audio.png" mode="aspectFit"></image>
        <text>播放发音</text>
      </view>
      <view class="action-btn" bindtap="togglePhonetic">
        <image class="icon" src="/images/phonetic.png" mode="aspectFit"></image>
        <text>{{showPhonetic ? '隐藏音标' : '显示音标'}}</text>
      </view>
      <view class="action-btn" bindtap="toggleMeaning">
        <image class="icon" src="/images/meaning.png" mode="aspectFit"></image>
        <text>{{showMeaning ? '隐藏释义' : '显示释义'}}</text>
      </view>
      <view class="action-btn" bindtap="toggleExample">
        <image class="icon" src="/images/example.png" mode="aspectFit"></image>
        <text>{{showExample ? '隐藏例句' : '显示例句'}}</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-bar">
      <view class="btn btn-difficult" bindtap="markAsDifficult">困难</view>
      <view class="btn btn-normal" bindtap="markAsNormal">一般</view>
      <view class="btn btn-easy" bindtap="markAsEasy">简单</view>
    </view>

    <!-- 学习完成弹窗 -->
    <view class="modal" wx:if="{{showCompleteModal && !mode}}">
      <view class="modal-content">
        <view class="modal-title">学习完成</view>
        <view class="modal-stats">
          <view class="stat-item">
            <text class="stat-label">学习单词</text>
            <text class="stat-value">{{totalWords}}个</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">用时</text>
            <text class="stat-value">{{formatTime(learningTime)}}</text>
          </view>
        </view>
        <view class="modal-buttons">
          <view class="modal-btn" bindtap="goToReview">开始复习</view>
          <view class="modal-btn" bindtap="goToHome">返回首页</view>
        </view>
      </view>
    </view>
  </view>
</view>