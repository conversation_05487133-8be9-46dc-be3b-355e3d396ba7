Page({
  data: {
    mistake: null, // 错题数据
    innerAudioContext: null, // 音频播放器
    isPlaying: false, // 是否正在播放
    audioProgress: 0 // 音频播放进度
  },

  onLoad(options) {
    const { id } = options;
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 初始化音频播放器
    this.initAudioContext();
    
    // 加载错题数据
    this.loadMistake(id);
  },

  onUnload() {
    // 页面卸载时销毁音频播放器
    if (this.innerAudioContext) {
      this.innerAudioContext.destroy();
    }
  },

  // 初始化音频播放器
  initAudioContext() {
    this.innerAudioContext = wx.createInnerAudioContext();
    
    // 监听播放进度
    this.innerAudioContext.onTimeUpdate(() => {
      const progress = (this.innerAudioContext.currentTime / this.innerAudioContext.duration) * 100;
      this.setData({ 
        audioProgress: progress,
        isPlaying: true
      });
    });

    // 监听播放结束
    this.innerAudioContext.onEnded(() => {
      this.setData({ 
        isPlaying: false,
        audioProgress: 0
      });
    });

    // 监听播放错误
    this.innerAudioContext.onError((res) => {
      console.error('音频播放错误:', res);
      wx.showToast({
        title: '音频播放失败',
        icon: 'none'
      });
      this.setData({ isPlaying: false });
    });
  },

  // 加载错题数据
  async loadMistake(id) {
    const db = wx.cloud.database();
    
    try {
      const res = await db.collection('mistake_listening')
        .doc(id)
        .get();
      
      const mistake = {
        ...res.data,
        createTime: this.formatTime(res.data.createTime)
      };

      this.setData({ mistake });

      // 设置音频
      if (mistake.audioUrl) {
        this.innerAudioContext.src = mistake.audioUrl;
      }
    } catch (error) {
      console.error('加载错题失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 格式化时间
  formatTime(date) {
    date = new Date(date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}-${month}-${day}`;
  },

  // 播放/暂停音频
  playAudio() {
    if (!this.data.mistake.audioUrl) return;

    if (this.data.isPlaying) {
      this.innerAudioContext.pause();
      this.setData({ isPlaying: false });
    } else {
      this.innerAudioContext.play();
      this.setData({ isPlaying: true });
    }
  },

  // 滑动进度条
  onSliderChange(e) {
    const value = e.detail.value;
    const time = (value / 100) * this.innerAudioContext.duration;
    this.innerAudioContext.seek(time);
    this.setData({ audioProgress: value });
  },

  // 标记为已掌握
  async onMasteredTap() {
    wx.showModal({
      title: '提示',
      content: '确定要将该错题标记为已掌握吗？',
      success: async (res) => {
        if (res.confirm) {
          const db = wx.cloud.database();
          try {
            await db.collection('mistake_listening')
              .doc(this.data.mistake._id)
              .remove();
            
            wx.showToast({
              title: '已标记为掌握',
              icon: 'success'
            });

            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          } catch (error) {
            console.error('标记失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
}); 