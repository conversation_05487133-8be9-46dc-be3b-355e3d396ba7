/* pages/profile/share/share.wxss */
.share-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  padding-bottom: 100rpx;
}

/* 页面标题 */
.page-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20rpx;
}

.header-actions {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 10rpx;
}

.edit-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 25rpx;
  padding: 12rpx 30rpx;
  font-size: 24rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  min-width: 80rpx;
}

.edit-btn.active {
  background: #ff6b6b;
  border-color: #ff6b6b;
}

.edit-btn::after {
  border: none;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  margin-top: 100rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 15rpx;
}

.empty-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.empty-btn {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #ffffff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.empty-btn::after {
  border: none;
}

/* 编辑模式工具栏 */
.edit-toolbar {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selected-count {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
}

.batch-btn {
  background: #ff6b6b;
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.batch-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
}

.batch-btn::after {
  border: none;
}

/* 模式分组 */
.mode-groups {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.mode-group {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  overflow: hidden;
}

/* 模式标题栏 */
.mode-header {
  padding: 25rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80rpx;
}

.mode-header:active {
  background: rgba(255, 255, 255, 0.1);
}

.mode-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.mode-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.mode-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  margin-right: 8rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

.mode-count {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  flex-shrink: 0;
}

.mode-stats {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin: 0 8rpx;
  flex-shrink: 1;
  min-width: 0;
  flex: 1;
  justify-content: flex-end;
}

.stat {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.mode-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0;
  min-width: 60rpx;
}

.clear-btn {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 2rpx solid rgba(255, 107, 107, 0.3);
  border-radius: 8rpx;
  padding: 3rpx 6rpx;
  font-size: 16rpx;
  font-weight: bold;
  min-width: 32rpx;
  max-width: 40rpx;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  writing-mode: horizontal-tb;
  direction: ltr;
  text-align: center;
  vertical-align: middle;
}

.clear-btn::after {
  border: none;
}

.expand-icon {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: transform 0.3s ease;
  margin-left: 8rpx;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* 测试列表 */
.test-list {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.test-list.expanded {
  max-height: 2000rpx;
}

/* 测试卡片 */
.test-card {
  background: #ffffff;
  margin: 0 20rpx 20rpx 20rpx;
  border-radius: 15rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.test-card.edit-mode {
  padding-left: 60rpx;
}

.test-card.selected {
  border: 2rpx solid #4CAF50;
}

.test-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

/* 选择框容器 */
.test-selector {
  position: absolute;
  left: 20rpx;
  top: 25rpx;
  z-index: 10;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8rpx;
  transition: all 0.2s ease;
}

.test-selector:active {
  background: rgba(255, 255, 255, 1);
  transform: scale(0.95);
}

/* 选择圆圈 - 未选中状态 */
.selector-circle {
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
}

/* 选择圆圈 - 选中状态 */
.selector-circle.selected {
  border: 2rpx solid #4CAF50;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.4);
  transform: scale(1.1);
  animation: selectPulse 0.3s ease-out;
}

/* 选中动画 */
@keyframes selectPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(76, 175, 80, 0.4);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 0 2rpx 12rpx rgba(76, 175, 80, 0.6);
  }
  100% {
    transform: scale(1.1);
    box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.4);
  }
}

/* 对勾符号 */
.selector-check {
  color: #4CAF50;
  font-size: 16rpx;
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  line-height: 1;
}

/* 测试信息 */
.test-info {
  flex: 1;
  margin-bottom: 15rpx;
}

.test-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.test-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333333;
}

.create-time {
  font-size: 20rpx;
  color: #999999;
}

.test-stats {
  display: flex;
  align-items: center;
  gap: 25rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 22rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 2rpx;
}

.stat-label {
  font-size: 18rpx;
  color: #666666;
}

/* 操作按钮 */
.test-actions {
  display: flex;
  gap: 6rpx;
  justify-content: space-between;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  padding: 6rpx 6rpx;
  border-radius: 12rpx;
  font-size: 16rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
  text-align: center;
  min-width: 0;
  max-width: 80rpx;
  line-height: 1.2;
  height: 40rpx;
}

.action-btn::after {
  border: none;
}

.action-btn.secondary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2rpx solid rgba(102, 126, 234, 0.2);
}

.action-btn.primary {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  border: 2rpx solid rgba(76, 175, 80, 0.2);
}

.action-btn.share-btn {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  color: #ffffff;
  border: 2rpx solid rgba(76, 175, 80, 0.3);
  font-weight: bold;
  /* 确保继承基础按钮的大小设置 */
  height: 40rpx;
  padding: 6rpx 6rpx;
  font-size: 16rpx;
}

.action-btn.danger {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 2rpx solid rgba(244, 67, 54, 0.2);
}

.action-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 使用说明 */
.help-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-top: 30rpx;
}

.help-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.help-item {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 浮动创建按钮 */
.floating-create-btn {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.4);
  z-index: 10;
  transition: all 0.3s ease;
}

.floating-create-btn:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.4);
}

.fab-icon {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 分享选项弹窗 */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.share-content {
  background: #ffffff;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  overflow: hidden;
}

.share-header {
  padding: 30rpx 30rpx 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.share-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  font-size: 28rpx;
  color: #666666;
  cursor: pointer;
}

.share-info {
  padding: 0 30rpx 10rpx 30rpx;
}

.share-desc {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.share-note {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

.share-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 32rpx;
  border: none;
  gap: 15rpx;
}

.share-btn::after {
  border: none;
}

.copy-btn {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #e9ecef;
}

.copy-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.wechat-share-btn {
  background: linear-gradient(135deg, #07c160, #38d9a9);
  color: white;
}

.wechat-share-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.4);
}

.timeline-btn {
  background: linear-gradient(135deg, #ff6b6b, #ffa8a8);
  color: white;
}

.timeline-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.4);
}

.share-btn-icon {
  font-size: 32rpx;
}

.share-btn-text {
  font-size: 28rpx;
  color: inherit;
}

/* 分页信息 */
.pagination-info {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 15rpx;
  padding: 15rpx 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-info, .page-info {
  color: rgba(255, 255, 255, 0.9);
  font-size: 24rpx;
}

/* 过期提醒 */
.title-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
  flex: 1;
}

.expire-warning {
  background: linear-gradient(135deg, #ff9500, #ff6b35);
  color: white;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  font-weight: bold;
  animation: pulse 2s infinite;
}

.expire-text {
  font-size: 18rpx;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* 模式分页控件 */
.mode-pagination {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  padding: 6rpx 8rpx;
  margin-top: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
  min-height: 50rpx;
  width: 100%;
  box-sizing: border-box;
  gap: 4rpx;
}

.mode-pagination-controls {
  display: flex;
  align-items: center;
  gap: 2rpx;
  flex: 0 0 auto;
}

.mode-page-btn {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  padding: 8rpx 6rpx;
  font-size: 18rpx;
  line-height: 1;
  width: 100rpx !important;
  height: 40rpx;
  text-align: center;
  flex-shrink: 0;
}

.mode-page-btn:disabled {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
}

.mode-page-btn:not(:disabled):active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.mode-page-info {
  display: flex;
  align-items: center;
  gap: 1rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 16rpx;
  font-weight: 500;
  width: 30rpx;
  text-align: center;
  justify-content: center;
  flex-shrink: 0;
}

.mode-current-page {
  color: #FFD700;
  font-weight: bold;
}

.mode-page-separator {
  color: rgba(255, 255, 255, 0.5);
}

.mode-total-pages {
  color: rgba(255, 255, 255, 0.7);
}

.mode-jump-to-page {
  display: flex;
  align-items: center;
  gap: 2rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

.mode-jump-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14rpx;
  flex-shrink: 0;
}

.mode-page-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  padding: 0rpx 6rpx !important;
  width: 40rpx;
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 18rpx;
  flex-shrink: 0;
  height: 20px !important;
  min-height: 20px !important;
  max-height: 20px !important;
  line-height: 20px !important;
}

.mode-jump-btn {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  padding: 8rpx 6rpx;
  font-size: 18rpx;
  line-height: 1;
  width: 100rpx !important;
  flex-shrink: 0;
  height: 40rpx;
  text-align: center;
}

.mode-jump-btn:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.95);
}

/* 全局分页控件（保留但隐藏） */
.pagination {
  display: none;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.page-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 8rpx 12rpx;
  font-size: 24rpx;
  font-weight: bold;
  min-width: 80rpx;
  max-width: 100rpx;
  flex-shrink: 0;
  text-align: center;
}

.page-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.2);
}

.page-btn::after {
  border: none;
}

.page-info {
  display: flex;
  align-items: center;
  gap: 5rpx;
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.current-page {
  color: #ffc107;
}

.page-separator {
  color: rgba(255, 255, 255, 0.7);
}

.total-pages {
  color: rgba(255, 255, 255, 0.9);
}

/* 跳转到指定页 */
.jump-to-page {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0;
}

.jump-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 22rpx;
  white-space: nowrap;
}

.page-input {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 8rpx;
  padding: 6rpx 8rpx;
  width: 60rpx;
  text-align: center;
  color: white;
  font-size: 22rpx;
}

.page-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.jump-btn {
  background: rgba(255, 193, 7, 0.8);
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 6rpx 8rpx;
  font-size: 22rpx;
  font-weight: bold;
  min-width: 50rpx;
  max-width: 60rpx;
  text-align: center;
  flex-shrink: 0;
}

.jump-btn::after {
  border: none;
}