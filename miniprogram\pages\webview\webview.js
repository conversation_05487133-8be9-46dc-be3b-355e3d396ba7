// pages/webview/webview.js
Page({
  data: {
    webviewUrl: ''
  },

  onLoad(options) {
    console.log('webview页面参数:', options);
    
    if (options.url) {
      const decodedUrl = decodeURIComponent(options.url);
      console.log('解码后的URL:', decodedUrl);
      
      this.setData({
        webviewUrl: decodedUrl
      });
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '试卷分析报告'
      });
    } else {
      wx.showToast({
        title: '链接地址无效',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onShareAppMessage() {
    return {
      title: '试卷分析报告',
      path: `/pages/webview/webview?url=${encodeURIComponent(this.data.webviewUrl)}`,
      imageUrl: '/assets/icons/logo.png'
    };
  },

  onShareTimeline() {
    return {
      title: '试卷分析报告',
      query: `url=${encodeURIComponent(this.data.webviewUrl)}`,
      imageUrl: '/assets/icons/logo.png'
    };
  }
});
