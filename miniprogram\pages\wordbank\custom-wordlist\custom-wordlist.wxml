<view class="container">
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误提示 -->
  <view class="error-container" wx:elif="{{error}}">
    <text class="error-icon">😕</text>
    <text class="error-text">{{error}}</text>
    <button class="retry-btn" bindtap="loadWordbankData">重新加载</button>
  </view>

  <!-- 词库内容 -->
  <view class="wordbank-content" wx:else>
    <!-- 词库信息 -->
    <view class="wordbank-header">
      <view class="wordbank-info">
        <text class="wordbank-name">{{wordbank.name}}</text>
        <text class="wordbank-count">共 {{words.length}} 个词汇</text>
      </view>
      <view class="wordbank-actions">
        <button class="action-btn edit" bindtap="editWordbank">编辑</button>
        <button class="action-btn delete" bindtap="deleteWordbank">删除</button>
      </view>
    </view>

    <!-- 选择模式工具栏 -->
    <view class="selection-toolbar" wx:if="{{mode === 'select'}}">
      <view class="selection-info">
        <text>已选择 {{selectedWords.length}} 个</text>
      </view>
      <view class="selection-actions">
        <button class="select-btn" bindtap="selectAll">全选</button>
        <button class="select-btn" bindtap="clearSelection">清空</button>
      </view>
    </view>

    <!-- 词汇列表 -->
    <view class="words-list">
      <view class="word-item {{item.selected ? 'selected' : ''}}" 
            wx:for="{{words}}" 
            wx:key="_id"
            bindtap="onWordSelect"
            data-index="{{index}}">
        
        <!-- 选择框（选择模式时显示） -->
        <view class="checkbox" wx:if="{{mode === 'select'}}">
          <icon type="{{item.selected ? 'success' : 'circle'}}" size="20"/>
        </view>
        
        <!-- 词汇内容 -->
        <view class="word-content">
          <view class="word-main">
            <text class="word-text">{{item.word}}</text>
            <text class="phonetic" wx:if="{{item.phonetic}}">{{item.phonetic}}</text>
          </view>
          <view class="word-meaning">
            <text class="chinese">{{item.chinese}}</text>
          </view>
          <view class="word-example" wx:if="{{item.example}}">
            <text class="example">{{item.example}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{words.length === 0}}">
      <text class="empty-icon">📚</text>
      <text class="empty-text">词库中还没有词汇</text>
      <button class="add-btn" bindtap="editWordbank">添加词汇</button>
    </view>

    <!-- 底部操作区（选择模式） -->
    <view class="bottom-bar" wx:if="{{mode === 'select' && selectedWords.length > 0}}">
      <button class="start-btn" bindtap="startLearning">
        开始学习 ({{selectedWords.length}})
      </button>
    </view>
  </view>
</view> 