<!-- 学习页面 -->
<view class="learning-container">
  <!-- 顶部进度条 -->
  <view class="progress-header">
    <view class="progress-info">
      <text class="progress-text">{{currentIndex + 1}} / {{words.length}}</text>
      <text class="score-text">得分: {{score}}</text>
    </view>
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{(currentIndex / words.length) * 100}}%"></view>
    </view>
  </view>

  <!-- 学习区域 -->
  <view class="learning-area" wx:if="{{!showResult && currentWord}}">
    <!-- 题目显示 -->
    <view class="question-section">
      <!-- 英译汉模式 -->
      <view wx:if="{{mode === 'en_to_cn'}}" class="question-content">
        <view class="word-display">
          <text class="word-text">{{currentWord.words || currentWord.word}}</text>
          <text class="phonetic" wx:if="{{currentWord.phonetic}}">{{currentWord.phonetic}}</text>
        </view>
        <text class="question-prompt">请选择正确的中文含义</text>
      </view>

      <!-- 汉译英模式 -->
      <view wx:elif="{{mode === 'cn_to_en'}}" class="question-content">
        <view class="meaning-display">
          <text class="meaning-text">{{currentWord.meaning}}</text>
        </view>
        <text class="question-prompt">请选择正确的英文单词</text>
      </view>

      <!-- 单词消消乐模式 -->
      <view wx:elif="{{mode === 'elimination'}}" class="question-content">
        <view class="word-display">
          <text class="word-text">{{currentWord.words || currentWord.word}}</text>
        </view>
        <text class="question-prompt">选择对应的中文含义</text>
      </view>

      <!-- 全书学习模式 -->
      <view wx:elif="{{mode === 'fullbook'}}" class="question-content">
        <view class="word-display">
          <text class="word-text">{{currentWord.words || currentWord.word}}</text>
          <text class="phonetic" wx:if="{{currentWord.phonetic}}">{{currentWord.phonetic}}</text>
        </view>
        <text class="question-prompt">请选择正确的中文含义</text>
      </view>
    </view>

    <!-- 选项区域 -->
    <view class="options-section">
      <block wx:for="{{options}}" wx:key="index">
        <view 
          class="option-item {{selectedOption === index ? 'selected' : ''}} {{showAnswer && index === correctOption ? 'correct' : ''}} {{showAnswer && selectedOption === index && index !== correctOption ? 'wrong' : ''}}"
          data-index="{{index}}"
          bindtap="onOptionTap"
        >
          <text class="option-text">{{item}}</text>
          <view class="option-icon" wx:if="{{showAnswer}}">
            <text wx:if="{{index === correctOption}}">✓</text>
            <text wx:elif="{{selectedOption === index}}">✗</text>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 结果页面 -->
  <view class="result-section" wx:if="{{showResult}}">
    <view class="result-card">
      <view class="result-title">🎉 学习完成！</view>
      
      <view class="result-stats">
        <view class="stat-item">
          <text class="stat-label">正确率</text>
          <text class="stat-value">{{accuracy}}%</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">总得分</text>
          <text class="stat-value">{{score}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">用时</text>
          <text class="stat-value">{{totalTime}}秒</text>
        </view>
      </view>

      <view class="result-details">
        <view class="detail-row">
          <text class="detail-label">答对题目：</text>
          <text class="detail-value correct">{{correctCount}}题</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">答错题目：</text>
          <text class="detail-value wrong">{{wrongCount}}题</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">总计题目：</text>
          <text class="detail-value">{{correctCount + wrongCount}}题</text>
        </view>
      </view>

      <view class="result-actions">
        <button class="action-btn secondary" bindtap="restartLearning">重新学习</button>
        <button class="action-btn primary" bindtap="backToSelect">返回选择</button>
      </view>
    </view>
  </view>
</view> 