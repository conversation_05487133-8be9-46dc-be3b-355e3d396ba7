Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    totalPages: {
      type: Number,
      value: 1
    },
    currentPage: {
      type: Number,
      value: 1
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    inputValue: '',
    inputFocused: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onInput(e) {
      this.setData({
        inputValue: e.detail.value
      });
    },

    // 当输入框获取焦点时，隐藏提示词
    onFocus() {
      this.setData({
        inputFocused: true
      });
    },

    // 当输入框失去焦点时，如果没有内容则显示提示词
    onBlur() {
      this.setData({
        inputFocused: false
      });
    },

    onCancel() {
      this.setData({
        inputValue: '',
        inputFocused: false,
        show: false
      });
      this.triggerEvent('cancel');
    },

    onConfirm() {
      const { inputValue, totalPages } = this.data;
      const page = parseInt(inputValue);
      
      if (!inputValue.trim()) {
        wx.showToast({
          title: '请输入页码',
          icon: 'none'
        });
        return;
      }
      
      if (isNaN(page)) {
        wx.showToast({
          title: '请输入有效数字',
          icon: 'none'
        });
        return;
      }

      if (page < 1 || page > totalPages) {
        wx.showToast({
          title: `页码范围：1-${totalPages}`,
          icon: 'none'
        });
        return;
      }

      if (page === this.properties.currentPage) {
        wx.showToast({
          title: '已在当前页',
          icon: 'none'
        });
        return;
      }

      this.triggerEvent('confirm', { page });
      this.setData({
        inputValue: '',
        inputFocused: false,
        show: false
      });
    },

    // 显示对话框
    showDialog() {
      this.setData({
        show: true,
        inputValue: '',
        inputFocused: false
      });
    }
  }
})
