.container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.header {
  background: linear-gradient(135deg, #007AFF, #0056D6);
  color: white;
  padding-top: calc(var(--status-bar-height) + 20rpx);
  padding-bottom: 30rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
  min-height: calc(88rpx + var(--status-bar-height));
}

.nav-back {
  position: absolute;
  left: 20rpx;
  top: calc(var(--status-bar-height) + 80rpx);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  z-index: 10;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.back-icon {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
}

/* 测试顺序选择 */
.order-selector {
  background: white;
  padding: 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-label {
  font-size: 30rpx;
  color: #333;
  margin-right: 30rpx;
}

.order-options {
  display: flex;
  gap: 20rpx;
}

.order-btn {
  padding: 16rpx 32rpx;
  background-color: #f8f9fa;
  color: #666;
  font-size: 26rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.order-btn.active {
  background-color: #007AFF;
  color: white;
}

/* 双列选择区域 */
.selection-area {
  flex: 1;
  overflow: hidden;
}

.dual-columns {
  height: 100%;
  display: flex;
}

/* 左右列样式 */
.left-column, .right-column {
  flex: 1;
  background: white;
  overflow-y: auto;
}

.left-column {
  border-right: 1rpx solid #f0f0f0;
}

.column-title {
  padding: 30rpx;
  font-size: 28rpx;
  color: #999;
  text-align: center;
  border-bottom: 1rpx solid #f8f9fa;
  background-color: #fafafa;
}

/* 分类列表样式 */
.category-list {
  padding: 0;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f9fa;
  transition: all 0.3s ease;
}

.category-item:hover,
.category-item.active {
  background-color: #e3f2fd;
}

.category-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.category-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.arrow {
  font-size: 24rpx;
  color: #999;
}

/* 占位符样式 */
.placeholder {
  padding: 100rpx 30rpx;
  text-align: center;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}

/* 词库列表样式 */
.library-list {
  padding: 0;
}

.library-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f9fa;
  transition: all 0.3s ease;
}

.library-item:hover {
  background-color: #f8f9fa;
}

.library-info {
  flex: 1;
}

.library-name {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.library-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.upload-tag {
  display: inline-block;
  margin-top: 8rpx;
  padding: 4rpx 12rpx;
  background-color: #ff9500;
  color: white;
  font-size: 20rpx;
  border-radius: 6rpx;
}

/* 选择信息样式 */
.selected-info {
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e0e0e0;
}

.selected-label {
  font-size: 24rpx;
  color: #666;
}

.selected-value {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
}

/* 册数列表样式 */
.volume-list {
  padding: 0;
}

.volume-section {
  padding: 0;
}

.section-title {
  padding: 20rpx 30rpx;
  font-size: 26rpx;
  color: #007AFF;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e0e0e0;
}

.volume-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f9fa;
  transition: all 0.3s ease;
}

.volume-item:hover {
  background-color: #f8f9fa;
}

.volume-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

/* 面包屑导航 */
.breadcrumb {
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e0e0e0;
}

.breadcrumb-item {
  font-size: 24rpx;
  color: #666;
}

.breadcrumb-separator {
  margin: 0 16rpx;
  color: #999;
}

/* 单元控制区域 */
.unit-controls {
  padding: 30rpx;
}

.control-title {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.control-buttons {
  display: flex;
  gap: 20rpx;
}

.control-btn {
  flex: 1;
  padding: 16rpx 0;
  font-size: 24rpx;
  background-color: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 8rpx;
}

/* 单元列表样式 */
.unit-list {
  padding: 0 20rpx;
}

.unit-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  margin-bottom: 16rpx;
  background: white;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.unit-item.selected {
  background-color: #e3f2fd;
  border-color: #007AFF;
}

.unit-checkbox {
  margin-right: 20rpx;
}

.unit-info {
  flex: 1;
}

.unit-name {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.unit-count {
  font-size: 24rpx;
  color: #666;
}

.unit-tip {
  padding: 20rpx 30rpx;
  text-align: center;
}

.unit-tip text {
  font-size: 24rpx;
  color: #999;
}

.selected-count {
  font-size: 24rpx;
  color: #007AFF;
  margin-left: 20rpx;
}

/* 自定义词库样式 */
.custom-list {
  padding: 0;
}

.custom-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f9fa;
  transition: all 0.3s ease;
}

.custom-item:hover {
  background-color: #f8f9fa;
}

.custom-info {
  flex: 1;
}

.custom-name {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.custom-count {
  font-size: 24rpx;
  color: #666;
}

.empty-custom {
  padding: 100rpx 30rpx;
  text-align: center;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
}

/* 底部操作按钮 */
.bottom-actions {
  background: white;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.confirm-btn {
  width: 100%;
  padding: 32rpx 0;
  background: linear-gradient(135deg, #007AFF, #0056D6);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 16rpx;
  border: none;
  transition: all 0.3s ease;
}

.confirm-btn[disabled] {
  background: #f0f0f0;
  color: #ccc;
}

/* 适配安全区域 */
.container {
  padding-top: env(safe-area-inset-top);
}

/* 滚动优化 */
.left-column, .right-column {
  -webkit-overflow-scrolling: touch;
} 