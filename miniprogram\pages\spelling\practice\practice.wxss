/* 拼写练习页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

/* ========================= 听写模式样式 ========================= */
.dictation-mode {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 顶部信息栏 */
.header-info {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.progress-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 15rpx;
}

.progress-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 15rpx;
  text-align: center;
}

.progress-bar {
  height: 8rpx;
  background: #ecf0f1;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.word-count {
  font-size: 24rpx;
  color: #7f8c8d;
  text-align: center;
}

/* 播放控制区域 */
.audio-controls {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.play-status {
  text-align: center;
  margin-bottom: 25rpx;
}

.play-status.waiting .status-text {
  color: #95a5a6;
  font-size: 28rpx;
}

.play-status.completed .status-text {
  color: #27ae60;
  font-size: 28rpx;
  font-weight: bold;
}

.status-text {
  font-size: 28rpx;
  color: #3498db;
  font-weight: 500;
}

.play-buttons {
  display: flex;
  justify-content: center;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 15rpx;
  border: none;
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
}

.start-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.replay-btn {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.control-btn:disabled {
  opacity: 0.5;
  transform: none;
}

.control-btn:not(:disabled):active {
  transform: scale(0.95);
}

.btn-hover {
  opacity: 0.8;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.btn-text {
  font-size: 28rpx;
}

/* 听写输入区域 */
.dictation-page {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.words-input-area {
  margin-bottom: 40rpx;
  padding: 0 10rpx;
}

.input-row {
  display: flex;
  gap: 15rpx;
  margin-bottom: 25rpx;
}

.input-item {
  flex: 1;
  padding: 18rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  min-width: 0; /* 防止flex项目溢出 */
}

.input-item:focus-within {
  border-color: #3498db;
  box-shadow: 0 0 0 3rpx rgba(52, 152, 219, 0.1);
}

.input-label {
  font-size: 24rpx;
  color: #495057;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
  text-align: center;
}

.word-input {
  width: 100%;
  height: 70rpx;
  border: none;
  background: #fff;
  border-radius: 10rpx;
  padding: 0 15rpx;
  font-size: 28rpx;
  color: #2c3e50;
  box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
}

.word-input:focus {
  box-shadow: inset 0 2rpx 8rpx rgba(52, 152, 219, 0.2);
}

.word-input.has-content {
  color: #27ae60;
  font-weight: bold;
}

.word-input::placeholder {
  color: #adb5bd;
  font-size: 24rpx;
}

/* 倒计时显示 */
.countdown-section {
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
}

.countdown-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 20rpx;
  padding: 25rpx 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(240, 147, 251, 0.3);
  animation: pulse 2s infinite;
}

.countdown-title {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 10rpx;
}

.countdown-timer {
  font-size: 60rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.countdown-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 音频播放提示 */
.audio-playing-tip {
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
}

.tip-icon {
  font-size: 32rpx;
  color: #f39c12;
}

.tip-text {
  font-size: 26rpx;
  color: #856404;
  font-weight: 500;
}

/* 页面操作按钮 */
.page-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  justify-content: center;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 30rpx;
  border-radius: 15rpx;
  border: none;
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
  min-width: 200rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
}

.check-btn {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-width: 250rpx;
}

.next-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.finish-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-btn:disabled {
  opacity: 0.4;
  transform: none;
}

.action-btn:not(:disabled):active {
  transform: scale(0.95);
}

/* ========================= 拼写模式样式 ========================= */
.spelling-mode {
  padding: 30rpx;
}

/* 进度指示器 */
.progress-container {
  margin-bottom: 40rpx;
}

.progress-info {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

/* 单词卡片 */
.word-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.audio-section {
  text-align: center;
  margin-bottom: 30rpx;
}

.play-button {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #fff;
  border: none;
  border-radius: 15rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 20rpx rgba(79, 172, 254, 0.3);
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 200rpx;
  max-width: 280rpx;
}

.play-button.playing {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.play-button:active {
  transform: scale(0.95);
}

.play-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.word-display {
  text-align: center;
  margin-bottom: 30rpx;
}

.word-content {
  margin-bottom: 20rpx;
}

.word-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
  margin-bottom: 10rpx;
}

.phonetic {
  font-size: 28rpx;
  color: #7f8c8d;
  font-style: italic;
}

.meaning-section {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.meaning-text {
  font-size: 26rpx;
  color: #495057;
  line-height: 1.6;
}

.spelling-input {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.spell-input {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  color: #2c3e50;
}

.spell-input:focus {
  border-color: #3498db;
}

.check-spelling-btn {
  height: 80rpx;
  padding: 0 30rpx;
  background: #3498db;
  color: #fff;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.check-spelling-btn:disabled {
  opacity: 0.5;
}

/* 控制按钮 */
.controls {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.control-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.hint-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.skip-btn {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
}

.control-btn:active {
  transform: scale(0.95);
}

/* 统计信息 */
.stats {
  display: flex;
  justify-content: space-around;
  background: #fff;
  border-radius: 15rpx;
  padding: 25rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 24rpx;
  color: #7f8c8d;
  display: block;
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
}

.stat-value.correct {
  color: #27ae60;
}

.stat-value.wrong {
  color: #e74c3c;
}

.stat-value.skipped {
  color: #f39c12;
}

/* 练习模式提示 */
.practice-mode-hint {
  margin-bottom: 30rpx;
}

.hint-card {
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.hint-text {
  font-size: 28rpx;
  color: #856404;
  text-align: center;
  line-height: 1.4;
}

.show-answer-btn {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border-radius: 12rpx;
  padding: 16rpx 32rpx;
  border: none;
  display: flex;
  align-items: center;
  gap: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(250, 177, 160, 0.3);
  transition: all 0.3s ease;
}

.show-answer-btn::after {
  border: none;
}

.show-answer-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(250, 177, 160, 0.4);
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 28rpx;
  color: #856404;
  font-weight: 500;
}

/* 练习卡片 */
.practice-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25rpx;
  padding: 40rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
}

/* 单词信息 */
.word-info {
  text-align: center;
  margin-bottom: 40rpx;
}

.phonetic {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
  font-family: 'Courier New', monospace;
}

.meaning {
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.example {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #4CAF50;
}

.example-label {
  font-weight: 500;
  color: #4CAF50;
}

.example-text {
  margin-left: 20rpx;
}

/* 拼写输入区域 */
.spelling-input {
  margin-bottom: 40rpx;
}

.input-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  text-align: center;
}

.input-area {
  position: relative;
}

.input-area input {
  width: 100%;
  height: 100rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 15rpx;
  padding: 0 30rpx;
  font-size: 36rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.input-area input:focus {
  border-color: #4CAF50;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(76, 175, 80, 0.1);
}

.input-area input.correct {
  border-color: #4CAF50;
  background: #f1f8e9;
  color: #2e7d32;
}

.input-area input.wrong {
  border-color: #f44336;
  background: #ffebee;
  color: #c62828;
}

/* 音频播放区域 */
.audio-player {
  margin-bottom: 40rpx;
}

.audio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.audio-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.play-count {
  font-size: 26rpx;
  color: #666;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.play-btn {
  min-width: 140rpx;
  height: 80rpx;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.play-btn:hover {
  background: #45a049;
  transform: translateY(-2rpx);
}

.play-btn.playing {
  background: #ff9800;
}

.play-btn::after {
  border: none;
}

.audio-slider {
  flex: 1;
  margin: 0 20rpx;
}

.time {
  font-size: 24rpx;
  color: #666;
  min-width: 120rpx;
  text-align: right;
}

/* 结果展示 */
.result-area {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.result-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.correct-word {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  letter-spacing: 2rpx;
}

.user-answer {
  margin-bottom: 20rpx;
}

.user-answer .label {
  font-size: 26rpx;
  color: #666;
}

.user-answer .answer {
  font-size: 32rpx;
  color: #f44336;
  font-weight: 500;
  margin-left: 10rpx;
}

.result-message {
  font-size: 32rpx;
  font-weight: 500;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  display: inline-block;
}

.result-message.correct {
  background: #e8f5e8;
  color: #2e7d32;
}

.result-message.wrong {
  background: #ffebee;
  color: #c62828;
}

/* 操作按钮 */
.action-buttons {
  text-align: center;
}

.action-btn {
  width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #4CAF50;
  color: white;
}

.action-btn.primary:hover {
  background: #45a049;
  transform: translateY(-2rpx);
}

.action-btn.secondary {
  background: #e9ecef;
  color: #333;
}

.action-btn.secondary:hover {
  background: #dee2e6;
}

.action-btn:disabled {
  opacity: 0.5;
  transform: none !important;
}

.action-btn::after {
  border: none;
}

/* 完成提示模态框 */
.completion-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 25rpx;
  padding: 50rpx;
  margin: 40rpx;
  max-width: 600rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.statistics {
  margin-bottom: 40rpx;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 30rpx;
  color: #666;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 500;
  color: #4CAF50;
}

.modal-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.modal-btn {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  background: #e9ecef;
  color: #333;
  transition: all 0.3s ease;
}

.modal-btn.primary {
  background: #4CAF50;
  color: white;
}

.modal-btn::after {
  border: none;
}

/* 详细结果页面 */
.result-page {
  background: white;
  border-radius: 25rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.15);
}

.result-header {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  padding: 40rpx;
  text-align: center;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.result-summary {
  display: flex;
  justify-content: space-around;
}

.summary-item {
  text-align: center;
}

.summary-item .number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.summary-item .label {
  font-size: 26rpx;
  opacity: 0.9;
}

.summary-item.error .number {
  color: #ffcccb;
}

.result-actions {
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.result-actions .action-btn {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
}

.result-list {
  max-height: 800rpx;
  overflow-y: auto;
}

.result-item {
  position: relative;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.result-item.wrong {
  cursor: pointer;
}

.result-item.wrong:hover {
  background: #f8f9fa;
}

.result-item.selected {
  background: #e3f2fd;
  border-left: 4rpx solid #2196f3;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.word-section .word {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.word-section .meaning {
  font-size: 26rpx;
  color: #666;
}

.status-section .status-icon {
  font-size: 36rpx;
}

.result-item.correct .status-icon {
  color: #4CAF50;
}

.result-item.wrong .status-icon {
  color: #f44336;
}

.item-content {
  margin-top: 20rpx;
}

.answer-comparison {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.answer-comparison > view {
  margin-bottom: 15rpx;
}

.answer-comparison > view:last-child {
  margin-bottom: 0;
}

.answer-comparison .label {
  font-size: 24rpx;
  color: #666;
  margin-right: 15rpx;
}

.answer-comparison .answer {
  font-size: 28rpx;
  font-weight: 500;
}

.user-answer .answer {
  color: #f44336;
}

.correct-answer .answer {
  color: #4CAF50;
}

.select-indicator {
  position: absolute;
  top: 30rpx;
  right: 60rpx;
  font-size: 32rpx;
}

.result-footer {
  padding: 30rpx;
  text-align: center;
  background: #f8f9fa;
}

.footer-btn {
  width: 200rpx;
  height: 70rpx;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 35rpx;
  font-size: 28rpx;
}

.footer-btn::after {
  border: none;
}

/* 响应式适配 */
@media (max-width: 320px) {
  .container {
    padding: 15rpx;
  }
  
  .practice-card {
    padding: 30rpx;
  }
  
  .modal-content {
    margin: 20rpx;
    padding: 30rpx;
  }
}

/* 模拟环境提示 */
.simulation-notice {
  background: linear-gradient(135deg, #fff3cd, #ffeeba);
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 12px;
  margin: 10px 0;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.notice-icon {
  font-size: 24px;
  margin-right: 10px;
  flex-shrink: 0;
}

.notice-text {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.notice-title {
  font-size: 16px;
  font-weight: bold;
  color: #856404;
  margin-bottom: 4px;
}

.notice-desc {
  font-size: 14px;
  color: #856404;
  line-height: 1.4;
}

/* 调试控制按钮 */
.debug-control {
  display: flex;
  justify-content: center;
  margin: 10rpx 0;
  gap: 15rpx;
}

.debug-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: #f0f0f0;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  min-width: 140rpx;
  transition: all 0.2s;
}

.debug-btn:hover {
  background: #e0e0e0;
}

.debug-btn .btn-icon {
  font-size: 28rpx;
}

.debug-btn .btn-text {
  font-size: 24rpx;
}

.debug-btn::after {
  border: none;
}

/* ========================= 听写练习模式样式 ========================= */
.dictation-practice-page {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.words-display-area {
  margin-bottom: 40rpx;
}

.word-card-practice {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #4CAF50;
}

.word-number {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.reveal-sections {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.word-reveal-section,
.phonetic-reveal-section,
.meaning-reveal-section,
.example-reveal-section {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.reveal-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reveal-btn:hover {
  background: #e9ecef;
}

.reveal-btn.revealed {
  background: #e8f5e8;
  border-color: #4CAF50;
}

.reveal-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.reveal-btn.revealed .reveal-title {
  color: #2e7d32;
}

.reveal-icon {
  font-size: 24rpx;
}

.word-content {
  margin-top: 20rpx;
  text-align: center;
}

.word-text-large {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.phonetic-text-large {
  font-size: 28rpx;
  color: #666;
  font-style: italic;
  display: block;
  margin-top: 15rpx;
}

.meaning-text-large {
  font-size: 32rpx;
  color: #2e7d32;
  font-weight: 500;
  display: block;
  margin-top: 15rpx;
  line-height: 1.5;
}

.example-text-large {
  font-size: 28rpx;
  color: #555;
  display: block;
  margin-top: 15rpx;
  line-height: 1.6;
  font-style: italic;
}

/* 练习模式操作按钮 */
.practice-page-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  justify-content: center;
  padding: 20rpx 0;
}

.practice-page-actions .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  min-width: 120rpx;
  height: 70rpx;
  border-radius: 35rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  color: white;
}

.reveal-all-btn {
  background: #4CAF50;
}

.reveal-all-btn:hover {
  background: #45a049;
  transform: translateY(-2rpx);
}

.hide-all-btn {
  background: #ff9800;
}

.hide-all-btn:hover {
  background: #f57c00;
  transform: translateY(-2rpx);
}

.practice-page-actions .next-btn {
  background: #2196F3;
}

.practice-page-actions .next-btn:hover {
  background: #1976D2;
  transform: translateY(-2rpx);
}

.practice-page-actions .finish-btn {
  background: #9C27B0;
}

.practice-page-actions .finish-btn:hover {
  background: #7B1FA2;
  transform: translateY(-2rpx);
}

.practice-page-actions .action-btn:disabled {
  opacity: 0.5;
  transform: none !important;
}

.practice-page-actions .action-btn::after {
  border: none;
}

.practice-page-actions .btn-icon {
  font-size: 24rpx;
}

.practice-page-actions .btn-text {
  font-size: 26rpx;
}

/* ========================= 测试结果页面样式 ========================= */
.test-result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20rpx;
}

.result-container {
  background: white;
  border-radius: 20rpx;
  max-height: 90vh;
  width: 100%;
  max-width: 700rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
}

.result-close {
  font-size: 40rpx;
  cursor: pointer;
  padding: 10rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-overview {
  padding: 30rpx;
  background: #f8f9fa;
}

.stats-card {
  display: flex;
  justify-content: space-around;
  background: white;
  border-radius: 15rpx;
  padding: 25rpx 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 🔧 修复：使用更具体的选择器避免样式冲突 */
.stats-card .stat-item {
  text-align: center;
  flex: 1;
  display: block; /* 覆盖其他地方的flex设置 */
}

.stats-card .stat-value {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #333;
  display: block; /* 确保数字在标签上方 */
}

.stats-card .stat-value.correct {
  color: #4CAF50;
}

.stats-card .stat-value.wrong {
  color: #f44336;
}

.stats-card .stat-value.accuracy {
  color: #2196F3;
}

.stats-card .stat-label {
  font-size: 26rpx;
  color: #666;
  display: block; /* 确保标签在数字下方 */
}

.test-time {
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

.time-label {
  margin-right: 10rpx;
}

.time-value {
  font-weight: bold;
  color: #333;
}

.answer-results-section {
  padding: 0 30rpx;
  margin-bottom: 10rpx;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  flex-shrink: 0;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.select-actions {
  display: flex;
  gap: 15rpx;
}

.select-btn {
  font-size: 22rpx;
  color: #2196F3;
  padding: 6rpx 12rpx;
  border: 1rpx solid #2196F3;
  border-radius: 12rpx;
  cursor: pointer;
}

.select-btn.primary {
  background: #2196F3;
  color: white;
}

.select-btn:hover {
  background: #2196F3;
  color: white;
}

.results-list {
  flex: 1;
  overflow-y: auto;
  min-height: 400rpx;
  max-height: 800rpx;
  padding-bottom: 10rpx;
}

.result-item {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 10rpx 15rpx;
  margin-bottom: 6rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  gap: 10rpx;
  min-height: 45rpx;
}

.result-item.selected {
  border-color: #4CAF50;
  background: #e8f5e8;
}

.result-item.correct {
  border-left: 4rpx solid #4CAF50;
}

.result-item.wrong {
  border-left: 4rpx solid #f44336;
}

/* 错词结果项内容区域 - 强制一行显示 */
.result-item .word-content {
  flex: 1 !important;
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 2rpx !important;
  min-width: 0 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  flex-wrap: nowrap !important;
  height: auto !important;
  line-height: 1 !important;
}

/* 强制所有text元素在同一行 */
.result-item .word-content text {
  display: inline-block !important;
  vertical-align: middle !important;
  line-height: 1.2 !important;
  white-space: nowrap !important;
  margin: 0 !important;
  padding: 0 !important;
  font-size: inherit !important;
}



/* 🔧 听写模式专用样式 - 恢复正常显示 */
.word-content-dictation {
  flex: 1;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  gap: 6rpx;
}

/* 听写模式单词文本 */
.word-text-dictation {
  font-size: 20rpx;
  font-weight: bold;
  color: #333;
  flex-shrink: 0;
  max-width: 100rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #e3f2fd;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
}

/* 听写模式答案标签 */
.answer-label-dictation {
  font-size: 12rpx;
  color: #666;
  flex-shrink: 0;
}

/* 听写模式用户答案 */
.user-answer-dictation {
  font-size: 12rpx;
  color: #f44336;
  flex-shrink: 0;
  max-width: 80rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #ffebee;
  padding: 1rpx 4rpx;
  border-radius: 3rpx;
}

/* 听写模式正确答案 */
.correct-answer-dictation {
  font-size: 12rpx;
  color: #4CAF50;
  flex-shrink: 0;
  max-width: 100rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #e8f5e8;
  padding: 1rpx 4rpx;
  border-radius: 3rpx;
}

/* 听写模式分隔符 */
.separator-dictation {
  font-size: 12rpx;
  color: #ccc;
  flex-shrink: 0;
}





/* 错词结果一行显示 - 与单词测试保持一致 */
.word-content-inline {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 3rpx;
  white-space: nowrap;
  overflow: hidden;
}

/* 单词 - 与单词测试保持一致 */
.word-text-inline {
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
  flex-shrink: 0;
  max-width: 110rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 18rpx;
}

/* 您的答案、正确答案标签 - 与单词测试保持一致 */
.answer-label-inline {
  font-size: 13rpx;
  color: #666;
  flex-shrink: 0;
  white-space: nowrap;
  margin-right: 2rpx;
}

/* 答案内容 - 与单词测试保持一致 */
.answer-text-inline {
  font-size: 13rpx;
  font-weight: 500;
  flex-shrink: 0;
  max-width: 140rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 6rpx;
}

/* 错误答案红色 */
.answer-text-inline.wrong-inline {
  color: #f44336;
}

/* 正确答案绿色 */
.answer-text-inline.correct-inline {
  color: #4CAF50;
}

/* 分隔符 - 与单词测试保持一致 */
.answer-separator-inline {
  font-size: 13rpx;
  color: #ccc;
  margin: 0 6rpx;
  flex-shrink: 0;
  white-space: nowrap;
}

.select-checkbox {
  position: static;
  flex-shrink: 0;
}

.checkbox {
  font-size: 24rpx;
}

.item-number {
  background: #e9ecef;
  color: #666;
  font-size: 16rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.result-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0;
}

.status-icon {
  font-size: 32rpx;
}

.user-input {
  font-size: 24rpx;
  color: #f44336;
  font-style: italic;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
}

.action-btn {
  background: white;
  border: 2rpx solid #e9ecef;
  border-radius: 15rpx;
  padding: 20rpx 15rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  min-height: 100rpx;
  transition: all 0.3s ease;
  color: #333;
}

.action-btn.retry-dictation {
  border-color: #667eea;
  color: #667eea;
}

.action-btn.retry-en2cn {
  border-color: #4facfe;
  color: #4facfe;
}

.action-btn.retry-cn2en {
  border-color: #43e97b;
  color: #43e97b;
}

.action-btn.retry-game {
  border-color: #f093fb;
  color: #f093fb;
}

.action-btn.share-test {
  border-color: #56ab2f;
  color: #56ab2f;
}

.action-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-btn.retry-dictation:hover {
  background: #667eea;
  color: white;
}

.action-btn.retry-en2cn:hover {
  background: #4facfe;
  color: white;
}

.action-btn.retry-cn2en:hover {
  background: #43e97b;
  color: white;
}

.action-btn.retry-game:hover {
  background: #f093fb;
  color: white;
}

.action-btn.share-test:hover {
  background: #56ab2f;
  color: white;
}

.action-btn::after {
  border: none;
}

.action-btn .btn-icon {
  font-size: 32rpx;
}

.action-btn .btn-text {
  font-size: 22rpx;
  text-align: center;
  line-height: 1.2;
}

.perfect-score {
  text-align: center;
  padding: 50rpx 30rpx;
}

.perfect-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.perfect-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 15rpx;
}

.perfect-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.result-footer {
  padding: 30rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #e9ecef;
  display: flex;
  gap: 15rpx;
  justify-content: center;
}

/* 竞赛模式下的四个按钮网格布局 */
.competition-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  width: 100%;
}

/* 非竞赛模式下的正常按钮布局 */
.normal-buttons {
  display: flex;
  gap: 15rpx;
  justify-content: center;
}

.footer-btn {
  min-width: 150rpx;
  height: 70rpx;
  border-radius: 35rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.footer-btn.share-result {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.footer-btn.share-result:active {
  transform: scale(0.95);
}

.footer-btn.secondary {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.footer-btn.secondary:hover {
  background: linear-gradient(135deg, #3d8bfe, #00d4fe);
  transform: translateY(-2rpx);
}

.footer-btn.disabled {
  background: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}

.footer-btn.disabled:active {
  transform: none;
}

.footer-btn::after {
  border: none;
}

/* ================ 分享弹窗样式 ================ */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.share-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: calc(100% - 80rpx);
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.share-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-info {
  margin-bottom: 40rpx;
}

.share-desc {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.share-note {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.share-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.wechat-share-btn, .copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 32rpx;
  border: none;
  gap: 15rpx;
}

.wechat-share-btn {
  background: linear-gradient(135deg, #07c160, #00ae47);
  color: white;
}

.copy-btn {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #e0e0e0;
}

.share-btn-icon, .copy-btn-icon {
  font-size: 36rpx;
}

.share-btn-text, .copy-btn-text {
  font-size: 32rpx;
  font-weight: bold;
}

/* 单个单词听写模式样式 */
.single-word-dictation {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 40rpx 0;
}

.play-status-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin: 0 20rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.status-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.status-icon {
  font-size: 80rpx;
  line-height: 1;
}

.status-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.countdown-display {
  margin-top: 20rpx;
}

.countdown-text {
  font-size: 60rpx;
  font-weight: bold;
  color: #667eea;
}

.word-input-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 20rpx;
}

.input-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.input-label {
  font-size: 32rpx;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
  font-weight: 500;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.dictation-input {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 15rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  background: #fff;
  transition: all 0.3s ease;
}

.dictation-input.has-content {
  border-color: #667eea;
  background: #f8f9ff;
}

.dictation-input.correct {
  border-color: #4caf50;
  background: #f1f8e9;
  color: #2e7d32;
}

.dictation-input.wrong {
  border-color: #f44336;
  background: #ffebee;
  color: #c62828;
}

.submit-word-btn {
  width: 120rpx;
  height: 80rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.submit-word-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.submit-word-btn.disabled {
  background: #e0e0e0;
  color: #999;
}

.dictation-answer-display {
  margin-top: 20rpx;
}

.answer-result {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  padding: 20rpx;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
}

.answer-result.correct {
  background: #e8f5e8;
  color: #2e7d32;
}

.answer-result.wrong {
  background: #ffebee;
  color: #c62828;
}

.correct-word {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  padding: 15rpx 20rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.word-label {
  font-size: 26rpx;
  color: #666;
}

.word-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.word-meaning {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  padding: 15rpx 20rpx;
  background: #f0f8ff;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.meaning-label {
  font-size: 26rpx;
  color: #666;
}

.meaning-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.word-phonetic {
  text-align: center;
  padding: 10rpx;
}

.dictation-controls {
  padding: 40rpx 20rpx 20rpx;
  display: flex;
  justify-content: center;
  gap: 30rpx;
}

.control-btn {
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  transition: all 0.3s ease;
}

.replay-btn {
  background: linear-gradient(135deg, #ffa726, #ff7043);
  color: white;
}

.next-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* ========================= 新的错词重测选项样式 ========================= */

/* 错词重测选项区域 */
.retry-options-section {
  padding: 15rpx 30rpx;
  background: #f8f9fa;
  margin-top: 5rpx;
}

.retry-title {
  margin-bottom: 15rpx;
  text-align: center;
}

.retry-title-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 测试模式选项 - 一行显示 */
.retry-modes {
  display: flex;
  gap: 8rpx;
  margin-bottom: 15rpx;
}

.mode-btn {
  flex: 1;
  background: white !important;
  border: 2rpx solid #e9ecef !important;
  border-radius: 12rpx !important;
  padding: 12rpx 6rpx !important;
  font-size: 22rpx !important;
  font-weight: 500 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4rpx !important;
  min-height: 65rpx !important;
  transition: all 0.3s ease !important;
  color: #333 !important;
}

.mode-icon {
  font-size: 22rpx;
  line-height: 1;
  color: inherit;
}

.mode-text {
  font-size: 18rpx;
  line-height: 1.1;
  text-align: center;
  color: inherit;
}

/* 分享选项 - 单独一行 */
.share-option {
  border-top: 1rpx solid #e9ecef;
  padding-top: 15rpx;
  display: flex;
  justify-content: center;
}

.share-btn {
  background: white !important;
  border: 2rpx solid #9c27b0 !important;
  border-radius: 12rpx !important;
  padding: 12rpx 25rpx !important;
  font-size: 24rpx !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 6rpx !important;
  color: #9c27b0 !important;
  min-width: 180rpx !important;
  height: 50rpx !important;
}

.share-btn .share-icon,
.share-btn .share-text {
  color: #9c27b0 !important;
}

.share-icon {
  font-size: 20rpx;
  line-height: 1;
  color: inherit;
}

.share-text {
  font-size: 20rpx;
  line-height: 1;
  color: inherit;
}

.mode-btn.retry-dictation {
  border-color: #667eea !important;
  color: #667eea !important;
}

.mode-btn.retry-dictation .mode-icon,
.mode-btn.retry-dictation .mode-text {
  color: #667eea !important;
}

.mode-btn.retry-en2cn {
  border-color: #4facfe !important;
  color: #4facfe !important;
}

.mode-btn.retry-en2cn .mode-icon,
.mode-btn.retry-en2cn .mode-text {
  color: #4facfe !important;
}

.mode-btn.retry-cn2en {
  border-color: #43e97b !important;
  color: #43e97b !important;
}

.mode-btn.retry-cn2en .mode-icon,
.mode-btn.retry-cn2en .mode-text {
  color: #43e97b !important;
}

.mode-btn.retry-game {
  border-color: #ff9800 !important;
  color: #ff9800 !important;
}

.mode-btn.retry-game .mode-icon,
.mode-btn.retry-game .mode-text {
  color: #ff9800 !important;
}

/* 无错词提示样式 */
.no-wrong-words {
  text-align: center;
  padding: 60rpx 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin: 20rpx 0;
}

.no-wrong-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.no-wrong-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 10rpx;
}

.no-wrong-desc {
  font-size: 26rpx;
  color: #666;
}
