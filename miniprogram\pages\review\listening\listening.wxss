.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* 进度条样式 */
.progress-bar {
  height: 6rpx;
  background-color: #e0e0e0;
  border-radius: 3rpx;
  margin-bottom: 40rpx;
  position: relative;
}

.progress {
  height: 100%;
  background-color: #4A90E2;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  right: 0;
  top: 20rpx;
  font-size: 24rpx;
  color: #666;
}

/* 练习卡片样式 */
.practice-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 音频区域样式 */
.audio-section {
  margin-bottom: 40rpx;
}

.audio-text {
  font-size: 36rpx;
  color: #333;
  margin-bottom: 30rpx;
  line-height: 1.6;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.play-btn {
  width: 80rpx;
  height: 80rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
}

.play-btn::after {
  display: none;
}

.play-icon {
  width: 40rpx;
  height: 40rpx;
}

.audio-slider {
  flex: 1;
  margin: 0;
}

/* 翻译区域样式 */
.translation-section {
  background-color: #f8f8f8;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 40rpx;
}

.translation-text {
  font-size: 32rpx;
  color: #666;
  line-height: 1.6;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #4A90E2;
  border: none;
}

.action-btn:last-child {
  background-color: #FF6B6B;
}

/* 完成提示样式 */
.completion-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  width: 560rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
}

.completion-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.completion-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #4A90E2;
}

.completion-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #4A90E2;
  border: none;
} 