const app = getApp();

Page({
  data: {
    testMode: '',     // en_to_cn, cn_to_en, elimination, phrase_en2zh, phrase_zh2en
    libraryId: '',
    selectedWords: [],
    wordsPerGroup: 10, // 用户选择的每组词汇数量，默认10
    showWordsPerGroupSelection: false, // 是否显示分组选择（仅当词汇数量过多时）
    
    // 模式配置 - 英译汉/汉译英
    practiceMode: {
      title: '练习模式',
      desc: '查看完整单词信息，按自己的节奏学习',
      icon: '📖',
      features: ['显示完整单词信息', '包含音标和例句', '自主控制学习节奏', '可标记掌握程度']
    },
    
    testModeConfig: {
      title: '测试模式', 
      desc: '选择题形式，测试词汇掌握情况',
      icon: '✏️',
      features: ['选择题形式', '自动记录错题', '支持时间限制', '可分享给他人']
    },
    
    competitionMode: {
      title: '创建单词竞赛',
      desc: '创建公开竞赛，与他人一起挑战',
      icon: '🏆',
      features: ['创建公开竞赛', '实时排行榜', '7天自动删除', '支持分享转发']
    },
    
    // 消消乐模式配置
    eliminationPracticeMode: {
      title: '练习模式',
      desc: '不限时的单词消消乐游戏',
      icon: '🎮',
      features: ['不限时游戏', '熟悉游戏规则', '学习词汇对应', '无压力练习']
    },
    
    eliminationTestMode: {
      title: '测试模式',
      desc: '限时的单词消消乐挑战',
      icon: '⚡',
      features: ['限时挑战', '测试反应速度', '记录游戏成绩', '提升专注力']
    },
    
    selectedMode: '',  // practice 或 test
    
    // 测试模式配置 - 英译汉/汉译英
    timeLimit: '15', // 默认15秒每题
    timeLimitOptions: [
      { value: '5', label: '5秒/题', desc: '快速挑战' },
      { value: '10', label: '10秒/题', desc: '标准速度' },
      { value: '15', label: '15秒/题', desc: '正常节奏' },
      { value: '20', label: '20秒/题', desc: '充裕时间' },
      { value: '30', label: '30秒/题', desc: '慢节奏' }
    ],
    
    // 消消乐测试模式配置
    eliminationGameMode: '60', // 60, 10, 30, 300 - 默认标准模式1分钟
    eliminationGameModes: [
      { value: '60', label: '标准模式', desc: '1分钟挑战' },
      { value: '10', label: '闪电模式', desc: '10秒挑战' },
      { value: '30', label: '火速模式', desc: '30秒挑战' },
      { value: '300', label: '学习模式', desc: '5分钟充裕时间' }
    ],
    
    // 🔧 英译汉/汉译英分组选择选项
    wordsPerGroupOptions: [
      { value: 5, label: '5个/组', desc: '快速练习' },
      { value: 10, label: '10个/组', desc: '推荐默认' },
      { value: 15, label: '15个/组', desc: '标准分组' },
      { value: 20, label: '20个/组', desc: '适中分组' },
      { value: 30, label: '30个/组', desc: '大分组' },
      { value: 50, label: '50个/组', desc: '挑战分组' }
    ],
    
    shareOption: 'self', // self 或 others
    
    // 有效期选择选项
    expireDays: 7,  // 默认7天
    expireDaysOptions: [
      { value: 7, label: '7天', desc: '短期分享' },
      { value: 30, label: '30天', desc: '一个月' },
      { value: 60, label: '60天', desc: '两个月' },
      { value: 90, label: '90天', desc: '三个月' },
      { value: 180, label: '180天', desc: '半年' },
      { value: 365, label: '1年', desc: '长期分享' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('=== 单词测试模式选择页面加载 ===');
    console.log('接收到的options:', options);
    
    // 启用分享功能，包括朋友圈分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    // 检查是否来自分享
    if (options.shareId && options.shareMode === 'share') {
      // 如果是通过分享链接进入，跳转到测试页面
      wx.redirectTo({
        url: `/pages/wordtest/test/test?shareId=${options.shareId}&shareMode=share&testMode=${options.testMode || 'en_to_cn'}`
      });
      return;
    }

    // 原有的初始化逻辑
    this.initializePage(options);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('=== 单词测试模式选择页面显示 ===');

    // 检测分享成功（通过时间差判断）
    const { shareStartTime, shareId } = this.data;
    if (shareStartTime) {
      const now = Date.now();
      const timeDiff = now - shareStartTime;

      // 如果页面显示时间与分享开始时间差在合理范围内（1-10秒），认为是分享成功返回
      if (timeDiff > 1000 && timeDiff < 10000) {
        console.log('检测到分享成功返回，时间差:', timeDiff);

        // 记录分享成功
        if (shareId) {
          this.recordShareAction(shareId, 'wechat_share_success');
        }

        // 显示分享成功弹窗，提供返回和查看分享页选项
        this.showShareSuccessModal();

        // 清除分享开始时间
        this.setData({
          shareStartTime: null
        });
      }
    }
  },

  initializePage(options) {
    // 页面初始化逻辑
    console.log('页面初始化完成');
    
    // 解析URL参数
    const { testMode, libraryId, wordsPerGroup, words, isGrouped, currentGroup, totalGroups } = options;
    const parsedWordsPerGroup = wordsPerGroup ? parseInt(wordsPerGroup) : 10;
    const parsedCurrentGroup = currentGroup ? parseInt(currentGroup) : 1;
    const parsedTotalGroups = totalGroups ? parseInt(totalGroups) : 1;
    const isGroupedMode = isGrouped === 'true';

    console.log('解析参数:', {
      testMode,
      libraryId,
      wordsPerGroup: wordsPerGroup,
      parsedWordsPerGroup: parsedWordsPerGroup,
      isGrouped: isGroupedMode,
      currentGroup: parsedCurrentGroup,
      totalGroups: parsedTotalGroups
    });

    this.setData({
      testMode: testMode,
      libraryId: libraryId,
      wordsPerGroup: parsedWordsPerGroup,
      isGrouped: isGroupedMode,
      currentGroup: parsedCurrentGroup,
      totalGroups: parsedTotalGroups
    });
    
    // 从全局数据或参数中获取选中的词汇
    if (words) {
      try {
        const selectedWords = JSON.parse(decodeURIComponent(words));
        this.setData({ selectedWords });
      } catch (error) {
        console.error('解析词汇数据失败:', error);
      }
    } else {
      // 从全局数据中获取
      const app = getApp();
      const learningData = app.globalData.learningData;
      if (learningData && learningData.words) {
        this.setData({ selectedWords: learningData.words });
        console.log('从全局数据获取词汇数量:', learningData.words.length);
      } else {
        const globalData = app.globalData.selectedWordsForTest;
        if (globalData) {
          this.setData({ selectedWords: globalData });
        }
      }
    }
    
    // 检测是否需要显示分组选择
    this.checkWordsPerGroupVisibility();
    
    // 🔧 确保默认分组选项和时间限制被正确选中
    if (testMode === 'en_to_cn') {
      // 英译汉：默认30个/组，5秒/题
      if (!wordsPerGroup) {
        this.setData({
          wordsPerGroup: 30,
          timeLimit: '5'
        });
      }
    } else if (testMode === 'cn_to_en') {
      // 汉译英：默认10个/组，15秒/题
      if (!wordsPerGroup) {
        this.setData({
          wordsPerGroup: 10,
          timeLimit: '15'
        });
      }
    } else if (testMode === 'phrase_en2zh') {
      // 短语英译汉：默认30个/组，5秒/题（与单词英译汉一致）
      if (!wordsPerGroup) {
        this.setData({
          wordsPerGroup: 30,
          timeLimit: '5'
        });
      }
    } else if (testMode === 'phrase_zh2en') {
      // 短语汉译英：默认10个/组，5秒/题（与单词汉译英一致）
      if (!wordsPerGroup) {
        this.setData({
          wordsPerGroup: 10,
          timeLimit: '5'
        });
      }
    } else if (testMode === 'elimination') {
      this.setData({
        wordsPerGroup: 10 // 消消乐模式默认10个每组
      });
    }

    // 如果是分组学习模式，更新显示信息
    if (this.data.isGrouped) {
      console.log('检测到分组学习模式，更新显示信息');
      this.setData({
        groupInfo: `第${this.data.currentGroup}组 / 共${this.data.totalGroups}组`,
        showGroupInfo: true
      });
    }

    console.log('模式选择页面最终数据:', this.data);
  },
  
  // 🔧 修改：检查是否需要显示分组选择
  checkWordsPerGroupVisibility() {
    const { testMode, selectedWords } = this.data;
    
    // 🔧 修改：英译汉/汉译英/短语测试模式总是显示分组选择，不限制词汇数量
    if (testMode === 'en_to_cn' || testMode === 'cn_to_en' || testMode === 'phrase_en2zh' || testMode === 'phrase_zh2en') {
      this.setData({ showWordsPerGroupSelection: true });
      console.log('英译汉/汉译英/短语测试模式，显示分组选择，词汇数量:', selectedWords.length);
    } else {
      this.setData({ showWordsPerGroupSelection: false });
    }
  },

  // 选择学习模式
  onModeSelect(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({ selectedMode: mode });
    
    // 添加触觉反馈
    wx.vibrateShort();
    
    // 练习模式不需要额外配置，测试模式需要配置
    console.log('选择模式:', mode);
  },

  // 选择时间限制 - 英译汉/汉译英
  onTimeLimitSelect(e) {
    const timeLimit = e.currentTarget.dataset.value;
    console.log('选择时间限制:', timeLimit);
    
    // 添加触觉反馈
    wx.vibrateShort();
    
    this.setData({ 
      timeLimit: timeLimit 
    });
  },

  // 选择消消乐游戏模式
  onEliminationModeSelect(e) {
    const gameMode = e.currentTarget.dataset.value;
    console.log('选择消消乐游戏模式:', gameMode);
    
    // 添加触觉反馈
    wx.vibrateShort();
    
    this.setData({ 
      eliminationGameMode: gameMode 
    });
  },

  // 🔧 新增：选择每组词汇数量
  onWordsPerGroupSelect(e) {
    const count = parseInt(e.currentTarget.dataset.value);
    console.log('选择每组词汇数量:', count);
    
    // 添加触觉反馈
    wx.vibrateShort();
    
    this.setData({ wordsPerGroup: count });
  },

  // 选择分享选项
  onShareOptionSelect(e) {
    const option = e.currentTarget.dataset.option;
    this.setData({ shareOption: option });
  },

  // 选择有效期
  onExpireDaysSelect(e) {
    const days = e.currentTarget.dataset.value;
    this.setData({
      expireDays: days
    });
  },



  // 开始练习模式
  startPractice() {
    const { testMode, selectedWords } = this.data;
    
    // 从全局获取学习数据
    const app = getApp();
    const learningData = app.globalData.learningData;
    
    if (!learningData || !learningData.words) {
      wx.showToast({ title: '数据加载失败', icon: 'error' });
      return;
    }
    
    if (testMode === 'elimination') {
      // 消消乐练习模式 - 跳转到消消乐游戏页面，使用练习模式
      this.startEliminationGame('practice');
    } else if (testMode === 'phrase_en2zh' || testMode === 'phrase_zh2en') {
      // 短语测试练习模式 - 跳转到单词测试页面，使用短语模式
      const mode = testMode === 'phrase_en2zh' ? 'en_to_cn' : 'cn_to_en';
      wx.navigateTo({
        url: `/pages/wordtest/test/test?testMode=${mode}&shareMode=practice&libraryId=${learningData.libraryId}&isPhrase=true`,
        success: () => {
          console.log('跳转到短语练习页面成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({ title: '跳转失败', icon: 'error' });
        }
      });
    } else {
      // 英译汉/汉译英练习模式 - 跳转到测试页面，但使用练习模式参数
      wx.navigateTo({
        url: `/pages/wordtest/test/test?testMode=${testMode}&shareMode=practice`,
        success: () => {
          console.log('跳转到练习页面成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({ title: '跳转失败', icon: 'error' });
        }
      });
    }
  },

  // 开始测试模式
  startTest() {
    const { testMode, selectedWords, timeLimit, shareOption } = this.data;
    
    if (testMode === 'elimination') {
      // 消消乐测试模式 - 根据分享选项决定操作
      if (shareOption === 'self') {
        // 自己测试 - 直接开始消消乐游戏
        this.startEliminationGame(this.data.eliminationGameMode);
      } else if (shareOption === 'competition') {
        // 创建竞赛
        this.startCompetition();
      } else {
        // 分享给他人测试 - 创建消消乐分享测试
        this.createEliminationShareTest();
      }
    } else {
      if (shareOption === 'self') {
        // 自己测试
        this.startSelfTest();
      } else if (shareOption === 'competition') {
        // 创建竞赛
        this.startCompetition();
      } else {
        // 分享给他人测试
        this.createShareTest();
      }
    }
  },

  // 将测试模式转换为竞赛模式
  testModeToCompetitionMode(testMode) {
    console.log('转换测试模式到竞赛模式:', testMode);
    switch(testMode) {
      case 'en_to_cn': return 'en2zh';
      case 'cn_to_en': return 'zh2en';
      case 'dictation': return 'dictation';
      case 'elimination': return 'elimination';
      default: return testMode;
    }
  },

  // 开始创建竞赛
  async startCompetition() {
    const { testMode, selectedWords } = this.data;
    
    // 检查用户登录状态
    const app = getApp();
    if (!app.isLoggedIn()) {
      wx.showModal({
        title: '需要登录',
        content: '创建竞赛需要登录，是否立即登录？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 从全局获取学习数据
    const learningData = app.globalData.learningData;
    
    if (!learningData || !learningData.words) {
      wx.showToast({ title: '数据加载失败', icon: 'error' });
      return;
    }

    // 使用自定义输入组件替代wx.showModal
    this.selectComponent('#competitionInput').showDialog();
  },

  // 处理竞赛名称输入确认
  async onCompetitionInputConfirm(e) {
    const { testMode } = this.data;
    const app = getApp();
    const learningData = app.globalData.learningData;

    // 获取输入的竞赛名称
    const competitionName = e.detail.value.trim() || (testMode === 'elimination' ? '我的消消乐竞赛' : '我的单词竞赛');
    
    wx.showLoading({
      title: '创建中...',
      mask: true
    });

    // 将测试模式转换为竞赛模式
    const competitionMode = this.testModeToCompetitionMode(testMode);
    console.log('创建竞赛:', { testMode, competitionMode });

    try {
      // 超级优化：判断是否使用基于索引的存储策略
      const isSystemLibrary = learningData.libraryId &&
                              !learningData.libraryId.includes('custom') &&
                              !learningData.libraryId.includes('mistake');
      const isLargeLibrary = learningData.words && learningData.words.length >= 100;
      const useIndexBasedStorage = isSystemLibrary && isLargeLibrary;

      // 调用云函数创建竞赛
      const result = await wx.cloud.callFunction({
        name: 'createCompetition',
        data: {
          name: competitionName,
          mode: competitionMode, // 使用转换后的竞赛模式
          // 超级优化：基于索引存储时只传递必要信息
          words: useIndexBasedStorage ?
            [{
              _id: learningData.words[0]._id,
              words: learningData.words[0].words,
              totalCount: learningData.words.length
            }] :
            learningData.words,
          libraryId: learningData.libraryId,
          libraryName: learningData.libraryName,
          // 传递乱序信息，支持基于索引的存储
          isRandomOrder: learningData.isRandom || false,
          wordsPerGroup: this.data.wordsPerGroup,
          timeLimit: this.data.timeLimit, // 添加时间限制
          gameMode: testMode === 'elimination' ? this.data.eliminationGameMode : undefined // 添加消消乐游戏模式
        }
      });

      wx.hideLoading();

      if (result.result.success) {
        wx.showModal({
          title: '创建成功',
          content: `竞赛"${competitionName}"创建成功！现在可以分享给好友参与了。`,
          showCancel: true,
          cancelText: '稍后分享',
          confirmText: '立即分享',
          success: (shareRes) => {
            // 设置全局标记以便高亮显示新创建的竞赛
            const app = getApp();
            app.globalData.highlightCompetitionId = result.result.competitionId;
            
            if (shareRes.confirm) {
              // 跳转到竞赛页面进行分享
              wx.redirectTo({
                url: `/pages/competition/competition?mode=${competitionMode}&highlightId=${result.result.competitionId}`
              });
            } else {
              // 跳转到竞赛列表
              wx.redirectTo({
                url: `/pages/competition/competition?mode=${competitionMode}&highlightId=${result.result.competitionId}`
              });
            }
          }
        });
      } else {
        throw new Error(result.result.message || '创建失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('创建竞赛失败:', error);
      wx.showToast({
        title: '创建失败，请重试',
        icon: 'none'
      });
    }
  },

  // 处理竞赛名称输入取消
  onCompetitionInputCancel() {
    console.log('取消创建竞赛');
  },

  // 开始消消乐游戏
  startEliminationGame(gameMode) {
    const app = getApp();
    const learningData = app.globalData.learningData;
    
    if (!learningData || !learningData.words) {
      wx.showToast({ title: '数据加载失败', icon: 'error' });
      return;
    }

    // 转换词汇格式为消消乐需要的格式（与wordlist.js中的逻辑保持一致）
    const gameWords = learningData.words.map(word => ({
      english: word.words || word.word || word.english,
      chinese: word.meaning || (word.meanings && word.meanings[0] && word.meanings[0].definitions && word.meanings[0].definitions[0].definition) || word.chinese || '无释义'
    }));

    console.log('原始词汇数据样本:', learningData.words[0]);
    console.log('转换后游戏数据样本:', gameWords[0]);
    console.log('总词汇数量:', gameWords.length);

    // 使用用户选择的每组词汇数量
    const MAX_WORDS_PER_GROUP = this.data.wordsPerGroup;
    
    console.log('使用用户选择的每组词汇数量:', MAX_WORDS_PER_GROUP);
    
    if (gameWords.length <= MAX_WORDS_PER_GROUP) {
      // 词汇数量少于等于设定值，直接开始游戏
      console.log('词汇数量少，直接开始单关游戏');
      this.startSingleEliminationGame(gameWords, 1, 1, gameMode);
    } else {
      // 词汇数量大于设定值，直接开始第一关（用户已经在前一个页面确认过分组）
      const totalGroups = Math.ceil(gameWords.length / MAX_WORDS_PER_GROUP);
      const firstGroupWords = gameWords.slice(0, MAX_WORDS_PER_GROUP);
      console.log('词汇数量多，分组游戏:', {
        totalWords: gameWords.length,
        wordsPerGroup: MAX_WORDS_PER_GROUP,
        totalGroups: totalGroups,
        firstGroupWords: firstGroupWords.length
      });
      this.startSingleEliminationGame(firstGroupWords, 1, totalGroups, gameMode, gameWords);
    }
  },

  // 计算最大可容纳的词汇数量（与puzzle页面保持一致）
  calculateMaxWords() {
    const containerWidth = 710;
    const containerHeight = 1200;
    const minBlockWidth = 80;
    const minBlockHeight = 40;
    const paddingX = 20;
    const paddingY = 10;
    const spacingX = 15;
    const spacingY = 10;
    
    // 计算最大可能的行列数
    const maxCols = Math.floor((containerWidth - paddingX * 2 + spacingX) / (minBlockWidth + spacingX));
    const maxRows = Math.floor((containerHeight - paddingY * 2 + spacingY) / (minBlockHeight + spacingY));
    
    // 最大位置数 = 最大行数 * 最大列数
    const maxPositions = maxCols * maxRows;
    
    // 最大词汇数 = 最大位置数 / 2
    const maxWords = Math.floor(maxPositions / 2);
    
    console.log('计算最大词汇数:', {
      containerWidth,
      containerHeight,
      maxCols,
      maxRows,
      maxPositions,
      maxWords
    });
    
    return maxWords;
  },

  // 开始单个消消乐游戏
  startSingleEliminationGame(groupWords, currentGroup, totalGroups, gameMode, allWords = null) {
    const app = getApp();
    
    // 设置消消乐游戏数据
    app.globalData.eliminationGameData = {
      words: groupWords,
      currentGroup: currentGroup,
      totalGroups: totalGroups,
      allWords: allWords, // 保存所有词汇，用于下一关
      wordsPerGroup: this.data.wordsPerGroup, // 保存用户选择的每组词汇数量
      mode: gameMode,
      fromCustom: true
    };

    console.log('消消乐游戏数据:', app.globalData.eliminationGameData);
    
    // 跳转到消消乐游戏页面
    wx.navigateTo({
      url: `/pages/task/puzzle/puzzle?mode=custom&gameMode=${gameMode}&group=${currentGroup}&total=${totalGroups}`,
      success: () => {
        console.log('跳转到消消乐游戏成功');
      },
      fail: (err) => {
        console.error('跳转到消消乐游戏失败:', err);
        wx.showToast({ title: '跳转失败', icon: 'error' });
      }
    });
  },

  // 创建消消乐分享测试
  async createEliminationShareTest() {
    const { testMode, eliminationGameMode, libraryId } = this.data;
    
    wx.showLoading({ title: '创建分享链接...' });
    
    try {
      // 生成分享测试ID
      const shareId = 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      
      // 从全局获取学习数据
      const app = getApp();
      const learningData = app.globalData.learningData;
      
      if (!learningData || !learningData.words) {
        wx.hideLoading();
        wx.showToast({ title: '数据加载失败', icon: 'error' });
        return;
      }
      
      // 获取当前用户信息
      const currentUser = wx.getStorageSync('userInfo') || {};
      
      // 转换词汇格式为消消乐需要的格式
      const gameWords = learningData.words.map(word => ({
        english: word.words || word.word || word.english,
        chinese: word.meaning || (word.meanings && word.meanings[0] && word.meanings[0].definitions && word.meanings[0].definitions[0].definition) || word.chinese || '无释义'
      }));
      
      // 使用用户选择的每组词汇数量
      const MAX_WORDS_PER_GROUP = this.data.wordsPerGroup;
      
      // 对于分享测试，使用第一组词汇
      let shareWords = gameWords;
      if (gameWords.length > MAX_WORDS_PER_GROUP) {
        shareWords = gameWords.slice(0, MAX_WORDS_PER_GROUP);
      }
      
      // 创建分享测试数据
      const shareTestData = {
        shareId: shareId,
        testMode: 'elimination',
        libraryId: libraryId,
        libraryName: learningData.libraryName || '自定义词库',
        words: shareWords,
        allWords: gameWords, // 保存所有词汇，用于多关卡
        wordsPerGroup: MAX_WORDS_PER_GROUP, // 保存每组词汇数量
        eliminationGameMode: eliminationGameMode, // 消消乐游戏模式
        createdAt: new Date().getTime(),
        createTime: Date.now(),
        createdBy: currentUser.nickName || '匿名用户',
        creatorInfo: {
          openid: currentUser.openid,
          nickName: currentUser.nickName,
          avatarUrl: currentUser.avatarUrl
        },
        visitors: [],
        results: [] // 存储测试结果
      };
      
      // 保存到本地存储
      this.saveShareTestToLocal(shareTestData);

      // 保存到云端（必须成功）
      try {
        await this.saveShareTestToCloud(shareTestData);
        console.log('消消乐分享测试已成功保存到云端和本地');
      } catch (err) {
        console.error('云端保存失败:', err);
        wx.hideLoading();
        wx.showModal({
          title: '保存失败',
          content: '分享测试保存到云端失败，可能是网络问题。请检查网络连接后重试。',
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }
      
      wx.hideLoading();
      
      // 显示分享链接
      const shareUrl = `pages/task/puzzle/puzzle?shareId=${shareId}&isShared=true&mode=custom&gameMode=${eliminationGameMode}`;
      
      this.showShareOptions({
        title: '消消乐分享测试已创建',
        content: `分享ID: ${shareId}\n模式: 消消乐\n单词数: ${shareWords.length}个`,
        shareId: shareId,
        shareUrl: shareUrl,
        shareData: {
          testMode: 'elimination',
          testName: `消消乐测试 - ${shareWords.length}个单词`,
          shareId: shareId
        }
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('创建消消乐分享测试失败:', error);
      wx.showToast({ title: '创建失败', icon: 'error' });
    }
  },

  // 加载词库数据用于生成选项
  async loadLibraryWordsForOptions() {
    const { libraryId } = this.data;

    if (!libraryId) {
      console.warn('没有词库ID，无法加载词库数据');
      return [];
    }

    try {
      console.log('加载词库数据用于生成选项，词库ID:', libraryId);

      const result = await wx.cloud.callFunction({
        name: 'getWords',
        data: {
          libraryId: libraryId,
          skip: 0,
          limit: 200, // 加载200个词汇用于生成选项，应该足够了
          getTotalCount: false
        }
      });

      if (result.result && result.result.code === 200) {
        console.log('成功加载词库数据:', result.result.data.length, '个词汇');
        return result.result.data;
      } else {
        console.error('加载词库数据失败:', result.result);
        return [];
      }
    } catch (error) {
      console.error('加载词库数据出错:', error);
      return [];
    }
  },

  // 开始自己测试
  async startSelfTest() {
    const { testMode, selectedWords, timeLimit, wordsPerGroup, isGrouped, currentGroup, totalGroups } = this.data;

    // 从全局获取学习数据
    const app = getApp();
    const learningData = app.globalData.learningData;

    if (!learningData || !learningData.words) {
      wx.showToast({ title: '数据加载失败', icon: 'error' });
      return;
    }

    // 加载词库数据用于生成选项
    const libraryWords = await this.loadLibraryWordsForOptions();

    let actualWords = learningData.words;
    let groupingData = null;
    let needsGrouping = false; // 初始化变量
    let totalWords = learningData.words.length;
    let actualWordsPerGroup = wordsPerGroup;

    // 检查是否是分组学习模式
    if (isGrouped && currentGroup && totalGroups) {
      // 继续分组学习模式
      console.log('继续分组学习模式:', {
        currentGroup: currentGroup,
        totalGroups: totalGroups,
        wordsPerGroup: wordsPerGroup,
        totalWords: learningData.words.length
      });

      // 计算当前组的词汇范围
      const startIndex = (currentGroup - 1) * wordsPerGroup;
      const endIndex = Math.min(startIndex + wordsPerGroup, learningData.words.length);
      actualWords = learningData.words.slice(startIndex, endIndex);

      // 保存分组信息到全局
      groupingData = {
        allWords: learningData.words,
        wordsPerGroup: wordsPerGroup,
        currentGroup: currentGroup,
        totalGroups: totalGroups,
        testMode: testMode,
        timeLimit: timeLimit
      };

      console.log('当前组词汇:', {
        startIndex: startIndex,
        endIndex: endIndex,
        currentGroupWords: actualWords.length,
        firstWord: actualWords[0]?.words,
        lastWord: actualWords[actualWords.length - 1]?.words
      });

    } else {
      // 普通分组逻辑：总是支持分组，如果词汇数量小于分组数量，就用实际数量
      totalWords = learningData.words.length;
      actualWordsPerGroup = Math.min(wordsPerGroup, totalWords);
      needsGrouping = totalWords > actualWordsPerGroup;

      if (needsGrouping) {
        // 获取第一组词汇
        actualWords = learningData.words.slice(0, actualWordsPerGroup);

        // 保存完整的词汇列表和分组信息到全局
        groupingData = {
          allWords: learningData.words,
          wordsPerGroup: actualWordsPerGroup,
          currentGroup: 1,
          totalGroups: Math.ceil(totalWords / actualWordsPerGroup),
          testMode: testMode,
          timeLimit: timeLimit
        };

        console.log('开始分组测试:', {
          totalWords: totalWords,
          selectedWordsPerGroup: wordsPerGroup,
          actualWordsPerGroup: actualWordsPerGroup,
          totalGroups: Math.ceil(totalWords / actualWordsPerGroup),
          firstGroupWords: actualWords.length
        });
      } else {
        console.log('词汇数量不足分组，创建单组测试:', {
          totalWords: totalWords,
          selectedWordsPerGroup: wordsPerGroup
        });
      }
    }

    // 保存分组数据到全局
    if (groupingData) {
      app.globalData.testGroupingData = groupingData;
    }
    
    // 更新学习数据为当前组的词汇
    app.globalData.learningData = {
      ...learningData,
      words: actualWords,
      originalWords: libraryWords.length > 0 ? libraryWords : learningData.words, // 优先使用词库数据，否则使用用户选择的词汇
      isGrouped: needsGrouping,
      currentGroup: needsGrouping ? 1 : null,
      totalGroups: needsGrouping ? Math.ceil(totalWords / actualWordsPerGroup) : null,
      wordsPerGroup: needsGrouping ? actualWordsPerGroup : null
    };
    
    // 跳转到对应的测试页面
    if (testMode === 'phrase_en2zh' || testMode === 'phrase_zh2en') {
      // 短语测试模式 - 跳转到单词测试页面，使用短语模式
      const mode = testMode === 'phrase_en2zh' ? 'en_to_cn' : 'cn_to_en';
      wx.navigateTo({
        url: `/pages/wordtest/test/test?testMode=${mode}&shareMode=self&perQuestionTime=${timeLimit}&isPhrase=true`,
        success: () => {
          console.log('跳转到短语测试页面成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({ title: '跳转失败', icon: 'error' });
        }
      });
    } else {
      // 单词测试模式 - 跳转到单词测试页面
      wx.navigateTo({
        url: `/pages/wordtest/test/test?testMode=${testMode}&shareMode=self&perQuestionTime=${timeLimit}`,
        success: () => {
          console.log('跳转到测试页面成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({ title: '跳转失败', icon: 'error' });
        }
      });
    }
  },

  // 创建分享测试
  async createShareTest() {
    const { testMode, selectedWords, timeLimit, libraryId } = this.data;
    
    wx.showLoading({ title: '创建分享链接...' });
    
    try {
      // 生成分享测试ID
      const shareId = 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      
      // 从全局获取学习数据
      const app = getApp();
      const learningData = app.globalData.learningData;
      
      if (!learningData || !learningData.words) {
        wx.hideLoading();
        wx.showToast({ title: '数据加载失败', icon: 'error' });
        return;
      }
      
      // 获取当前用户信息
      const currentUser = wx.getStorageSync('userInfo') || {};
      
      // 创建分享测试数据
      const shareTestData = {
        shareId: shareId,
        testMode: testMode,
        libraryId: libraryId,
        libraryName: learningData.libraryName || '自定义词库',
        words: learningData.words,
        perQuestionTime: timeLimit, // 改为每题时间
        createdAt: new Date().getTime(),
        createTime: Date.now(),
        createdBy: currentUser.nickName || '匿名用户',
        creatorInfo: {
          openid: currentUser.openid,
          nickName: currentUser.nickName,
          avatarUrl: currentUser.avatarUrl
        },
        visitors: [],
        results: [] // 存储测试结果
      };
      
      // 保存到本地存储
      this.saveShareTestToLocal(shareTestData);

      // 保存到云端（必须成功）
      try {
        await this.saveShareTestToCloud(shareTestData);
        console.log('分享测试已成功保存到云端和本地');
      } catch (err) {
        console.error('云端保存失败:', err);
        wx.hideLoading();
        wx.showModal({
          title: '保存失败',
          content: '分享测试保存到云端失败，可能是网络问题。请检查网络连接后重试。',
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }
      
      wx.hideLoading();
      
      // 使用底部弹出选项，不使用单独弹窗页面
      const testModeText = testMode === 'en_to_cn' ? '英译汉' : '汉译英';
      const emoji = testMode === 'en_to_cn' ? '🇨🇳' : '🇺🇸';
      
      // 设置分享数据（为后续分享做准备）
      this.setData({
        currentShareData: {
          title: `${emoji} ${testModeText}测试 - ${learningData.words.length}个单词`,
          path: this.getSharePath(testMode, shareId),
          imageUrl: '/assets/icons/logo.png'
        },
        shareId: shareId
      });
      
      // 确保分享记录存在
      this.ensureShareRecordExists(shareId);
      
      // 显示分享弹窗
      this.setData({
        showShareModal: true
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('创建分享测试失败:', error);
      wx.showToast({ title: '创建失败', icon: 'error' });
    }
  },

  // 保存分享测试到本地存储
  saveShareTestToLocal(shareTestData) {
    try {
      const shareTests = wx.getStorageSync('shareTests') || {};

      // 为了避免本地存储大小限制，只保存必要的元数据
      // 完整的词汇数据已经保存到云端，本地只需要保存基本信息
      const lightShareTestData = {
        shareId: shareTestData.shareId,
        testMode: shareTestData.testMode,
        libraryId: shareTestData.libraryId,
        libraryName: shareTestData.libraryName,
        wordsCount: shareTestData.words ? shareTestData.words.length : 0, // 只保存词汇数量
        perQuestionTime: shareTestData.perQuestionTime,
        createdAt: shareTestData.createdAt,
        createTime: shareTestData.createTime,
        createdBy: shareTestData.createdBy,
        creatorInfo: shareTestData.creatorInfo,
        lastShareTime: shareTestData.lastShareTime,
        // 不保存完整的words数组，以节省存储空间
        // words: shareTestData.words, // 注释掉这行
        visitors: [],
        results: []
      };

      shareTests[shareTestData.shareId] = lightShareTestData;
      wx.setStorageSync('shareTests', shareTests);
      console.log('分享测试已保存到本地:', shareTestData.shareId, '词汇数量:', lightShareTestData.wordsCount);
    } catch (error) {
      console.error('保存到本地存储失败:', error);
      // 如果本地存储失败，不要抛出错误，因为云端保存是主要的
      // 本地存储主要用于分享记录和统计，不是必需的
      console.warn('本地存储失败，但云端保存成功，分享功能仍可正常使用');
    }
  },

  // 保存分享测试到云端
  async saveShareTestToCloud(shareTestData) {
    try {
      // 获取学习数据以确定是否为乱序
      const app = getApp();
      const learningData = app.globalData.learningData;

      // 超级优化：判断是否使用基于索引的存储策略
      const isSystemLibrary = shareTestData.libraryId &&
                              !shareTestData.libraryId.includes('custom') &&
                              !shareTestData.libraryId.includes('mistake');
      const isLargeLibrary = shareTestData.words && shareTestData.words.length >= 100;
      const useIndexBasedStorage = isSystemLibrary && isLargeLibrary;

      console.log('存储策略分析:', {
        libraryId: shareTestData.libraryId,
        wordsCount: shareTestData.words?.length,
        isSystemLibrary,
        isLargeLibrary,
        useIndexBasedStorage,
        isRandomOrder: learningData?.isRandom
      });

      const result = await wx.cloud.callFunction({
        name: 'createShareTest',
        data: {
          shareId: shareTestData.shareId,  // 传递shareId
          testType: shareTestData.testMode,
          // 超级优化：基于索引存储时只传递必要信息
          words: useIndexBasedStorage ?
            // 索引存储：只传递词汇数量和第一个词汇（用于验证）
            [{
              _id: shareTestData.words[0]._id,
              words: shareTestData.words[0].words,
              totalCount: shareTestData.words.length
            }] :
            // 直接存储：传递完整词汇数据
            shareTestData.words,
          libraryId: shareTestData.libraryId,
          libraryName: shareTestData.libraryName,
          // 超级优化：传递乱序信息，支持基于索引的存储
          isRandomOrder: learningData ? (learningData.isRandom || false) : false,
          settings: shareTestData.testMode === 'elimination' ? {
            eliminationGameMode: shareTestData.eliminationGameMode,
            wordsPerGroup: this.data.wordsPerGroup
          } : {
            timeLimit: shareTestData.perQuestionTime,
            perQuestionTime: shareTestData.perQuestionTime,
            wordsPerGroup: this.data.wordsPerGroup
          },
          expireDays: this.data.expireDays,
          wordsPerGroup: this.data.wordsPerGroup
        }
      });
      
      if (result.result.success) {
        console.log('分享测试已保存到云端:', shareTestData.shareId);
      } else {
        throw new Error(result.result.message || '云端保存失败');
      }
    } catch (error) {
      console.error('保存到云端失败:', error);
      throw error;
    }
  },
  
  // 显示分享选项
  showShareOptions(options) {
    const { title, content, shareId, shareUrl, shareData } = options;
    
    wx.showActionSheet({
      itemList: ['复制测试ID', '分享到微信', '查看管理'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 复制测试ID
            this.copyTestIdOnly({ currentTarget: { dataset: { shareId } } });
            break;
          case 1:
            // 分享到微信
            this.shareToWeChat(shareData);
            break;
          case 2:
            // 查看管理
            this.goToShareManagement(shareId);
            break;
        }
      }
    });
  },

  // 复制分享链接
  copyShareLink(shareUrl) {
    // 从URL中提取shareId
    const match = shareUrl.match(/shareId=([^&]+)/);
    const shareId = match ? match[1] : '';
    
    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);
    
    // 生成分享文本
    const shareText = `墨词自习室单词测试邀请\n\n测试ID: ${shareId}\n\n请在"墨词自习室"小程序中，进入"我的"->"收到的分享"，输入测试ID参与测试。`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        // 记录分享操作
        this.recordShareAction(shareId, 'copy_link');
        
        wx.showModal({
          title: '分享内容已复制',
          content: '已复制分享信息到剪贴板。\n\n对方可以：\n1. 在小程序搜索"墨词自习室"\n2. 进入"我的"->"收到的分享"\n3. 输入测试ID进行测试',
          showCancel: false,
          confirmText: '知道了'
        });
        console.log('分享内容已复制:', shareText);
      },
      fail: () => {
        wx.showToast({ title: '复制失败', icon: 'error' });
      }
    });
  },

  // 分享给微信好友
  directShareToWeChat() {
    const { currentShareData, shareId } = this.data;

    if (!currentShareData || !shareId) {
      wx.showToast({ title: '分享数据不完整', icon: 'error' });
      return;
    }

    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);

    // 记录分享操作
    this.recordShareAction(shareId, 'wechat_share');

    // 提示用户使用右上角分享按钮
    wx.showModal({
      title: '分享给微信好友',
      content: '请点击右上角"..."按钮，选择"转发"来分享给朋友，朋友点击卡片即可直接参与测试！',
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 更新分享菜单，确保右上角分享可用
        wx.updateShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage']
        });
      }
    });
  },

  // 分享到企业微信
  directShareToEnterpriseWeChat() {
    const { currentShareData, shareId } = this.data;

    if (!currentShareData || !shareId) {
      wx.showToast({ title: '分享数据不完整', icon: 'error' });
      return;
    }

    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);

    // 记录分享操作
    this.recordShareAction(shareId, 'enterprise_wechat_share');

    // 设置企业微信分享标识
    this.setData({
      isEnterpriseWeChatShare: true
    });

    // 提示用户使用右上角分享按钮
    wx.showModal({
      title: '分享到企业微信',
      content: '请点击右上角"..."按钮，选择"转发"来分享到企业微信，同事点击卡片即可直接参与测试！',
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 更新分享菜单，确保右上角分享可用
        wx.updateShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage']
        });
      }
    });
  },

  // 分享到朋友圈
  directShareToTimeline() {
    const { currentShareData, shareId } = this.data;

    if (!currentShareData || !shareId) {
      wx.showToast({ title: '分享数据不完整', icon: 'error' });
      return;
    }

    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);

    // 记录分享操作
    this.recordShareAction(shareId, 'timeline_share');

    // 设置朋友圈分享数据
    this.setData({
      currentTimelineShareData: {
        title: currentShareData.title,
        query: `shareId=${shareId}&shareMode=share`,
        imageUrl: currentShareData.imageUrl
      }
    });

    // 更新分享菜单，启用朋友圈分享
    wx.updateShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        // 提示用户点击右上角分享到朋友圈
        wx.showModal({
          title: '分享到朋友圈',
          content: '请点击右上角"..."按钮，选择"分享到朋友圈"来分享测试！',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    });
  },

  // 分享到微信（保留原有方法以兼容其他调用）
  shareToWeChat(shareData) {
    const { testMode, testName, shareId } = shareData;

    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);

    // 获取测试模式的emoji图标
    const modeEmojis = {
      'en_to_cn': '🇨🇳',
      'cn_to_en': '🇺🇸',
      'dictation': '🎧',
      'elimination': '🎮'
    };

    const emoji = modeEmojis[testMode] || '📝';
    const testModeText = testMode === 'en_to_cn' ? '英译汉' : testMode === 'cn_to_en' ? '汉译英' : '消消乐';

    console.log('开始分享到微信:', shareData);

    // 设置当前分享数据
    this.setData({
      currentShareData: {
        title: `${emoji} ${testName}`,
        path: this.getSharePath(testMode, shareId),
        imageUrl: '/assets/icons/logo.png'
      },
      shareId: shareId,
      showShareModal: true
    });

    console.log('分享数据设置完成，显示分享弹窗');
  },

  // 触发微信分享
  triggerWeChatShare(testModeText) {
    // 显示分享按钮弹窗
    wx.showModal({
      title: `分享${testModeText}测试`,
      content: '点击下方"分享给朋友"按钮，选择要发送的朋友，朋友点击卡片即可直接参与测试！',
      showCancel: true,
      cancelText: '取消',
      confirmText: '分享给朋友',
      success: (res) => {
        if (res.confirm) {
          // 显示分享按钮
          this.setData({
            showShareModal: true
          });
        }
      }
    });
  },

  // 关闭分享弹窗
  closeShareModal() {
    this.setData({
      showShareModal: false,
      isEnterpriseWeChatShare: false
    });
  },

  // 复制测试信息按钮点击
  onCopyTestInfo() {
    this.copyTestIdOnly();
    this.closeShareModal();
  },





  // 分享到朋友圈按钮点击
  onShareToTimeline() {
    this.directShareToTimeline();
    this.closeShareModal();
  },

  // 分享给微信好友按钮点击
  onShareToWeChatFriends() {
    const { shareId } = this.data;

    if (shareId) {
      // 记录分享操作
      this.recordShareAction(shareId, 'wechat_share');
    }

    // 设置分享开始标识和特殊标记
    const shareTimestamp = Date.now();
    this.setData({
      showShareModal: false,
      shareStartTime: shareTimestamp
    });

    // 在本地存储中设置标识，用于检测"查看分享页"点击
    wx.setStorageSync('shareButtonClickTime', shareTimestamp);
    wx.setStorageSync('currentShareId', shareId);

    // 分享数据通过 onShareAppMessage 回调提供
  },

  // 分享按钮点击（保留兼容性）
  onShareButtonTap() {
    this.onShareToWeChatFriends();
  },

  // 分享失败时的备选方案
  showShareFallback(shareId, testName, testModeText) {
    wx.showModal({
      title: '分享提示',
      content: '请点击右上角"..."按钮，选择"转发"来分享给朋友，朋友点击卡片即可直接参与测试！',
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 更新分享菜单，确保右上角分享可用
        wx.updateShareMenu({
          withShareTicket: true
        });
      }
    });
  },

  // 复制测试ID
  copyTestIdOnly(e) {
    const shareId = e?.currentTarget?.dataset?.shareId || this.data.shareId;
    
    if (!shareId) {
      wx.showToast({ title: '测试ID不存在', icon: 'error' });
      return;
    }
    
    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);
    
    wx.setClipboardData({
      data: shareId,
      success: () => {
        // 记录分享操作
        this.recordShareAction(shareId, 'copy_test_id');
        
        wx.showToast({ 
          title: '测试ID已复制', 
          icon: 'success' 
        });
        // 关闭分享弹窗
        this.setData({
          showShareModal: false
        });
        setTimeout(() => {
          wx.showModal({
            title: '测试ID已复制',
            content: '测试ID已复制到剪贴板。\n\n对方可以在"我的"->"收到的分享"中输入这个ID来参与测试。',
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1500);
      },
      fail: () => {
        wx.showToast({ title: '复制失败', icon: 'error' });
      }
    });
  },

  // 分享到企业微信
  shareToEnterpriseWeChat(e) {
    const shareId = this.data.shareId;
    const currentShareData = this.data.currentShareData;
    
    if (!shareId || !currentShareData) {
      wx.showToast({ title: '分享数据不完整', icon: 'error' });
      return;
    }
    
    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);
    
    // 记录分享操作
    this.recordShareAction(shareId, 'share_to_enterprise_wechat');
    
    // 设置企业微信分享标识
    this.setData({
      isEnterpriseWeChatShare: true
    });
    
    // 提示用户选择企业微信联系人
    wx.showToast({
      title: '即将打开分享选择',
      icon: 'none',
      duration: 1500
    });
    
    // 关闭分享弹窗
    this.setData({
      showShareModal: false
    });
    
    // 延迟一点时间，然后触发分享
    setTimeout(() => {
      // 这将触发 onShareAppMessage 方法
      wx.updateShareMenu({
        withShareTicket: true,
        success: () => {
          // 模拟点击分享按钮的效果
          wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage']
          });
        }
      });
    }, 1600);
  },

  // 复制完整分享信息（保留原有功能）
  copyCompleteShareInfo(e) {
    const shareId = e?.currentTarget?.dataset?.shareId || this.data.shareId;
    const testName = e?.currentTarget?.dataset?.testName || this.data.currentShareData?.title;
    
    // 确保分享记录存在
    this.ensureShareRecordExists(shareId);
    
    // 从测试名称推断测试模式
    let emoji = '📝';
    let testModeText = '测试';
    if (testName.includes('英译汉')) {
      emoji = '🇨🇳';
      testModeText = '英译汉';
    } else if (testName.includes('汉译英')) {
      emoji = '🇺🇸';
      testModeText = '汉译英';
    } else if (testName.includes('听写')) {
      emoji = '🎧';
      testModeText = '听写';
    }
    
    const shareText = `${emoji} 墨词自习室${testModeText}测试邀请\n\n📝 ${testName}\n🆔 测试ID: ${shareId}\n\n📲 参与方式：\n1. 打开"墨词自习室"小程序\n2. 进入"我的"->"收到的分享"\n3. 输入测试ID开始测试\n\n快来挑战吧！`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        // 记录分享操作
        this.recordShareAction(shareId, 'copy_complete_info');
        
        wx.showToast({ 
          title: '分享信息已复制', 
          icon: 'success' 
        });
        // 关闭分享弹窗
        this.setData({
          showShareModal: false
        });
        setTimeout(() => {
          wx.showModal({
            title: '发送给朋友',
            content: '完整的分享信息已复制，可以发送到微信群聊或好友对话中。\n\n朋友看到后按照说明即可参与测试。',
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1500);
      },
      fail: () => {
        wx.showToast({ title: '复制失败', icon: 'error' });
      }
    });
  },

  // 确保分享记录存在
  async ensureShareRecordExists(shareId) {
    try {
      const shareTests = wx.getStorageSync('shareTests') || {};
      if (!shareTests[shareId]) {
        console.warn('分享记录不存在，尝试重新创建:', shareId);
        // 如果记录不存在，尝试从当前数据重新创建
        const currentUser = wx.getStorageSync('userInfo') || {};
        const { testMode, timeLimit, libraryId } = this.data;
        const app = getApp();
        const learningData = app.globalData.learningData;

        if (learningData && learningData.words) {
          const shareTestData = {
            shareId: shareId,
            testMode: testMode,
            libraryId: libraryId,
            libraryName: learningData.libraryName || '自定义词库',
            words: learningData.words,
            perQuestionTime: timeLimit,
            createdAt: new Date().getTime(),
            createTime: Date.now(),
            createdBy: currentUser.nickName || '匿名用户',
            creatorInfo: {
              openid: currentUser.openid,
              nickName: currentUser.nickName,
              avatarUrl: currentUser.avatarUrl
            },
            visitors: [],
            results: []
          };

          this.saveShareTestToLocal(shareTestData);

          // 同时保存到云端
          try {
            await this.saveShareTestToCloud(shareTestData);
            console.log('重新创建的分享记录已保存到云端');
          } catch (err) {
            console.warn('重新创建的分享记录云端保存失败:', err);
          }
        }
      }
    } catch (error) {
      console.error('确保分享记录存在时出错:', error);
    }
  },

  // 记录分享操作
  recordShareAction(shareId, actionType) {
    try {
      const shareTests = wx.getStorageSync('shareTests') || {};
      if (shareTests[shareId]) {
        // 记录操作历史
        if (!shareTests[shareId].shareActions) {
          shareTests[shareId].shareActions = [];
        }
        
        shareTests[shareId].shareActions.push({
          type: actionType,
          timestamp: Date.now()
        });
        
        // 更新最后分享时间
        shareTests[shareId].lastShareTime = Date.now();
        
        wx.setStorageSync('shareTests', shareTests);
        console.log('分享操作已记录:', { shareId, actionType });
      }
    } catch (error) {
      console.error('记录分享操作失败:', error);
    }
  },

  // 显示分享指引
  showShareGuide(shareId) {
    wx.showModal({
      title: '📱 微信分享指引',
      content: `方式一：复制分享\n• 复制测试ID发送给朋友\n• 朋友在小程序中输入ID参与\n\n方式二：小程序转发\n• 点击右上角"..."按钮\n• 选择"转发"分享给朋友\n• 朋友点击可直接参与测试\n\n推荐使用方式一，更简单快捷！`,
      showCancel: true,
      cancelText: '知道了',
      confirmText: '复制测试ID',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: shareId,
            success: () => {
              wx.showToast({ title: '测试ID已复制', icon: 'success' });
            }
          });
        }
      }
    });
  },

  // 获取分享路径
  getSharePath(testMode, shareId) {
    if (testMode === 'elimination') {
      // 消消乐需要传递gameMode参数，避免显示模式选择界面
      const gameMode = this.data.eliminationGameMode || 'time';
      return `pages/task/puzzle/puzzle?shareId=${shareId}&isShared=true&mode=custom&gameMode=${gameMode}`;
    } else if (testMode === 'dictation') {
      return `pages/spelling/practice/practice?shareId=${shareId}&shareMode=share`;
    } else if (testMode === 'phrase_en2zh' || testMode === 'phrase_zh2en') {
      // 短语测试需要传递isPhrase参数
      const actualTestMode = testMode === 'phrase_en2zh' ? 'en_to_cn' : 'cn_to_en';
      return `pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${actualTestMode}&isPhrase=true`;
    } else {
      return `pages/wordtest/test/test?shareId=${shareId}&shareMode=share`;
    }
  },

  // 跳转到分享管理页面
  goToShareManagement(shareId) {
    // 跳转到我的分享页面
    wx.navigateTo({
      url: '/pages/profile/share/share',
      success: () => {
        console.log('跳转到我的分享页面成功');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({ 
          title: '跳转失败', 
          icon: 'none'
        });
      }
    });
  },

  /**
   * 显示分享成功弹窗
   */
  showShareSuccessModal() {
    wx.showModal({
      title: '分享成功',
      content: '测试已成功分享给好友！',
      cancelText: '返回',
      confirmText: '查看分享页',
      success: (res) => {
        if (res.confirm) {
          // 用户点击"查看分享页"
          this.goToShareManagement();
        } else if (res.cancel) {
          // 用户点击"返回"，不做任何操作，保持在当前页面
          console.log('用户选择返回当前页面');
        }
      }
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const { currentShareData, isEnterpriseWeChatShare, shareId } = this.data;

    if (currentShareData) {
      console.log('分享数据:', currentShareData);

      // 准备分享内容
      let shareTitle = currentShareData.title;
      let sharePath = currentShareData.path;

      // 如果是企业微信分享，添加特殊标识
      if (isEnterpriseWeChatShare) {
        shareTitle = `💼 ${shareTitle} (企业微信分享)`;
        // 重置企业微信分享标识
        this.setData({ isEnterpriseWeChatShare: false });

        // 提示用户这是企业微信分享
        setTimeout(() => {
          wx.showModal({
            title: '企业微信分享提示',
            content: `分享链接已生成！\n\n📋 测试ID: ${shareId}\n\n💡 提醒收到分享的同事：\n1. 点击分享链接打开小程序\n2. 或在小程序"我的"->"收到的分享"中输入测试ID\n\n企业微信用户也可以直接点击链接参与测试！`,
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1000);
      }

      return {
        title: shareTitle,
        path: sharePath,
        imageUrl: currentShareData.imageUrl
      };
    }

    return {
      title: '墨词自习室 - 英语学习好帮手',
      path: '/pages/index/index',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function () {
    const { currentTimelineShareData, shareId } = this.data;

    if (currentTimelineShareData && shareId) {
      console.log('朋友圈分享数据:', currentTimelineShareData);

      // 记录分享操作
      this.recordShareAction(shareId, 'timeline_share');

      return {
        title: currentTimelineShareData.title,
        query: currentTimelineShareData.query,
        imageUrl: currentTimelineShareData.imageUrl,
        success: (res) => {
          console.log('朋友圈分享成功', res);
          this.recordShareAction(shareId, 'timeline_share_success');
          wx.showToast({ title: '分享到朋友圈成功', icon: 'success' });

          setTimeout(() => {
            wx.showModal({
              title: '朋友圈分享成功',
              content: '测试已成功分享到朋友圈，朋友看到后可以直接参与测试！',
              showCancel: false,
              confirmText: '知道了'
            });
          }, 500);
        },
        fail: (err) => {
          console.error('朋友圈分享失败', err);
          wx.showToast({ title: '朋友圈分享失败', icon: 'error' });
        }
      };
    }

    // 默认朋友圈分享数据
    return {
      title: '墨词自习室 - 单词测试挑战',
      query: '',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 返回按钮处理
  onBack() {
    // 检查页面栈，如果只有一个页面或者前一个页面是主页，则使用switchTab
    const pages = getCurrentPages();
    if (pages.length <= 1) {
      wx.switchTab({
        url: '/pages/index/index'
      });
    } else {
      const prevPage = pages[pages.length - 2];
      if (prevPage && prevPage.route === 'pages/index/index') {
        wx.switchTab({
          url: '/pages/index/index'
        });
      } else {
        wx.navigateBack();
      }
    }
  }
});