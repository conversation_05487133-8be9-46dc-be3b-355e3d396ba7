/* 容器样式 */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部统计样式 */
.stats-bar {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 进度条样式 */
.progress-bar {
  height: 6rpx;
  background-color: #e0e0e0;
  border-radius: 3rpx;
  margin-bottom: 30rpx;
}

.progress {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 计划卡片样式 */
.plan-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.card-actions {
  display: flex;
  gap: 20rpx;
}

.action-button {
  min-width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #fff;
  border: none;
  padding: 0 30rpx;
}

.action-button.edit {
  background-color: #2196F3;
}

.action-button.save {
  background-color: #4CAF50;
}

.plan-content {
  padding: 20rpx 0;
}

.plan-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.plan-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 28rpx;
  color: #666;
}

.item-content {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 28rpx;
  color: #333;
}

.item-input {
  width: 120rpx;
  height: 60rpx;
  text-align: right;
  font-size: 28rpx;
  color: #333;
  border-bottom: 2rpx solid #e0e0e0;
}

.picker-value {
  font-size: 28rpx;
  color: #333;
  padding: 10rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
}

/* 学习记录样式 */
.record-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.record-list {
  padding: 20rpx 0;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.record-item:last-child {
  border-bottom: none;
}

.record-date {
  display: flex;
  flex-direction: column;
}

.date-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.week-text {
  font-size: 24rpx;
  color: #999;
}

.record-stats {
  display: flex;
  gap: 40rpx;
}

.record-stats .stat-item {
  align-items: flex-end;
}

.record-stats .stat-value {
  font-size: 32rpx;
  margin-bottom: 6rpx;
}

.record-stats .stat-label {
  font-size: 24rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
}

/* 底部按钮样式 */
.action-bar {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.action-button.start {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #4CAF50;
  border: none;
  margin-right: 20rpx;
}

.action-button.review {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #2196F3;
  border: none;
  margin-left: 20rpx;
} 