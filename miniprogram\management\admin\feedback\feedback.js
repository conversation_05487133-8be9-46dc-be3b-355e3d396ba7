Page({
  data: {
    feedbacks: [],
    loading: true,
    isEmpty: false,
    filterStatus: 'all' // all, pending, replied, closed
  },

  onLoad(options) {
    this.loadFeedbacks();
  },

  onShow() {
    this.loadFeedbacks();
    
    // 标记反馈已查看，清除角标
    const pages = getCurrentPages();
    if (pages.length >= 2) {
      const prevPage = pages[pages.length - 2];
      // 如果从个人中心或管理员工具页面进入，清除未读角标
      if (prevPage.route === 'pages/profile/profile' || prevPage.route === 'pages/admin/admin-tools/admin-tools') {
        if (typeof prevPage.setData === 'function') {
          prevPage.setData({ unreadFeedbackCount: 0 });
        }
      }
    }
  },

  // 加载反馈列表
  async loadFeedbacks() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      // 调用云函数获取反馈数据
      const result = await wx.cloud.callFunction({
        name: 'manageFeedback',
        data: {
          action: 'list'
        }
      });

      if (result.result && result.result.success) {
        this.setData({
          feedbacks: result.result.data || [],
          loading: false,
          isEmpty: result.result.data.length === 0
        });
      } else {
        throw new Error(result.result?.error || '加载失败');
      }

    } catch (error) {
      console.error('加载反馈失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
      
      this.setData({
        loading: false,
        isEmpty: true
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 点击反馈项，查看详情
  onFeedbackTap(e) {
    const { index } = e.currentTarget.dataset;
    const feedback = this.data.feedbacks[index];
    
    let content = '';
    if (feedback.problemText) {
      content += `问题反馈：\n${feedback.problemText}\n\n`;
    }
    if (feedback.suggestionText) {
      content += `建议内容：\n${feedback.suggestionText}`;
    }
    if (feedback.adminReply) {
      content += `\n\n管理员回复：\n${feedback.adminReply}`;
    }
    
    const actions = ['回复用户', '标记已读', '删除反馈'];
    if (feedback.status === 'replied') {
      actions[0] = '修改回复';
    }
    
    wx.showActionSheet({
      itemList: actions,
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.showReplyModal(feedback, index);
            break;
          case 1:
            this.markAsRead(index);
            break;
          case 2:
            this.showDeleteConfirm(feedback, index);
            break;
        }
      }
    });
  },

  // 显示回复弹窗
  showReplyModal(feedback, index) {
    const that = this;
    wx.showModal({
      title: '回复用户',
      content: feedback.adminReply || '',
      editable: true,
      placeholderText: '请输入回复内容...',
      success: async (res) => {
        if (res.confirm && res.content.trim()) {
          await that.replyToFeedback(feedback._id, res.content.trim(), index);
        }
      }
    });
  },

  // 回复反馈
  async replyToFeedback(feedbackId, replyContent, index) {
    wx.showLoading({ title: '回复中...' });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'manageFeedback',
        data: {
          action: 'reply',
          data: {
            id: feedbackId,
            reply: replyContent
          }
        }
      });

      if (result.result && result.result.success) {
        // 更新本地数据
        const feedbacks = [...this.data.feedbacks];
        feedbacks[index].adminReply = replyContent;
        feedbacks[index].status = 'replied';
        feedbacks[index].replyTime = new Date();
        
        this.setData({ feedbacks });
        
        wx.showToast({
          title: '回复成功',
          icon: 'success'
        });
      } else {
        throw new Error(result.result?.error || '回复失败');
      }
    } catch (error) {
      console.error('回复反馈失败:', error);
      wx.showToast({
        title: '回复失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 标记为已读
  async markAsRead(index) {
    const feedback = this.data.feedbacks[index];
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'manageFeedback',
        data: {
          action: 'updateStatus',
          data: {
            id: feedback._id,
            status: 'replied'
          }
        }
      });

      if (result.result && result.result.success) {
        const feedbacks = [...this.data.feedbacks];
        feedbacks[index].status = 'replied';
        this.setData({ feedbacks });
        
        wx.showToast({
          title: '已标记为已读',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('标记已读失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    }
  },

  // 显示删除确认
  showDeleteConfirm(feedback, index) {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除来自"${feedback.userNickName}"的反馈吗？`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteFeedback(feedback._id, index);
        }
      }
    });
  },

  // 删除反馈
  async deleteFeedback(feedbackId, index) {
    wx.showLoading({ title: '删除中...' });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'manageFeedback',
        data: {
          action: 'delete',
          data: {
            id: feedbackId
          }
        }
      });

      if (result.result && result.result.success) {
        const feedbacks = [...this.data.feedbacks];
        feedbacks.splice(index, 1);
        
        this.setData({ 
          feedbacks,
          isEmpty: feedbacks.length === 0
        });
        
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
      } else {
        throw new Error(result.result?.error || '删除失败');
      }
    } catch (error) {
      console.error('删除反馈失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 刷新数据
  onPullDownRefresh() {
    this.loadFeedbacks();
    wx.stopPullDownRefresh();
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 24小时内
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return date.toLocaleDateString();
    }
  }
}) 