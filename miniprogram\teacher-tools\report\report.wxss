/* pages/profile/report/report.wxss */

/* 页面级别样式 - 彻底修复无限滚动问题 */
page {
  height: 100vh;
  overflow: hidden;
  position: fixed;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  background: transparent;
  padding: 20rpx;
  height: 100vh;
  max-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 20rpx;
  border: 2rpx solid rgba(102, 126, 234, 0.2);
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
  letter-spacing: 2rpx;
}

.page-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
  font-style: italic;
}

/* 主要内容区域 */
.main-content {
  height: calc(100vh - 80rpx); /* 为压缩的进度指示器预留更少空间 */
  max-height: calc(100vh - 80rpx); /* 严格限制最大高度 */
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 0; /* 移除底部内边距 */
  /* 防止橡皮筋效果和过度滚动 */
  -webkit-overflow-scrolling: touch;
  position: relative;
  /* 添加背景避免显示页面背景 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 步骤容器 */
.step-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.step-section.active {
  opacity: 1;
  transform: translateY(0);
}

.step-section.completed {
  opacity: 1;
}

/* 步骤标题 */
.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.step-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 表单组件 */
.form-section {
  margin-bottom: 40rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  display: block;
  margin-bottom: 12rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.input::placeholder {
  color: #adb5bd;
}

/* 题型选择 */
.instruction {
  margin-bottom: 30rpx;
  text-align: center;
}

.instruction-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.section-selection {
  margin-bottom: 30rpx;
}

/* 题型分组 */
.section-group {
  margin-bottom: 30rpx;
}

.section-group-header {
  margin-bottom: 10rpx;
}

.section-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.section-item.selected {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-color: #667eea;
}

/* 听口考试整体选择样式 */
.section-item.group-item {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
  border-color: #ffc107;
  font-weight: bold;
}

.section-item.group-item.selected {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.2));
  border-color: #ff9800;
}

.section-item.group-item .section-checkbox {
  border-color: #ffc107;
}

.section-item.group-item.selected .section-checkbox {
  background: #ff9800;
  border-color: #ff9800;
}

.group-name {
  font-size: 30rpx !important;
  font-weight: bold !important;
  color: #e65100 !important;
}

/* 听口小题型样式 */
.section-sub-items {
  margin-left: 40rpx;
  border-left: 3rpx solid #e9ecef;
  padding-left: 20rpx;
}

.section-item.sub-item {
  background: #ffffff;
  border-color: #dee2e6;
  margin-bottom: 12rpx;
  padding: 20rpx;
}

.section-item.sub-item.selected {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
  border-color: #667eea;
}

.section-item.sub-item .section-name {
  font-size: 26rpx;
  color: #495057;
}

.section-item.sub-item .section-score {
  font-size: 22rpx;
  color: #6c757d;
}

.section-checkbox {
  width: 50rpx;
  height: 50rpx;
  border: 3rpx solid #e9ecef;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  transition: all 0.3s ease;
}

.section-item.selected .section-checkbox {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-icon {
  font-size: 28rpx;
  color: white;
  font-weight: bold;
}

.section-info {
  flex: 1;
}

.section-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.section-score {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.selection-summary {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 134, 55, 0.1));
  border: 2rpx solid #28a745;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  margin-bottom: 30rpx;
}

.summary-text {
  font-size: 26rpx;
  color: #28a745;
  font-weight: 600;
}

/* 题型进度 */
.section-progress {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  border: 2rpx solid #e9ecef;
}

.progress-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
  display: block;
  margin-bottom: 16rpx;
  text-align: center;
}

.progress-bar {
  height: 8rpx;
  background: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 题型标签 */
.section-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 30rpx;
}

.tab-item {
  flex: 1;
  min-width: 160rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
  color: white;
}

.tab-name {
  font-size: 24rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 4rpx;
}

.tab-score {
  font-size: 20rpx;
  opacity: 0.8;
  display: block;
}

/* 题型分析 */
.section-analysis {
  margin-top: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  text-align: center;
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 12rpx;
}

/* 分数输入 */
.score-input {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
  align-items: end;
}

.score-group {
  flex: 1;
  text-align: center;
}

.score-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.score-field {
  width: 100%;
  height: 60rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

/* 移除只读样式，现在总分可编辑 */

.score-field:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.score-group.calculated {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 134, 55, 0.1));
  border-radius: 8rpx;
  padding: 12rpx;
}

.score-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #28a745;
  display: block;
  margin-top: 8rpx;
}

/* 柱状图样式 */
.chart-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.chart-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.chart-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.chart-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.chart-tip {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.chart-container {
  width: 100%;
  height: 300rpx; /* 增加柱状图高度，为题型名称预留更多空间 */
  position: relative;
  overflow-x: auto; /* 允许横向滚动 */
  overflow-y: hidden;
}

.chart-wrapper {
  display: flex;
  height: 100%;
  align-items: flex-end;
  min-width: 100%; /* 确保最小宽度 */
}

.y-axis {
  width: 60rpx;
  height: 200rpx; /* 保持Y轴高度不变 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  padding-right: 10rpx;
  margin-right: 10rpx;
  border-right: 2rpx solid #e9ecef;
}

.y-label {
  font-size: 20rpx;
  color: #666;
  line-height: 1;
}

.chart-bars {
  flex: 1;
  height: 200rpx; /* 保持chart-bars高度不变 */
  display: flex;
  align-items: flex-end;
  justify-content: flex-start; /* 改为左对齐，避免space-around压缩 */
  padding: 0 20rpx; /* 恢复合理的padding */
  position: relative;
  min-width: max-content; /* 确保内容不被压缩 */
}

.chart-bars::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: #e9ecef;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: none; /* 不使用flex伸缩，固定宽度 */
  width: 60rpx; /* 继续减小固定宽度 */
  margin: 0 1rpx; /* 继续减小间距 */
}

.bar-wrapper {
  width: 100%;
  height: 170rpx; /* 保持bar-wrapper高度不变 */
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
}

.bar-fill {
  width: 70%; /* 稍微减少柱子宽度，为百分比标签留出空间 */
  border-radius: 8rpx 8rpx 0 0;
  transition: height 0.8s ease;
  position: relative;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
  min-height: 10rpx; /* 确保即使是0%也有最小高度显示 */
}

.bar-value {
  position: absolute;
  top: -35rpx; /* 稍微向上移动，避免重叠 */
  left: 50%;
  transform: translateX(-50%);
  font-size: 18rpx; /* 稍微减小字体 */
  font-weight: bold;
  color: #333;
  background: rgba(255, 255, 255, 0.95);
  padding: 3rpx 6rpx;
  border-radius: 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  white-space: nowrap; /* 防止百分比换行 */
  z-index: 10; /* 确保百分比显示在最上层 */
}

.bar-label {
  margin-top: 20rpx; /* 增加上边距，为百分比标签留出空间 */
  font-size: 20rpx; /* 稍微减小字体 */
  color: #333;
  text-align: center;
  font-weight: 500;
  line-height: 1.1; /* 减少行高，节省空间 */
  min-height: 60rpx; /* 增加最小高度，确保长题型名称有足够空间 */
  max-height: 80rpx; /* 设置最大高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  word-break: break-all; /* 允许在任意字符间换行 */
  overflow: hidden; /* 隐藏超出部分 */
}

/* 分析组 */
.analysis-group {
  margin-bottom: 30rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.options-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.option-item {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.option-item.selected {
  background: linear-gradient(135deg, #28a745, #20c997);
  border-color: #28a745;
  color: white;
}

.option-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.option-item.selected::before {
  left: 100%;
}

.option-text {
  font-size: 24rpx;
  line-height: 1.4;
}

.custom-input {
  margin-top: 16rpx;
}

/* 按钮 */
.step-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn.secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.btn:active {
  transform: scale(0.95);
}

/* 报告预览 */
.report-actions {
  background: #f8f9fa;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 2rpx solid #e9ecef;
}

/* 水平操作按钮区域 */
.action-buttons-horizontal {
  display: flex;
  gap: 16rpx;
  margin-top: 30rpx;
}

.btn-compact {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
  gap: 4rpx;
}

.btn-compact.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-compact.secondary {
  background: #fff;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.btn-compact:active {
  transform: scale(0.95);
}

.btn-compact .btn-icon {
  font-size: 24rpx;
}

.btn-compact .btn-text {
  font-size: 20rpx;
  font-weight: 600;
}

/* 移除旧的响应式样式 */

/* 样式选择器 */
.style-selector {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 20rpx 0;
  border: 2rpx solid #e9ecef;
}

.selector-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.style-options {
  display: flex;
  gap: 16rpx;
  justify-content: space-between;
}

.style-option {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 3rpx solid transparent;
}

.style-option.active {
  border-color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.15);
  transform: scale(1.05);
}

.style-name {
  color: white;
  font-size: 22rpx;
  font-weight: bold;
  text-shadow: 0 1rpx 3rpx rgba(0,0,0,0.3);
}

/* 隐藏画布 */
.hidden-canvas {
  position: fixed !important;
  top: -3000rpx !important;
  left: -3000rpx !important;
  z-index: -999;
}

.report-preview {
  border-radius: 20rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
  margin-top: 20rpx;
  margin-bottom: 5rpx; /* 进一步减少底部间距 */
  animation: fadeInUp 0.6s ease-out;
}

/* 报告头部 */
.report-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #896ac7 100%);
  color: white;
  padding: 50rpx 40rpx;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.report-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  background-size: 50rpx 50rpx;
}

.header-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  position: relative;
}

.decoration-line {
  width: 80rpx;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.5);
}

.decoration-icon {
  font-size: 40rpx;
  margin: 0 20rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.report-title {
  font-size: 28rpx !important;
  line-height: 1.4 !important;
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
  max-width: 100% !important;
  word-break: break-all !important;
  padding: 0 20rpx !important;
}

.report-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  margin-bottom: 40rpx;
  font-style: italic;
}

/* 基本信息卡片 */
.basic-info-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 30rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.card-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.card-row:last-child {
  margin-bottom: 0;
}

.info-badge {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.badge-label {
  font-size: 22rpx;
  opacity: 0.8;
  display: block;
  margin-bottom: 8rpx;
}

.badge-value {
  font-size: 28rpx;
  font-weight: 600;
  display: block;
}

.badge-value.highlight {
  color: #ffd700;
  font-size: 32rpx;
}

/* 分析表格 */
.analysis-table {
  margin: 40rpx 30rpx;
}

.table-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 12rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.2);
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 表格头部 */
.table-header {
  display: flex;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12rpx 12rpx 0 0;
  overflow: hidden;
}

.th {
  color: white;
  font-weight: bold;
  font-size: 24rpx;
  padding: 20rpx 16rpx;
  text-align: center;
  border-right: 1rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.th:last-child {
  border-right: none;
}

.th-subject { flex: 1.2; }
.th-total { flex: 0.8; }
.th-lost { flex: 0.8; }
.th-reasons { flex: 2; }
.th-suggestions { flex: 2; }

/* 表格内容 */
.table-body {
  background: white;
  border-radius: 0 0 12rpx 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #f0f2f5;
  transition: all 0.3s ease;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row.even {
  background: #fafbfc;
}

.table-row.odd {
  background: white;
}

.table-row:hover {
  background-color: rgba(102, 126, 234, 0.05) !important;
  transform: translateX(4rpx);
}

.td {
  padding: 24rpx 16rpx;
  border-right: 1rpx solid #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.td:last-child {
  border-right: none;
}

.td-subject {
  flex: 1.2;
  flex-direction: column;
  gap: 8rpx;
}

.td-total { flex: 0.8; }
.td-lost { flex: 0.8; }
.td-reasons { flex: 2; text-align: left; justify-content: flex-start; }
.td-suggestions { flex: 2; text-align: left; justify-content: flex-start; }

.subject-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.subject-score {
  font-size: 22rpx;
  color: #667eea;
  font-weight: 500;
}

.score-number {
  font-size: 28rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

.score-number.total {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  color: #1976d2;
}

.score-number.lost {
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
  color: #d32f2f;
}

.content-list {
  width: 100%;
}

.list-item {
  font-size: 22rpx;
  color: #555;
  line-height: 1.5;
  margin-bottom: 6rpx;
  display: block;
  padding: 4rpx 8rpx;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 6rpx;
  border-left: 3rpx solid rgba(102, 126, 234, 0.3);
}

.list-item.custom {
  background: rgba(255, 193, 7, 0.1);
  border-left-color: #ffc107;
  color: #e65100;
  font-weight: 500;
}

/* 统计摘要 */
.summary-section {
  margin: 40rpx 30rpx;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.summary-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.summary-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.summary-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.summary-card {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
  border: 1rpx solid #dee2e6;
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.total-card {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-color: #90caf9;
}

.lost-card {
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
  border-color: #ef9a9a;
}

.gained-card {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
  border-color: #a5d6a7;
}

.rate-card {
  background: linear-gradient(135deg, #fff3e0, #ffcc02);
  border-color: #ffb74d;
}

.card-number {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.total-card .card-number { color: #1976d2; }
.lost-card .card-number { color: #d32f2f; }
.gained-card .card-number { color: #388e3c; }
.rate-card .card-number { color: #f57c00; }

.card-label {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

/* 报告底部 */
.report-footer {
  background: linear-gradient(135deg, #37474f, #263238);
  color: white;
  padding: 20rpx 40rpx 15rpx; /* 减少上下内边距，保持左右内边距 */
  text-align: center;
  position: relative;
}

.footer-decoration {
  display: flex;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.decoration-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  animation: blink 1.5s infinite ease-in-out;
}

.decoration-dot:nth-child(2) {
  animation-delay: 0.3s;
}

.decoration-dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes blink {
  0%, 50%, 100% { opacity: 0.5; }
  25% { opacity: 1; }
}

.footer-info-centered {
  text-align: center;
  margin: 20rpx 0;
  width: 100%;
  overflow: hidden;
}

.footer-text {
  font-size: 26rpx;
  color: white;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  white-space: nowrap;
  display: inline-block;
  width: 100%;
}

.footer-signature {
  font-size: 20rpx;
  font-style: italic;
  opacity: 0.6;
  letter-spacing: 1rpx;
}

.report-loading {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 28rpx;
}

/* 进度指示器 */
.progress-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 5rpx; /* 进一步减少底部距离 */
  left: 20rpx;
  right: 20rpx;
  padding: 10rpx 20rpx; /* 大幅减少内边距 */
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  z-index: 100;
  height: 60rpx; /* 设置固定高度 */
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.progress-step.completed {
  opacity: 1;
}

.step-dot {
  width: 32rpx; /* 进一步缩小圆点尺寸 */
  height: 32rpx; /* 进一步缩小圆点尺寸 */
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx; /* 进一步缩小字体 */
  font-weight: bold;
  margin-bottom: 4rpx; /* 进一步减少下边距 */
  transition: all 0.3s ease;
}

.progress-step.completed .step-dot {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.step-text {
  font-size: 14rpx; /* 进一步缩小步骤文字 */
  color: #666;
  text-align: center;
  line-height: 1.2;
}

.progress-line {
  width: 80rpx;
  height: 4rpx;
  background: #e9ecef;
  margin: 0 20rpx;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
}

.progress-line.completed {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .section-tabs {
    flex-direction: column;
  }
  
  .tab-item {
    min-width: auto;
  }
  
  .score-input {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .step-buttons {
    flex-direction: column;
  }
  
  .report-actions {
    flex-direction: column;
  }
  
  .progress-indicator {
    flex-direction: column;
    gap: 20rpx;
  }
  
  .progress-line {
    width: 4rpx;
    height: 40rpx;
    margin: 0;
  }
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .report-actions {
    flex-direction: column;
  }
  
  .style-options {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .style-option {
    height: 60rpx;
  }
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 紧凑样式选择器 */
.style-selector-compact {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
  gap: 16rpx;
}

.selector-label-small {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.style-options-compact {
  display: flex;
  gap: 12rpx;
}

.style-dot {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
  border: 3rpx solid transparent;
}

.style-dot.active {
  border-color: #333;
  transform: scale(1.2);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.settings-toggle {
  font-size: 28rpx;
  padding: 8rpx;
  border-radius: 6rpx;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
}

.settings-toggle:active {
  background: #e9ecef;
}

/* 设置面板 */
.settings-panel {
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-top: 16rpx;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  animation: slideDown 0.3s ease;
}

.setting-item {
  margin-bottom: 20rpx;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.setting-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.setting-input {
  width: 100%;
  height: 60rpx;
  background: #fff;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 24rpx;
  color: #333;
  box-sizing: border-box;
}

.setting-input:focus {
  border-color: #667eea;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 成绩统计（移到上方） */
.summary-section-top {
  background: #ffffff;
  padding: 30rpx;
  margin: 0;
}

.summary-section-top .summary-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  gap: 12rpx;
}

.summary-section-top .summary-icon {
  font-size: 32rpx;
}

.summary-section-top .summary-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 课程规划表格样式 */
.th-week { flex: 0.8; }
.th-content { flex: 2; }
.th-goal { flex: 1.5; }
.th-method { flex: 1.5; }
.th-note { flex: 1.2; }

.td-week { flex: 0.8; }
.td-content { flex: 2; }
.td-goal { flex: 1.5; }
.td-method { flex: 1.5; }
.td-note { flex: 1.2; }

.week-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #667eea;
}

.empty-content {
  color: #999;
  font-style: italic;
  font-size: 24rpx;
  padding: 20rpx;
  min-height: 80rpx;
  border: 2rpx dashed #e9ecef;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafbfc;
  transition: all 0.3s ease;
}

.empty-content:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

/* 课程规划表格样式 */
.th-lesson {
  width: 120rpx !important;
  flex: none !important;
}

.th-content-plan {
  flex: 1 !important;
}

.td-lesson {
  width: 120rpx !important;
  flex: none !important;
}

.td-content-plan {
  flex: 1 !important;
}

.table-header-input {
  background: transparent;
  border: none;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  text-align: center;
  width: 100%;
  height: 40rpx;
  line-height: 40rpx;
}

.table-header-input::placeholder {
  color: rgba(255, 255, 255, 0.8);
}

.table-input {
  background: transparent;
  border: none;
  color: #333;
  font-size: 22rpx;
  text-align: center;
  width: 100%;
  height: 40rpx;
  line-height: 40rpx;
}

.table-input::placeholder {
  color: #999;
}

.lesson-text {
  font-size: 22rpx;
  color: #667eea;
  font-weight: bold;
}

/* 课程规划操作按钮 */
.td-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
  margin: 0;
}

.delete-btn {
  background: #ff4d4f;
  color: white;
}

.delete-btn:active {
  background: #d32f2f;
}

.table-actions {
  display: flex;
  justify-content: center;
  padding: 20rpx;
  border-left: 2rpx solid #e8e8e8;
  border-right: 2rpx solid #e8e8e8;
  border-bottom: 2rpx solid #e8e8e8;
  background: #fafafa;
}

.add-row-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 32rpx;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.add-row-btn:active {
  background: #0c7cd5;
}

.add-icon {
  font-size: 32rpx;
  font-weight: bold;
}

/* 课程规划选择页面样式 */
.courseplan-selection {
  margin-bottom: 40rpx;
}

.selection-info {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.info-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  display: block;
}

.courseplan-preview {
  margin-bottom: 30rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 20rpx;
  border: 2rpx solid rgba(102, 126, 234, 0.2);
}

.preview-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}

.preview-table {
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  overflow: hidden;
  background: white;
}

.preview-header {
  display: flex;
  background: #667eea;
  color: white;
  font-weight: bold;
}

.preview-th {
  padding: 20rpx 16rpx;
  text-align: center;
  border-right: 2rpx solid rgba(255, 255, 255, 0.2);
}

.preview-header-input {
  background: transparent;
  border: none;
  color: white;
  text-align: center;
  font-weight: bold;
  font-size: 26rpx;
}

.preview-header-input::placeholder {
  color: rgba(255, 255, 255, 0.8);
}

.preview-body {
  background: white;
}

.preview-row {
  display: flex;
  border-bottom: 2rpx solid #f1f3f4;
}

.preview-row.even {
  background: #f8f9fa;
}

.preview-row.odd {
  background: white;
}

.preview-td {
  padding: 20rpx 16rpx;
  border-right: 2rpx solid #f1f3f4;
  display: flex;
  align-items: center;
}

.lesson-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  width: 100%;
}

.preview-input {
  width: 100%;
  background: transparent;
  border: none;
  font-size: 24rpx;
  color: #333;
  padding: 8rpx;
}

.preview-btn {
  font-size: 20rpx;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  background: #dc3545;
  color: white;
  border: none;
}

.add-plan-btn {
  width: 100%;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  border-radius: 0 0 12rpx 12rpx;
}

.add-icon {
  font-size: 32rpx;
  font-weight: bold;
}

.selection-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.selection-buttons .btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.selection-buttons .btn.secondary {
  background: #6c757d;
  color: white;
}

.selection-buttons .btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

/* 报告底部信息行样式 */
.footer-info-centered {
  text-align: center;
  width: 100%;
  margin: 20rpx 0;
}

.footer-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

 