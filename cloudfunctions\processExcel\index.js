const cloud = require('wx-server-sdk')
const XLSX = require('xlsx')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 设置云函数的超时配置
exports.config = {
  timeout: 120,
  memorySize: 512
}

exports.main = async (event, context) => {
  const { fileID } = event
  
  console.log('开始处理Excel文件，fileID:', fileID)
  const startTime = Date.now()
  
  if (!fileID) {
    return { code: 400, message: '缺少文件ID' }
  }
  
  try {
    console.log('正在下载文件...')
    // 从云存储下载文件，设置超时
    const downloadStart = Date.now()
    const result = await Promise.race([
      cloud.downloadFile({ fileID: fileID }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('下载超时')), 30000))
    ])
    console.log('文件下载完成，耗时:', Date.now() - downloadStart, 'ms')
    
    console.log('正在解析Excel文件...')
    const parseStart = Date.now()
    
    // 解析Excel文件，使用更高效的选项
    const workbook = XLSX.read(result.fileContent, { 
      type: 'buffer',
      cellText: false,
      cellNF: false,
      cellHTML: false,
      cellFormula: false,
      cellStyles: false,
      cellDates: false,
      sheetStubs: false
    })
    
    // 获取第一个工作表
    const sheetName = workbook.SheetNames[0]
    if (!sheetName) {
      return { code: 400, message: 'Excel文件中没有找到工作表' }
    }
    
    const sheet = workbook.Sheets[sheetName]
    
    // 转换为JSON数据，使用标题行作为键名
    const jsonData = XLSX.utils.sheet_to_json(sheet, { 
      raw: false,
      defval: '', // 空单元格默认值
      range: 0 // 处理所有行
    }).slice(0, 10000) // 限制最大10000行
    
    console.log('Excel解析完成，耗时:', Date.now() - parseStart, 'ms，数据行数:', jsonData.length)
    
    // 检查Excel格式
    if (jsonData.length === 0) {
      return { code: 400, message: 'Excel文件中没有数据，请检查文件内容' }
    }
    
    // 检查必需的列名
    const firstRow = jsonData[0]
    const columnNames = Object.keys(firstRow)
    console.log('检测到的列名:', columnNames)
    
    // 查找可能的列名映射（支持中英文和大小写）
    const wordsColumnName = findColumnName(columnNames, ['words', 'word', '单词', 'Words', 'Word'])
    const phoneticColumnName = findColumnName(columnNames, ['phonetic', 'phonetics', '音标', 'pronunciation', 'Phonetic', 'Phonetics'])
    const meaningColumnName = findColumnName(columnNames, ['meaning', 'chinese', '释义', '意思', '中文', 'Meaning', 'Chinese'])
    const exampleColumnName = findColumnName(columnNames, ['example', 'sentence', '例句', '例子', 'Example', 'Sentence'])
    const unitColumnName = findColumnName(columnNames, ['unit', 'units', '单元', 'Unit', 'Units'])
    
    if (!wordsColumnName) {
      return { 
        code: 400, 
        message: `Excel格式错误：未找到单词列。\n请确保第一列的标题为 "words" 或 "单词"。\n当前检测到的列名：${columnNames.join(', ')}` 
      }
    }
    
    if (!meaningColumnName) {
      return { 
        code: 400, 
        message: `Excel格式错误：未找到释义列。\n请确保第二列的标题为 "meaning" 或 "释义"。\n当前检测到的列名：${columnNames.join(', ')}` 
      }
    }
    
    console.log('正在处理词汇数据...')
    const processStart = Date.now()
    
    // 处理数据，提取词汇信息
    const words = []
    const seenWords = new Set()
    const errors = []
    
    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i]
      const rowNumber = i + 2 // Excel行号（考虑标题行）
      
      // 获取单词
      const rawWord = row[wordsColumnName]
      if (!rawWord || typeof rawWord !== 'string') {
        errors.push(`第${rowNumber}行：单词列为空或格式错误`)
        continue
      }
      
      const word = rawWord.trim().toLowerCase()
      if (!word) {
        errors.push(`第${rowNumber}行：单词不能为空`)
        continue
      }
      
      // 验证单词格式
      if (word.length > 50) {
        errors.push(`第${rowNumber}行：单词长度不能超过50个字符`)
        continue
      }
      
      // 验证单词只包含英文字母、空格、连字符、撇号和点号
      if (!/^[a-zA-Z\s\-'\.]+$/.test(word)) {
        errors.push(`第${rowNumber}行：单词格式错误，只能包含英文字母、空格、连字符、撇号和点号`)
        continue
      }
      
      // 检查重复
      if (seenWords.has(word)) {
        errors.push(`第${rowNumber}行：单词 "${word}" 重复`)
        continue
      }
      
      // 获取释义
      const rawMeaning = row[meaningColumnName]
      if (!rawMeaning || typeof rawMeaning !== 'string') {
        errors.push(`第${rowNumber}行：释义列为空或格式错误`)
        continue
      }
      
      const meaning = rawMeaning.trim()
      if (!meaning) {
        errors.push(`第${rowNumber}行：释义不能为空`)
        continue
      }
      
      if (meaning.length > 200) {
        errors.push(`第${rowNumber}行：释义长度不能超过200个字符`)
        continue
      }
      
      // 获取音标（可选）
      const rawPhonetic = row[phoneticColumnName]
      const phonetic = rawPhonetic && typeof rawPhonetic === 'string' ? rawPhonetic.trim() : ''
      
      if (phonetic && phonetic.length > 100) {
        errors.push(`第${rowNumber}行：音标长度不能超过100个字符`)
        continue
      }
      
      // 获取例句（可选）
      const rawExample = row[exampleColumnName]
      const example = rawExample && typeof rawExample === 'string' ? rawExample.trim() : ''
      
      if (example && example.length > 300) {
        errors.push(`第${rowNumber}行：例句长度不能超过300个字符`)
        continue
      }
      
      // 获取单元（可选，用于教材同步词汇）
      const rawUnit = row[unitColumnName]
      const unit = rawUnit && typeof rawUnit === 'string' ? rawUnit.trim() : ''
      
      if (unit && unit.length > 20) {
        errors.push(`第${rowNumber}行：单元标记长度不能超过20个字符`)
        continue
      }
      
      seenWords.add(word)
      
      const wordData = {
        word: word,
        phonetic: phonetic || '',
        meaning: meaning,
        example: example || ''
      }
      
      // 如果有单元字段，则添加到数据中
      if (unit) {
        wordData.unit = unit
      }
      
      words.push(wordData)
      
      // 限制最大词汇数量
      if (words.length >= 10000) {
        console.log('达到最大词汇数量限制(10000)，停止处理')
        break
      }
    }
    
    console.log('词汇处理完成，耗时:', Date.now() - processStart, 'ms，词汇数量:', words.length)
    
    const totalTime = Date.now() - startTime
    console.log('Excel处理完成，总耗时:', totalTime, 'ms')
    
    // 返回结果
    const result_data = {
      words: words,
      totalRows: jsonData.length,
      validWords: words.length,
      errors: errors,
      processingTime: totalTime,
      format: {
        wordsColumn: wordsColumnName,
        phoneticColumn: phoneticColumnName || '未找到',
        meaningColumn: meaningColumnName,
        exampleColumn: exampleColumnName || '未找到',
        unitColumn: unitColumnName || '未找到'
      }
    }
    
    return {
      code: 200,
      message: errors.length > 0 ? `处理完成，发现 ${errors.length} 个错误` : '处理完成',
      data: result_data
    }
    
  } catch (error) {
    const totalTime = Date.now() - startTime
    console.error('处理Excel文件失败，耗时:', totalTime, 'ms，错误:', error)
    
    let errorMessage = '文件解析失败'
    if (error.message.includes('下载超时')) {
      errorMessage = '文件下载超时，请检查网络连接'
    } else if (error.message.includes('Invalid file')) {
      errorMessage = '无效的Excel文件格式，请确保文件是 .xlsx 或 .xls 格式'
    } else if (error.message.includes('timeout')) {
      errorMessage = '处理超时，请尝试上传较小的文件'
    } else if (error.message.includes('ENOENT') || error.message.includes('does not exist')) {
      errorMessage = '文件不存在或已过期，请重新选择文件'
    } else {
      errorMessage = '文件解析失败：' + error.message
    }
    
    return {
      code: 500,
      message: errorMessage
    }
  }
}

// 查找列名的辅助函数
function findColumnName(columnNames, possibleNames) {
  for (const possible of possibleNames) {
    const found = columnNames.find(col => 
      col.toLowerCase().trim() === possible.toLowerCase()
    )
    if (found) return found
  }
  return null
} 