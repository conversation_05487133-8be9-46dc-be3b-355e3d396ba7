/* 关卡选择页面样式 */
.container {
  padding: 20rpx;
  background: #f8f9fa;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 页面头部 */
.page-header {
  margin-bottom: 30rpx;
}

.header-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 6rpx 24rpx rgba(102, 126, 234, 0.3);
  color: white;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.back-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.back-icon {
  font-size: 32rpx;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
  margin: 0 20rpx;
}

.competition-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.competition-mode {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

.header-action {
  width: 48rpx;
  height: 48rpx;
}

.ranking-btn {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.ranking-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.ranking-icon {
  font-size: 28rpx;
}

/* 统计区域 */
.stats-section {
  margin-bottom: 30rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
  line-height: 1.2;
}

/* 关卡区域 */
.levels-section {
  margin-bottom: 30rpx;
}

/* 关卡卡片 */
.level-card {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.level-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.level-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24rpx 24rpx 16rpx 24rpx;
}

.level-info {
  flex: 1;
}

.level-title {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.level-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.level-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.badge-text {
  font-weight: 500;
}

.level-name {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.level-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.stat-row {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.stat-icon {
  font-size: 20rpx;
}

.stat-text {
  font-size: 22rpx;
  color: #666;
}

/* 关卡内容 */
.level-content {
  padding: 0 24rpx 24rpx 24rpx;
}

.progress-section {
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-label {
  font-size: 22rpx;
  color: #666;
}

.progress-text {
  font-size: 22rpx;
  color: #333;
  font-weight: 500;
}

.progress-bar {
  height: 8rpx;
  background: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  transition: width 0.3s ease;
  border-radius: 4rpx;
}

.level-action {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.challenge-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.challenge-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);
}

/* 锁定状态样式 */
.level-card.locked {
  opacity: 0.8;
}



.level-card.locked:active {
  transform: none;
}

.locked-btn {
  background: #e9ecef;
  color: #6c757d;
}

/* 已完成状态样式 */
.level-card.completed {
  background: rgba(40, 167, 69, 0.05);
  border: 2rpx solid rgba(40, 167, 69, 0.2);
}

.completed-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.3);
}

.completed-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(40, 167, 69, 0.2);
}

.action-icon {
  font-size: 24rpx;
}

.action-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 20rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #999;
}

/* 说明区域 */
.info-section {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.info-content {
  text-align: left;
}

.info-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.info-list {
  space-y: 12rpx;
}

.info-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

/* 分页控件样式 */
.pagination {
  margin: 30rpx 0;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.page-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.total-text {
  font-size: 22rpx;
  color: #666;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12rpx;
  flex-wrap: wrap;
}

.page-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  min-width: 100rpx;
  flex-shrink: 0;
}

.page-btn:active {
  transform: scale(0.95);
}

.page-btn.disabled {
  background: #e9ecef;
  color: #6c757d;
  transform: none;
}

.page-btn-text {
  font-size: 24rpx;
  font-weight: 500;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 6rpx;
  flex: 1;
  justify-content: center;
  min-width: 0;
  overflow: hidden;
}

.page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #666;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.page-number:active {
  transform: scale(0.95);
}

.page-number.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.page-number.ellipsis {
  background: transparent;
  color: #999;
  cursor: default;
  transform: none !important;
}

.page-number.ellipsis:active {
  transform: none;
}

.page-input-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  margin-left: 8rpx;
}

.page-input-btn:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.input-icon {
  font-size: 20rpx;
}

/* 响应式布局 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12rpx;
  }

  .level-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .level-stats {
    align-items: flex-start;
    margin-top: 16rpx;
  }

  .pagination-controls {
    flex-direction: column;
    gap: 12rpx;
  }

  .page-numbers {
    order: -1;
    margin-bottom: 12rpx;
    width: 100%;
  }

  .page-btn {
    min-width: 160rpx;
    width: 100%;
  }
}

/* 更小屏幕的额外优化 */
@media (max-width: 600rpx) {
  .page-btn {
    min-width: 140rpx;
    padding: 10rpx 12rpx;
  }

  .page-btn-text {
    font-size: 22rpx;
  }

  .page-numbers {
    gap: 4rpx;
  }

  .page-number {
    width: 50rpx;
    height: 50rpx;
    font-size: 22rpx;
  }

  .page-input-btn {
    width: 50rpx;
    height: 50rpx;
  }
}