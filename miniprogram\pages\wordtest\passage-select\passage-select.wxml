<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <view class="back-btn" bindtap="onBack">
        <text class="back-icon">←</text>
      </view>
      <view class="header-text">
        <text class="page-title">短文翻译</text>
        <text class="page-subtitle">选择您喜欢的阅读类型</text>
      </view>
    </view>
  </view>

  <!-- 选择卡片列表 -->
  <view class="passage-list">
    <view 
      class="passage-card" 
      wx:for="{{passageTypes}}" 
      wx:key="id"
      bindtap="onSelectPassageType"
      data-id="{{item.id}}"
    >
      <view class="card-content {{item.color}}">
        <view class="card-icon-wrapper">
          <view class="card-icon">{{item.icon}}</view>
          <view class="icon-bg"></view>
        </view>
        <view class="card-text">
          <view class="card-title">{{item.title}}</view>
          <view class="card-subtitle">{{item.subtitle}}</view>
        </view>
        <view class="arrow-icon">→</view>
      </view>
    </view>
  </view>

  <!-- 底部提示 -->
  <view class="bottom-tips">
    <text class="tip-text">💡 各类内容正在逐步完善中，敬请期待</text>
  </view>
</view> 