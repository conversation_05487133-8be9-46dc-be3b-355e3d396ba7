const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    // 检查competitions集合是否存在
    console.log('检查competitions集合...')
    
    // 尝试查询集合（如果不存在会报错）
    try {
      const testQuery = await db.collection('competitions').limit(1).get()
      console.log('competitions集合已存在，有', testQuery.data.length, '条数据')
      
      // 如果集合存在，检查索引
      return {
        success: true,
        message: 'competitions集合已存在',
        count: testQuery.data.length
      }
    } catch (error) {
      console.log('competitions集合可能不存在，尝试创建...')
      
      // 创建一条测试数据来初始化集合
      try {
        await db.collection('competitions').add({
          data: {
            _id: 'init_test_' + Date.now(),
            name: '初始化测试',
            mode: 'test',
            status: 'deleted',
            createTime: new Date(),
            words: [],
            creatorOpenId: 'system',
            creatorName: '系统'
          }
        })
        
        console.log('competitions集合创建成功')
        
        // 立即删除测试数据
        await db.collection('competitions').doc('init_test_' + Date.now()).remove()
        
        return {
          success: true,
          message: 'competitions集合创建成功'
        }
      } catch (createError) {
        console.error('创建competitions集合失败:', createError)
        return {
          success: false,
          message: '创建competitions集合失败: ' + createError.message
        }
      }
    }
  } catch (error) {
    console.error('初始化竞赛集合失败:', error)
    return {
      success: false,
      message: '初始化失败: ' + error.message
    }
  }
} 