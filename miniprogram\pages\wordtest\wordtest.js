// pages/wordtest/wordtest.js
const app = getApp();
const { LIBRARY_INFO } = require('../../config/constants.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentLibrary: {
      id: 'gaokao_3500',
      name: '加载中...'
    },
    isCreatingCompetition: false,
    competitionMode: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { mode, libraryId, isRandom, continue: continueStudy, shareMode, testMode, createCompetition } = options;
    
    // 检查全局变量是否为创建竞赛模式
    const app = getApp();
    if (app.globalData.isCreatingCompetition) {
      console.log('=== 创建竞赛模式 ===');
      console.log('当前竞赛模式:', app.globalData.competitionMode);
      
      // 设置页面状态
      this.setData({
        isCreatingCompetition: true,
        competitionMode: app.globalData.competitionMode
      });
      
      // 清除全局标记
      app.globalData.isCreatingCompetition = false;
      
      // 不做任何跳转，让用户在模式选择页面选择
      return;
    }
    
    // 兼容旧的URL参数方式
    if (createCompetition === 'true' && mode) {
      console.log('=== 创建竞赛模式（旧方式）===');
      this.setData({
        isCreatingCompetition: true,
        competitionMode: mode
      });
      return;
    }
    
    // 检查是否为分享模式创建测试
    if (shareMode === 'create' && testMode) {
      console.log('=== 分享模式创建测试 ===');
      console.log('测试模式:', testMode);
      
      // 存储分享模式状态
      this.setData({
        shareMode: true,
        testMode: testMode
      });
      
      // 根据测试模式直接跳转到单词选择页面
      setTimeout(() => {
        wx.navigateTo({
          url: `/pages/wordbank/wordlist/wordlist?mode=test&testMode=${testMode}&shareMode=create`,
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }, 500);
      return;
    }
    
    // 检查是否为全书学习模式
    if (mode === 'fullbook') {
      console.log('=== 全书学习模式跳转 ===');
      console.log('参数:', options);
      
      // 直接跳转到学习页面
      wx.redirectTo({
        url: `/pages/learning/learning?mode=fullbook&libraryId=${libraryId}&isRandom=${isRandom}&continue=${continueStudy || 'false'}`,
        success: () => {
          console.log('跳转到全书学习页面成功');
        },
        fail: (err) => {
          console.error('跳转到全书学习页面失败:', err);
          wx.showToast({ title: '跳转失败', icon: 'error' });
        }
      });
      return;
    }
    
    this.loadCurrentLibrary();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查是否有从词库页面选择的词库
    const app = getApp();
    if (app.globalData.selectedLibrary) {
      console.log('=== 从词库选择页面返回 ===', app.globalData.selectedLibrary);
      
      // 更新当前词库
      const selectedLibrary = app.globalData.selectedLibrary;
      this.setData({
        currentLibrary: {
          id: selectedLibrary.id,
          name: selectedLibrary.name,
          isCustom: selectedLibrary.isCustom || false,
          testOrder: selectedLibrary.testOrder || 'random'
        }
      });
      
      // 保存到本地缓存
      wx.setStorageSync('currentLibrary', this.data.currentLibrary);
      
      // 清除全局数据
      app.globalData.selectedLibrary = null;
      
      console.log('=== 词库更新完成 ===', this.data.currentLibrary);
    } else {
      // 页面显示时重新加载词库信息
      this.loadCurrentLibrary();
    }
  },

  /**
   * 加载当前词库信息
   */
  async loadCurrentLibrary() {
    const { LIBRARY_INFO } = require('../../config/constants.js');

    try {
      // 首先从本地缓存获取
      const cachedLibrary = wx.getStorageSync('currentLibrary');
      if (cachedLibrary && cachedLibrary.id) {
        // 更新缓存中的词库名称为最新的
        const libraryInfo = LIBRARY_INFO[cachedLibrary.id];
        if (libraryInfo) {
          const updatedLibrary = {
            ...cachedLibrary,
            name: libraryInfo.name
          };
          this.setData({
            currentLibrary: updatedLibrary
          });
          // 更新缓存
          wx.setStorageSync('currentLibrary', updatedLibrary);
        } else {
          this.setData({
            currentLibrary: cachedLibrary
          });
        }
        return;
      }

      // 从用户设置中获取当前词库
      const app = getApp();
      const userInfo = await app.getUserInfo();
      if (userInfo && userInfo.settings && userInfo.settings.currentLibrary) {
        const userLibrary = userInfo.settings.currentLibrary;
        const libraryInfo = LIBRARY_INFO[userLibrary.id];
        const updatedLibrary = {
          ...userLibrary,
          name: libraryInfo ? libraryInfo.name : userLibrary.name
        };
        this.setData({
          currentLibrary: updatedLibrary
        });
      } else {
        // 默认使用高考3500（顺序版）
        const defaultLibraryId = 'gaokao_3500';
        const libraryInfo = LIBRARY_INFO[defaultLibraryId];
        const defaultLibrary = {
          id: defaultLibraryId,
          name: libraryInfo ? libraryInfo.name : '高考3500（顺序版）'
        };
        this.setData({
          currentLibrary: defaultLibrary
        });
        // 保存默认词库到缓存
        wx.setStorageSync('currentLibrary', defaultLibrary);
      }
    } catch (error) {
      console.error('加载词库信息失败：', error);
      // 出错时也使用默认词库
      const defaultLibrary = {
        id: 'gaokao_3500',
        name: '3500大纲词汇'
      };
      this.setData({
        currentLibrary: defaultLibrary
      });
      // 保存默认词库到缓存
      wx.setStorageSync('currentLibrary', defaultLibrary);
    }
  },

  /**
   * 更换词库
   */
  onSwitchLibrary() {
    console.log('=== 点击换书按钮 ===');
    
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });
    
    console.log('=== 跳转到新的词库选择页面 ===');
    
    // 跳转到新的双列词库选择页面
    wx.navigateTo({
      url: '/pages/wordbank/library-selector/library-selector?returnTo=wordtest&testType=word',
      success: () => {
        console.log('=== 成功跳转到词库选择页面 ===');
      },
      fail: (err) => {
        console.error('=== 跳转词库选择页面失败 ===:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 选择检测模式
   */
  onModeSelect(e) {
    const mode = e.currentTarget.dataset.mode;
    
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'medium'
    });

    // 显示加载提示
    wx.showLoading({
      title: '准备检测...',
      mask: true
    });

    // 根据不同模式跳转到对应页面
    setTimeout(() => {
      wx.hideLoading();
      
      // 判断是否为自定义词库
      const isCustom = this.data.currentLibrary.isCustom || false;
      const baseUrl = isCustom 
        ? `/pages/wordbank/custom-wordlist/custom-wordlist?wordbankId=${this.data.currentLibrary.id}`
        : `/pages/wordbank/wordlist/wordlist?libraryId=${this.data.currentLibrary.id}`;
      
      // 如果是创建竞赛模式，添加参数
      const createCompetitionParam = this.data.isCreatingCompetition ? '&createCompetition=true' : '';
      
      switch (mode) {
        case 'dictation':
          // 听写模式 - 跳转到词汇选择页面
          wx.navigateTo({
            url: isCustom 
              ? `${baseUrl}&mode=select&returnTo=wordtest&testMode=dictation${createCompetitionParam}`
              : `${baseUrl}&mode=test&testMode=dictation${createCompetitionParam}`
          });
          break;
          
        case 'en2zh':
          // 英译汉模式 - 直接跳转到学习页面
          wx.navigateTo({
            url: isCustom
              ? `${baseUrl}&mode=select&returnTo=wordtest&testMode=en_to_cn${createCompetitionParam}`
              : `${baseUrl}&mode=test&testMode=en_to_cn${createCompetitionParam}`
          });
          break;
          
        case 'zh2en':
          // 汉译英模式 - 直接跳转到学习页面
          wx.navigateTo({
            url: isCustom
              ? `${baseUrl}&mode=select&returnTo=wordtest&testMode=cn_to_en${createCompetitionParam}`
              : `${baseUrl}&mode=test&testMode=cn_to_en${createCompetitionParam}`
          });
          break;
          
        case 'puzzle':
          // 消消乐模式 - 直接跳转到学习页面
          wx.navigateTo({
            url: isCustom
              ? `${baseUrl}&mode=select&returnTo=wordtest&testMode=elimination${createCompetitionParam}`
              : `${baseUrl}&mode=test&testMode=elimination${createCompetitionParam}`
          });
          break;
          
        case 'passage':
          // 短文翻译模式 - 跳转到选择页面
          if (this.data.isCreatingCompetition) {
            wx.showToast({
              title: '短文翻译暂不支持创建竞赛',
              icon: 'none'
            });
            return;
          }
          wx.navigateTo({
            url: `/pages/wordtest/passage-select/passage-select`
          });
          break;
          
        case 'custom':
          // 自定义检测模式 - 直接跳转
          if (this.data.isCreatingCompetition) {
            wx.showToast({
              title: '自定义检测暂不支持创建竞赛',
              icon: 'none'
            });
            return;
          }
          wx.navigateTo({
            url: '/pages/wordtest/custom/custom'
          });
          break;
          
        default:
          wx.showToast({
            title: '该模式暂未开放',
            icon: 'none'
          });
      }
    }, 300);
  },

  /**
   * 检查VIP权限并跳转
   */
  checkVIPAndNavigate(url) {
    // 这里应该检查用户的VIP状态
    // 目前先直接跳转，后续可以添加VIP验证逻辑
    wx.navigateTo({
      url: url,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '墨词自习室 - 单词检测',
      path: '/pages/wordtest/wordtest',
      imageUrl: '/assets/icons/logo.png'
    };
  }
});