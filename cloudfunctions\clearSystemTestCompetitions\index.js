const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 管理员账号识别配置
const ADMIN_OPENID_PART = '5938DE76' // 微信登录账号ID部分
const ADMIN_PHONE = '15547663399' // 用户名密码登录账号

// 检查是否为管理员
function isAdmin(userOpenId, userInfo = null) {
  // 检查openid（微信登录）
  if (userOpenId && userOpenId.includes(ADMIN_OPENID_PART)) {
    return true
  }

  // 如果有额外的用户信息，检查手机号等字段
  if (userInfo) {
    if (userInfo.phone === ADMIN_PHONE || userInfo.username === ADMIN_PHONE) {
      return true
    }

    // 检查用户ID是否包含管理员标识（适用于微信登录用户）
    if (userInfo._id && userInfo._id.includes(ADMIN_OPENID_PART)) {
      return true
    }
  }

  return false
}

// 获取用户信息用于管理员验证
async function getUserInfoForAdmin(openId) {
  try {
    // 查询用户信息
    const userResult = await db.collection('users').where({ openid: openId }).get()
    if (userResult.data && userResult.data.length > 0) {
      return userResult.data[0]
    }
    return null
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

exports.main = async (event, context) => {
  try {
    const wxContext = cloud.getWXContext()
    const currentUserOpenId = wxContext.OPENID
    console.log('清理系统测试竞赛请求，用户:', currentUserOpenId)

    // 获取用户信息用于管理员验证
    const userInfo = await getUserInfoForAdmin(currentUserOpenId)

    // 检查管理员权限
    const isAdminUser = isAdmin(currentUserOpenId, userInfo)

    console.log('权限检查:', {
      currentUserOpenId,
      isAdminUser,
      userInfo: userInfo ? {
        username: userInfo.username,
        phone: userInfo.phone,
        _id: userInfo._id
      } : null
    })

    if (!isAdminUser) {
      return {
        success: false,
        message: '无权限执行此操作，仅管理员可清理系统测试竞赛'
      }
    }

    // 查询所有包含"系统测试竞赛"的竞赛
    const query = {
      name: db.RegExp({
        regexp: '系统测试竞赛',
        options: 'i', // 不区分大小写
      }),
      status: 'active'
    }

    // 查询符合条件的竞赛
    const competitionsResult = await db.collection('competitions')
      .where(query)
      .get()

    const competitions = competitionsResult.data
    console.log(`找到 ${competitions.length} 个系统测试竞赛`)

    if (competitions.length === 0) {
      return {
        success: true,
        message: '没有找到系统测试竞赛',
        deleted: 0
      }
    }

    // 批量删除（软删除）
    const updatePromises = competitions.map(comp => {
      return db.collection('competitions').doc(comp._id).update({
        data: {
          status: 'deleted',
          deletedAt: new Date(),
          deletedBy: 'system_cleanup'
        }
      })
    })

    await Promise.all(updatePromises)

    return {
      success: true,
      message: `成功清理 ${competitions.length} 个系统测试竞赛`,
      deleted: competitions.length,
      competitions: competitions.map(c => ({ id: c._id, name: c.name }))
    }
  } catch (error) {
    console.error('清理系统测试竞赛失败:', error)
    return {
      success: false,
      message: '清理系统测试竞赛失败: ' + error.message,
      error: error
    }
  }
} 