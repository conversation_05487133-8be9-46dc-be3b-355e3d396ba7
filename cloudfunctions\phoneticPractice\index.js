const cloud = require('wx-server-sdk');
const https = require('https');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

// 腾讯元器API配置 - 音标专练
const API_CONFIG = {
  endpoint: 'https://yuanqi.tencent.com/openapi/v1/agent/chat/completions',
  token: 'kpPTHcWuCDwoEgCr93nTcRUvR3iGhCQc',
  assistant_id: 'D0Frv4Z0KWlh'
}

// 模拟音标学习回复，用于测试界面
function generateMockResponse(phonetics) {
  let response = `🎯 音标学习指导\n\n`;
  
  if (Array.isArray(phonetics)) {
    phonetics.forEach((phonetic, index) => {
      response += `📢 /${phonetic.symbol}/ - ${phonetic.name}\n`;
      response += `💡 发音要点：注意口型和舌位的准确定位\n`;
      response += `🔤 例词：${phonetic.example}\n`;
      response += `📝 练习方法：重复朗读，对比标准发音\n`;
      response += `⚠️ 常见错误：避免受母语影响的发音习惯\n`;
      if (index < phonetics.length - 1) response += `\n`;
    });
  } else {
    response += `📢 音标发音学习\n`;
    response += `💡 发音要点：准确掌握口型和气流控制\n`;
    response += `🔤 多练习相关单词\n`;
    response += `📝 建议：结合音频材料进行模仿练习\n`;
  }
  
  response += `\n✨ 学习小贴士：坚持每日练习，录音对比，循序渐进！`;
  return response;
}

// 使用Node.js内置模块发送HTTP请求（真实API调用）
function sendHttpRequest(requestBody) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(requestBody)
    
    const options = {
      hostname: 'yuanqi.tencent.com',
      port: 443,
      path: '/openapi/v1/agent/chat/completions',
      method: 'POST',
      headers: {
        'X-Source': 'openapi',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_CONFIG.token}`,
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 1500 // 降低到1.5秒，确保在2秒云函数超时内完成
    }

    console.log('使用Node.js内置模块发送请求')
    console.log('请求选项:', JSON.stringify(options, null, 2))

    const req = https.request(options, (res) => {
      console.log('响应状态码:', res.statusCode)
      console.log('响应头:', JSON.stringify(res.headers, null, 2))

      let data = ''
      res.on('data', (chunk) => {
        data += chunk
      })

      res.on('end', () => {
        try {
          const result = JSON.parse(data)
          console.log('响应数据:', JSON.stringify(result, null, 2))
          resolve({
            status: res.statusCode,
            data: result,
            headers: res.headers
          })
        } catch (error) {
          console.error('解析响应数据失败:', error.message)
          console.error('原始响应数据:', data)
          reject(new Error('解析响应数据失败: ' + error.message))
        }
      })
    })

    req.on('error', (error) => {
      console.error('请求错误:', error.message)
      reject(error)
    })

    req.on('timeout', () => {
      console.error('请求超时')
      req.destroy()
      reject(new Error('请求超时'))
    })

    req.write(postData)
    req.end()
  })
}

exports.main = async (event, context) => {
  console.log('=== 云函数 phoneticPractice 开始执行 ===');
  console.log('接收到的参数:', JSON.stringify(event, null, 2));
  
  try {
    const { text = '', images = [], test = false, messages = [] } = event;
    
    // 测试模式
    if (test) {
      console.log('=== 测试模式 ===');
      return {
        success: true,
        code: 200,
        message: '云函数运行正常',
        timestamp: new Date().toISOString()
      };
    }

    // 从messages中提取文本（兼容旧格式）
    let inputText = text;
    if (!inputText && messages && messages.length > 0) {
      inputText = messages[0]?.content?.[0]?.text || '';
    }

    console.log('提取到的输入文本:', inputText);
    console.log('原始text参数:', text);
    console.log('原始messages参数:', JSON.stringify(messages, null, 2));

    // 验证输入 - 如果没有文本，直接返回模拟内容而不是报错
    if (!inputText && (!images || images.length === 0)) {
      console.log('没有输入文本，使用默认文本生成模拟内容');
      inputText = '音标发音练习'; // 设置默认文本
    }

    // 暂时使用模拟模式，避免超时问题
    console.log('=== 使用模拟模式（避免超时） ===');
    const mockContent = generateMockResponse(inputText);
    return {
      success: true,
      code: 200,
      data: {
        content: mockContent,
        source: 'mock'
      }
    };

    // 下面的代码暂时不会执行，因为上面已经return了
    console.log('=== 调用腾讯元器API ===');
    
    // 简单测试：如果参数中包含test=true，返回测试数据
    if (event.test === true) {
      console.log('返回测试数据')
      return {
        code: 200,
        data: {
          choices: [{
            message: {
              content: '这是测试数据：音标 /æ/ 的发音方法是张开嘴巴，舌头放低并向前。'
            }
          }]
        },
        message: '测试成功'
      }
    }
    
    const { assistant_id, user_id, stream, useMock } = event
    
    // 验证必要参数
    if (!user_id) {
      console.error('缺少用户ID')
      return {
        code: 400,
        message: '缺少用户ID'
      }
    }
    
    if (!event.messages || !Array.isArray(event.messages) || event.messages.length === 0) {
      console.error('消息内容不能为空')
      return {
        code: 400,
        message: '消息内容不能为空'
      }
    }

    // 提取用户输入的文本
    const userMessage = event.messages[0]?.content?.[0]?.text || '';
    console.log('用户输入文本:', userMessage);

    // 如果是模拟模式或包含特定关键词，返回模拟数据
    if (useMock === true || userMessage.includes('测试') || userMessage.includes('test')) {
      console.log('返回模拟AI回复')
      const mockContent = generateMockResponse(userMessage);
      return {
        code: 200,
        data: {
          choices: [{
            message: {
              content: mockContent
            }
          }]
        },
        message: '模拟响应成功'
      }
    }

    // 构造请求体
    const requestBody = {
      assistant_id: assistant_id || API_CONFIG.assistant_id,
      user_id: user_id,
      stream: stream || false,
      messages: event.messages
    }

    console.log('准备发送请求到腾讯元器API')
    console.log('请求URL:', API_CONFIG.endpoint)
    console.log('请求头:', {
      'X-Source': 'openapi',
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${API_CONFIG.token.substring(0, 10)}...` // 只显示前10位token
    })
    console.log('请求体:', JSON.stringify(requestBody, null, 2))

    const startTime = Date.now()
    
    console.log('开始发送HTTP请求...')
    
    try {
      // 尝试真实API调用
      const response = await sendHttpRequest(requestBody)

      const endTime = Date.now()
      console.log(`API请求耗时: ${endTime - startTime}ms`)
      console.log('腾讯元器API响应状态:', response.status)
      console.log('腾讯元器API响应头:', JSON.stringify(response.headers, null, 2))
      console.log('腾讯元器API响应数据:', JSON.stringify(response.data, null, 2))

      if (response.status === 200 && response.data) {
        // 验证响应数据结构
        if (!response.data.choices || !Array.isArray(response.data.choices) || response.data.choices.length === 0) {
          console.warn('API返回的数据结构不符合预期:', response.data)
          
          // 如果API返回格式异常，返回模拟数据作为备用
          console.log('使用模拟数据作为备用')
          const mockContent = generateMockResponse(userMessage);
          return {
            code: 200,
            data: {
              choices: [{
                message: {
                  content: mockContent + '\n\n⚠️ 注意：当前为备用模式，如需真实AI回复请稍后重试'
                }
              }]
            },
            message: '备用响应成功'
          }
        }

        console.log('API调用成功，返回结果')
        return {
          code: 200,
          data: response.data,
          message: '成功'
        }
      } else {
        console.error('API请求失败，状态码:', response.status, '响应数据:', response.data)
        
        // API失败时返回模拟数据
        console.log('API失败，使用模拟数据')
        const mockContent = generateMockResponse(userMessage);
        return {
          code: 200,
          data: {
            choices: [{
              message: {
                content: mockContent + '\n\n⚠️ 注意：当前为备用模式，如需真实AI回复请稍后重试'
              }
            }]
          },
          message: '备用响应成功'
        }
      }
    } catch (apiError) {
      console.error('API调用失败:', apiError.message)
      
      // API调用失败时返回模拟数据
      console.log('API调用失败，使用模拟数据作为备用')
      const mockContent = generateMockResponse(userMessage);
      return {
        code: 200,
        data: {
          choices: [{
            message: {
              content: mockContent + '\n\n⚠️ 注意：当前为备用模式，如需真实AI回复请稍后重试'
            }
          }]
        },
        message: '备用响应成功'
      }
    }
    
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      error: error.message,
      fallbackContent: generateMockResponse('音标学习')
    };
  }
}; 