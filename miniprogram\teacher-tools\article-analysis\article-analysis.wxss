/* pages/teacher-tools/article-analysis/article-analysis.wxss */
.container {
  padding: 32rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 32rpx;
  backdrop-filter: blur(10rpx);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.step.active {
  color: #fff;
}

.step.completed {
  color: #4ecdc4;
}

.step-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 28rpx;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
}

.step.active .step-circle {
  background: #fff;
  color: #667eea;
  box-shadow: 0 4rpx 20rpx rgba(255, 255, 255, 0.3);
}

.step.completed .step-circle {
  background: #4ecdc4;
  color: #fff;
}

.step-text {
  font-size: 24rpx;
  font-weight: 500;
}

.step-line {
  width: 120rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 40rpx;
  border-radius: 2rpx;
  transition: all 0.3s ease;
}

.step-line.active {
  background: #4ecdc4;
}

/* 词库选择区域 */
.vocab-selection-area {
  margin: 32rpx 0;
  padding: 32rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid #e9ecef;
}

.selection-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 24rpx;
}

.vocab-checkboxes {
  display: flex;
  flex-direction: row;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.vocab-checkbox {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 12rpx;
  background: #fff;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
}

.vocab-checkbox.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.1);
}

.checkbox-icon {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  font-size: 20rpx;
  color: #fff;
  transition: all 0.3s ease;
}

.vocab-checkbox.selected .checkbox-icon {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.start-analysis-btn {
  width: 100%;
  padding: 32rpx;
  border: none;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.start-analysis-btn.enabled {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.start-analysis-btn.enabled:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.4);
}

.start-analysis-btn.disabled {
  background: #e9ecef;
  color: #6c757d;
}

/* 输入区域 */
.input-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  height: calc(100vh - 200rpx);
  display: flex;
  flex-direction: column;
}

.section-header {
  margin-bottom: 32rpx;
  text-align: center;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.section-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.input-area {
  margin-top: 32rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.article-input {
  width: 100%;
  flex: 1;
  min-height: 400rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  transition: all 0.3s ease;
}

.article-input:focus {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.input-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 16rpx;
  padding: 0 8rpx;
}

.action-buttons-row {
  display: flex;
  gap: 12rpx;
  align-items: center;
  justify-content: center;
}

.char-count {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.action-btn {
  padding: 16rpx 24rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
}

.action-btn.primary:hover {
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.4);
  transform: translateY(-2rpx);
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #495057;
  border: 2rpx solid #e9ecef;
}

.action-btn.secondary:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

.action-btn.info {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: #fff;
}

.action-btn.info:hover {
  background: linear-gradient(135deg, #138496, #117a8b);
  box-shadow: 0 4rpx 20rpx rgba(23, 162, 184, 0.4);
  transform: translateY(-2rpx);
}

.action-btn.warning {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #fff;
}

.action-btn.warning:hover {
  background: linear-gradient(135deg, #e0a800, #d39e00);
  box-shadow: 0 4rpx 20rpx rgba(255, 193, 7, 0.4);
  transform: translateY(-2rpx);
}

.action-btn.danger {
  background: #ff6b6b;
  color: #fff;
}

.action-btn.danger:hover {
  background: #ff5252;
  box-shadow: 0 4rpx 20rpx rgba(255, 107, 107, 0.4);
}

.action-btn.small {
  padding: 12rpx 20rpx;
  font-size: 22rpx;
}

/* 词库选择器 */
.vocab-selector {
  margin-bottom: 40rpx;
}

.selector-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.vocab-types {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.vocab-type {
  flex: 1;
  min-width: 180rpx;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.vocab-type.selected {
  border-width: 3rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transform: translateY(-2rpx);
}

.vocab-type.small {
  min-width: 120rpx;
  padding: 16rpx;
}

.vocab-name {
  font-size: 28rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.vocab-type.small .vocab-name {
  font-size: 24rpx;
  margin-bottom: 0;
}

.vocab-desc {
  font-size: 22rpx;
  color: #666;
  display: block;
}

/* 开始按钮 */
.start-btn-container {
  text-align: center;
}

.start-btn {
  width: 100%;
  padding: 32rpx;
  border: none;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.start-btn.enabled {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.start-btn.enabled:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.4);
}

.start-btn.disabled {
  background: #e9ecef;
  color: #6c757d;
}

/* 结果区域 */
.result-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

/* 结果头部 */
.result-header {
  margin-bottom: 32rpx;
}

.result-title {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.vocab-indicator {
  padding: 8rpx 16rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
}

.current-vocab {
  font-size: 24rpx;
  font-weight: 500;
}

/* 词库管理栏 */
.vocab-management-bar {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.management-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.vocab-toggles {
  display: flex;
  gap: 12rpx;
  justify-content: center;
}

.vocab-toggle {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  border: 2rpx solid #ddd;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120rpx;
  justify-content: center;
}

.vocab-toggle.active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);
}

.toggle-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}

.toggle-icon {
  font-size: 20rpx;
  color: #667eea;
  font-weight: bold;
}

.switch-btn {
  padding: 12rpx 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  background: #fff;
  color: #666;
  transition: all 0.3s ease;
}

.switch-btn.gaokao.active {
  background: #ff6b6b;
  border-color: #ff6b6b;
  color: #fff;
}

.switch-btn.cet4.active {
  background: #4ecdc4;
  border-color: #4ecdc4;
  color: #fff;
}

.switch-btn.cet6.active {
  background: #45b7d1;
  border-color: #45b7d1;
  color: #fff;
}

.switch-btn:hover {
  border-color: #dee2e6;
  background: #f8f9fa;
}

/* 统计信息 */
.stats-info {
  display: flex;
  justify-content: space-around;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

.stat-item.highlight .stat-value {
  color: #667eea;
}

/* 详细统计 */
.detailed-stats {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  margin-top: 20rpx;
  padding: 20rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12rpx;
}

.detailed-stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-label-small {
  font-size: 24rpx;
  color: #666;
}

.stat-value-small {
  font-size: 28rpx;
  font-weight: 600;
}

/* 标注内容 */
.marked-content {
  margin-bottom: 32rpx;
}

.marked-text-container {
  background: #fff;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  min-height: 300rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.marked-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
}

/* 标记词汇的通用样式 */
.marked-word {
  position: relative;
  display: inline-block;
  cursor: pointer;
  transition: all 0.2s ease;
}

.marked-word:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
}

/* 高考词汇标记 */
.marked-gaokao {
  background: linear-gradient(120deg, rgba(255, 107, 107, 0.2), rgba(255, 107, 107, 0.3)) !important;
  border: 2rpx solid rgba(255, 107, 107, 0.6) !important;
  border-radius: 8rpx !important;
  padding: 2rpx 6rpx !important;
  margin: 0 2rpx !important;
  box-shadow: 0 2rpx 6rpx rgba(255, 107, 107, 0.25);
  font-weight: 500 !important;
}

.marked-gaokao:hover {
  background: linear-gradient(120deg, rgba(255, 107, 107, 0.3), rgba(255, 107, 107, 0.4)) !important;
  border-color: rgba(255, 107, 107, 0.8) !important;
  box-shadow: 0 3rpx 10rpx rgba(255, 107, 107, 0.35);
}

/* 四级词汇标记 - 与高考样式保持一致 */
.marked-cet4 {
  background: #BBDEFB !important;  /* 浅蓝色背景 */
  border: 2rpx solid #2196F3 !important;  /* 蓝色边框 */
  border-radius: 8rpx !important;
  padding: 2rpx 6rpx !important;
  margin: 0 2rpx !important;
  box-shadow: 0 2rpx 6rpx rgba(33, 150, 243, 0.25);
  font-weight: 500 !important;
}

.marked-cet4:hover {
  background: #90CAF9 !important;  /* 悬停时稍深的蓝色 */
  border-color: #1976D2 !important;
  box-shadow: 0 3rpx 10rpx rgba(33, 150, 243, 0.35);
}

/* 六级词汇标记 - 与高考样式保持一致 */
.marked-cet6 {
  background: #C8E6C9 !important;  /* 浅绿色背景 */
  border: 2rpx solid #4CAF50 !important;  /* 绿色边框 */
  border-radius: 8rpx !important;
  padding: 2rpx 6rpx !important;
  margin: 0 2rpx !important;
  box-shadow: 0 2rpx 6rpx rgba(76, 175, 80, 0.25);
  font-weight: 500 !important;
}

.marked-cet6:hover {
  background: #A5D6A7 !important;  /* 悬停时稍深的绿色 */
  border-color: #388E3C !important;
  box-shadow: 0 3rpx 10rpx rgba(76, 175, 80, 0.35);
}

/* 重合词汇的特殊样式 */
.marked-multiple {
  position: relative;
  border-width: 3rpx !important;
  box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.2) !important;
}

.marked-multiple::after {
  content: '●';
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 16rpx;
  height: 16rpx;
  background: #FF9800;
  color: #fff;
  border-radius: 50%;
  font-size: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #fff;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
}

.marked-multiple:hover::after {
  background: #F57C00;
}

/* 标记词汇的动画效果 */
@keyframes wordHighlight {
  0% {
    transform: scale(1);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }
}

.marked-word.animate {
  animation: wordHighlight 0.6s ease-in-out;
}

/* 底部操作 */
.result-actions {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.result-actions .action-btn {
  flex: 1;
  text-align: center;
  justify-content: center;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 48rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #e9ecef;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
} 