<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">{{isCreatingCompetition ? '创建竞赛' : '单词检测'}}</text>
      <text class="page-subtitle">{{isCreatingCompetition ? '选择竞赛模式' : '选择您喜欢的检测方式'}}</text>
    </view>
  </view>

  <!-- 创建竞赛提示 -->
  <view class="competition-tip" wx:if="{{isCreatingCompetition}}">
    <text class="tip-icon">🏆</text>
    <text class="tip-text">您正在创建{{competitionMode === 'elimination' ? '消消乐' : competitionMode === 'en2zh' ? '英译汉' : competitionMode === 'zh2en' ? '汉译英' : competitionMode === 'dictation' ? '听写' : '单词'}}竞赛</text>
  </view>

  <!-- 当前词库显示 -->
  <view class="library-bar">
    <view class="library-info">
      <text class="library-label">当前词库</text>
      <text class="library-name">{{currentLibrary.name || '高考词汇'}}</text>
    </view>
    <view class="switch-btn" bindtap="onSwitchLibrary">
      <text>换书</text>
    </view>
  </view>

  <!-- 检测模式网格 -->
  <view class="test-modes">
    <view class="mode-card" bindtap="onModeSelect" data-mode="en2zh">
      <view class="card-content blue">
        <view class="mode-icon-wrapper">
          <view class="mode-icon">🔤</view>
          <view class="icon-glow"></view>
        </view>
        <view class="mode-title">英译汉</view>
        <view class="mode-desc">看英文选中文</view>
        <view class="mode-level">基础</view>
      </view>
    </view>

    <view class="mode-card" bindtap="onModeSelect" data-mode="dictation">
      <view class="card-content pink">
        <view class="mode-icon-wrapper">
          <view class="mode-icon">✏️</view>
          <view class="icon-glow"></view>
        </view>
        <view class="mode-title">听写</view>
        <view class="mode-desc">听音拼写单词</view>
        <view class="mode-level">进阶</view>
      </view>
    </view>

    <view class="mode-card" bindtap="onModeSelect" data-mode="zh2en">
      <view class="card-content green">
        <view class="mode-icon-wrapper">
          <view class="mode-icon">🈳</view>
          <view class="icon-glow"></view>
        </view>
        <view class="mode-title">汉译英</view>
        <view class="mode-desc">看中文写英文</view>
        <view class="mode-level">高级</view>
      </view>
    </view>

    <view class="mode-card" bindtap="onModeSelect" data-mode="passage">
      <view class="card-content purple">
        <view class="mode-icon-wrapper">
          <view class="mode-icon">📄</view>
          <view class="icon-glow"></view>
        </view>
        <view class="mode-title">短文翻译</view>
        <view class="mode-desc">语境理解能力</view>
        <view class="mode-level">专家</view>
      </view>
    </view>

    <view class="mode-card" bindtap="onModeSelect" data-mode="custom">
      <view class="card-content teal">
        <view class="mode-icon-wrapper">
          <view class="mode-icon">⚙️</view>
          <view class="icon-glow"></view>
        </view>
        <view class="mode-title">自定义检测</view>
        <view class="mode-desc">个性化学习方案</view>
        <view class="mode-level">个性</view>
      </view>
    </view>

    <view class="mode-card" bindtap="onModeSelect" data-mode="puzzle">
      <view class="card-content orange">
        <view class="mode-icon-wrapper">
          <view class="mode-icon">🧩</view>
          <view class="icon-glow"></view>
        </view>
        <view class="mode-title">消消乐</view>
        <view class="mode-desc">趣味配对游戏</view>
        <view class="mode-level">娱乐</view>
      </view>
    </view>
  </view>

  <!-- 底部提示信息 -->
  <view class="bottom-tips">
    <text class="tip-text">💡 建议从基础模式开始，循序渐进提升能力</text>
  </view>
</view> 