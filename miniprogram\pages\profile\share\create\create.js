Page({

  /**
   * 页面的初始数据
   */
  data: {
    testTypes: [
      {
        id: 'word-test',
        title: '单词测试',
        subtitle: '英译汉、汉译英、听写、消消乐等',
        icon: '🔍',
        color: 'blue',
        modes: [
          { id: 'en_to_cn', name: '英译汉', icon: '🇨🇳' },
          { id: 'cn_to_en', name: '汉译英', icon: '🇺🇸' },
          { id: 'dictation', name: '听写', icon: '🎧' },
          { id: 'elimination', name: '消消乐', icon: '🎮' },
          { id: 'custom', name: '自定义检测', icon: '📝' }
        ]
      },
      {
        id: 'phrase-test',
        title: '短语测试',
        subtitle: '短语理解与应用能力检测',
        icon: '📝',
        color: 'green',
        modes: [
          { id: 'phrase_comprehension', name: '短语理解', icon: '💭' },
          { id: 'phrase_application', name: '短语应用', icon: '✍️' }
        ]
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 选择测试类型
   */
  selectTestType(e) {
    const testTypeId = e.currentTarget.dataset.id;
    const testType = this.data.testTypes.find(type => type.id === testTypeId);
    
    wx.vibrateShort({
      type: 'light'
    });

    if (testTypeId === 'word-test') {
      // 跳转到单词测试模式选择
      this.showTestModeSelection(testType);
    } else if (testTypeId === 'phrase-test') {
      // 跳转到短语测试页面
      wx.navigateTo({
        url: '/pages/phrasetest/phrasetest?shareMode=create',
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 显示测试模式选择
   */
  showTestModeSelection(testType) {
    const items = testType.modes.map(mode => `${mode.icon} ${mode.name}`);
    
    wx.showActionSheet({
      itemList: items,
      success: (res) => {
        const selectedMode = testType.modes[res.tapIndex];
        this.navigateToTestMode(selectedMode);
      },
      fail: (res) => {
        console.log('用户取消选择');
      }
    });
  },

  /**
   * 跳转到对应的测试模式
   */
  navigateToTestMode(mode) {
    let url = '';
    
    switch(mode.id) {
      case 'en_to_cn':
      case 'cn_to_en':
        // 跳转到单词测试页面
        url = `/pages/wordtest/wordtest?shareMode=create&testMode=${mode.id}`;
        break;
      case 'dictation':
        // 跳转到听写测试模式选择页面
        url = '/pages/spelling/mode-select/mode-select?shareMode=create';
        break;
      case 'elimination':
        // 跳转到消消乐游戏创建页面
        url = '/pages/task/puzzle/puzzle?shareMode=create&mode=custom';
        break;
      case 'custom':
        // 跳转到自定义测试页面
        url = '/pages/task/custom/custom?shareMode=create';
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
        return;
    }

    if (url) {
      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 返回上级页面
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '墨词自习室 - 创建分享测试',
      path: '/pages/index/index',
      imageUrl: '/assets/icons/logo.png'
    };
  }
}) 