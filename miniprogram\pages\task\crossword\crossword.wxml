<view class="container">
  <!-- 游戏标题栏 -->
  <view class="game-header">
    <view class="header-content">
      <text class="game-title">填词游戏</text>
      <view class="game-info">
        <text class="difficulty">难度：{{gameData.difficulty}}</text>
        <text class="progress">{{completedWords}}/{{totalWords}}</text>
      </view>
    </view>
  </view>

  <!-- 游戏网格 -->
  <view class="crossword-grid">
    <view class="grid-container">
      <view 
        class="grid-cell {{item.type}}" 
        wx:for="{{gameData.grid}}" 
        wx:key="index"
        bindtap="onCellTap"
        data-index="{{index}}"
      >
        <text wx:if="{{item.type === 'white' && item.number}}" class="cell-number">{{item.number}}</text>
        <input 
          wx:if="{{item.type === 'white'}}"
          class="cell-input {{selectedCell === index ? 'selected' : ''}}"
          value="{{item.letter}}"
          maxlength="1"
          bindinput="onLetterInput"
          data-index="{{index}}"
          disabled="{{item.locked}}"
        />
      </view>
    </view>
  </view>

  <!-- 线索区域 -->
  <view class="clues-section">
    <view class="clues-tabs">
      <view 
        class="tab-item {{currentTab === 'across' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="across"
      >
        <text>横向 ({{acrossClues.length}})</text>
      </view>
      <view 
        class="tab-item {{currentTab === 'down' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="down"
      >
        <text>纵向 ({{downClues.length}})</text>
      </view>
    </view>

    <scroll-view class="clues-list" scroll-y="true">
      <view 
        class="clue-item {{item.completed ? 'completed' : ''}} {{selectedWord === item.id ? 'selected' : ''}}"
        wx:for="{{currentTab === 'across' ? acrossClues : downClues}}"
        wx:key="id"
        bindtap="selectWord"
        data-word-id="{{item.id}}"
      >
        <view class="clue-number">{{item.number}}</view>
        <view class="clue-content">
          <text class="clue-text">{{item.clue}}</text>
          <text class="clue-length">({{item.length}} 字母)</text>
        </view>
        <view class="clue-status">
          <text wx:if="{{item.completed}}" class="check-icon">✓</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 游戏控制按钮 -->
  <view class="game-controls">
    <view class="control-btn hint-btn" bindtap="getHint">
      <text>💡 提示</text>
    </view>
    <view class="control-btn check-btn" bindtap="checkAnswers">
      <text>✓ 检查</text>
    </view>
    <view class="control-btn clear-btn" bindtap="clearGrid">
      <text>🗑️ 清空</text>
    </view>
    <view class="control-btn solve-btn" bindtap="showAnswers">
      <text>📝 答案</text>
    </view>
  </view>

  <!-- 完成弹窗 -->
  <view class="success-modal {{showSuccess ? 'show' : ''}}" bindtap="hideSuccessModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="success-icon">🎉</view>
      <text class="success-title">恭喜完成！</text>
      <text class="success-desc">您已成功完成本次填词游戏</text>
      <view class="success-stats">
        <text>用时：{{gameTime}}</text>
        <text>正确率：{{accuracy}}%</text>
      </view>
      <view class="modal-buttons">
        <view class="modal-btn secondary" bindtap="restartGame">再玩一次</view>
        <view class="modal-btn primary" bindtap="backToMenu">返回主页</view>
      </view>
    </view>
  </view>
</view> 