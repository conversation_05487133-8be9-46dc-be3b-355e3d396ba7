const cloud = require('wx-server-sdk')
const crypto = require('crypto')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()
const usersCollection = db.collection('users')

function generateToken() {
  return crypto.randomBytes(32).toString('hex')
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  const unionid = wxContext.UNIONID
  
  // 从事件中获取用户信息，如果没有则使用默认值
  const userInfo = event.userInfo || {}
  const nickName = userInfo.nickName || '微信用户'
  const avatarUrl = userInfo.avatarUrl || 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
  const gender = userInfo.gender || 0
  const country = userInfo.country || ''
  const province = userInfo.province || ''
  const city = userInfo.city || ''
  
  console.log('微信登录 - event:', event)
  console.log('微信登录 - openid:', openid)
  console.log('微信登录 - userInfo:', userInfo)

  try {
    // 查找是否已存在用户
    const userRes = await usersCollection.where({ openid }).get()
    let userInfo_db
    
    if (userRes.data.length > 0) {
      // 用户已存在，更新登录信息和微信信息
      userInfo_db = userRes.data[0]
      const token = generateToken()
      
      // 保留已有的昵称和头像，除非用户明确更改
      const existingWechatInfo = userInfo_db.wechatInfo || {}
      
      const updateData = {
        token,
        lastLoginTime: new Date(),
        // 智能更新微信信息：只在新信息更有意义时更新
        wechatInfo: {
          nickName: existingWechatInfo.nickName || nickName,
          avatarUrl: existingWechatInfo.avatarUrl || avatarUrl,
          gender: gender || existingWechatInfo.gender || 0,
          country: country || existingWechatInfo.country || '',
          province: province || existingWechatInfo.province || '',
          city: city || existingWechatInfo.city || ''
        }
      }
      
      // 如果传入的昵称不是默认值且与现有不同，则更新
      if (nickName && nickName !== '微信用户' && nickName !== existingWechatInfo.nickName) {
        updateData.wechatInfo.nickName = nickName
      }
      
      // 如果传入的头像不是默认值且与现有不同，则更新
      if (avatarUrl && !avatarUrl.includes('icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg') && avatarUrl !== existingWechatInfo.avatarUrl) {
        updateData.wechatInfo.avatarUrl = avatarUrl
      }
      
      // 如果有unionid，也更新
      if (unionid) {
        updateData.unionid = unionid
      }
      
      await usersCollection.doc(userInfo_db._id).update({
        data: updateData
      })
      
      userInfo_db.token = token
      userInfo_db.wechatInfo = updateData.wechatInfo
    } else {
      // 新用户，创建账户
      const now = new Date()
      const token = generateToken()
      const username = 'wx_' + openid.substr(-8) // 使用更多字符避免重复
      
      const newUserData = {
        openid,
        username,
        password: '', // 微信登录用户无密码
        token,
        createTime: now,
        lastLoginTime: now,
        wechatInfo: {
          nickName: '', // 新用户昵称为空，需要设置
          avatarUrl,
          gender,
          country,
          province,
          city
        },
        registrationType: 'wechat', // 标记注册方式
        settings: {
          dailyWords: 20,
          reminderEnabled: false,
          reminderTime: '20:00',
          autoPlayAudio: true,
          showPhonetic: true,
          showExample: true
        },
        stats: {
          totalWords: 0,
          learnedWords: 0,
          reviewWords: 0,
          mistakeWords: 0,
          continuousDays: 0,
          lastLearnDate: null
        },
        membership: {
          isVip: false,
          expireTime: null,
          type: 'free'
        },
        isNewUser: true // 标记为新用户
      }
      
      // 如果有unionid，添加
      if (unionid) {
        newUserData.unionid = unionid
      }
      
      const addRes = await usersCollection.add({
        data: newUserData
      })
      
      userInfo_db = {
        _id: addRes._id,
        ...newUserData
      }
    }
    
    // 返回成功结果
    return {
      success: true,
      message: '微信登录成功',
      data: {
        userInfo: userInfo_db,
        token: userInfo_db.token,
        openid: openid
      }
    }
  } catch (error) {
    console.error('微信登录失败:', error)
    return {
      success: false,
      message: '服务器错误',
      error: error.message
    }
  }
} 