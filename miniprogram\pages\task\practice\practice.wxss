.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* 进度条样式 */
.progress-bar {
  height: 6rpx;
  background-color: #e0e0e0;
  border-radius: 3rpx;
  margin-bottom: 40rpx;
  position: relative;
}

.progress {
  height: 100%;
  background-color: #4A90E2;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  right: 0;
  top: 20rpx;
  font-size: 24rpx;
  color: #666;
}

/* 计时器样式 */
.timer {
  text-align: center;
  font-size: 36rpx;
  color: #333;
  margin-bottom: 40rpx;
}

/* 练习卡片样式 */
.practice-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.word-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.word-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.word-phonetic {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #666;
}

.speaker-icon {
  width: 40rpx;
  height: 40rpx;
  margin-left: 10rpx;
}

.word-meaning {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.word-example {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 10rpx;
}

.example-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  display: block;
  margin-bottom: 10rpx;
}

.example-translation {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 选项列表样式 */
.options-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
  text-align: center;
}

.option-item.selected {
  background-color: #4A90E2;
  color: #fff;
}

/* 单词消消乐样式 */
.game-grid {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.game-row {
  display: flex;
  gap: 10rpx;
}

.game-cell {
  flex: 1;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
}

.game-cell.selected {
  background-color: #4A90E2;
  color: #fff;
}

.game-cell.matched {
  background-color: #50E3C2;
  color: #fff;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.action-btn {
  width: 300rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #4A90E2;
  border: none;
}

/* 完成提示样式 */
.completion-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.completion-content {
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
}

.completion-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.completion-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}

.completion-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.completion-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #4A90E2;
  border: none;
} 