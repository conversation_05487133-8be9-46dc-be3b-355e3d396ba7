# 墨词自习室

一款专注于英语单词学习的微信小程序，通过科学的学习方法和个性化的学习计划，帮助用户高效记忆单词。

## 功能特点

- 个性化学习计划
- 智能复习提醒
- 学习数据统计
- 标准发音
- 例句学习
- 错题本功能
- 学习报告

## 技术栈

- 微信小程序原生开发
- 云开发
- 云数据库
- 云函数

## 项目结构

```
miniprogram/
  ├── components/          # 全局组件
  ├── pages/              # 页面文件
  ├── utils/              # 工具函数
  ├── config/             # 配置文件
  ├── assets/             # 静态资源
  └── app.js              # 入口文件

cloudfunctions/           # 云函数
  ├── login/             # 登录
  ├── register/          # 注册
  └── wechat-login/      # 微信登录

database/                # 数据库集合
  ├── users.json         # 用户集合
  ├── words.json         # 单词集合
  └── learning_records.json # 学习记录集合
```

## 开发环境

- 微信开发者工具
- Node.js
- 云开发环境

## 使用说明

1. 克隆项目到本地
2. 在微信开发者工具中导入项目
3. 创建云开发环境，并将环境ID替换到 `app.js` 中
4. 在云开发控制台中创建数据库集合
5. 上传并部署云函数
6. 编译运行项目

## 配置说明

1. 在微信小程序管理后台配置订阅消息模板
2. 将模板ID替换到 `settings.js` 中
3. 配置云开发环境的安全规则

## 注意事项

1. 请确保微信开发者工具版本在 1.05.2201240 以上
2. 请确保基础库版本在 2.2.3 以上
3. 请确保已开通云开发功能

## 贡献指南

1. Fork 本仓库
2. 创建新的分支
3. 提交代码
4. 创建 Pull Request

## 许可证

MIT License 

## 最新更新说明（多关卡竞赛系统）

### 需要更新的云函数

本次更新修改了以下云函数，请重新部署：

1. **getGroupedCompetitionDetail**
   - 路径：`cloudfunctions/getGroupedCompetitionDetail/`
   - 修改内容：添加了用户进度获取功能，返回 `userProgress` 字段
   - 部署方式：
     - 在微信开发者工具中右键点击该云函数文件夹
     - 选择"上传并部署：云端安装依赖"

### 主要功能更新

1. **多关卡竞赛顺序解锁**
   - 用户必须按顺序完成关卡
   - 第一关直接可进入，后续关卡需完成前一关才能解锁
   - 完成一关后可直接点击"进入第X关"按钮继续挑战
   - 已完成的关卡可以重复挑战刷新成绩

2. **Excel上传界面修复**
   - 修复了模态框底部按钮被遮挡的问题
   - 调整了模态框高度和定位

### 测试步骤

1. **测试多关卡竞赛**：
   - 创建一个大词库的竞赛（如整本书3500词）
   - 选择英译汉/汉译英模式，在提示选择分组时选择每组50个词
   - 确认竞赛列表显示"多关卡"徽章和"选择关卡"按钮
   - 点击进入关卡选择页面
   - 验证第一关可直接进入，其他关卡显示锁定状态
   - 完成第一关后，点击"进入第2关"按钮
   - 验证第二关已解锁可以进入

2. **测试Excel上传**：
   - 进入词库页面
   - 创建自定义词库，选择Excel上传
   - 确认底部的"开始处理文件"按钮可见 