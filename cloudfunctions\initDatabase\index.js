const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    console.log('开始初始化数据库集合...')
    
    // 初始化 custom_wordbanks 集合
    try {
      // 先查询集合是否存在
      const checkResult = await db.collection('custom_wordbanks').limit(1).get()
      console.log('custom_wordbanks 集合已存在')
      
      return {
        code: 200,
        message: 'custom_wordbanks 集合已存在',
        data: {
          collections: ['custom_wordbanks'],
          status: 'already_exists'
        }
      }
      
    } catch (error) {
      if (error.errCode === -502005) {
        console.log('custom_wordbanks 集合不存在，开始创建...')
        
        // 创建一个临时文档来初始化集合
        const tempResult = await db.collection('custom_wordbanks').add({
          data: {
            _temp: true,
            _description: '临时文档，用于初始化集合',
            createTime: db.serverDate(),
            createdBy: 'initDatabase',
            openid: wxContext.OPENID
          }
        })
        
        console.log('临时文档创建成功:', tempResult._id)
        
        // 删除临时文档
        await db.collection('custom_wordbanks').doc(tempResult._id).remove()
        console.log('临时文档已删除，集合初始化完成')
        
        // 验证集合是否创建成功
        const verifyResult = await db.collection('custom_wordbanks').limit(1).get()
        
        return {
          code: 200,
          message: 'custom_wordbanks 集合创建成功',
          data: {
            collections: ['custom_wordbanks'],
            status: 'created',
            tempDocId: tempResult._id
          }
        }
        
      } else {
        throw error
      }
    }
    
  } catch (error) {
    console.error('初始化数据库失败:', error)
    
    return {
      code: 500,
      message: '初始化失败: ' + error.message,
      error: {
        errCode: error.errCode,
        errMsg: error.errMsg
      }
    }
  }
} 