.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 公共样式 */
.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

/* 册列表样式 */
.book-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.book-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  transition: background-color 0.2s;
}

.book-item:last-child {
  border-bottom: none;
}

.book-item:active {
  background-color: #f8f8f8;
}

.book-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.book-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.book-count {
  font-size: 24rpx;
  color: #666;
}

.book-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 单元列表样式 */
.unit-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 返回按钮 */
.back-bar {
  margin-bottom: 10rpx;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx;
  background-color: transparent;
  color: #007AFF;
  font-size: 28rpx;
  transition: opacity 0.2s;
}

.back-btn:active {
  opacity: 0.6;
}

/* 箭头图标样式 */
.arrow-icon {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: bold;
}

.back-arrow {
  font-size: 28rpx;
}

/* 全册学习样式 */
.full-book-section {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.full-book-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  transition: background-color 0.2s;
}

.full-book-item:active {
  background-color: #f8f8f8;
}

.full-book-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.full-book-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.full-book-desc {
  font-size: 24rpx;
  color: #666;
}

.full-book-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 单元区域样式 */
.unit-section {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.unit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  transition: background-color 0.2s;
}

.unit-item:last-child {
  border-bottom: none;
}

.unit-item:active {
  background-color: #f8f8f8;
}

.unit-info {
  display: flex;
  align-items: center;
}

.unit-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.unit-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
} 