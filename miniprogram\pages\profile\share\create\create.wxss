/* pages/profile/share/create/create.wxss */
.create-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 0);
}

.nav-left,
.nav-right {
  width: 80rpx;
}

.nav-center {
  flex: 1;
  text-align: center;
}

.back-icon {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: bold;
}

.nav-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
}

/* 页面内容 */
.page-content {
  padding-top: calc(88rpx + var(--status-bar-height, 0));
  padding: calc(88rpx + var(--status-bar-height, 0)) 30rpx 30rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 50rpx;
  padding: 40rpx 20rpx;
}

.header-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.header-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 15rpx;
}

.header-subtitle {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 测试类型列表 */
.test-types {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
  margin-bottom: 50rpx;
}

.test-type-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 25rpx;
  padding: 35rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.1);
}

.test-type-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

/* 背景装饰 */
.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.test-type-card.blue .bg-circle {
  background: rgba(102, 126, 234, 0.1);
}

.test-type-card.green .bg-circle {
  background: rgba(82, 196, 26, 0.1);
}

.circle1 {
  width: 120rpx;
  height: 120rpx;
  top: -30rpx;
  right: -30rpx;
}

.circle2 {
  width: 80rpx;
  height: 80rpx;
  top: 40rpx;
  right: 40rpx;
}

.circle3 {
  width: 60rpx;
  height: 60rpx;
  bottom: -15rpx;
  left: -15rpx;
}

/* 卡片内容 */
.card-content {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.type-icon {
  font-size: 50rpx;
  margin-right: 25rpx;
  width: 60rpx;
  text-align: center;
}

.type-info {
  flex: 1;
}

.type-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.type-subtitle {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.arrow-icon {
  font-size: 32rpx;
  color: #cccccc;
  font-weight: bold;
  margin-left: 20rpx;
}

/* 模式预览 */
.modes-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.mode-tag {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(102, 126, 234, 0.1);
  border: 1rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 15rpx;
  padding: 8rpx 15rpx;
  font-size: 22rpx;
}

.test-type-card.green .mode-tag {
  background: rgba(82, 196, 26, 0.1);
  border-color: rgba(82, 196, 26, 0.2);
}

.mode-icon {
  font-size: 20rpx;
}

.mode-name {
  color: #333333;
  font-size: 22rpx;
}

.mode-tag.more {
  background: rgba(153, 153, 153, 0.1);
  border-color: rgba(153, 153, 153, 0.2);
}

.more-text {
  color: #666666;
  font-size: 22rpx;
}

/* 功能说明 */
.feature-description {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 35rpx;
}

.desc-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 25rpx;
  text-align: center;
}

.desc-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.desc-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.desc-icon {
  font-size: 28rpx;
  width: 40rpx;
  text-align: center;
}

.desc-text {
  flex: 1;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.test-type-card {
  animation: slideInUp 0.6s ease forwards;
}

.test-type-card:nth-child(1) {
  animation-delay: 0.1s;
}

.test-type-card:nth-child(2) {
  animation-delay: 0.2s;
}

.feature-description {
  animation: slideInUp 0.6s ease forwards;
  animation-delay: 0.3s;
} 