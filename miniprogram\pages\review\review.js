const app = getApp();

Page({
  data: {
    // 复习数据
    words: [], // 单词列表
    currentIndex: 0, // 当前单词索引
    currentWord: null, // 当前单词数据
    total: 0, // 总单词数
    progress: 0, // 进度百分比

    // 音频播放数据
    audioContext: null, // 音频上下文
    isPlaying: false, // 是否正在播放
    audioProgress: 0, // 音频进度
    currentTime: '00:00', // 当前时间
    duration: '00:00', // 总时长
    playCount: 0, // 播放次数

    // 界面控制
    showButtons: false, // 是否显示记忆程度按钮

    // 统计数据
    startTime: 0, // 开始时间
    totalTime: '00:00', // 总用时
    reviewedCount: 0, // 已复习数量
    masteredCount: 0, // 已掌握数量
    memoryStats: {
      forget: 0,
      hard: 0,
      good: 0,
      easy: 0
    },
    forgetWords: [], // 忘记的单词列表

    // 完成数据
    showCompletion: false, // 是否显示完成提示
    memoryLevel: 0, // 记忆等级
    memoryLevelText: '', // 记忆程度文本

    // 新加的复习数据
    totalWords: 0,
    showPhonetic: false,
    showMeaning: false,
    showExample: false,
    showAudio: true,
    showCompleteModal: false,
    reviewTime: 0,
    correctCount: 0
  },

  onLoad(options) {
    console.log('复习页面接收到的参数:', options);

    // 初始化音频上下文
    this.initAudioContext();

    // 初始化复习数据
    this.setData({
      startTime: Date.now()
    });

    // 根据参数类型加载数据
    if (options.data) {
      // 来自任务数据（旧方式）
      const taskData = JSON.parse(options.data);
      this.loadWordsFromTaskData(taskData.words);
    } else if (options.libraryId && options.mode) {
      // 来自学习进度页面（新方式）
      this.loadReviewWordsFromProgress(options.libraryId, options.mode);
    } else {
      // 直接加载所有需要复习的单词
      this.loadAllReviewWords();
    }
  },

  onUnload() {
    // 销毁音频上下文
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }

    // 计算复习时间
    if (this.data.startTime) {
      const endTime = new Date();
      this.setData({
        reviewTime: endTime - this.data.startTime
      });
    }
  },

  // 初始化音频上下文
  initAudioContext() {
    const audioContext = wx.createInnerAudioContext();
    audioContext.onPlay(() => {
      this.setData({ isPlaying: true });
    });
    audioContext.onPause(() => {
      this.setData({ isPlaying: false });
    });
    audioContext.onStop(() => {
      this.setData({ isPlaying: false });
    });
    audioContext.onEnded(() => {
      this.setData({ 
        isPlaying: false,
        audioProgress: 0,
        currentTime: '00:00'
      });
    });
    audioContext.onTimeUpdate(() => {
      const progress = (audioContext.currentTime / audioContext.duration) * 100;
      this.setData({
        audioProgress: progress,
        currentTime: this.formatTime(audioContext.currentTime)
      });
    });

    this.setData({ audioContext });
  },

  // 加载单词数据
  loadWords(words) {
    // 随机打乱单词顺序
    this.shuffleArray(words);
    
    this.setData({
      words,
      total: words.length,
      currentWord: words[0],
      startTime: Date.now()
    });

    // 设置音频
    this.setAudio();
  },

  // 随机打乱数组
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  },

  // 设置音频
  setAudio() {
    if (!this.data.audioContext || !this.data.currentWord) return;

    this.data.audioContext.src = this.data.currentWord.audioUrl;
    this.data.audioContext.onCanplay(() => {
      this.setData({
        duration: this.formatTime(this.data.audioContext.duration)
      });
    });
  },

  // 播放/暂停音频
  togglePlay() {
    if (!this.data.audioContext) return;

    if (this.data.isPlaying) {
      this.data.audioContext.pause();
    } else {
      this.data.audioContext.play();
      this.setData({
        playCount: this.data.playCount + 1
      });
    }
  },

  // 滑动条变化
  onSliderChange(e) {
    if (!this.data.audioContext) return;

    const value = e.detail.value;
    const time = (value / 100) * this.data.audioContext.duration;
    this.data.audioContext.seek(time);
  },

  // 显示记忆程度按钮
  onShowButtons() {
    this.setData({
      showButtons: true
    });
  },

  // 标记记忆程度
  onMarkMemory(e) {
    const level = parseInt(e.currentTarget.dataset.level);
    this.setData({
      memoryLevel: level
    });
  },

  // 下一个单词
  onNextWord() {
    if (this.data.memoryLevel === 0) {
      wx.showToast({
        title: '请选择记忆程度',
        icon: 'none'
      });
      return;
    }

    try {
      const db = wx.cloud.database();
      const userInfo = app.globalData.userInfo;
      
      // 更新单词记忆状态
      db.collection('word_memory').add({
        data: {
          _openid: userInfo._openid,
          wordId: this.data.currentWord._id,
          level: this.data.memoryLevel,
          reviewTime: db.serverDate()
        }
      });

      // 更新统计数据
      const reviewedCount = this.data.reviewedCount + 1;
      const masteredCount = this.data.masteredCount + (this.data.memoryLevel === 4 ? 1 : 0);
      
      // 计算进度
      const progress = (reviewedCount / this.data.total) * 100;
      
      // 检查是否完成
      if (reviewedCount >= this.data.total) {
        this.onComplete();
        return;
      }

      // 更新当前单词
      const nextIndex = this.data.currentIndex + 1;
      this.setData({
        currentIndex: nextIndex,
        currentWord: this.data.words[nextIndex],
        progress: progress,
        reviewedCount: reviewedCount,
        masteredCount: masteredCount,
        memoryLevel: 0
      });

      // 设置新的音频
      this.setAudio();
    } catch (error) {
      console.error('更新记忆状态失败：', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  // 完成复习
  onComplete() {
    // 计算总用时
    const totalTime = Math.floor((Date.now() - this.data.startTime) / 1000);
    const minutes = Math.floor(totalTime / 60);
    const seconds = totalTime % 60;
    const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    // 计算记忆程度
    const stats = this.data.memoryStats;
    const total = this.data.total;
    const memoryLevelText = this.calculateMemoryLevel(stats, total);

    this.setData({
      showCompletion: true,
      totalTime: timeStr,
      memoryLevelText
    });
  },

  // 计算记忆程度
  calculateMemoryLevel(stats, total) {
    const score = (stats.easy * 1 + stats.good * 0.8 + stats.hard * 0.5) / total;
    
    if (score >= 0.9) return '优秀';
    if (score >= 0.7) return '良好';
    if (score >= 0.5) return '一般';
    return '需加强';
  },

  // 格式化时间
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  },

  // 加入错题本
  async onAddToMistakes() {
    const db = wx.cloud.database();
    const forgetWords = this.data.forgetWords;

    try {
      // 批量添加错题
      for (const word of forgetWords) {
        await db.collection('mistake_review').add({
          data: {
            ...word,
            createTime: db.serverDate(),
            openid: app.globalData.openid
          }
        });
      }

      wx.showToast({
        title: '已加入错题本',
        icon: 'success'
      });
    } catch (error) {
      console.error('添加错题失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    }
  },

  // 错题重练
  onRetryMistakes() {
    if (this.data.forgetWords.length === 0) return;

    // 构建任务数据
    const taskData = {
      words: this.data.forgetWords
    };

    // 跳转到复习页面
    wx.redirectTo({
      url: `/pages/review/review?data=${JSON.stringify(taskData)}`
    });
  },

  // 返回首页
  onBackToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 从任务数据加载单词（旧方式）
  loadWordsFromTaskData(words) {
    if (!words || words.length === 0) {
      wx.showToast({
        title: '没有需要复习的单词',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      words: words,
      total: words.length,
      totalWords: words.length,
      currentWord: words[0]
    });
  },

  // 从学习进度加载复习词汇（新方式）
  async loadReviewWordsFromProgress(libraryId, mode) {
    wx.showLoading({ title: '加载复习词汇...' });

    try {
      // 调用云函数获取复习词汇
      const result = await wx.cloud.callFunction({
        name: 'getLearningProgress',
        data: {
          libraryId: libraryId,
          mode: mode,
          includeReviewWords: true
        }
      });

      wx.hideLoading();

      if (result.result && result.result.success && result.result.data.length > 0) {
        const progressData = result.result.data[0];
        console.log('获取到的进度数据:', progressData);

        // 获取需要复习的词汇
        const reviewWords = progressData.reviewInfo ? progressData.reviewInfo.reviewWords : [];

        if (reviewWords.length === 0) {
          wx.showToast({
            title: '暂无需要复习的词汇',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }

        // 同时获取错词进行复习
        await this.loadMistakeWords(libraryId, mode, reviewWords);

      } else {
        wx.showToast({
          title: '未找到复习数据',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载复习词汇失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载错词并合并到复习列表
  async loadMistakeWords(libraryId, mode, ebbinghausWords) {
    try {
      // 调用云函数获取错词
      const mistakeResult = await wx.cloud.callFunction({
        name: 'getMistakes',
        data: {
          libraryId: libraryId,
          testMode: mode,
          limit: 50 // 限制错词数量
        }
      });

      let allReviewWords = [...ebbinghausWords];

      if (mistakeResult.result && mistakeResult.result.success && mistakeResult.result.data.length > 0) {
        const mistakeWords = mistakeResult.result.data.map(mistake => ({
          ...mistake,
          isFromMistakes: true, // 标记为错词
          reviewType: 'mistake'
        }));

        // 合并艾宾浩斯复习词汇和错词，去重
        const wordIds = new Set(allReviewWords.map(w => w.wordId || w._id));
        const uniqueMistakeWords = mistakeWords.filter(w => !wordIds.has(w.wordId || w._id));

        allReviewWords = [...allReviewWords, ...uniqueMistakeWords];
        console.log(`合并复习词汇: 艾宾浩斯${ebbinghausWords.length}个, 错词${uniqueMistakeWords.length}个`);
      }

      if (allReviewWords.length === 0) {
        wx.showToast({
          title: '暂无需要复习的词汇',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      // 打乱顺序
      allReviewWords = this.shuffleArray(allReviewWords);

      this.setData({
        words: allReviewWords,
        total: allReviewWords.length,
        totalWords: allReviewWords.length,
        currentWord: allReviewWords[0],
        libraryId: libraryId,
        mode: mode
      });

    } catch (error) {
      console.error('加载错词失败:', error);
      // 即使错词加载失败，也继续使用艾宾浩斯词汇
      this.setData({
        words: ebbinghausWords,
        total: ebbinghausWords.length,
        totalWords: ebbinghausWords.length,
        currentWord: ebbinghausWords[0],
        libraryId: libraryId,
        mode: mode
      });
    }
  },

  // 打乱数组顺序
  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  },

  // 加载所有需要复习的单词（通用方式）
  async loadAllReviewWords() {
    try {
      const db = wx.cloud.database();
      const userInfo = await app.getUserInfo();

      // 获取需要复习的单词
      const now = new Date();
      const recordsRes = await db.collection('learning_records')
        .where({
          userId: userInfo._id,
          nextReviewTime: db.command.lte(now)
        })
        .get();

      if (recordsRes.data.length === 0) {
        wx.showToast({
          title: '暂无需要复习的单词',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      // 获取单词详情
      const wordIds = recordsRes.data.map(record => record.wordId);
      const wordsRes = await db.collection('words')
        .where({
          _id: db.command.in(wordIds)
        })
        .get();

      const words = wordsRes.data;

      this.setData({
        words,
        totalWords: words.length,
        total: words.length,
        currentWord: words[0]
      });
    } catch (error) {
      console.error('加载单词失败：', error);
      wx.showToast({
        title: '加载单词失败',
        icon: 'none'
      });
    }
  },

  // 播放发音
  playAudio() {
    if (this.data.currentWord && this.data.currentWord.audioUrl) {
      const innerAudioContext = wx.createInnerAudioContext();
      innerAudioContext.src = this.data.currentWord.audioUrl;
      innerAudioContext.play();
    }
  },

  // 切换音标显示
  togglePhonetic() {
    this.setData({
      showPhonetic: !this.data.showPhonetic
    });
  },

  // 切换释义显示
  toggleMeaning() {
    this.setData({
      showMeaning: !this.data.showMeaning
    });
  },

  // 切换例句显示
  toggleExample() {
    this.setData({
      showExample: !this.data.showExample
    });
  },

  // 标记为不认识
  async markAsWrong() {
    await this.saveReviewRecord(false);
    this.nextWord();
  },

  // 标记为认识
  async markAsRemember() {
    await this.saveReviewRecord(true);
    this.nextWord();
  },

  // 保存复习记录
  async saveReviewRecord(isCorrect) {
    try {
      const db = wx.cloud.database();
      const userInfo = await app.getUserInfo();
      
      // 更新学习记录
      const record = await db.collection('learning_records')
        .where({
          userId: userInfo._id,
          wordId: this.data.currentWord._id
        })
        .get();
      
      if (record.data.length > 0) {
        const currentRecord = record.data[0];
        const reviewTimes = currentRecord.reviewTimes + 1;
        const masteryLevel = isCorrect ? 
          Math.min(currentRecord.masteryLevel + 1, 4) : 
          Math.max(currentRecord.masteryLevel - 1, 1);
        
        // 计算下次复习时间
        const nextReviewTime = this.calculateNextReviewTime(masteryLevel);
        
        await db.collection('learning_records').doc(currentRecord._id).update({
          data: {
            reviewTimes,
            lastReviewTime: new Date(),
            nextReviewTime,
            masteryLevel
          }
        });
        
        // 更新正确计数
        if (isCorrect) {
          this.setData({
            correctCount: this.data.correctCount + 1
          });
        }
      }
    } catch (error) {
      console.error('保存复习记录失败：', error);
      wx.showToast({
        title: '保存复习记录失败',
        icon: 'none'
      });
    }
  },

  // 计算下次复习时间
  calculateNextReviewTime(masteryLevel) {
    const now = new Date();
    let days = 0;
    
    switch (masteryLevel) {
      case 1: // 困难
        days = 1;
        break;
      case 2: // 一般
        days = 3;
        break;
      case 3: // 简单
        days = 7;
        break;
      case 4: // 掌握
        days = 14;
        break;
    }
    
    now.setDate(now.getDate() + days);
    return now;
  },

  // 下一个单词
  nextWord() {
    const nextIndex = this.data.currentIndex + 1;
    
    if (nextIndex < this.data.totalWords) {
      this.setData({
        currentIndex: nextIndex,
        currentWord: this.data.words[nextIndex],
        progress: (nextIndex / this.data.totalWords) * 100,
        showPhonetic: false,
        showMeaning: false,
        showExample: false
      });
    } else {
      this.setData({
        showCompleteModal: true
      });
    }
  },

  // 返回首页
  goToHome() {
    wx.redirectTo({
      url: '/pages/index/index'
    });
  }
}); 