const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 艾宾浩斯记忆曲线间隔（天）
const EBBINGHAUS_INTERVALS = [1, 2, 4, 7, 15, 30, 60]

// 计算下次复习时间
function calculateNextReviewTime(masteryLevel, lastReviewTime) {
  const now = new Date()
  const lastReview = new Date(lastReviewTime)
  
  // 根据掌握程度确定复习间隔
  let intervalDays = EBBINGHAUS_INTERVALS[Math.min(masteryLevel - 1, EBBINGHAUS_INTERVALS.length - 1)]
  
  // 计算下次复习时间
  const nextReviewTime = new Date(lastReview.getTime() + intervalDays * 24 * 60 * 60 * 1000)
  
  return {
    nextReviewTime: nextReviewTime,
    intervalDays: intervalDays,
    isReviewDue: now >= nextReviewTime
  }
}

// 获取需要复习的词汇（从learning_records集合获取）
async function getReviewWords(progressData, openid) {
  console.log('getReviewWords - 输入数据:', {
    libraryId: progressData.libraryId,
    mode: progressData.mode,
    openid: openid
  })

  try {
    const now = new Date()

    // 首先根据openid获取用户ID
    const userResult = await db.collection('users')
      .where({
        openid: openid
      })
      .get()

    if (userResult.data.length === 0) {
      console.log('getReviewWords - 未找到用户信息')
      return []
    }

    const userId = userResult.data[0]._id
    console.log('getReviewWords - 找到用户ID:', userId)

    // 从learning_records集合获取需要复习的词汇
    const learningRecordsResult = await db.collection('learning_records')
      .where({
        userId: userId,
        nextReviewTime: db.command.lte(now)
      })
      .get()

    console.log('getReviewWords - 查询到的学习记录:', learningRecordsResult.data.length, '条')

    if (learningRecordsResult.data.length === 0) {
      console.log('getReviewWords - 没有需要复习的词汇')
      return []
    }

    // 获取词汇详情，并根据词库筛选
    const wordIds = learningRecordsResult.data.map(record => record.wordId)
    let wordsQuery = db.collection('words')
      .where({
        _id: db.command.in(wordIds)
      })

    // 如果指定了词库，则筛选特定词库的词汇
    if (progressData.libraryId) {
      wordsQuery = wordsQuery.where({
        _id: db.command.in(wordIds),
        libraryId: progressData.libraryId
      })
    }

    const wordsResult = await wordsQuery.get()
    console.log('getReviewWords - 获取到词汇详情:', wordsResult.data.length, '条')

    // 合并学习记录和词汇详情，只保留匹配词库的词汇
    const reviewWords = learningRecordsResult.data
      .map(record => {
        const wordDetail = wordsResult.data.find(word => word._id === record.wordId)
        if (!wordDetail) return null // 如果词汇不属于指定词库，则过滤掉
        return {
          ...wordDetail,
          ...record,
          wordId: record.wordId,
          isReviewDue: true
        }
      })
      .filter(word => word !== null) // 移除null值

    // 按优先级排序：掌握程度低的优先，过期时间长的优先
    reviewWords.sort((a, b) => {
      if (a.masteryLevel !== b.masteryLevel) {
        return a.masteryLevel - b.masteryLevel // 掌握程度低的优先
      }
      return new Date(a.nextReviewTime) - new Date(b.nextReviewTime) // 过期时间长的优先
    })

    console.log('getReviewWords - 最终结果:', reviewWords.length, '个需要复习的词汇')
    return reviewWords

  } catch (error) {
    console.error('getReviewWords - 查询失败:', error)
    return []
  }
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { libraryId, mode, includeReviewWords = false } = event

    console.log('获取学习进度:', {
      openid: wxContext.OPENID,
      libraryId,
      mode,
      includeReviewWords
    })

    let query = db.collection('learning_progress')
      .where({
        openid: wxContext.OPENID
      })

    // 如果指定了词库和模式，添加过滤条件
    if (libraryId) {
      query = query.where({
        openid: wxContext.OPENID,
        libraryId: libraryId
      })
      
      if (mode) {
        query = query.where({
          openid: wxContext.OPENID,
          libraryId: libraryId,
          mode: mode
        })
      }
    }

    const result = await query
      .orderBy('updateTime', 'desc')
      .get()

    console.log('查询到的学习进度:', result.data.length, '条')

    // 处理学习进度数据
    const progressList = await Promise.all(result.data.map(async (progress) => {
      console.log('处理进度数据:', progress.libraryId, progress.mode, 'includeReviewWords:', includeReviewWords)

      const reviewWords = includeReviewWords ? await getReviewWords(progress, wxContext.OPENID) : []
      const processedProgress = {
        ...progress,
        // 计算复习相关信息
        reviewInfo: includeReviewWords ? {
          reviewWords: reviewWords,
          totalReviewWords: reviewWords.length
        } : null
      }

      console.log('处理后的reviewInfo:', processedProgress.reviewInfo)
      return processedProgress
    }))

    return {
      success: true,
      message: '获取学习进度成功',
      data: progressList
    }

  } catch (error) {
    console.error('获取学习进度失败:', error)
    return {
      success: false,
      message: '获取学习进度失败',
      error: error.message
    }
  }
}
