// 教师权限管理工具
const TEACHER_PASSWORD = 'Xes.2025'; // 教师权限密码
const TEACHER_AUTH_KEY = 'teacher_auth_verified'; // 本地存储键名

const TeacherAuth = {
  // 检查用户是否已验证教师权限
  isTeacherVerified: function(userOpenid) {
    try {
      const authData = wx.getStorageSync(TEACHER_AUTH_KEY);
      if (authData && typeof authData === 'object') {
        // 检查当前用户是否已验证
        return authData[userOpenid] === true;
      }
      return false;
    } catch (error) {
      console.error('检查教师权限失败:', error);
      return false;
    }
  },

  // 验证教师密码
  verifyTeacherPassword: function(password) {
    return password === TEACHER_PASSWORD;
  },

  // 保存教师权限验证状态
  saveTeacherAuth: function(userOpenid) {
    try {
      let authData = {};
      try {
        const existingData = wx.getStorageSync(TEACHER_AUTH_KEY);
        if (existingData && typeof existingData === 'object') {
          authData = existingData;
        }
      } catch (e) {
        console.log('读取现有权限数据失败，使用空对象');
      }
      
      // 设置当前用户的权限状态
      authData[userOpenid] = true;
      
      wx.setStorageSync(TEACHER_AUTH_KEY, authData);
      console.log('教师权限验证状态已保存');
      return true;
    } catch (error) {
      console.error('保存教师权限失败:', error);
      return false;
    }
  },

  // 清除教师权限验证状态（可选功能）
  clearTeacherAuth: function(userOpenid) {
    try {
      const authData = wx.getStorageSync(TEACHER_AUTH_KEY);
      if (authData && typeof authData === 'object' && authData[userOpenid]) {
        delete authData[userOpenid];
        wx.setStorageSync(TEACHER_AUTH_KEY, authData);
        console.log('教师权限验证状态已清除');
      }
      return true;
    } catch (error) {
      console.error('清除教师权限失败:', error);
      return false;
    }
  },

  // 显示教师权限验证弹窗
  showTeacherAuthDialog: function(callback, userIdentifier) {
    // 创建自定义弹窗页面
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    // 设置页面数据用于显示自定义弹窗
    if (currentPage && currentPage.setData) {
      currentPage.setData({
        showTeacherAuthModal: true,
        teacherAuthCallback: callback,
        teacherAuthUserIdentifier: userIdentifier,
        teacherAuthPassword: '',
        teacherAuthPasswordVisible: false
      });
    } else {
      // 如果无法获取当前页面，降级使用系统弹窗
      this.showSystemAuthDialog(callback, userIdentifier);
    }
  },

  // 系统弹窗备用方案
  showSystemAuthDialog: function(callback, userIdentifier) {
    wx.showModal({
      title: '🔐 教师权限验证',
      content: '此功能为教师专用，请输入教师权限密码',
      editable: true,
      placeholderText: '请输入密码',
      confirmText: '验证',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.handlePasswordVerification(res.content || '', callback, userIdentifier);
        } else {
          if (callback && typeof callback === 'function') {
            callback(false);
          }
        }
      },
      fail: () => {
        wx.showToast({
          title: '操作失败',
          icon: 'error'
        });
        if (callback && typeof callback === 'function') {
          callback(false);
        }
      }
    });
  },

  // 处理密码验证逻辑
  handlePasswordVerification: function(inputPassword, callback, userIdentifier) {
    if (this.verifyTeacherPassword(inputPassword)) {
      // 密码正确，保存验证状态
      this.saveTeacherAuth(userIdentifier);
      
      wx.showToast({
        title: '验证成功',
        icon: 'success',
        duration: 1500
      });
      
      console.log('教师权限验证成功，用户标识:', userIdentifier);
      
      // 延迟执行回调，让用户看到成功提示
      setTimeout(() => {
        if (callback && typeof callback === 'function') {
          callback(true);
        }
      }, 1500);
    } else {
      // 密码错误
      wx.showToast({
        title: '密码错误',
        icon: 'error',
        duration: 2000
      });
      
      console.log('教师权限验证失败，密码错误');
      
      // 询问是否重新输入
      setTimeout(() => {
        wx.showModal({
          title: '❌ 密码错误',
          content: '您输入的密码不正确，是否重新输入？',
          confirmText: '重新输入',
          cancelText: '取消',
          success: (retryRes) => {
            if (retryRes.confirm) {
              // 重新显示验证弹窗
              this.showTeacherAuthDialog(callback, userIdentifier);
            } else {
              if (callback && typeof callback === 'function') {
                callback(false);
              }
            }
          }
        });
      }, 2000);
    }
  },

  // 关闭自定义弹窗
  closeTeacherAuthModal: function() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage && currentPage.setData) {
      currentPage.setData({
        showTeacherAuthModal: false,
        teacherAuthCallback: null,
        teacherAuthUserIdentifier: null,
        teacherAuthPassword: '',
        teacherAuthPasswordVisible: false
      });
    }
  },

  // 处理自定义弹窗的密码输入
  onTeacherAuthPasswordInput: function(e) {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage && currentPage.setData) {
      currentPage.setData({
        teacherAuthPassword: e.detail.value
      });
    }
  },

  // 切换密码可见性
  togglePasswordVisibility: function() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage && currentPage.data) {
      currentPage.setData({
        teacherAuthPasswordVisible: !currentPage.data.teacherAuthPasswordVisible
      });
    }
  },

  // 处理自定义弹窗的验证
  onTeacherAuthConfirm: function() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage && currentPage.data) {
      const { teacherAuthPassword, teacherAuthCallback, teacherAuthUserIdentifier } = currentPage.data;
      
      this.closeTeacherAuthModal();
      this.handlePasswordVerification(teacherAuthPassword, teacherAuthCallback, teacherAuthUserIdentifier);
    }
  },

  // 处理自定义弹窗的取消
  onTeacherAuthCancel: function() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage && currentPage.data && currentPage.data.teacherAuthCallback) {
      const callback = currentPage.data.teacherAuthCallback;
      this.closeTeacherAuthModal();
      
      if (callback && typeof callback === 'function') {
        callback(false);
      }
    }
  },

  // 检查并验证教师权限的主要方法
  checkTeacherAccess: function(callback) {
    const app = getApp();
    
    // 1. 首先检查登录状态
    if (!app.isLoggedIn()) {
      wx.showModal({
        title: '需要登录',
        content: '使用教师工具箱需要先登录账号',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
          if (callback && typeof callback === 'function') {
            callback(false);
          }
        }
      });
      return;
    }
    
    // 2. 获取用户标识 - 使用更稳定的标识符
    const userInfo = app.globalData.userInfo;
    
    // 优先使用openid，然后是_id，最后是username
    let userIdentifier = '';
    if (userInfo.openid) {
      userIdentifier = userInfo.openid;
    } else if (userInfo._id) {
      userIdentifier = userInfo._id;
    } else if (userInfo.username) {
      userIdentifier = userInfo.username;
    } else {
      // 如果都没有，获取微信openid
      try {
        const openid = wx.getStorageSync('openid');
        if (openid) {
          userIdentifier = openid;
        } else {
          userIdentifier = 'unknown_user';
        }
      } catch (error) {
        console.error('获取openid失败:', error);
        userIdentifier = 'unknown_user';
      }
    }
    
    console.log('教师权限验证 - 用户标识:', userIdentifier);
    console.log('教师权限验证 - 完整用户信息:', userInfo);
    
    // 3. 检查是否已验证教师权限
    if (this.isTeacherVerified(userIdentifier)) {
      // 已验证，直接允许访问
      console.log('用户已获得教师权限，直接允许访问');
      if (callback && typeof callback === 'function') {
        callback(true);
      }
    } else {
      // 未验证，显示验证弹窗
      console.log('用户未验证教师权限，显示密码验证弹窗');
      this.showTeacherAuthDialog(callback, userIdentifier);
    }
  }
};

module.exports = TeacherAuth; 