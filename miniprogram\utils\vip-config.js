// VIP配置文件 - 用于控制会员功能的开启和关闭
// 设置为false可以临时封存会员功能，设置为true可以重新开启

const VIP_CONFIG = {
  // 是否启用VIP功能
  enabled: false,
  
  // VIP功能列表
  vipFunctions: [
    'custom-test',    // 自定义检测
    'listening',      // 听口练习
    'phonetic',       // 音标专练
    'reading',        // 阅读理解
    'exam-analysis'   // 考试分析
  ],
  
  // VIP词库列表
  vipLibraries: [
    'outline_weikeduo',           // 维克多高考词汇
    'special_yuedu',              // 阅读高频词
    'special_shucishengyi',       // 熟词生义
    'special_xingjinci',          // 形近词
    'special_buguize'             // 不规则动词
  ],
  
  // 重新启用VIP功能的方法
  enable() {
    this.enabled = true;
    console.log('VIP功能已重新启用');
  },
  
  // 封存VIP功能的方法
  disable() {
    this.enabled = false;
    console.log('VIP功能已封存');
  },
  
  // 检查是否为VIP功能
  isVipFunction(functionId) {
    return this.enabled && this.vipFunctions.includes(functionId);
  },
  
  // 检查是否为VIP词库
  isVipLibrary(libraryId) {
    return this.enabled && this.vipLibraries.includes(libraryId);
  },
  
  // 检查用户是否有VIP权限（当VIP功能关闭时，所有用户都有权限）
  checkVipAccess(userInfo) {
    if (!this.enabled) {
      return true; // VIP功能关闭时，所有用户都有完整权限
    }
    
    // VIP功能开启时，检查用户的会员状态
    return userInfo && userInfo.membership && userInfo.membership.isVip;
  }
};

module.exports = VIP_CONFIG; 