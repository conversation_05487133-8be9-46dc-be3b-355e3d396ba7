/* pages/reading/foreign-media/foreign-media.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.articles-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.article-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.article-card:active {
  transform: scale(0.98);
}

.article-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.article-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.article-source {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
}

.article-date {
  font-size: 24rpx;
  color: #999;
}

.article-badges {
  display: flex;
  gap: 12rpx;
}

.difficulty-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: white;
}

.difficulty-beginner {
  background: #10B981;
}

.difficulty-intermediate {
  background: #F59E0B;
}

.difficulty-advanced {
  background: #EF4444;
}

.category-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  background: #E5E7EB;
  color: #374151;
}

.article-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: block;
}

.article-summary {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: block;
}

.article-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.article-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.tag {
  background: #F3F4F6;
  color: #6B7280;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.read-time {
  font-size: 24rpx;
  color: #9CA3AF;
  margin-left: 20rpx;
}
