# 正确率刷分功能修改说明

## 问题描述
"我的分享"页面的任务卡片和详情页面中的正确率数据存在以下问题：
1. 任务卡片上缺少正确率显示
2. 当用户重复测试已完成的关卡进行刷分时，正确率数据没有实时更新
3. 正确率计算没有基于用户的历史最高分进行统计

## 解决方案

### 1. 前端显示修改

#### 1.1 "我的分享"页面 (miniprogram/pages/profile/share/)
- **share.js**: 
  - 新增 `calculateAverageAccuracy()` 方法，支持多关卡和单关卡任务的正确率计算
  - 新增 `calculateMultiLevelAverageAccuracy()` 方法，专门处理多关卡任务的正确率计算
  - 修改 `calculateDetailedStats()` 方法，包含正确率统计
  - 在数据处理时添加 `averageAccuracy` 字段

- **share.wxml**: 
  - 在任务卡片的统计信息中添加正确率显示

#### 1.2 分享管理页面 (miniprogram/management/share-management/)
- **share-management.js**:
  - 修改 `processParticipants()` 方法中的正确率计算逻辑，优先使用历史最高分对应的正确率
  - 修改 `processLevelScoresWithAccuracy()` 方法，支持从 levelProgress 中获取最佳正确率数据
  - 对于多关卡任务，使用每个关卡的历史最高分对应的正确率计算总体正确率
  - 对于单关卡任务，使用最高分对应的正确率

### 2. 后端数据结构修改

#### 2.1 云函数修改 (cloudfunctions/submitShareTestResult/)
- **index.js**:
  - 在 levelProgress 中新增 `accuracies` 字段，记录每个关卡的最佳正确率
  - 修改关卡进度更新逻辑，当用户获得更高分数时，同时更新对应的正确率
  - 在访问者信息中新增 `bestAccuracy` 字段，记录用户的最佳正确率

### 3. 数据计算逻辑

#### 3.1 多关卡任务正确率计算
1. 优先从 `levelProgress[userId].accuracies` 中获取每个关卡的最佳正确率
2. 如果没有 accuracies 数据，从测试结果中找出每个关卡的最高分及对应正确率
3. 计算所有关卡最佳正确率的平均值作为用户总体正确率
4. 计算所有参与者总体正确率的平均值作为任务平均正确率

#### 3.2 单关卡任务正确率计算
1. 优先使用访问者的 `bestAccuracy` 字段
2. 如果没有该字段，从测试结果中找出最高分对应的正确率
3. 计算所有参与者最佳正确率的平均值作为任务平均正确率

#### 3.3 刷分处理逻辑
- 当用户重复测试同一关卡时：
  - 如果新分数更高，更新该关卡的最高分和对应正确率
  - 如果新分数不高，保持原有的最高分和正确率
  - 实时重新计算总体正确率并更新显示

### 4. 数据存储结构

#### 4.1 levelProgress 结构
```javascript
levelProgress: {
  "用户openid": {
    currentLevel: 1,
    completedLevels: [1, 2],
    scores: {
      "1": 85,  // 第1关最高分
      "2": 90   // 第2关最高分
    },
    accuracies: {
      "1": 85,  // 第1关最高分对应的正确率
      "2": 90   // 第2关最高分对应的正确率
    }
  }
}
```

#### 4.2 访问者信息结构
```javascript
visitors: [{
  openid: "用户openid",
  nickName: "用户昵称",
  bestScore: 90,        // 最高分
  bestAccuracy: 90,     // 最高分对应的正确率
  latestScore: 85,      // 最新分数
  latestAccuracy: 85    // 最新正确率
}]
```

## 预期效果
1. 任务卡片上显示基于历史最高分的平均正确率
2. 用户刷分后，正确率数据实时更新
3. 详情页面中每个关卡显示该关卡的历史最高分对应的正确率
4. 总体正确率基于所有关卡的最佳正确率计算，真实反映用户的最佳表现
