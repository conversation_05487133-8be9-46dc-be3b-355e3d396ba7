const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('=== getStudentDetail云函数开始执行 ===');
  console.log('接收到的参数:', event);

  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const { studentId } = event;

  try {
    if (!studentId) {
      console.log('学生ID为空');
      return {
        success: false,
        message: '学生ID不能为空'
      };
    }

    console.log('准备获取学生信息，ID:', studentId);

    // 获取学生基本信息
    const studentResult = await db.collection('students').doc(studentId).get();

    if (!studentResult.data) {
      console.log('学生不存在');
      return {
        success: false,
        message: '学生不存在'
      };
    }

    const student = studentResult.data;
    console.log('获取到学生信息:', student);

    // 验证权限：只能查看自己的学生
    if (student.teacherOpenid !== openid) {
      console.log('权限验证失败，学生归属:', student.teacherOpenid, '当前用户:', openid);
      return {
        success: false,
        message: '无权限查看该学生信息'
      };
    }

    console.log('权限验证通过，准备获取成绩');

    // 获取学生成绩，先检查集合是否存在
    let scoresResult;
    try {
      scoresResult = await db.collection('student_scores').where({
        studentId: studentId
      }).orderBy('examDate', 'desc').get();
      console.log('获取到成绩数据:', scoresResult.data);
    } catch (scoreError) {
      console.log('获取成绩失败，可能是集合不存在:', scoreError);
      if (scoreError.errCode === -502005) {
        console.log('student_scores集合不存在，返回空成绩');
        scoresResult = { data: [] };
      } else {
        throw scoreError;
      }
    }

    const result = {
      success: true,
      data: {
        ...student,
        scores: scoresResult.data || []
      }
    };

    console.log('返回结果:', result);
    return result;
  } catch (error) {
    console.error('获取学生详情失败:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });
    return {
      success: false,
      message: '获取学生详情失败: ' + error.message,
      error: error.message,
      errorCode: error.code
    };
  }
};
