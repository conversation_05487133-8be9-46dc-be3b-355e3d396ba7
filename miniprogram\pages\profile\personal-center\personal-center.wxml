<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px; height: {{navHeight}}px;">
    <view class="nav-bar">
      <view class="nav-left" bindtap="onGoBack">
        <text class="nav-icon">‹</text>
      </view>
      <view class="nav-title">编辑个人信息</view>
      <view class="nav-right">
        <!-- 移除保存按钮 -->
      </view>
    </view>
  </view>

  <!-- 用户信息卡片 -->
  <view class="user-profile-card" style="margin-top: {{contentTop}}px;" wx:if="{{userInfo}}">
    <!-- 头像编辑 -->
    <view class="avatar-section">
      <view class="avatar-container">
        <!-- 头像选择 -->
        <view class="avatar-edit-wrapper">
          <!-- 新版头像选择方式 -->
          <button 
            wx:if="{{canChooseAvatar}}"
            class="avatar-button" 
            open-type="chooseAvatar" 
            bind:chooseavatar="onChooseAvatar"
          >
            <image 
              class="user-avatar" 
              src="{{userInfo.wechatInfo.avatarUrl || '/assets/icons/profile.png'}}"
              mode="aspectFill"
            />
            <view class="avatar-edit-overlay">
              <text class="edit-icon">📷</text>
              <text class="edit-text">更换头像</text>
            </view>
          </button>
          <!-- 备用头像选择方式 -->
          <view wx:else class="avatar-button" bindtap="onFallbackChooseAvatar">
            <image 
              class="user-avatar" 
              src="{{userInfo.wechatInfo.avatarUrl || '/assets/icons/profile.png'}}"
              mode="aspectFill"
            />
            <view class="avatar-edit-overlay">
              <text class="edit-icon">📷</text>
              <text class="edit-text">更换头像</text>
            </view>
          </view>
        </view>
      </view>
      <text class="avatar-tip">点击头像更换</text>
    </view>

    <!-- 个人信息表单 -->
    <view class="info-form">
      <!-- 昵称 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-icon">👤</text>
          <text class="label-text">昵称</text>
        </view>
        <view class="form-value-container">
          <input 
            class="form-input"
            type="{{canUseNickname ? 'nickname' : 'text'}}"
            value="{{editForm.nickName}}"
            placeholder="请输入昵称"
            placeholder-class="input-placeholder"
            data-field="nickName"
            bindinput="onFormInput"
            maxlength="20"
            cursor-spacing="20"
            focus="true"
          />
        </view>
      </view>



      <!-- 用户ID -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-icon">🆔</text>
          <text class="label-text">用户ID</text>
        </view>
        <text class="form-value">{{shortUserId || '未知'}}</text>
      </view>

      <!-- 会员状态 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-icon">👑</text>
          <text class="label-text">会员状态</text>
        </view>
        <text class="form-value {{userInfo.membership && userInfo.membership.isVip ? 'vip' : ''}}">
          {{userInfo.membership && userInfo.membership.isVip ? 'VIP会员' : '普通用户'}}
        </text>
      </view>

      <!-- 注册时间 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-icon">📅</text>
          <text class="label-text">注册时间</text>
        </view>
        <text class="form-value">{{userInfo.createTime || '未知'}}</text>
      </view>
    </view>

    <!-- 操作提示 -->
    <view class="tips-section">
      <view class="tip-item">
        <text class="tip-icon">💡</text>
        <text class="tip-text">修改昵称会自动保存，无需手动确认</text>
      </view>

    </view>
  </view>


</view> 