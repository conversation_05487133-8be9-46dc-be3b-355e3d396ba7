<view class="container">
  <!-- 音频播放区域 -->
  <view class="audio-section">
    <view class="audio-text">{{mistake.text}}</view>
    <view class="audio-controls">
      <button class="play-btn" bindtap="playAudio">
        <image class="play-icon" src="/images/{{isPlaying ? 'pause.png' : 'play.png'}}" mode="aspectFit"></image>
      </button>
      <slider class="audio-slider" 
              value="{{audioProgress}}" 
              bindchange="onSliderChange"
              block-size="12"
              activeColor="#4A90E2"/>
    </view>
  </view>

  <!-- 翻译区域 -->
  <view class="translation-section">
    <view class="section-title">翻译</view>
    <view class="translation-text">{{mistake.translation}}</view>
  </view>

  <!-- 来源信息 -->
  <view class="source-section">
    <view class="section-title">来源信息</view>
    <view class="source-info">
      <view class="info-item">
        <text class="info-label">来源任务：</text>
        <text class="info-value">{{mistake.sourceName}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">添加时间：</text>
        <text class="info-value">{{mistake.createTime}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="action-btn" bindtap="onMasteredTap">标记为已掌握</button>
  </view>
</view> 