.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 音频区域样式 */
.audio-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.audio-text {
  font-size: 36rpx;
  color: #333;
  margin-bottom: 30rpx;
  line-height: 1.6;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.play-btn {
  width: 80rpx;
  height: 80rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
}

.play-btn::after {
  display: none;
}

.play-icon {
  width: 40rpx;
  height: 40rpx;
}

.audio-slider {
  flex: 1;
  margin: 0;
}

/* 翻译区域样式 */
.translation-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.translation-text {
  font-size: 32rpx;
  color: #666;
  line-height: 1.6;
}

/* 来源信息样式 */
.source-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.source-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 操作按钮样式 */
.action-buttons {
  margin-top: 60rpx;
}

.action-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #4A90E2;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
}

.action-btn::after {
  display: none;
} 