const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const learningRecordsCollection = db.collection('learning_records')
const usersCollection = db.collection('users')

exports.main = async (event, context) => {
  const { userId, wordId, status, learnTime, notes } = event

  try {
    const now = new Date()

    // 创建学习记录
    const record = {
      userId,
      wordId,
      status,
      learnTime: learnTime || now,
      reviewTimes: 0,
      lastReviewTime: null,
      nextReviewTime: null,
      masteryLevel: 1,
      notes: notes || ''
    }

    // 计算下次复习时间
    if (status === 'learned') {
      record.nextReviewTime = new Date(now.getTime() + 24 * 60 * 60 * 1000) // 24小时后
    }

    const result = await learningRecordsCollection.add({
      data: record
    })

    // 更新用户统计数据
    const user = await usersCollection.doc(userId).get()
    const stats = user.data.stats

    // 更新今日统计
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayStats = stats.dailyStats.find(s => new Date(s.date).getTime() === today.getTime())
    
    if (todayStats) {
      todayStats.words += 1
      if (status === 'learned') {
        todayStats.mastered += 1
      }
    } else {
      stats.dailyStats.push({
        date: today.toISOString().split('T')[0],
        time: 0,
        words: 1,
        mastered: status === 'learned' ? 1 : 0,
        correctRate: 0
      })
    }

    // 更新总体统计
    stats.totalWords += 1
    if (status === 'learned') {
      stats.masteredWords += 1
    }

    // 更新连续学习天数
    const lastStudyDate = stats.lastStudyDate ? new Date(stats.lastStudyDate) : null
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (!lastStudyDate || lastStudyDate.getTime() === yesterday.getTime()) {
      stats.streakDays += 1
    } else if (lastStudyDate.getTime() !== today.getTime()) {
      stats.streakDays = 1
    }

    stats.lastStudyDate = today

    await usersCollection.doc(userId).update({
      data: {
        stats: stats
      }
    })

    return {
      code: 200,
      message: '保存成功',
      data: {
        recordId: result._id
      }
    }
  } catch (error) {
    console.error(error)
    return {
      code: 500,
      message: '服务器错误'
    }
  }
} 