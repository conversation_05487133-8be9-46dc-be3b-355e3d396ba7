.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.mistake-list {
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.mistake-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}

.mistake-item:last-child {
  border-bottom: none;
}

.mistake-item:active {
  transform: scale(0.98);
  background-color: #f8f9fa;
}

.mistake-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.word-mistake {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.listening-mistake {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.icon-text {
  font-size: 40rpx;
  line-height: 1;
}

.mistake-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.mistake-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.mistake-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.mistake-count {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

.mistake-arrow {
  font-size: 32rpx;
  color: #ccc;
  font-weight: bold;
  margin-left: 16rpx;
} 