.container {
  min-height: 100vh;
  padding: 40rpx;
  background-color: #f8f9fa;
  position: relative;
}

/* 进度条 */
.progress-bar {
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-inner {
  height: 100%;
  background-color: #4a90e2;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

/* 单词卡片 */
.word-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.word {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 24rpx;
}

.phonetic {
  font-size: 32rpx;
  color: #666;
  text-align: center;
  margin-bottom: 32rpx;
}

.meaning {
  margin-bottom: 32rpx;
}

.meaning-item {
  margin-bottom: 16rpx;
}

.part-of-speech {
  font-size: 28rpx;
  color: #4a90e2;
  margin-right: 16rpx;
}

.definition {
  font-size: 28rpx;
  color: #333;
}

.example {
  border-top: 2rpx solid #f0f0f0;
  padding-top: 32rpx;
}

.example-item {
  margin-bottom: 16rpx;
}

.example-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 操作按钮 */
.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
}

.action-btn .icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.action-btn text {
  font-size: 24rpx;
  color: #666;
}

/* 底部按钮 */
.bottom-bar {
  position: fixed;
  left: 40rpx;
  right: 40rpx;
  bottom: 40rpx;
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}

.btn {
  flex: 1;
  height: 96rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
}

.btn-wrong {
  background-color: #ff4d4f;
}

.btn-remember {
  background-color: #52c41a;
}

/* 完成弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 32rpx;
}

.modal-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-buttons {
  display: flex;
  gap: 24rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  background-color: #4a90e2;
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
} 