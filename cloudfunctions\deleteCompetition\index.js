const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 管理员账号识别配置
const ADMIN_OPENID_PART = '5938DE76' // 微信登录账号ID部分
const ADMIN_PHONE = '15547663399' // 用户名密码登录账号

// 检查是否为管理员
function isAdmin(userOpenId, userInfo = null) {
  // 检查openid（微信登录）
  if (userOpenId && userOpenId.includes(ADMIN_OPENID_PART)) {
    return true
  }

  // 如果有额外的用户信息，检查手机号等字段
  if (userInfo) {
    if (userInfo.phone === ADMIN_PHONE || userInfo.username === ADMIN_PHONE) {
      return true
    }

    // 检查用户ID是否包含管理员标识（适用于微信登录用户）
    if (userInfo._id && userInfo._id.includes(ADMIN_OPENID_PART)) {
      return true
    }
  }

  return false
}

// 获取用户信息用于管理员验证
async function getUserInfoForAdmin(openId) {
  try {
    // 查询用户信息
    const userResult = await db.collection('users').where({ openid: openId }).get()
    if (userResult.data && userResult.data.length > 0) {
      return userResult.data[0]
    }
    return null
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

exports.main = async (event, context) => {
  try {
    const { competitionId } = event
    const wxContext = cloud.getWXContext()
    
    if (!competitionId) {
      return {
        success: false,
        message: '缺少竞赛ID'
      }
    }

    // 获取竞赛信息
    let competitionResult
    try {
      competitionResult = await db.collection('competitions').doc(competitionId).get()
    } catch (error) {
      if (error.errCode === -502005) {
        return {
          success: false,
          message: '竞赛不存在'
        }
      }
      throw error
    }
    
    if (!competitionResult.data) {
      return {
        success: false,
        message: '竞赛不存在'
      }
    }

    const competition = competitionResult.data
    const currentUserOpenId = wxContext.OPENID

    // 检查是否为系统创建的测试竞赛
    const isSystemTest = competition.name && competition.name.includes('系统测试竞赛')

    // 获取用户信息用于管理员验证
    const userInfo = await getUserInfoForAdmin(currentUserOpenId)

    // 检查权限：创建者或管理员可以删除，系统测试数据也可直接删除
    const isCreator = competition.creatorOpenId === currentUserOpenId
    const isAdminUser = isAdmin(currentUserOpenId, userInfo)

    console.log('权限检查:', {
      currentUserOpenId,
      creatorOpenId: competition.creatorOpenId,
      isCreator,
      isAdminUser,
      isSystemTest,
      userInfo: userInfo ? {
        username: userInfo.username,
        phone: userInfo.phone,
        _id: userInfo._id
      } : null
    })

    if (!isCreator && !isAdminUser && !isSystemTest) {
      return {
        success: false,
        message: '无权限删除此竞赛'
      }
    }

    // 执行删除（软删除，标记为已删除）
    await db.collection('competitions').doc(competitionId).update({
      data: {
        status: 'deleted',
        deletedAt: new Date(),
        deletedBy: currentUserOpenId || 'system'
      }
    })

    return {
      success: true,
      message: '竞赛删除成功'
    }
    
  } catch (error) {
    console.error('删除竞赛失败:', error)
    return {
      success: false,
      message: '删除竞赛失败: ' + error.message
    }
  }
} 