.container {
  padding: 20rpx;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  padding: 0 20rpx;
}

.search-btn {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

/* 学习模式按钮 */
.mode-buttons {
  display: flex;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.mode-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mode-btn.primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(0, 123, 255, 0.4);
}

.mode-btn.secondary {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(40, 167, 69, 0.4);
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.selected-count {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  background: #e9ecef;
  color: #333;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

/* VIP提示 */
.vip-notice {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(255, 215, 0, 0.3);
}

/* 消消乐配置样式 */
.puzzle-config {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.config-section {
  padding: 30rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.config-section:last-child {
  border-bottom: none;
}

.config-title {
  margin-bottom: 24rpx;
}

.config-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.config-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.quantity-options {
  display: flex;
  flex-wrap: nowrap;
  gap: 12rpx;
  justify-content: space-between;
}

.quantity-option {
  flex: 1;
  min-width: 0;
  padding: 16rpx 12rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.quantity-option.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.option-value {
  font-size: 30rpx;
  font-weight: 600;
  display: block;
}

.option-unit {
  font-size: 24rpx;
  opacity: 0.8;
  margin-left: 4rpx;
}

/* 消消乐提示 */
.puzzle-notice {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 0 0 20rpx 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(79, 172, 254, 0.3);
}

.notice-content {
  display: flex;
  align-items: center;
  padding: 24rpx;
}

.notice-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.notice-text {
  font-size: 26rpx;
  color: white;
  font-weight: bold;
}

/* 词汇列表 */
.words-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.word-item {
  display: flex;
  align-items: flex-start;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  position: relative;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.word-item.selected {
  border: 2rpx solid #007bff;
  background: #f8f9fa;
}

.word-item.disabled {
  opacity: 0.6;
  background: #f0f0f0;
}

.word-item.disabled .word-content {
  color: #999;
}

/* 选择状态 */
.select-status {
  margin-right: 20rpx;
  margin-top: 4rpx;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #007bff;
  border-color: #007bff;
}

.checkmark {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

/* 单词内容 */
.word-content {
  flex: 1;
}

.word-header {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.word-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.phonetic {
  font-size: 24rpx;
  color: #666;
  font-style: italic;
}

.meaning-text {
  margin-bottom: 12rpx;
}

.definition {
  font-size: 26rpx;
  color: #555;
  line-height: 1.4;
}

.example-text {
  margin-bottom: 12rpx;
  padding: 12rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007bff;
}

.example {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  font-style: italic;
}

.word-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.tag {
  font-size: 20rpx;
  color: #666;
  background: #f0f0f0;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

/* VIP标识 */
.vip-mark {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
  color: #333;
  font-size: 18rpx;
  font-weight: bold;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.4);
}

/* 加载和提示 */
.loading-tip, .no-more-tip {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 24rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.next-btn {
  width: 100%;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
}

/* 翻页控件样式 */
.pagination {
  background: white;
  border-radius: 20rpx;
  margin: 30rpx 0;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.page-info {
  text-align: center;
  margin-bottom: 24rpx;
}

.page-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.total-text {
  font-size: 24rpx;
  color: #666;
}

.page-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.page-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.page-btn::after {
  border: none;
}

.page-btn.prev, .page-btn.next {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(0, 123, 255, 0.3);
}

.page-btn.goto {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(40, 167, 69, 0.3);
}

.page-btn.disabled {
  background: #e9ecef !important;
  color: #adb5bd !important;
  box-shadow: none !important;
  transform: none !important;
}

.page-btn:active:not(.disabled) {
  transform: scale(0.98);
}

.btn-icon {
  font-size: 32rpx;
  font-weight: bold;
  margin: 0 8rpx;
}

.btn-text {
  font-size: 26rpx;
}

/* 快速跳转页码 */
.quick-pages {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 12rpx;
}

.quick-page {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  color: #666;
  transition: all 0.3s ease;
  cursor: pointer;
}

.quick-page:active {
  transform: scale(0.95);
}

.quick-page.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .page-buttons {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .page-btn {
    width: 100%;
  }
  
  .quick-pages {
    gap: 8rpx;
  }
  
  .quick-page {
    width: 50rpx;
    height: 50rpx;
    font-size: 22rpx;
  }
} 