<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">🏆 单词竞赛</text>
      <text class="page-subtitle">挑战排行榜，与他人竞技</text>
    </view>
  </view>

  <!-- 模式切换 -->
  <view class="mode-tabs">
    <view class="tab-list">
      <view 
        class="tab-item {{currentMode === item.id ? 'active' : ''}}"
        wx:for="{{modes}}"
        wx:key="id"
        bindtap="onModeChange"
        data-mode="{{item.id}}"
      >
              <text class="tab-icon">{{item.icon}}</text>
      <text class="tab-text">{{item.name}}</text>
    </view>
  </view>
</view>

  <!-- 操作栏 -->
  <view class="action-bar">
    <button class="create-btn" bindtap="onCreateCompetition">
      <text class="btn-icon">➕</text>
      <text class="btn-text">创建竞赛</text>
    </button>
    

    
    <view class="sort-dropdown">
      <view class="sort-trigger" bindtap="toggleSortDropdown">
        <text class="sort-current">{{currentSortText}}</text>
        <text class="sort-arrow {{showSortDropdown ? 'active' : ''}}">▼</text>
      </view>
      <view class="sort-options {{showSortDropdown ? 'show' : ''}}">
        <view class="sort-option {{sortBy === 'latest' ? 'active' : ''}}" 
              bindtap="onSortSelect" 
              data-sort="latest">
          <text class="sort-text">按创建时间</text>
        </view>
        <view class="sort-option {{sortBy === 'participants' ? 'active' : ''}}" 
              bindtap="onSortSelect" 
              data-sort="participants">
          <text class="sort-text">按参与人数</text>
        </view>
        <view class="sort-option {{sortBy === 'wordCount' ? 'active' : ''}}" 
              bindtap="onSortSelect" 
              data-sort="wordCount">
          <text class="sort-text">按词汇数量</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 竞赛列表 -->
  <view class="competition-list">
    <!-- 加载中 -->
    <view class="loading" wx:if="{{loading}}">
      <view class="loading-icon"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 竞赛项目 -->
    <view class="competition-item" 
          wx:for="{{competitions[currentMode]}}" 
          wx:key="id">
      
      <view class="competition-header">
        <view class="competition-info">
          <view class="competition-title-row">
            <text class="competition-name">{{item.name}}</text>
            <view class="competition-type-badge" wx:if="{{item.type === 'master'}}">
              <text class="badge-text">多关卡</text>
            </view>
            <view class="creating-badge" wx:if="{{item.levelCreationStatus === 'creating'}}">
              <text class="badge-text">⏳ 关卡创建中{{item.levelCreationProgress}}%</text>
            </view>
            <view class="pin-badge" wx:if="{{item.pinned}}">
              <text class="badge-text">🔝 置顶</text>
            </view>
          </view>
          <view class="competition-meta">
            <text class="competition-time">{{item.createTime}}</text>
            <text class="competition-creator">by {{item.creatorName || '匿名'}}</text>
          </view>
        </view>
        <view class="competition-stats">
          <text class="stat-item" wx:if="{{item.type === 'master'}}">{{item.totalLevels}}关</text>
          <text class="stat-item">{{item.wordCount}}词</text>
          <text class="stat-item {{item.participants > 0 ? 'highlight-red' : ''}}">{{item.participants}}人参与</text>
          <text class="stat-item highlight-red" wx:if="{{item.topScore > 0}}">最高{{item.topScore}}分</text>
        </view>
      </view>

      <!-- 竞赛详情 -->
      <view class="competition-details">
        <view class="detail-row">
          <text class="detail-label">模式:</text>
          <text class="detail-value">{{currentModeName}}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">词库:</text>
          <text class="detail-value">{{item.libraryName || '自定义词汇'}}</text>
        </view>
        <view class="detail-row" wx:if="{{item.type === 'master'}}">
          <text class="detail-label">分组:</text>
          <text class="detail-value">每关最多{{item.maxWordsPerLevel}}词</text>
        </view>
      </view>

      <view class="competition-actions {{item.canDelete ? 'has-delete' : 'no-delete'}}">
        <button class="action-btn join-btn" 
                bindtap="onJoinCompetition"
                data-id="{{item.id}}"
                data-type="{{item.type}}">
          <text class="action-text">{{item.type === 'master' ? '选择关卡开始' : '参与比赛'}}</text>
        </button>
        
        <view class="action-btn ranking-btn" 
              bindtap="onViewRanking" 
              data-id="{{item.id}}"
              data-type="{{item.type}}">
          <text class="action-text">排行榜</text>
        </view>
        
        <view class="action-btn share-btn" 
              bindtap="onShareCompetition" 
              data-id="{{item.id}}"
              data-type="{{item.type}}">
          <text class="action-text">分享</text>
        </view>
        
        <!-- 置顶按钮（仅管理员可见） -->
        <view class="action-btn pin-btn" 
              wx:if="{{isAdmin}}"
              bindtap="onTogglePin" 
              data-id="{{item.id}}"
              data-pinned="{{item.pinned}}">
          <text class="action-text">{{item.pinned ? '取消置顶' : '置顶'}}</text>
        </view>
        
        <!-- 删除按钮（仅创建者和管理员可见） -->
        <view class="action-btn delete-btn" 
              wx:if="{{item.canDelete}}"
              bindtap="onDeleteCompetition" 
              data-id="{{item.id}}"
              data-type="{{item.type}}">
          <text class="action-text">删除</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && competitions[currentMode].length === 0}}">
      <text class="empty-icon">🏁</text>
      <text class="empty-text">暂无{{currentModeName}}竞赛</text>
      <text class="empty-tip">点击上方"创建竞赛"按钮开始第一个竞赛</text>
    </view>
  </view>

  <!-- 说明区域 -->
  <view class="info-section">
    <view class="info-content">
      <view class="info-title">📝 竞赛说明</view>
      <view class="info-list">
        <view class="info-item">• 单一竞赛：自动保存7天后删除</view>
        <view class="info-item">• 多关卡竞赛：永久保存，除非管理员删除</view>
        <view class="info-item">• 可查看实时排行榜和成绩</view>
        <view class="info-item">• 支持分享给好友一起参与</view>
        <view class="info-item">• 可多次参与，不断刷新最佳成绩</view>
      </view>
    </view>
  </view>

  <!-- 分享弹窗 -->
  <view class="share-modal" wx:if="{{showShareModal}}" bindtap="closeShareModal">
    <view class="share-content" catchtap="">
      <view class="share-header">
        <text class="share-title">分享竞赛</text>
        <text class="close-btn" bindtap="closeShareModal">✕</text>
      </view>

      <view class="share-info">
        <text class="share-desc">选择分享方式，邀请朋友参与竞赛</text>
        <text class="share-note">让朋友看到你创建的精彩竞赛</text>
      </view>

      <view class="share-buttons">
        <button class="share-btn copy-btn" bindtap="onCopyCompetitionInfo">
          <text class="share-btn-icon">📋</text>
          <text class="share-btn-text">复制竞赛信息</text>
        </button>

        <button
          class="share-btn wechat-share-btn"
          open-type="share"
          bindtap="onShareToWeChatFriends"
        >
          <text class="share-btn-icon">📤</text>
          <text class="share-btn-text">分享给微信好友</text>
        </button>



        <button class="share-btn timeline-btn" bindtap="onShareToTimeline">
          <text class="share-btn-icon">🌟</text>
          <text class="share-btn-text">分享到朋友圈</text>
        </button>
      </view>
    </view>
  </view>
</view>