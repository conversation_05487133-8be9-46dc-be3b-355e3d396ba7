<!--测试页面-->
<view class="container" wx:if="{{!isRedirecting}}">
  <!-- 练习模式内容 -->
  <view class="practice-content" wx:if="{{!isTestMode && testStarted && !testCompleted}}">
    <!-- 进度条 -->
    <view class="practice-header">
      <view class="progress-info">
        <text class="progress-text">{{currentIndex + 1}}/{{totalQuestions}}</text>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
      </view>
    </view>

    <!-- 单词卡片 -->
    <view class="word-card-container">
      <view class="word-card-large">
        <!-- 英文单词（根据测试模式显示） -->
        <view class="word-section" wx:if="{{testMode === 'en_to_cn'}}">
          <view class="word-header">
            <text class="word-text-large">{{currentWord.words || currentWord.word}}</text>
            <button class="play-btn {{isPlaying ? 'playing' : ''}}" bindtap="onPlayWord">
              <text class="play-icon">{{isPlaying ? '🔊' : '🔉'}}</text>
            </button>
          </view>
          <text class="phonetic-large" wx:if="{{currentWord.phonetic}}">{{currentWord.phonetic}}</text>
        </view>

        <!-- 中文释义（汉译英模式显示） -->
        <view class="word-section" wx:if="{{testMode === 'cn_to_en'}}">
          <text class="word-text-large">{{currentWord.meaning}}</text>
        </view>

        <!-- 点击显示区域 -->
        <view class="reveal-sections">
          <!-- 中文释义（英译汉模式，点击显示） -->
          <view class="meaning-section" wx:if="{{testMode === 'en_to_cn'}}">
            <view class="reveal-btn {{showMeaning ? 'revealed' : ''}}" bindtap="toggleMeaning">
              <text class="reveal-title">{{showMeaning ? '中文释义' : '点击查看中文释义'}}</text>
              <text class="reveal-icon">{{showMeaning ? '👁️' : '👀'}}</text>
            </view>
            <text class="meaning-text-large" wx:if="{{showMeaning}}">{{currentWord.meaning}}</text>
          </view>

          <!-- 英文单词（汉译英模式，点击显示） -->
          <view class="meaning-section" wx:if="{{testMode === 'cn_to_en'}}">
            <view class="reveal-btn {{showMeaning ? 'revealed' : ''}}" bindtap="toggleMeaning">
              <text class="reveal-title">{{showMeaning ? '英文单词' : '点击查看英文单词'}}</text>
              <text class="reveal-icon">{{showMeaning ? '👁️' : '👀'}}</text>
            </view>
            <view class="english-reveal" wx:if="{{showMeaning}}">
              <view class="english-content">
                <text class="meaning-text-large">{{currentWord.words || currentWord.word}}</text>
                <button class="play-btn-small {{isPlaying ? 'playing' : ''}}" bindtap="onPlayWord">
                  <text class="play-icon-small">{{isPlaying ? '🔊' : '🔉'}}</text>
                </button>
              </view>
              <text class="phonetic-large" wx:if="{{currentWord.phonetic}}">{{currentWord.phonetic}}</text>
            </view>
          </view>

          <!-- 例句（点击显示） -->
          <view class="example-section" wx:if="{{currentWord.example}}">
            <view class="reveal-btn {{showExample ? 'revealed' : ''}}" bindtap="toggleExample">
              <text class="reveal-title">{{showExample ? '例句' : '点击查看例句'}}</text>
              <text class="reveal-icon">{{showExample ? '👁️' : '👀'}}</text>
            </view>
            <text class="example-text-large" wx:if="{{showExample}}">{{currentWord.example}}</text>
          </view>


        </view>
      </view>
    </view>

    <!-- 导航按钮 -->
    <view class="practice-nav">
      <button class="nav-btn {{currentIndex === 0 ? 'disabled' : ''}}" 
              bindtap="onPracticePrev" 
              disabled="{{currentIndex === 0}}">上一个</button>
      <button class="nav-btn mastered" bindtap="onMastered">
        已掌握
      </button>
      <button class="nav-btn primary" bindtap="onPracticeNext">
        {{currentIndex === totalQuestions - 1 ? '完成练习' : '下一个'}}
      </button>
    </view>
    

  </view>

  <!-- 测试进行中 -->
  <view class="test-content" wx:if="{{isTestMode && testStarted && !testCompleted}}">
    <!-- 顶部信息栏 -->
    <view class="test-header">
      <view class="progress-info">
        <text class="progress-text">{{currentIndex + 1}}/{{totalQuestions}}</text>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
      </view>
      <view class="timer">
        <text class="time-text">{{timeDisplay}}</text>
      </view>
    </view>

    <!-- 题目区域 -->
    <view class="question-area">
      <view class="mode-indicator">
        <text class="mode-text">{{testMode === 'en_to_cn' ? '英译汉' : '汉译英'}}</text>
      </view>
      
      <view class="question-card">
        <view class="question-content">
          <view class="question-header" wx:if="{{testMode === 'en_to_cn'}}">
            <text class="question-text">{{currentWord.words || currentWord.word}}</text>
            <button class="play-btn-test {{isPlaying ? 'playing' : ''}}" bindtap="onPlayWord">
              <text class="play-icon-test">{{isPlaying ? '🔊' : '🔉'}}</text>
            </button>
          </view>
          <text class="question-text" wx:if="{{testMode === 'cn_to_en'}}">{{currentWord.meaning}}</text>
        </view>
        
        <!-- 音标显示（英译汉模式） -->
        <view class="phonetic" wx:if="{{testMode === 'en_to_cn' && currentWord.phonetic}}">
          <text class="phonetic-text">[{{currentWord.phonetic}}]</text>
        </view>
      </view>
    </view>

    <!-- 选项区域 - 英译汉选择题和短语汉译英选择题 -->
    <view class="options-area" wx:if="{{testMode === 'en_to_cn' || (testMode === 'cn_to_en' && isPhrase)}}">
      <view class="options-grid">
        <view
          class="option-item {{selectedOption === index ? (index === correctOption ? 'correct' : 'wrong') : ''}}"
          wx:for="{{options}}"
          wx:key="index"
          data-index="{{index}}"
          bindtap="onOptionTap"
        >
          <view class="option-content">
            <text class="option-label">{{index === 0 ? 'A' : index === 1 ? 'B' : index === 2 ? 'C' : 'D'}}.</text>
            <text class="option-text">{{item}}</text>
          </view>
          <view class="option-indicator" wx:if="{{isAnswered && index === correctOption}}">✓</view>
          <view class="option-indicator wrong" wx:if="{{isAnswered && selectedOption === index && index !== correctOption}}">✗</view>
        </view>
      </view>
    </view>

    <!-- 填空区域 - 单词汉译英填空题（短语汉译英不使用填空） -->
    <view class="input-area" wx:if="{{testMode === 'cn_to_en' && !isPhrase}}">
      <view class="input-card">
        <view class="input-label">请输入英文单词</view>
        <view class="input-container">
          <input
            class="word-input {{userInput ? 'has-content' : ''}} {{showAnswer ? (isCorrect ? 'correct' : 'wrong') : ''}}"
            placeholder="请输入英文单词..."
            value="{{userInput}}"
            bindinput="onInputChange"
            bindconfirm="onInputConfirm"
            focus="{{inputFocused}}"
            disabled="{{isAnswered}}"
          />
          <button
            class="submit-btn {{userInput ? 'active' : 'disabled'}}"
            bindtap="onSubmitAnswer"
            disabled="{{!userInput || isAnswered}}"
          >
            提交
          </button>
        </view>

        <!-- 答案显示 -->
        <view class="answer-display" wx:if="{{showAnswer}}">
          <view class="answer-result {{isCorrect ? 'correct' : 'wrong'}}">
            <text class="result-icon">{{isCorrect ? '✓' : '✗'}}</text>
            <text class="result-text">{{isCorrect ? '回答正确' : '回答错误'}}</text>
          </view>
          <view class="correct-answer" wx:if="{{!isCorrect}}">
            <text class="answer-label">正确答案：</text>
            <text class="answer-text">{{currentWord.words || currentWord.word}}</text>
            <button class="play-btn-small {{isPlaying ? 'playing' : ''}}" bindtap="onPlayWord">
              <text class="play-icon-small">{{isPlaying ? '🔊' : '🔉'}}</text>
            </button>
          </view>
          <view class="phonetic-display" wx:if="{{currentWord.phonetic}}">
            <text class="phonetic-text">[{{currentWord.phonetic}}]</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-bar">
      <view class="stat-item">
        <text class="stat-label">正确</text>
        <text class="stat-value correct">{{correctCount}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">错误</text>
        <text class="stat-value wrong">{{wrongCount}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">得分</text>
        <text class="stat-value">{{score}}</text>
      </view>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="result-content" wx:if="{{showResult}}">
    <view class="result-card">
      <view class="result-header">
        <view class="result-icon">🎯</view>
        <text class="result-title">{{testMode === 'en_to_cn' ? '英译汉' : testMode === 'cn_to_en' ? '汉译英' : ''}}测试结果</text>
        <view class="result-close" bindtap="closeTestResult">✕</view>
      </view>

      <!-- 统计概览 -->
      <view class="stats-overview">
        <view class="stats-card">
          <view class="stat-item">
            <view class="stat-value">{{totalQuestions}}</view>
            <view class="stat-label">总词数</view>
          </view>
          <view class="stat-item">
            <view class="stat-value correct">{{correctCount}}</view>
            <view class="stat-label">正确</view>
          </view>
          <view class="stat-item">
            <view class="stat-value wrong">{{wrongCount}}</view>
            <view class="stat-label">错误</view>
          </view>
          <view class="stat-item">
            <view class="stat-value accuracy">{{accuracyRate}}%</view>
            <view class="stat-label">准确率</view>
          </view>
        </view>
        <view class="test-time">
          <text class="time-label">用时：</text>
          <text class="time-value">{{formattedTestTime}}</text>
        </view>
      </view>

      <!-- 错词结果 -->
      <view class="answer-results-section">
        <view class="section-header">
          <view class="section-title">📝 错词结果</view>
          <view class="select-actions" wx:if="{{wrongCount > 0}}">
            <text class="select-btn primary" bindtap="selectAllWrongWords">全选</text>
          </view>
        </view>

        <!-- 有错词时显示列表 -->
        <view class="results-list" wx:if="{{wrongCount > 0}}">
          <view
            class="result-item wrong {{selectedWrongWords[item.wrongIndex] ? 'selected' : ''}} {{item.isPhrase ? 'phrase-mode' : ''}}"
            wx:for="{{detailedResults}}"
            wx:key="index"
            wx:if="{{!item.isCorrect}}"
            bindtap="toggleWrongWordSelection"
            data-index="{{item.wrongIndex}}"
          >
            <view class="item-number">{{item.wrongIndex + 1}}</view>
            <view class="word-content-flex">
              <text class="word-text-phrase">{{item.word.words || item.word.word}}</text>
              <text class="answer-label">您答：</text>
              <text class="user-answer-phrase">{{item.userAnswer || '无'}}</text>
              <text class="separator">｜</text>
              <text class="answer-label">正答：</text>
              <text class="correct-answer-phrase">{{item.correctAnswer}}</text>
            </view>
            <view class="result-status">
              <view class="select-checkbox">
                <text class="checkbox">{{selectedWrongWords[item.wrongIndex] ? '☑️' : '☐'}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 无错词时显示提示 -->
        <view class="no-wrong-words" wx:else>
          <view class="no-wrong-icon">🎉</view>
          <view class="no-wrong-text">暂无错词，太棒了！</view>
          <view class="no-wrong-desc">全部答对，继续保持！</view>
        </view>
      </view>

      <!-- 全部正确的情况 -->
      <view class="perfect-score" wx:if="{{wrongCount === 0}}">
        <view class="perfect-icon">🎉</view>
        <view class="perfect-title">完美表现！</view>
        <view class="perfect-desc">恭喜您全部答对，{{testMode === 'en_to_cn' ? '英译汉' : '汉译英'}}水平很棒！</view>
      </view>

      <!-- 错词重测选项 -->
      <view class="retry-options-section" wx:if="{{wrongCount > 0}}">
        <view class="retry-title">
          <text class="retry-title-text">💡 选中错词后进行进一步测试</text>
        </view>

        <!-- 测试模式选项 - 一行显示 -->
        <view class="retry-modes">
          <button class="mode-btn retry-same" bindtap="retrySelectedWords">
            <view class="mode-icon">{{testMode === 'en_to_cn' ? '🇬🇧' : '🇨🇳'}}</view>
            <view class="mode-text">{{testMode === 'en_to_cn' ? '英译汉' : '汉译英'}}</view>
          </button>
          <button class="mode-btn retry-opposite" bindtap="retryWithOppositeMode">
            <view class="mode-icon">{{testMode === 'en_to_cn' ? '🇨🇳' : '🇬🇧'}}</view>
            <view class="mode-text">{{testMode === 'en_to_cn' ? '汉译英' : '英译汉'}}</view>
          </button>
          <button class="mode-btn retry-dictation" bindtap="retryWithDictation">
            <view class="mode-icon">🎧</view>
            <view class="mode-text">听写</view>
          </button>
          <button class="mode-btn retry-game" bindtap="retryWithGame">
            <view class="mode-icon">🎮</view>
            <view class="mode-text">消消乐</view>
          </button>
        </view>

        <!-- 多关卡分享测试手动选择关卡模式：进入下一关/重新挑战和分享按钮一行显示 -->
        <view class="next-level-and-share" wx:if="{{shareMode === 'share' && isMultiLevel && isManualLevelSelection}}">
          <!-- 根据正确率显示不同按钮 -->
          <button
            class="next-level-btn-half"
            bindtap="goToNextShareLevel"
            wx:if="{{(accuracyRate >= 80 || parseFloat(accuracyRate) >= 80) && currentLevelId < totalLevels}}"
          >
            进入下一关
          </button>
          <button
            class="retry-btn-half"
            bindtap="retryCurrentLevel"
            wx:else
          >
            重新挑战
          </button>

          <button class="share-btn-half" bindtap="shareSelectedWords">
            <view class="share-icon">📤</view>
            <view class="share-text">分享测试</view>
          </button>
        </view>

        <!-- 其他情况：普通分享选项 - 单独一行 -->
        <view class="share-option" wx:else>
          <button class="share-btn" bindtap="shareSelectedWords">
            <view class="share-icon">📤</view>
            <view class="share-text">分享给他人测试</view>
          </button>
        </view>
      </view>

      <!-- 分组测试提示（仅在有下一组且不是多关卡分享测试时显示） -->
      <view class="group-test-section" wx:if="{{hasNextGroup && !(shareMode === 'share' && isMultiLevel)}}">
        <view class="group-info">
          <view class="group-title">📚 分组测试进度</view>
          <view class="group-progress">
            <text class="current-group">第{{currentGroup}}组完成</text>
            <text class="total-groups">还有{{totalGroups - currentGroup}}组</text>
          </view>
        </view>
        <view class="group-actions">
          <button class="group-btn primary" bindtap="goToNextGroup" wx:if="{{canProceedToNext}}">
            进入下一组 ({{currentGroup + 1}}/{{totalGroups}})
          </button>
          <button class="group-btn warning" bindtap="retryTest" wx:if="{{!passedCurrentGroup}}">
            重新挑战 (需要80%以上才能进入下一组)
          </button>
        </view>
      </view>

      <!-- 竞赛模式按钮 -->
      <view class="competition-buttons" wx:if="{{isCompetition}}">
        <button class="action-btn primary" bindtap="goToNextCompetitionLevel" wx:if="{{hasNextLevel && competitionCompleted}}">
          挑战下一关
        </button>
        <button class="action-btn secondary" bindtap="backToLevelSelect" wx:if="{{masterCompetitionId}}">
          返回关卡列表
        </button>
        <button class="action-btn secondary" bindtap="viewCompetitionRanking">
          查看排行榜
        </button>
      </view>

      <!-- 分享测试多关卡按钮 -->
      <view class="share-multilevel-buttons" wx:if="{{shareMode === 'share' && isMultiLevel}}">
        <view class="level-progress-info">
          <text class="level-info">第{{currentLevelId}}/{{totalLevels}}关</text>
          <text class="level-status">{{currentLevelId < totalLevels ? '还有' + (totalLevels - currentLevelId) + '关等待挑战' : '恭喜完成所有关卡！'}}</text>
        </view>
        <view class="level-actions">
          <!-- 自动进度检测模式：显示进入下一关按钮 -->
          <button
            class="action-btn primary"
            bindtap="goToNextShareLevel"
            wx:if="{{!isManualLevelSelection && currentLevelId < totalLevels && parseFloat(accuracyRate) >= 80}}"
          >
            进入第{{currentLevelId + 1}}关
          </button>
          <!-- 自动进度检测模式：重新挑战按钮 -->
          <button
            class="action-btn warning"
            bindtap="retryCurrentLevel"
            wx:if="{{!isManualLevelSelection && currentLevelId < totalLevels && parseFloat(accuracyRate) < 80}}"
          >
            重新挑战 (需要80%以上)
          </button>
          <!-- 手动选择关卡模式：三个操作按钮一行 -->
          <view class="manual-level-actions" wx:if="{{isManualLevelSelection}}">
            <button class="action-btn-small" bindtap="backToLevelSelect">
              选择关卡
            </button>
            <button class="action-btn-small" bindtap="retryCurrentLevel">
              重新刷分
            </button>
            <button class="action-btn-small" bindtap="backToShareList">
              分享列表
            </button>
          </view>

          <!-- 自动进度模式：返回分享列表按钮 -->
          <button
            class="action-btn secondary"
            bindtap="backToShareList"
            wx:if="{{!isManualLevelSelection}}"
          >
            返回分享列表
          </button>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="bottom-actions">
        <button class="bottom-btn secondary" bindtap="shareTestResult">
          分享成绩
        </button>
        <button class="bottom-btn" bindtap="backToHome">
          返回
        </button>
      </view>
    </view>
  </view>

  <!-- 测试未开始 -->
  <view class="waiting-content" wx:if="{{!testStarted}}">
    <view class="waiting-card">
      <view class="waiting-icon">⏱️</view>
      <text class="waiting-text">准备开始测试...</text>
    </view>
  </view>

  <!-- 隐藏的画布，用于生成分享图片 -->
  <canvas 
    canvas-id="resultCanvas" 
    style="width: 375px; height: 600px; position: fixed; top: -1000px; left: -1000px;"
  ></canvas>

  <!-- 分享弹窗 -->
  <view class="share-modal" wx:if="{{showShareModal}}" bindtap="closeShareModal">
    <view class="share-content" catchtap="">
      <view class="share-header">
        <text class="share-title">分享测试成绩</text>
        <text class="close-btn" bindtap="closeShareModal">✕</text>
      </view>
      
      <view class="share-info">
        <text class="share-desc">选择分享方式，与朋友分享你的测试成绩</text>
        <text class="share-note">让朋友看到你的学习成果</text>
      </view>

      <view class="share-buttons">
        <button 
          class="wechat-share-btn" 
          open-type="share"
          bindtap="onShareButtonTap"
        >
          <text class="share-btn-icon">📤</text>
          <text class="share-btn-text">分享给朋友</text>
        </button>
        
        <button 
          class="copy-btn" 
          bindtap="saveResultToAlbum"
        >
          <text class="copy-btn-icon">💾</text>
          <text class="copy-btn-text">保存到相册</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 隐藏的画布，用于生成分享图片 -->
  <canvas canvas-id="testResultCanvas" style="width: 375px; height: 600px; position: fixed; top: -9999px; left: -9999px;"></canvas>
</view>