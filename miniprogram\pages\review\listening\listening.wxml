<view class="container">
  <!-- 进度条 -->
  <view class="progress-bar">
    <view class="progress" style="width: {{progress}}%"></view>
    <text class="progress-text">{{currentIndex + 1}}/{{total}}</text>
  </view>

  <!-- 听口练习卡片 -->
  <view class="practice-card">
    <!-- 音频播放区域 -->
    <view class="audio-section">
      <view class="audio-text">{{currentMistake.text}}</view>
      <view class="audio-controls">
        <button class="play-btn" bindtap="playAudio">
          <image class="play-icon" src="/images/{{isPlaying ? 'pause.png' : 'play.png'}}" mode="aspectFit"></image>
        </button>
        <slider class="audio-slider" 
                value="{{audioProgress}}" 
                bindchange="onSliderChange"
                block-size="12"
                activeColor="#4A90E2"/>
      </view>
    </view>

    <!-- 翻译区域 -->
    <view class="translation-section">
      <view class="translation-text">{{currentMistake.translation}}</view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn" bindtap="onMarkAsKnown">认识</button>
      <button class="action-btn" bindtap="onMarkAsUnknown">不认识</button>
    </view>
  </view>

  <!-- 完成提示 -->
  <view class="completion-modal" wx:if="{{showCompletion}}">
    <view class="modal-content">
      <view class="completion-title">复习完成</view>
      <view class="completion-stats">
        <view class="stat-item">
          <text class="stat-label">认识</text>
          <text class="stat-value">{{knownCount}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">不认识</text>
          <text class="stat-value">{{unknownCount}}</text>
        </view>
      </view>
      <button class="completion-btn" bindtap="onCompletionConfirm">确定</button>
    </view>
  </view>
</view> 