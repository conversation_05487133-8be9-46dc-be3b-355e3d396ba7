Page({
  data: {
    articles: [
      {
        id: 1,
        title: 'The Future of Artificial Intelligence',
        source: 'BBC News',
        category: '科技',
        difficulty: 'intermediate',
        difficultyText: '中级',
        readTime: '5分钟',
        summary: 'Exploring how AI will reshape our daily lives and work in the coming decades.',
        tags: ['AI', '科技', '未来'],
        publishDate: '2024-01-15'
      },
      {
        id: 2,
        title: 'Climate Change and Global Action',
        source: 'The Guardian',
        category: '环境',
        difficulty: 'advanced',
        difficultyText: '高级',
        readTime: '8分钟',
        summary: 'An in-depth look at international efforts to combat climate change.',
        tags: ['环境', '气候', '全球'],
        publishDate: '2024-01-12'
      },
      {
        id: 3,
        title: 'The Rise of Remote Work Culture',
        source: 'CNN',
        category: '社会',
        difficulty: 'intermediate',
        difficultyText: '中级',
        readTime: '6分钟',
        summary: 'How remote work is changing the traditional workplace forever.',
        tags: ['工作', '远程', '文化'],
        publishDate: '2024-01-10'
      },
      {
        id: 4,
        title: 'Sustainable Fashion Revolution',
        source: 'Reuters',
        category: '时尚',
        difficulty: 'intermediate',
        difficultyText: '中级',
        readTime: '4分钟',
        summary: 'The growing movement towards eco-friendly fashion choices.',
        tags: ['时尚', '环保', '可持续'],
        publishDate: '2024-01-08'
      },
      {
        id: 5,
        title: 'Mental Health in Digital Age',
        source: 'The Times',
        category: '健康',
        difficulty: 'advanced',
        difficultyText: '高级',
        readTime: '7分钟',
        summary: 'Understanding the impact of technology on mental wellbeing.',
        tags: ['健康', '心理', '数字化'],
        publishDate: '2024-01-05'
      }
    ]
  },

  onLoad(options) {
    wx.setNavigationBarTitle({
      title: '外媒阅读'
    });
  },

  // 点击文章
  onArticleTap(e) {
    const articleId = e.currentTarget.dataset.id;
    const article = this.data.articles.find(a => a.id === articleId);

    wx.showModal({
      title: article.title,
      content: '文章内容正在完善中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    });
  }
})