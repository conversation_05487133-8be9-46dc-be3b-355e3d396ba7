<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">写作积累</text>
    <text class="page-subtitle">提升英语写作能力</text>
  </view>

  <!-- 学习模块 -->
  <view class="section">
    <text class="section-title">学习模块</text>
    <view class="modules-grid">
      <view class="module-card"
            wx:for="{{learningModules}}"
            wx:key="id"
            bindtap="onModuleTap"
            data-id="{{item.id}}">
        <view class="module-icon" style="background-color: {{item.color}}">
          <text class="icon-text">{{item.icon}}</text>
        </view>
        <view class="module-content">
          <text class="module-title">{{item.title}}</text>
          <text class="module-subtitle">{{item.subtitle}}</text>
        </view>
        <text class="module-arrow">→</text>
      </view>
    </view>
    <view class="module-description">
      <text class="desc-text">{{learningModules[0].description}}</text>
    </view>
  </view>

  <!-- 通盖话题 -->
  <view class="section">
    <text class="section-title">通盖话题</text>
    <view class="topics-grid">
      <view class="topic-tag"
            wx:for="{{commonTopics}}"
            wx:key="id"
            bindtap="onTopicTap"
            data-id="{{item.id}}"
            style="background-color: {{item.color}}20; border-color: {{item.color}}">
        <text class="topic-text" style="color: {{item.color}}">{{item.title}}</text>
      </view>
    </view>
  </view>

  <!-- 学习贴士 -->
  <view class="section">
    <text class="section-title">学习贴士</text>
    <view class="tips-card">
      <view class="tip-item" wx:for="{{learningTips}}" wx:key="*this">
        <text class="tip-bullet">•</text>
        <text class="tip-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 注意提示 -->
  <view class="notice-card">
    <view class="notice-icon">⚠️</view>
    <view class="notice-content">
      <text class="notice-title">注意：</text>
      <text class="notice-text">这只是示例内容，不是正式内容，具体内容正在完善中</text>
    </view>
  </view>
</view>