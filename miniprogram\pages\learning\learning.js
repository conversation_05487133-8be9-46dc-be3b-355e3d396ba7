const app = getApp()
const optionGenerator = require('../../utils/option-generator')

Page({
  data: {
    mode: '', // en_to_cn, cn_to_en, elimination, fullbook
    words: [],
    currentIndex: 0,
    currentWord: null,
    options: [],
    score: 0,
    correctCount: 0,
    wrongCount: 0,
    showResult: false,
    showAnswer: false,
    isAnswered: false,
    selectedOption: -1,
    correctOption: -1,
    startTime: null,
    endTime: null,
    
    // 音频相关
    audioContext: null,
    audioProgress: 0,
    
    // 完成状态
    showCompletion: false,
    
    // 全书学习模式
    isFullBookMode: false,
          libraryId: '',
      isRandom: false,
      savedProgress: null
    },

  onLoad: function(options) {
    console.log('=== 学习页面加载 ===');
    console.log('页面参数:', options);
    
    const { mode, total, libraryId, isRandom, continue: continueStudy } = options;
    
    // 检查是否为全书学习模式
    if (mode === 'fullbook') {
      this.loadFullBookLearning(options);
      return;
    }
    
    // 原有的学习模式逻辑
    const learningData = app.globalData.learningData;
    
    console.log('全局学习数据:', learningData);
    
    if (!learningData || !learningData.words) {
      console.error('学习数据缺失:', { learningData });
      wx.showToast({ title: '数据加载失败', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    console.log('词汇数量:', learningData.words.length);
    console.log('第一个词汇结构:', learningData.words[0]);

    this.setData({
      mode: mode,
      words: learningData.words,
      startTime: Date.now()
    });

    console.log('开始学习，当前设置:', { mode, wordsCount: learningData.words.length });
    this.startLearning();
  },

  // 加载全书学习
  loadFullBookLearning: function(options) {
    const { libraryId, isRandom, continue: continueStudy } = options;
    const fullBookData = app.globalData.fullBookLearningData;
    
    console.log('=== 全书学习模式 ===');
    console.log('参数:', options);
    console.log('全书学习数据:', fullBookData);
    
    if (!fullBookData || !fullBookData.words) {
      console.error('全书学习数据缺失');
      wx.showToast({ title: '数据加载失败', icon: 'error' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    let startIndex = 0;
    let savedProgress = null;
    
    // 如果是继续学习，获取保存的进度
    if (continueStudy === 'true') {
      try {
        const progressKey = `fullbook_progress_${libraryId}`;
        savedProgress = wx.getStorageSync(progressKey);
        if (savedProgress && savedProgress.currentIndex > 0) {
          startIndex = savedProgress.currentIndex;
          console.log('继续学习，从第', startIndex + 1, '个词汇开始');
        }
      } catch (error) {
        console.error('获取学习进度失败:', error);
      }
    }
    
    this.setData({
      mode: 'fullbook',
      words: fullBookData.words,
      currentIndex: startIndex,
      startTime: Date.now(),
      libraryId: libraryId,
      isRandom: isRandom === 'true',
      isFullBookMode: true,
      savedProgress: savedProgress
    });

    console.log('全书学习设置完成，开始学习');
    this.startLearning();
  },

  onUnload: function() {
    // 清理音频上下文
    if (this.audioContext) {
      this.audioContext.destroy()
    }
  },

  // 初始化音频上下文
  initAudioContext: function() {
    this.audioContext = wx.createInnerAudioContext()
    this.audioContext.onTimeUpdate(() => {
      this.setData({
        audioProgress: (this.audioContext.currentTime / this.audioContext.duration) * 100
      })
    })
    this.audioContext.onEnded(() => {
      this.setData({
        audioProgress: 0
      })
    })
  },

  // 开始学习
  startLearning: function() {
    if (this.data.currentIndex >= this.data.words.length) {
      this.showFinalResult();
      return;
    }

    const currentWord = this.data.words[this.data.currentIndex];
    this.setData({
      currentWord: currentWord,
      isAnswered: false,
      showAnswer: false,
      selectedOption: -1,
      correctOption: -1
    });

    this.generateOptions(currentWord);
  },

  // 生成选项
  generateOptions: function(currentWord) {
    const { mode, words } = this.data;

    console.log('当前词汇对象:', currentWord);
    console.log('词汇对象的所有字段:', Object.keys(currentWord));
    console.log('学习模式:', mode);

    let result = {};

    if (mode === 'en_to_cn') {
      // 英译汉：显示英文+音标，选择中文含义
      result = optionGenerator.generateWordOptions(words, 'en_to_cn', currentWord, 4);
    } else if (mode === 'cn_to_en') {
      // 汉译英：显示中文，选择英文
      result = optionGenerator.generateWordOptions(words, 'cn_to_en', currentWord, 4);
    } else if (mode === 'elimination') {
      // 单词消消乐：显示英文，选择中文（简化版）
      result = optionGenerator.generateWordOptions(words, 'en_to_cn', currentWord, 4);
    } else if (mode === 'fullbook') {
      // 全书学习模式：默认为英译汉模式
      result = optionGenerator.generateWordOptions(words, 'en_to_cn', currentWord, 4);
    }

    console.log('选项生成结果:', result);

    // 检查选项是否生成成功
    if (!result.options || result.options.length === 0) {
      console.error('选项生成失败:', result);
      wx.showToast({ title: '题目生成失败', icon: 'error' });
      return;
    }

    this.setData({
      options: result.options,
      correctOption: result.correctIndex >= 0 ? result.correctIndex : 0
    });
  },



  // 选择答案
  onOptionTap: function(e) {
    if (this.data.isAnswered) return;

    const selectedIndex = e.currentTarget.dataset.index;
    const isCorrect = selectedIndex === this.data.correctOption;

    this.setData({
      selectedOption: selectedIndex,
      isAnswered: true,
      showAnswer: true,
      score: this.data.score + (isCorrect ? 10 : 0),
      correctCount: this.data.correctCount + (isCorrect ? 1 : 0),
      wrongCount: this.data.wrongCount + (isCorrect ? 0 : 1)
    });

    // 播放音效反馈
    if (isCorrect) {
      wx.vibrateShort({ type: 'light' });
    } else {
      wx.vibrateShort({ type: 'heavy' });
    }

    // 自动进入下一题
    setTimeout(() => {
      this.nextWord();
    }, 1500);
  },

  // 下一个单词
  nextWord: function() {
    const nextIndex = this.data.currentIndex + 1;
    
    // 如果是全书学习模式，保存进度
    if (this.data.isFullBookMode) {
      this.saveFullBookProgress(nextIndex);
    }
    
    this.setData({
      currentIndex: nextIndex
    });
    this.startLearning();
  },

  // 保存全书学习进度
  saveFullBookProgress: function(currentIndex) {
    const { libraryId, words, correctCount, wrongCount } = this.data;
    
    try {
      const progressKey = `fullbook_progress_${libraryId}`;
      const progressData = {
        currentIndex: currentIndex,
        totalWords: words.length,
        correctCount: correctCount,
        wrongCount: wrongCount,
        totalAnswered: correctCount + wrongCount,
        lastStudyTime: Date.now(),
        totalStudyTime: Math.floor((Date.now() - this.data.startTime) / 1000)
      };
      
      wx.setStorageSync(progressKey, progressData);
      console.log('全书学习进度已保存:', progressData);
    } catch (error) {
      console.error('保存全书学习进度失败:', error);
    }
  },

  // 显示最终结果
  showFinalResult: function() {
    const { correctCount, wrongCount, startTime, isFullBookMode, libraryId } = this.data;
    const totalTime = Math.floor((Date.now() - startTime) / 1000);
    const accuracy = Math.round((correctCount / (correctCount + wrongCount)) * 100);

    // 如果是全书学习模式，学习完成后清除进度
    if (isFullBookMode) {
      try {
        const progressKey = `fullbook_progress_${libraryId}`;
        wx.removeStorageSync(progressKey);
        console.log('全书学习完成，进度已清除');
      } catch (error) {
        console.error('清除全书学习进度失败:', error);
      }
    }

    this.setData({
      showResult: true,
      endTime: Date.now(),
      totalTime: totalTime,
      accuracy: accuracy
    });
  },

  // 重新学习
  restartLearning: function() {
    this.setData({
      currentIndex: 0,
      score: 0,
      correctCount: 0,
      wrongCount: 0,
      showResult: false,
      startTime: Date.now()
    });
    this.startLearning();
  },

  // 返回选择页面
  backToSelect: function() {
    wx.navigateBack();
  },

  // 返回首页
  backToHome: function() {
    wx.switchTab({ url: '/pages/index/index' });
  },

  // 音频进度条变化
  onSliderChange: function(e) {
    if (this.data.audioContext) {
      const position = e.detail.value / 100 * this.data.audioContext.duration
      this.data.audioContext.seek(position)
    }
  }
}) 