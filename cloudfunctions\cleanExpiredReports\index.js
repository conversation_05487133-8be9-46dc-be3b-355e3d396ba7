// 云函数：清理过期的HTML报告文件
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('开始清理过期的HTML报告文件...');

  try {
    const now = new Date();

    // 查找过期的分享记录
    const expiredRecords = await db.collection('report_shares')
      .where({
        expirationTime: db.command.lt(now)
      })
      .get();

    console.log(`找到 ${expiredRecords.data.length} 个过期记录`);

    let deletedFiles = 0;
    let deletedRecords = 0;
    let failedDeletions = 0;

    // 删除过期的文件和记录
    for (const record of expiredRecords.data) {
      try {
        // 删除云存储文件
        await cloud.deleteFile({
          fileList: [record.fileId]
        });
        deletedFiles++;
        console.log(`删除文件成功: ${record.fileId}`);

        // 删除数据库记录
        await db.collection('report_shares').doc(record._id).remove();
        deletedRecords++;
        console.log(`删除记录成功: ${record._id}`);

      } catch (err) {
        failedDeletions++;
        console.error(`删除失败 - 文件ID: ${record.fileId}, 错误:`, err);

        // 如果文件不存在，仍然删除数据库记录
        if (err.errCode === -503003 || err.message.includes('file not found')) {
          try {
            await db.collection('report_shares').doc(record._id).remove();
            deletedRecords++;
            console.log(`文件不存在，但已删除数据库记录: ${record._id}`);
          } catch (dbErr) {
            console.error(`删除数据库记录失败: ${record._id}`, dbErr);
          }
        }
      }
    }

    // 清理可能存在的孤立文件（根据文件名时间戳判断是否过期）
    try {
      // 获取public/reports目录下的文件列表
      const fileList = await cloud.getFileList({
        prefix: 'public/reports/',
        limit: 100
      });

      let orphanedFiles = 0;
      const sevenDaysAgo = now.getTime() - (7 * 24 * 60 * 60 * 1000);

      for (const file of fileList.fileList) {
        // 从文件名中提取时间戳
        const match = file.cloudPath.match(/report_.*_(\d{13})_/);
        if (match) {
          const fileTimestamp = parseInt(match[1]);
          if (fileTimestamp < sevenDaysAgo) {
            try {
              await cloud.deleteFile({
                fileList: [file.fileID]
              });
              orphanedFiles++;
              console.log(`删除孤立过期文件: ${file.cloudPath}`);
            } catch (deleteErr) {
              console.error(`删除孤立文件失败: ${file.cloudPath}`, deleteErr);
            }
          }
        }
      }

      if (orphanedFiles > 0) {
        console.log(`清理了 ${orphanedFiles} 个孤立的过期文件`);
      }

    } catch (listErr) {
      console.error('获取文件列表失败:', listErr);
    }

    const result = {
      success: true,
      message: `清理完成 - 删除了 ${deletedFiles} 个文件和 ${deletedRecords} 条记录${failedDeletions > 0 ? `，${failedDeletions} 个删除失败` : ''}`,
      deletedFiles,
      deletedRecords,
      failedDeletions,
      totalExpired: expiredRecords.data.length
    };

    console.log('清理结果:', result);
    return result;

  } catch (error) {
    console.error('清理过程中发生错误:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
