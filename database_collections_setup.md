# 数据库集合设置说明

## 需要创建的集合

### 1. report_shares 集合
用于存储报告分享记录

**字段结构：**
```json
{
  "_id": "自动生成",
  "fileId": "云存储文件ID",
  "shareUrl": "分享链接",
  "type": "报告类型(analysis/courseplan/complete)",
  "studentName": "学生姓名",
  "examType": "考试类型",
  "grade": "年级",
  "analystName": "分析师姓名",
  "createdAt": "创建时间",
  "expirationTime": "过期时间",
  "openId": "用户openId"
}
```

**权限设置：**
- 读权限：仅创建者可读
- 写权限：仅创建者可写

### 2. short_links 集合（已移除）
短链接功能已移除，不需要创建此集合。

## 创建步骤

1. 登录微信开发者工具
2. 打开云开发控制台
3. 进入数据库管理
4. 点击"创建集合"
5. 输入集合名称
6. 设置相应的权限
7. 创建必要的索引

## 短链接功能说明

短链接功能已移除，现在直接使用原始的云存储链接。用户复制HTML链接时会得到完整的云存储URL，可以直接在浏览器中打开。

## 注意事项

- 如果不创建这些集合，相关功能会静默失败，不会影响其他功能
- 建议在生产环境中创建这些集合以获得完整功能
- 可以根据实际需求调整字段结构和权限设置
- HTML链接功能使用云存储直接链接，无需额外配置
