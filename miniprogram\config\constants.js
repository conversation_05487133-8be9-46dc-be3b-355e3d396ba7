// 应用配置
const APP_CONFIG = {
  name: '墨词自习室',
  version: '1.0.0',
  logo: '/images/logo.png'
}

// 学习计划配置
const LEARNING_CONFIG = {
  dailyWordsOptions: [10, 20, 30, 50, 100],
  defaultDailyWords: 20,
  reminderTimeOptions: [
    '08:00', '09:00', '10:00', '11:00', '12:00',
    '13:00', '14:00', '15:00', '16:00', '17:00',
    '18:00', '19:00', '20:00', '21:00', '22:00'
  ],
  defaultReminderTime: '20:00'
}

// 单词难度等级
const WORD_DIFFICULTY = {
  EASY: 1,
  MEDIUM: 2,
  HARD: 3
}

// 学习状态
const LEARNING_STATUS = {
  NOT_STARTED: 'not_started',
  LEARNING: 'learning',
  REVIEWING: 'reviewing',
  MASTERED: 'mastered'
}

// 掌握程度
const MASTERY_LEVEL = {
  LEVEL_1: 1, // 初次学习
  LEVEL_2: 2, // 复习一次
  LEVEL_3: 3, // 复习两次
  LEVEL_4: 4, // 复习三次
  LEVEL_5: 5  // 完全掌握
}

// 复习间隔（天）
const REVIEW_INTERVALS = {
  [MASTERY_LEVEL.LEVEL_1]: 1,    // 1天后复习
  [MASTERY_LEVEL.LEVEL_2]: 3,    // 3天后复习
  [MASTERY_LEVEL.LEVEL_3]: 7,    // 7天后复习
  [MASTERY_LEVEL.LEVEL_4]: 15,   // 15天后复习
  [MASTERY_LEVEL.LEVEL_5]: 30    // 30天后复习
}

// 错误类型
const ERROR_TYPES = {
  SPELLING: 'spelling',           // 拼写错误
  MEANING: 'meaning',            // 词义错误
  USAGE: 'usage',               // 用法错误
  PRONUNCIATION: 'pronunciation' // 发音错误
}

// 用户设置默认值
const DEFAULT_SETTINGS = {
  dailyWords: LEARNING_CONFIG.defaultDailyWords,
  reminderEnabled: false,
  reminderTime: LEARNING_CONFIG.defaultReminderTime,
  autoPlayAudio: true,
  showPhonetic: true,
  showExample: true
}

// 集合名称映射表
const COLLECTION_NAMES = {
  // 中考词汇
  zhongkao_1600: 'words_zhongkao',           // 中考1600词

  // 大学词汇
  college_cet4: 'words_siji',              // 四级词汇
  college_cet6: 'words_liuji',             // 六级词汇

  // 高考大纲词汇
  gaokao_3500: 'words_3500',              // 3500大纲词汇（顺序版）
  gaokao_3500_luan: 'words_3500_luan',    // 3500大纲词汇（乱序版）
  gaokao_weikeduo: 'words_weikeduo',      // 维克多高考词汇

  // 常用短语
  phrase_gaopin: 'phrase_gaopin',         // 高频短语
  phrase_hunxiao: 'phrase_hunxiao',       // 易错易混淆短语

  // 题型专项词汇
  special_yuedu_tongyong: 'words_reading_tongyong',     // 阅读高频词（通用）
  special_yuedu_beijing: 'words_reading_beijing',       // 阅读高频词（北京卷）
  special_wanxing_gaopin_beijing: 'words_wanxing_gaopin_beijing',   // 完形高频词（北京卷）
  special_wanxing_shucishengyi_beijing: 'words_wanxing_shucishengyi_beijing',   // 完型熟词生义（北京卷）
  special_wusan_gaopin_beijing: 'words_bjwusan',       // 五三高频词（北京卷）

  // 其他词汇
  other_buguize: 'words_buguize',                     // 不规则动词
  other_xingjinci: 'words_xingjinci',                 // 高考形近词
  other_shucishengyi: 'words_shucishengyi_tongyong',  // 熟词生义（通用）

  // 教材同步词汇（人教版）
  renjiao_bixiu1: 'words_renjiao_bixiu1',             // 人教版必修第一册
  renjiao_bixiu2: 'words_renjiao_bixiu2',             // 人教版必修第二册
  renjiao_bixiu3: 'words_renjiao_bixiu3',             // 人教版必修第三册
  renjiao_xuanxiu1: 'words_renjiao_xuanxiu1',         // 人教版选择性必修第一册
  renjiao_xuanxiu2: 'words_renjiao_xuanxiu2',         // 人教版选择性必修第二册
  renjiao_xuanxiu3: 'words_renjiao_xuanxiu3',         // 人教版选择性必修第三册
  renjiao_xuanxiu4: 'words_renjiao_xuanxiu4',         // 人教版选择性必修第四册

  // 教材同步词汇（北师版）
  beishi_bixiu1: 'words_beishi_bixiu1',               // 北师版必修第一册
  beishi_bixiu2: 'words_beishi_bixiu2',               // 北师版必修第二册
  beishi_bixiu3: 'words_beishi_bixiu3',               // 北师版必修第三册
  beishi_xuanxiu1: 'words_beishi_xuanxiu1',           // 北师版选择性必修第一册
  beishi_xuanxiu2: 'words_beishi_xuanxiu2',           // 北师版选择性必修第二册
  beishi_xuanxiu3: 'words_beishi_xuanxiu3',           // 北师版选择性必修第三册
  beishi_xuanxiu4: 'words_beishi_xuanxiu4'            // 北师版选择性必修第四册
};

// 教材同步词汇的单元定义（用于前端显示和unit字段过滤）
const TEXTBOOK_UNITS = {
  // 人教版
  renjiao: {
    bixiu1: [
      { id: 'welcome', name: 'Welcome Unit' },
      { id: 'U1', name: 'Unit 1' },
      { id: 'U2', name: 'Unit 2' },
      { id: 'U3', name: 'Unit 3' },
      { id: 'U4', name: 'Unit 4' },
      { id: 'U5', name: 'Unit 5' }
    ],
    bixiu2: [
      { id: 'U1', name: 'Unit 1' },
      { id: 'U2', name: 'Unit 2' },
      { id: 'U3', name: 'Unit 3' },
      { id: 'U4', name: 'Unit 4' },
      { id: 'U5', name: 'Unit 5' }
    ],
    bixiu3: [
      { id: 'U1', name: 'Unit 1' },
      { id: 'U2', name: 'Unit 2' },
      { id: 'U3', name: 'Unit 3' },
      { id: 'U4', name: 'Unit 4' },
      { id: 'U5', name: 'Unit 5' }
    ],
    xuanxiu1: [
      { id: 'U1', name: 'Unit 1' },
      { id: 'U2', name: 'Unit 2' },
      { id: 'U3', name: 'Unit 3' },
      { id: 'U4', name: 'Unit 4' },
      { id: 'U5', name: 'Unit 5' }
    ],
    xuanxiu2: [
      { id: 'U1', name: 'Unit 1' },
      { id: 'U2', name: 'Unit 2' },
      { id: 'U3', name: 'Unit 3' },
      { id: 'U4', name: 'Unit 4' },
      { id: 'U5', name: 'Unit 5' }
    ],
    xuanxiu3: [
      { id: 'U1', name: 'Unit 1' },
      { id: 'U2', name: 'Unit 2' },
      { id: 'U3', name: 'Unit 3' },
      { id: 'U4', name: 'Unit 4' },
      { id: 'U5', name: 'Unit 5' }
    ],
    xuanxiu4: [
      { id: 'U1', name: 'Unit 1' },
      { id: 'U2', name: 'Unit 2' },
      { id: 'U3', name: 'Unit 3' },
      { id: 'U4', name: 'Unit 4' },
      { id: 'U5', name: 'Unit 5' }
    ]
  },
  // 北师版
  beishi: {
    bixiu1: [
      { id: 'U1', name: 'Unit 1' },
      { id: 'U2', name: 'Unit 2' },
      { id: 'U3', name: 'Unit 3' }
    ],
    bixiu2: [
      { id: 'U4', name: 'Unit 4' },
      { id: 'U5', name: 'Unit 5' },
      { id: 'U6', name: 'Unit 6' }
    ],
    bixiu3: [
      { id: 'U7', name: 'Unit 7' },
      { id: 'U8', name: 'Unit 8' },
      { id: 'U9', name: 'Unit 9' }
    ],
    xuanxiu1: [
      { id: 'U1', name: 'Unit 1' },
      { id: 'U2', name: 'Unit 2' },
      { id: 'U3', name: 'Unit 3' }
    ],
    xuanxiu2: [
      { id: 'U4', name: 'Unit 4' },
      { id: 'U5', name: 'Unit 5' },
      { id: 'U6', name: 'Unit 6' }
    ],
    xuanxiu3: [
      { id: 'U7', name: 'Unit 7' },
      { id: 'U8', name: 'Unit 8' },
      { id: 'U9', name: 'Unit 9' }
    ],
    xuanxiu4: [
      { id: 'U10', name: 'Unit 10' },
      { id: 'U11', name: 'Unit 11' },
      { id: 'U12', name: 'Unit 12' }
    ]
  }
};

// 词库信息映射
const LIBRARY_INFO = {
  // 中考词汇
  zhongkao_1600: {
    name: '中考1600词',
    count: '1600词',
    type: 'zhongkao',
    requireVip: false,
    collection: COLLECTION_NAMES.zhongkao_1600
  },
  
  // 大学词汇
  college_cet4: {
    name: '四级词汇',
    count: '4428词',
    type: 'college',
    requireVip: false,
    collection: COLLECTION_NAMES.college_cet4
  },
  college_cet6: {
    name: '六级词汇',
    count: '5523词',
    type: 'college',
    requireVip: false,
    collection: COLLECTION_NAMES.college_cet6
  },
  
  // 高考大纲词汇
  gaokao_3500: {
    name: '高考3500（顺序版）',
    count: 3925,
    type: 'gaokao',
    requireVip: false,
    collection: COLLECTION_NAMES.gaokao_3500
  },
  gaokao_3500_luan: {
    name: '高考3500（乱序版）',
    count: 3925,
    type: 'gaokao',
    requireVip: false,
    collection: COLLECTION_NAMES.gaokao_3500_luan
  },
  gaokao_weikeduo: {
    name: '维克多高考词汇',
    count: 5000,
    type: 'gaokao',
    requireVip: false,
    collection: COLLECTION_NAMES.gaokao_weikeduo
  },
  
  // 常用短语
  phrase_gaopin: {
    name: '高频短语',
    count: '800条',
    type: 'phrase',
    requireVip: false,
    collection: COLLECTION_NAMES.phrase_gaopin
  },
  phrase_hunxiao: {
    name: '易错易混淆短语',
    count: '600条',
    type: 'phrase',
    requireVip: false,
    collection: COLLECTION_NAMES.phrase_hunxiao
  },
  
  // 题型专项词汇
  special_wusan_gaopin_beijing: {
    name: '五三高频词（北京卷）',
    count: '加载中...',
    type: 'special',
    requireVip: false,
    collection: COLLECTION_NAMES.special_wusan_gaopin_beijing
  },
  special_yuedu_tongyong: {
    name: '阅读高频词（通用）',
    count: 800,
    type: 'special',
    requireVip: false,
    collection: COLLECTION_NAMES.special_yuedu_tongyong
  },
  special_yuedu_beijing: {
    name: '阅读高频词（北京卷）',
    count: 600,
    type: 'special',
    requireVip: false,
    collection: COLLECTION_NAMES.special_yuedu_beijing
  },
  special_wanxing_gaopin_beijing: {
    name: '完形高频词（北京卷）',
    count: 500,
    type: 'special',
    requireVip: false,
    collection: COLLECTION_NAMES.special_wanxing_gaopin_beijing
  },
  special_wanxing_shucishengyi_beijing: {
    name: '完型熟词生义（北京卷）',
    count: 300,
    type: 'special',
    requireVip: false,
    collection: COLLECTION_NAMES.special_wanxing_shucishengyi_beijing
  },
  
  // 其他词汇
  other_buguize: {
    name: '不规则动词',
    count: 200,
    type: 'other',
    requireVip: false,
    collection: COLLECTION_NAMES.other_buguize
  },
  other_xingjinci: {
    name: '高考形近词',
    count: 400,
    type: 'other',
    requireVip: false,
    collection: COLLECTION_NAMES.other_xingjinci
  },
  other_shucishengyi: {
    name: '熟词生义（通用）',
    count: 500,
    type: 'other',
    requireVip: false,
    collection: COLLECTION_NAMES.other_shucishengyi
  },
  
  // 教材同步词汇（人教版）
  renjiao_bixiu1: {
    name: '人教版必修第一册',
    count: '按单元学习',
    type: 'textbook',
    series: 'renjiao',
    book: 'bixiu1',
    requireVip: false,
    collection: COLLECTION_NAMES.renjiao_bixiu1
  },
  renjiao_bixiu2: {
    name: '人教版必修第二册',
    count: '按单元学习',
    type: 'textbook',
    series: 'renjiao',
    book: 'bixiu2',
    requireVip: false,
    collection: COLLECTION_NAMES.renjiao_bixiu2
  },
  renjiao_bixiu3: {
    name: '人教版必修第三册',
    count: '按单元学习',
    type: 'textbook',
    series: 'renjiao',
    book: 'bixiu3',
    requireVip: false,
    collection: COLLECTION_NAMES.renjiao_bixiu3
  },
  renjiao_xuanxiu1: {
    name: '人教版选择性必修第一册',
    count: '按单元学习',
    type: 'textbook',
    series: 'renjiao',
    book: 'xuanxiu1',
    requireVip: false,
    collection: COLLECTION_NAMES.renjiao_xuanxiu1
  },
  renjiao_xuanxiu2: {
    name: '人教版选择性必修第二册',
    count: '按单元学习',
    type: 'textbook',
    series: 'renjiao',
    book: 'xuanxiu2',
    requireVip: false,
    collection: COLLECTION_NAMES.renjiao_xuanxiu2
  },
  renjiao_xuanxiu3: {
    name: '人教版选择性必修第三册',
    count: '按单元学习',
    type: 'textbook',
    series: 'renjiao',
    book: 'xuanxiu3',
    requireVip: false,
    collection: COLLECTION_NAMES.renjiao_xuanxiu3
  },
  renjiao_xuanxiu4: {
    name: '人教版选择性必修第四册',
    count: '按单元学习',
    type: 'textbook',
    series: 'renjiao',
    book: 'xuanxiu4',
    requireVip: false,
    collection: COLLECTION_NAMES.renjiao_xuanxiu4
  },
  
  // 教材同步词汇（北师版）
  beishi_bixiu1: {
    name: '北师版必修第一册',
    count: '按单元学习',
    type: 'textbook',
    series: 'beishi',
    book: 'bixiu1',
    requireVip: false,
    collection: COLLECTION_NAMES.beishi_bixiu1
  },
  beishi_bixiu2: {
    name: '北师版必修第二册',
    count: '按单元学习',
    type: 'textbook',
    series: 'beishi',
    book: 'bixiu2',
    requireVip: false,
    collection: COLLECTION_NAMES.beishi_bixiu2
  },
  beishi_bixiu3: {
    name: '北师版必修第三册',
    count: '按单元学习',
    type: 'textbook',
    series: 'beishi',
    book: 'bixiu3',
    requireVip: false,
    collection: COLLECTION_NAMES.beishi_bixiu3
  },
  beishi_xuanxiu1: {
    name: '北师版选择性必修第一册',
    count: '按单元学习',
    type: 'textbook',
    series: 'beishi',
    book: 'xuanxiu1',
    requireVip: false,
    collection: COLLECTION_NAMES.beishi_xuanxiu1
  },
  beishi_xuanxiu2: {
    name: '北师版选择性必修第二册',
    count: '按单元学习',
    type: 'textbook',
    series: 'beishi',
    book: 'xuanxiu2',
    requireVip: false,
    collection: COLLECTION_NAMES.beishi_xuanxiu2
  },
  beishi_xuanxiu3: {
    name: '北师版选择性必修第三册',
    count: '按单元学习',
    type: 'textbook',
    series: 'beishi',
    book: 'xuanxiu3',
    requireVip: false,
    collection: COLLECTION_NAMES.beishi_xuanxiu3
  },
  beishi_xuanxiu4: {
    name: '北师版选择性必修第四册',
    count: '按单元学习',
    type: 'textbook',
    series: 'beishi',
    book: 'xuanxiu4',
    requireVip: false,
    collection: COLLECTION_NAMES.beishi_xuanxiu4
  }
};

// 导出配置
module.exports = {
  APP_CONFIG,
  LEARNING_CONFIG,
  WORD_DIFFICULTY,
  LEARNING_STATUS,
  MASTERY_LEVEL,
  REVIEW_INTERVALS,
  ERROR_TYPES,
  DEFAULT_SETTINGS,
  COLLECTION_NAMES,
  LIBRARY_INFO,
  TEXTBOOK_UNITS
} 