const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { 
      competitionId, 
      mode,
      score, 
      duration, // 用时（秒）
      accuracy, // 正确率（百分比）
      correctCount, // 正确数量
      totalCount, // 总题数
      eliminatedPairs, // 消除对数（消消乐模式）
      totalPairs, // 总配对数（消消乐模式）
      isWin, // 是否获胜（消消乐模式）
      userName
    } = event
    
    const wxContext = cloud.getWXContext()
    
    if (!competitionId || score === undefined) {
      return {
        success: false,
        message: '缺少必要参数'
      }
    }

    // 获取竞赛信息
    let competitionResult
    try {
      competitionResult = await db.collection('competitions').doc(competitionId).get()
    } catch (error) {
      if (error.errCode === -502005) {
        return {
          success: false,
          message: '竞赛不存在'
        }
      }
      throw error
    }
    
    if (!competitionResult.data) {
      return {
        success: false,
        message: '竞赛不存在'
      }
    }

    const competition = competitionResult.data
    const currentUserOpenId = wxContext.OPENID
    
    // 检查竞赛状态
    if (competition.status !== 'active') {
      return {
        success: false,
        message: '竞赛已结束'
      }
    }

    // 检查是否过期
    const now = new Date()
    if (competition.autoDeleteTime && now > new Date(competition.autoDeleteTime)) {
      return {
        success: false,
        message: '竞赛已过期'
      }
    }

    // 获取用户信息以确保昵称正确
    let actualUserName = userName || '匿名用户';
    
    // 如果没有传入用户名，尝试从数据库获取
    if (!userName || userName === '匿名用户') {
      try {
        const userResult = await db.collection('users').where({
          openid: currentUserOpenId
        }).get();
        
        if (userResult.data.length > 0) {
          const user = userResult.data[0];
          // 优先使用微信昵称，其次使用用户名
          actualUserName = user.wechatInfo?.nickName || user.username || '匿名用户';
        }
      } catch (error) {
        console.log('获取用户信息失败，使用默认昵称:', error);
      }
    }

    // 构建结果记录
    const resultRecord = {
      openId: currentUserOpenId,
      openid: currentUserOpenId, // 添加小写的openid字段，确保兼容性
      userName: actualUserName,
      score: score,
      duration: duration || 0,
      submitTime: new Date()
    }

    // 根据不同模式添加特定字段
    if (mode === 'en2zh' || mode === 'zh2en') {
      resultRecord.accuracy = accuracy || 0
      resultRecord.correctCount = correctCount || 0
      resultRecord.totalCount = totalCount || 0
      // 判断是否通过关卡：正确率大于等于80%
      resultRecord.completed = (accuracy || 0) >= 80
    } else if (mode === 'dictation') {
      resultRecord.accuracy = accuracy || 0
      resultRecord.correctCount = correctCount || 0
      resultRecord.totalCount = totalCount || 0
      // 判断是否通过关卡：正确率大于等于80%
      resultRecord.completed = (accuracy || 0) >= 80
    } else if (mode === 'elimination') {
      resultRecord.eliminatedPairs = eliminatedPairs || 0
      resultRecord.totalPairs = totalPairs || 0
      // 修复消消乐模式的完成判定：
      // 1. 优先使用isWin参数（如果前端传递了）
      // 2. 否则检查是否消除了所有配对
      if (isWin !== undefined) {
        resultRecord.completed = isWin === true
      } else if (totalPairs && totalPairs > 0) {
        resultRecord.completed = (eliminatedPairs || 0) >= totalPairs
      } else {
        // 兼容旧版本：如果没有总配对数，假设完成了所有配对
        resultRecord.completed = true
      }
    }

    // 获取现有结果数组
    const results = competition.results || []
    
    // 查找是否有该用户的历史记录
    const existingIndex = results.findIndex(r => r.openId === currentUserOpenId)
    
    let isNewParticipant = false
    let isBestScore = false
    let completed = resultRecord.completed // 获取当前结果的完成状态
    
    if (existingIndex >= 0) {
      // 用户已参与过，比较分数
      const existingRecord = results[existingIndex]
      if (score > existingRecord.score) {
        // 新分数更高，更新记录
        results[existingIndex] = resultRecord
        isBestScore = true
      } else {
        // 如果新分数不高，但是新记录显示完成了而旧记录没完成，也要更新完成状态
        if (resultRecord.completed && !existingRecord.completed) {
          existingRecord.completed = true
          completed = true
        }
      }
      // 如果新分数不高，不更新记录但仍返回成功
    } else {
      // 新参与者，直接添加记录
      results.push(resultRecord)
      isNewParticipant = true
      isBestScore = true
    }

    // 更新竞赛数据
    const updateData = {
      results: results
    }

    // 如果是新参与者，增加参与人数
    if (isNewParticipant) {
      updateData.participants = (competition.participants || 0) + 1
    }

    await db.collection('competitions').doc(competitionId).update({
      data: updateData
    })

    return {
      success: true,
      message: isBestScore ? '成绩已更新！' : '参与成功！',
      isNewRecord: isBestScore,
      isNewParticipant: isNewParticipant,
      completed: completed, // 返回是否完成关卡的状态
      currentScore: score,
      bestScore: isBestScore ? score : (existingIndex >= 0 ? results[existingIndex].score : score)
    }
    
  } catch (error) {
    console.error('提交竞赛结果失败:', error)
    return {
      success: false,
      message: '提交结果失败: ' + error.message
    }
  }
} 