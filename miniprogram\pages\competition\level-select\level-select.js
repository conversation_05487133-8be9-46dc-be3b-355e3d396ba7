Page({
  data: {
    masterCompetitionId: '',
    competitionName: '',
    mode: '',
    modeName: '',
    levels: [], // 当前页显示的关卡
    allLevels: [], // 所有关卡数据
    loading: true,
    totalWords: 0,
    totalLevels: 0,
    averageScore: 0,
    totalParticipants: 0,

    // 分页相关
    currentPage: 1,
    pageSize: 10,
    totalPages: 0,
    hasMore: false,
    showPageInputModal: false,

    // 分享测试相关
    shareId: '',
    testType: '',
    shareData: null,
    userProgress: null
  },

  async onLoad(options) {
    const { masterCompetitionId, mode, competitionName, shareId, testType, refresh } = options;

    // 支持分享测试模式
    if (mode === 'share' && shareId && testType) {
      this.setData({
        shareId: shareId,
        mode: 'share',
        testType: testType,
        modeName: this.getTestModeText(testType),
        competitionName: '分享测试关卡选择',
        needRefresh: refresh === 'true' // 标记是否需要刷新数据
      });
      await this.loadShareLevels();
      return;
    }

    // 原有的竞赛模式
    if (!masterCompetitionId || !mode) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    const modeNames = {
      'elimination': '消消乐',
      'en2zh': '英译汉',
      'zh2en': '汉译英',
      'dictation': '听写'
    };

    this.setData({
      masterCompetitionId,
      mode,
      modeName: modeNames[mode] || '单词竞赛',
      competitionName: decodeURIComponent(competitionName || '多关卡竞赛')
    });

    await this.loadGroupedCompetitionDetail();
  },

  async onShow() {
    // 如果是分享测试模式且需要刷新，重新加载数据
    if (this.data.mode === 'share' && this.data.needRefresh) {
      console.log('检测到需要刷新分享测试关卡数据');
      this.setData({ needRefresh: false }); // 重置刷新标记
      await this.loadShareLevels();
    }
  },

  async loadGroupedCompetitionDetail() {
    this.setData({ loading: true });

    try {
      const result = await wx.cloud.callFunction({
        name: 'getCompetitions',
        data: {
          mode: 'getGroupedDetail',
          masterCompetitionId: this.data.masterCompetitionId
        }
      });

      if (result.result.success) {
        const data = result.result.data;

        // 获取用户进度和分数，确定哪些关卡已解锁
        const userProgress = data.userProgress || {};
        const userScores = data.userScores || {}; // 新增：用户分数数据
        const app = getApp();
        const userInfo = app.globalData.userInfo;
        let currentUserOpenId = null;
        
        // 尝试多种方式获取用户OpenID
        if (userInfo && userInfo.openid) {
          currentUserOpenId = userInfo.openid;
        } else if (app.globalData.openid) {
          currentUserOpenId = app.globalData.openid;
        } else {
          // 尝试从本地存储获取
          try {
            const storedUserInfo = wx.getStorageSync('userInfo');
            const storedOpenId = wx.getStorageSync('openid');
            if (storedUserInfo && storedUserInfo.openid) {
              currentUserOpenId = storedUserInfo.openid;
            } else if (storedOpenId) {
              currentUserOpenId = storedOpenId;
            }
          } catch (error) {
            console.error('获取存储的openid失败:', error);
          }
        }
        
        const userCompletedLevels = currentUserOpenId ? (userProgress[currentUserOpenId] || []) : [];
        const userLevelScores = currentUserOpenId ? (userScores[currentUserOpenId] || {}) : {}; // 新增：用户关卡分数

        console.log('=== 关卡解锁状态计算 ===');
        console.log('当前用户OpenID:', currentUserOpenId);
        console.log('用户信息:', userInfo);
        console.log('用户关卡分数:', userLevelScores);

        
        // 处理关卡锁定状态
        const processedLevels = data.levels.map((level, index) => {
          const isFirstLevel = index === 0;
          const previousLevelCompleted = index > 0 && userCompletedLevels.includes(data.levels[index - 1].id);
          const isCompleted = userCompletedLevels.includes(level.id);
          
          // 如果没有获取到用户OpenID，只有第一关解锁，其他关卡锁定
          let locked;
          if (!currentUserOpenId) {
            locked = !isFirstLevel; // 没有openid时，只有第一关解锁
          } else {
            locked = !isFirstLevel && !previousLevelCompleted && !isCompleted;
          }
          

          
          // 获取用户在该关卡的最高分
          const userScore = userLevelScores[level.id] || 0;

          // 计算完成率（对于已完成的关卡显示100%，未完成的显示0%）
          const completionRate = isCompleted ? 100 : 0;

          // 明确保留所有原始字段，特别是levelNumber
          const processedLevel = {
            id: level.id,
            levelNumber: level.levelNumber,
            name: level.name,
            wordCount: level.wordCount,
            // 保留其他原始字段
            ...level,
            // 设置状态字段
            locked: locked,
            isLocked: locked, // WXML中使用的字段名
            completed: isCompleted,
            isCompleted: isCompleted, // WXML中使用的字段名
            // 添加用户分数信息
            userScore: userScore, // 用户在该关卡的最高分
            score: userScore, // 兼容字段名
            completionRate: completionRate, // 添加完成率字段
            // 添加UI相关的字段
            completedText: isCompleted ? `已完成 - 最高分: ${userScore}分` : '已完成 - 再次挑战',
            challengeText: '开始挑战',
            // 添加cardClass字段
            cardClass: (locked ? 'locked' : '') + (isCompleted ? ' completed' : ''),
            clickable: !locked,
            // 添加prevLevelNumber字段用于锁定状态显示
            prevLevelNumber: level.levelNumber - 1
          };


          return processedLevel;
        });
        


        // 计算分页信息
        const totalPages = Math.ceil(processedLevels.length / this.data.pageSize);

        this.setData({
          allLevels: processedLevels,
          totalWords: data.totalWords,
          totalLevels: data.totalLevels,
          averageScore: data.averageScore,
          totalParticipants: data.totalParticipants,
          totalPages: totalPages,
          loading: false
        });

        // 加载第一页数据
        this.loadPage(1);
      } else {
        throw new Error(result.result.message || '加载失败');
      }
    } catch (error) {
      console.error('加载分组竞赛详情失败:', error);
      wx.showModal({
        title: '加载失败',
        content: '竞赛不存在或已过期',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 加载指定页的关卡数据
  loadPage(page) {
    const { allLevels, pageSize, totalPages } = this.data;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = allLevels.slice(startIndex, endIndex);

    // 生成页码数组
    let pageNumbers = [];
    if (totalPages <= 7) {
      // 总页数不超过7页，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // 总页数超过7页，显示当前页前后各2页，加上首页和末页
      let start = Math.max(2, page - 2);
      let end = Math.min(totalPages - 1, page + 2);

      // 始终显示第1页
      pageNumbers.push(1);

      // 如果start > 2，添加省略号标识
      if (start > 2) {
        pageNumbers.push('...');
      }

      // 添加中间页码
      for (let i = start; i <= end; i++) {
        if (i !== 1 && i !== totalPages) {
          pageNumbers.push(i);
        }
      }

      // 如果end < totalPages - 1，添加省略号标识
      if (end < totalPages - 1) {
        pageNumbers.push('...');
      }

      // 始终显示最后一页
      if (totalPages > 1) {
        pageNumbers.push(totalPages);
      }
    }

    console.log(`加载第${page}页关卡:`, {
      page,
      startIndex,
      endIndex,
      pageData: pageData.length,
      totalLevels: allLevels.length,
      pageNumbers,

    });

    // 预计算分页控件属性
    const processedPageNumbers = pageNumbers.map(item => {
      if (item === '...') {
        return {
          text: '...',
          page: null,
          pageClass: 'ellipsis',
          clickable: false
        };
      } else {
        return {
          text: item,
          page: item,
          pageClass: page === item ? 'active' : '',
          clickable: true
        };
      }
    });





    this.setData({
      levels: pageData,
      currentPage: page,
      hasMore: endIndex < allLevels.length,
      pageNumbers: processedPageNumbers,
      prevPageClass: page <= 1 ? 'disabled' : '',
      prevPageClickable: page > 1,
      nextPageClass: page >= totalPages ? 'disabled' : '',
      nextPageClickable: page < totalPages
    });
  },

  // 上一页
  onPrevPage() {
    if (this.data.currentPage > 1) {
      this.loadPage(this.data.currentPage - 1);
    }
  },

  // 下一页
  onNextPage() {
    if (this.data.currentPage < this.data.totalPages) {
      this.loadPage(this.data.currentPage + 1);
    }
  },

  // 跳转到指定页
  onGoToPage(e) {
    const page = parseInt(e.currentTarget.dataset.page);
    if (page >= 1 && page <= this.data.totalPages && page !== this.data.currentPage) {
      this.loadPage(page);
    }
  },

  // 显示页码输入框
  showPageInput() {
    this.setData({
      showPageInputModal: true
    });
  },

  // 页码输入确认
  onPageInputConfirm(e) {
    const { page } = e.detail;
    this.loadPage(page);
  },

  // 页码输入取消
  onPageInputCancel() {
    this.setData({
      showPageInputModal: false
    });
  },

  async onLevelTap(e) {
    // 分享测试模式
    if (this.data.mode === 'share') {
      const levelId = e.currentTarget.dataset.id;
      const levelNumber = e.currentTarget.dataset.level;
      const level = this.data.allLevels.find(l => l.id === levelId || l.levelNumber == levelNumber);

      if (!level) {
        wx.showToast({
          title: '关卡不存在',
          icon: 'error'
        });
        return;
      }

      // 检查关卡是否解锁
      if (level.locked || !level.isUnlocked) {
        let message = `请先完成第${level.levelNumber - 1}关`;
        if (level.levelNumber > 1 && this.data.testType !== 'elimination') {
          message += '，且正确率达到80%以上';
        }
        wx.showToast({
          title: message,
          icon: 'none',
          duration: 3000
        });
        return;
      }

      this.startShareTest(level.levelNumber);
      return;
    }

    // 原有的竞赛模式
    const levelId = e.currentTarget.dataset.id;
    const level = this.data.levels.find(l => l.id === levelId);

    if (!level) {
      wx.showToast({
        title: '关卡不存在',
        icon: 'error'
      });
      return;
    }

    // 检查关卡是否锁定
    if (level.locked) {
      wx.showToast({
        title: `请先完成第${level.levelNumber - 1}关`,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 检查用户登录状态
    const app = getApp();
    if (!app.isLoggedIn()) {
      wx.showModal({
        title: '需要登录',
        content: '参与竞赛需要登录，是否立即登录？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    wx.showLoading({
      title: '加载关卡...',
      mask: true
    });

    try {
      // 获取关卡详情
      const result = await wx.cloud.callFunction({
        name: 'getCompetitionDetail',
        data: {
          competitionId: levelId
        }
      });

      wx.hideLoading();

      if (result.result.success) {
        const levelData = result.result.data;
        
        // 根据不同模式跳转到不同页面
        if (this.data.mode === 'elimination') {
          // 消消乐模式
          const app = getApp();
          const gameWords = levelData.words.map(word => ({
            english: word.words || word.word || word.english,
            chinese: word.meaning || word.chinese || '无释义'
          }));
          
          app.globalData.eliminationGameData = {
            words: gameWords,
            currentGroup: 1,
            totalGroups: 1,
            libraryId: levelData.libraryId,
            libraryName: levelData.libraryName,
            competitionMode: true,
            competitionId: levelId,
            masterCompetitionId: this.data.masterCompetitionId,
            levelNumber: level.levelNumber,
            totalLevels: this.data.totalLevels
          };
          
          wx.navigateTo({
            url: `/pages/task/puzzle/puzzle?competitionId=${levelId}&mode=custom&gameMode=${levelData.gameMode || '60'}&masterCompetitionId=${this.data.masterCompetitionId}`
          });
        } else if (this.data.mode === 'dictation') {
          // 听写模式
          wx.navigateTo({
            url: `/pages/spelling/practice/practice?competitionId=${levelId}&mode=competition&practiceMode=test&masterCompetitionId=${this.data.masterCompetitionId}`
          });
        } else {
          // 英译汉/汉译英模式
          const app = getApp();
          app.globalData.learningData = {
            words: levelData.words,
            libraryId: levelData.libraryId,
            libraryName: levelData.libraryName,
            masterCompetitionId: this.data.masterCompetitionId,
            levelNumber: level.levelNumber,
            totalLevels: this.data.totalLevels
          };
          
          wx.navigateTo({
            url: `/pages/wordtest/test/test?competitionId=${levelId}&mode=${this.data.mode}&shareMode=competition&masterCompetitionId=${this.data.masterCompetitionId}`
          });
        }
      } else {
        throw new Error(result.result.message || '获取关卡详情失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载关卡失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  onViewOverallRanking() {
    // 查看整体排行榜（跨所有关卡）
    wx.navigateTo({
      url: `/pages/competition/ranking/ranking?competitionId=${this.data.masterCompetitionId}&mode=${this.data.mode}&type=master`
    });
  },

  onBackToCompetition() {
    wx.navigateBack();
  },

  // 刷新关卡状态（用于外部调用）
  refreshLevelStatus() {
    console.log('外部触发关卡状态刷新');
    this.loadGroupedCompetitionDetail();
  },

  // 页面显示时检查是否需要刷新
  onShow() {
    const app = getApp();

    // 检查是否需要刷新关卡状态
    if (app.globalData && app.globalData.needRefreshLevelSelect) {
      console.log('检测到需要刷新关卡状态');
      app.globalData.needRefreshLevelSelect = false;

      // 根据当前模式刷新数据
      if (this.data.mode === 'share') {
        // 分享模式，重新加载分享关卡数据
        this.loadShareLevels();
      } else {
        // 竞赛模式，重新加载竞赛关卡数据
        this.loadGroupedCompetitionDetail();
      }
    } else if (this.data.mode === 'share' && this.data.shareId) {
      // 分享模式下，每次显示都刷新数据以确保显示最新分数
      console.log('分享模式页面显示，刷新关卡数据');
      this.loadShareLevels();
    }
  },

  /**
   * 加载分享测试的关卡数据
   */
  async loadShareLevels() {
    try {
      this.setData({ loading: true });

      // 获取分享测试数据
      const result = await wx.cloud.callFunction({
        name: 'getShareTest',
        data: { shareId: this.data.shareId }
      });

      if (!result.result.success) {
        throw new Error(result.result.message || '获取分享测试失败');
      }

      const shareData = result.result.data;
      const currentUser = wx.getStorageSync('userInfo') || {};
      const userProgress = shareData.levelProgress ? shareData.levelProgress[currentUser.openid] : null;

      console.log('分享测试数据:', {
        wordsPerGroup: shareData.wordsPerGroup,
        totalLevels: shareData.totalLevels,
        wordsCount: shareData.wordsCount
      });

      // 生成关卡列表
      const levels = [];
      for (let i = 1; i <= shareData.totalLevels; i++) {
        // 检查关卡解锁状态
        let isUnlocked = i === 1; // 第一关默认解锁
        let prevLevelAccuracy = 0;

        if (i > 1 && userProgress && userProgress.completedLevels) {
          if (this.data.testType === 'elimination') {
            // 消消乐模式：只要前一关完成即可解锁下一关
            isUnlocked = userProgress.completedLevels.includes(i - 1);
          } else {
            // 其他模式：需要前一关达到80%正确率
            const prevLevelResults = shareData.results ? shareData.results.filter(r =>
              r.participantOpenid === currentUser.openid &&
              (r.levelId === (i-1) || r.level === (i-1) || r.levelNumber === (i-1) || r.groupIndex === (i-1))
            ) : [];

            if (prevLevelResults.length > 0) {
              const latestResult = prevLevelResults[prevLevelResults.length - 1];
              if (latestResult.accuracy !== undefined) {
                prevLevelAccuracy = latestResult.accuracy;
              } else if (latestResult.correctCount !== undefined && latestResult.totalCount !== undefined && latestResult.totalCount > 0) {
                prevLevelAccuracy = Math.round((latestResult.correctCount / latestResult.totalCount) * 100);
              }
            }

            // 只有前一关正确率达到80%才能解锁下一关
            isUnlocked = prevLevelAccuracy >= 80;
          }
        }

        const isCompleted = userProgress && userProgress.completedLevels && userProgress.completedLevels.includes(i);
        const score = userProgress && userProgress.scores ? userProgress.scores[i] : 0;

        // 计算当前关卡的正确率（使用最高分对应的结果）
        let accuracy = 0;
        const currentLevelResults = shareData.results ? shareData.results.filter(r =>
          r.participantOpenid === currentUser.openid &&
          (r.levelId === i || r.level === i || r.levelNumber === i || r.groupIndex === i)
        ) : [];

        if (currentLevelResults.length > 0) {
          // 找到最高分对应的结果
          let bestResult = currentLevelResults[0];
          for (const result of currentLevelResults) {
            if ((result.score || 0) > (bestResult.score || 0)) {
              bestResult = result;
            }
          }

          if (bestResult.accuracy !== undefined) {
            accuracy = bestResult.accuracy;
          } else if (bestResult.correctCount !== undefined && bestResult.totalCount !== undefined && bestResult.totalCount > 0) {
            accuracy = Math.round((bestResult.correctCount / bestResult.totalCount) * 100);
          }
        }

        const actualWordCount = shareData.wordsPerGroup || shareData.wordsPerLevel || shareData.wordCount || 10;

        // 计算完成率（对于已完成的关卡显示100%，未完成的显示0%）
        const completionRate = isCompleted ? 100 : 0;

        levels.push({
          id: `share_${this.data.shareId}_level_${i}`, // 确保唯一性
          levelNumber: parseInt(i), // 确保是数字类型
          levelName: `第${i}关`,
          name: `第${i}关`,
          keyId: parseInt(i), // 确保keyId是纯数字
          wordCount: actualWordCount,
          isUnlocked: isUnlocked,
          isCompleted: isCompleted,
          isLocked: !isUnlocked, // WXML中使用的字段名
          locked: !isUnlocked, // 使用原有的locked字段名
          completed: isCompleted, // 使用原有的completed字段名
          score: score,
          accuracy: accuracy,
          participantCount: 1,
          topScore: score,
          participants: 1,
          completionRate: completionRate, // 添加完成率字段
          // 预计算的UI属性
          cardClass: (!isUnlocked ? 'locked' : '') + (isCompleted ? ' completed' : ''),
          clickable: isUnlocked,
          prevLevelNumber: i - 1,
          completedText: this.data.mode === 'share' ? '已完成 - 刷分' : '已完成 - 再次挑战',
          challengeText: this.data.mode === 'share' ? '开始测试' : '开始挑战'
        });

        // 调试：输出第一个关卡的信息
        if (i === 1) {
          console.log('第1关卡信息:', {
            levelNumber: i,
            wordCount: actualWordCount,
            shareDataWordsPerGroup: shareData.wordsPerGroup
          });
        }
      }

      this.setData({
        shareData: shareData,
        userProgress: userProgress,
        allLevels: levels,
        totalLevels: shareData.totalLevels,
        loading: false
      });

      // 计算分页信息并加载第一页
      const totalPages = Math.ceil(levels.length / this.data.pageSize);
      this.setData({
        totalPages: totalPages,
        hasMore: totalPages > 1
      });

      // 加载第一页数据
      this.loadPage(1);

    } catch (error) {
      console.error('加载分享测试关卡失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 开始分享测试
   */
  startShareTest(levelNumber) {
    const testType = this.data.testType;
    const shareId = this.data.shareId;
    const shareData = this.data.shareData;

    // 获取原始分享测试的时间设置
    let timeParams = '';
    if (shareData && shareData.settings) {
      const settings = shareData.settings;
      if (settings.timeLimit !== undefined) {
        const timeLimit = settings.timeLimit === 0 ? 'unlimited' : 'limited';
        const perQuestionTime = settings.timeLimit || settings.perQuestionTime || 15;
        timeParams = `&timeLimit=${timeLimit}&perQuestionTime=${perQuestionTime}`;
      }
    }

    let url = '';
    switch (testType) {
      case 'en_to_cn':
      case 'cn_to_en':
        url = `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${testType}&levelId=${levelNumber}${timeParams}`;
        break;
      case 'dictation':
        url = `/pages/spelling/practice/practice?shareId=${shareId}&shareMode=share&levelId=${levelNumber}${timeParams}`;
        break;
      case 'phrase_en2zh':
        url = `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=en_to_cn&isPhrase=true&levelId=${levelNumber}${timeParams}`;
        break;
      case 'phrase_zh2en':
        url = `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=cn_to_en&isPhrase=true&levelId=${levelNumber}${timeParams}`;
        break;
      case 'elimination':
        // 获取消消乐游戏模式
        const gameMode = shareData?.settings?.eliminationGameMode || shareData?.eliminationGameMode || 'time';
        url = `/pages/task/puzzle/puzzle?shareId=${shareId}&isShared=true&mode=custom&gameMode=${gameMode}&levelId=${levelNumber}`;
        break;
      default:
        wx.showToast({
          title: `未知的测试类型: ${testType}`,
          icon: 'none'
        });
        return;
    }

    wx.navigateTo({
      url: url
    });
  },

  /**
   * 获取测试模式文本
   */
  getTestModeText(testType) {
    const modeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写',
      'elimination': '消消乐',
      'phrase_en2zh': '短语英译汉',
      'phrase_zh2en': '短语汉译英'
    };
    return modeMap[testType] || '未知模式';
  },

  onShareAppMessage() {
    if (this.data.mode === 'share') {
      return {
        title: `${this.data.modeName}分享测试 - 多关卡挑战`,
        path: `/pages/competition/level-select/level-select?shareId=${this.data.shareId}&mode=share&testType=${this.data.testType}`,
        imageUrl: '/assets/icons/logo.png'
      };
    }

    return {
      title: `${this.data.competitionName} - ${this.data.modeName}多关卡竞赛`,
      path: `/pages/competition/level-select/level-select?masterCompetitionId=${this.data.masterCompetitionId}&mode=${this.data.mode}&competitionName=${encodeURIComponent(this.data.competitionName)}`,
      imageUrl: '/assets/icons/logo.png'
    };
  }
});