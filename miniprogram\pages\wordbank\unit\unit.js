const { LIBRARY_INFO, TEXTBOOK_UNITS } = require('../../../config/constants.js');

Page({
  data: {
    series: '', // 出版社：renjiao 或 beishi
    returnTo: '',
    books: [], // 册列表
    seriesName: '', // 出版社名称
    showBookList: true, // 显示册列表
    showUnitList: false, // 显示单元列表
    selectedBook: null, // 选中的册
    units: [] // 单元列表
  },

  onLoad: function(options) {
    const { series, returnTo } = options;
    
    if (!series) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      series: series,
      returnTo: returnTo || ''
    });

    this.initBooksData();
  },

  // 初始化册数据
  initBooksData() {
    const books = [];
    const seriesName = this.data.series === 'renjiao' ? '人教版' : '北师版';
    
    // 从LIBRARY_INFO中获取该系列的所有册
    Object.keys(LIBRARY_INFO).forEach(libraryId => {
      const info = LIBRARY_INFO[libraryId];
      if (info.type === 'textbook' && info.series === this.data.series) {
        books.push({
          id: libraryId,
          name: info.name,
          book: info.book, // bixiu1, bixiu2等
          count: info.count
        });
      }
    });

    // 按册排序（必修1-3，选修1-4）
    books.sort((a, b) => {
      const order = ['bixiu1', 'bixiu2', 'bixiu3', 'xuanxiu1', 'xuanxiu2', 'xuanxiu3', 'xuanxiu4'];
      return order.indexOf(a.book) - order.indexOf(b.book);
    });

    this.setData({
      books: books,
      seriesName: seriesName
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: `${seriesName}同步词汇`
    });
  },

  // 点击册
  onBookTap(e) {
    const bookData = e.currentTarget.dataset.book;
    const units = TEXTBOOK_UNITS[this.data.series][bookData.book] || [];
    
    this.setData({
      selectedBook: bookData,
      units: units,
      showBookList: false,
      showUnitList: true
    });

    // 更新页面标题
    wx.setNavigationBarTitle({
      title: bookData.name
    });
  },

  // 点击单元
  onUnitTap(e) {
    const unit = e.currentTarget.dataset.unit;
    const { selectedBook } = this.data;
    
    if (!selectedBook || !unit) {
      return;
    }

    // 根据returnTo参数决定跳转逻辑
    if (this.data.returnTo === 'wordtest') {
      // 返回单词检测页面
      this.returnToWordtest(selectedBook.id, unit.id);
    } else {
      // 跳转到词汇列表页面
      wx.navigateTo({
        url: `/pages/wordbank/wordlist/wordlist?libraryId=${selectedBook.id}&libraryName=${selectedBook.name}&unit=${unit.id}&unitName=${unit.name}`
      });
    }
  },

  // 返回单词检测页面
  returnToWordtest(libraryId, unitId) {
    // 保存选择的词库和单元到全局数据
    const app = getApp();
    app.globalData.selectedLibrary = {
      id: libraryId,
      name: this.data.selectedBook.name,
      unit: unitId,
      unitName: this.data.units.find(u => u.id === unitId)?.name || ''
    };

    // 跳转回单词检测页面
    wx.navigateTo({
      url: '/pages/wordtest/wordtest',
      success: () => {
        const unitName = this.data.units.find(u => u.id === unitId)?.name || '';
        wx.showToast({
          title: `已选择${this.data.selectedBook.name} ${unitName}`,
          icon: 'success',
          duration: 2000
        });
      }
    });
  },

  // 返回册列表
  backToBookList() {
    this.setData({
      showBookList: true,
      showUnitList: false,
      selectedBook: null,
      units: []
    });

    // 恢复页面标题
    wx.setNavigationBarTitle({
      title: `${this.data.seriesName}同步词汇`
    });
  },

  // 全册学习
  onFullBookTap() {
    const { selectedBook } = this.data;
    
    if (!selectedBook) {
      return;
    }

    // 根据returnTo参数决定跳转逻辑
    if (this.data.returnTo === 'wordtest') {
      // 返回单词检测页面（全册，不指定单元）
      const app = getApp();
      app.globalData.selectedLibrary = {
        id: selectedBook.id,
        name: selectedBook.name,
        unit: '', // 空表示全册
        unitName: '全册'
      };

      wx.navigateTo({
        url: '/pages/wordtest/wordtest',
        success: () => {
          wx.showToast({
            title: `已选择${selectedBook.name} 全册`,
            icon: 'success',
            duration: 2000
          });
        }
      });
    } else {
      // 跳转到词汇列表页面（全册）
      wx.navigateTo({
        url: `/pages/wordbank/wordlist/wordlist?libraryId=${selectedBook.id}&libraryName=${selectedBook.name}`
      });
    }
  }
}); 