<view class="container">
  <!-- 统计信息栏 -->
  <view class="statistics-bar">
    <view class="stat-item">
      <text class="stat-number">{{statistics.total}}</text>
      <text class="stat-label">总计</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.en_to_cn}}</text>
      <text class="stat-label">英译汉</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.cn_to_en}}</text>
      <text class="stat-label">汉译英</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.dictation}}</text>
      <text class="stat-label">听写</text>
    </view>
  </view>

  <!-- 模式筛选栏 -->
  <view class="mode-filter-bar">
    <view class="mode-filter-item {{modeFilter === 'en_to_cn' ? 'active' : ''}}" bindtap="onModeFilterTap" data-mode="en_to_cn">
      英译汉
    </view>
    <view class="mode-filter-item {{modeFilter === 'cn_to_en' ? 'active' : ''}}" bindtap="onModeFilterTap" data-mode="cn_to_en">
      汉译英
    </view>
    <view class="mode-filter-item {{modeFilter === 'dictation' ? 'active' : ''}}" bindtap="onModeFilterTap" data-mode="dictation">
      听写
    </view>
  </view>

  <!-- 操作栏 -->
  <view class="action-bar">
    <view class="main-actions">
      <view class="review-btn" bindtap="onReviewTap">开始复习</view>
      <view class="action-btn export-btn" bindtap="onExportExcel">导出Excel</view>
    </view>
    <view class="manage-actions">
      <view class="action-btn manage-btn" bindtap="toggleEditMode">
        {{editMode ? '完成' : '管理'}}
      </view>
      <view class="action-btn sort-btn" bindtap="onSortTap">
        {{sortType === 'count' ? '按添加时间排序' : '按错误次数排序'}}
      </view>
    </view>
  </view>

  <!-- 编辑模式下的批量操作 -->
  <view class="batch-actions" wx:if="{{editMode}}">
    <view class="batch-info">
      <text>已选择 {{selectedWords.length}} 个单词</text>
    </view>
    <view class="batch-buttons">
      <button class="batch-btn" bindtap="onSelectAllTap">
        {{selectedWords.length === words.length ? '取消全选' : '全选'}}
      </button>
      <button class="batch-btn secondary" bindtap="showAddWordDialog">
        添加单词
      </button>
      <button class="batch-btn primary" bindtap="onReviewSelectedTap" wx:if="{{selectedWords.length > 0}}">
        复习选中
      </button>
      <button class="batch-btn danger" bindtap="onBatchDelete" wx:if="{{selectedWords.length > 0}}">
        删除选中
      </button>
    </view>
  </view>



  <!-- 错词列表 -->
  <view class="word-list">
    <view 
      class="word-item {{editMode ? 'edit-mode' : ''}} {{item.selected ? 'selected' : ''}}" 
      wx:for="{{words}}" 
      wx:key="_id"
      bindtap="{{editMode ? 'onWordSelect' : ''}}"
      data-index="{{index}}"
    >
      <!-- 选择框 -->
      <view class="word-checkbox" wx:if="{{editMode}}">
        <text class="checkbox-icon">{{item.selected ? '☑️' : '☐'}}</text>
      </view>
      
      <!-- 单词信息 -->
      <view class="word-info">
        <view class="word-header">
          <view class="word-text">{{item.word}}</view>
          <view class="error-count">错误{{item.errorCount}}次</view>
        </view>
        <view class="word-phonetic" wx:if="{{item.phonetic}}">[{{item.phonetic}}]</view>
        <view class="word-meaning">{{item.meaning}}</view>
      </view>

      <!-- 单个操作按钮 -->
      <view class="word-actions" wx:if="{{!editMode}}">
        <view class="action-btn small" bindtap="onMasteredTap" data-id="{{item._id}}">
          标记已掌握
        </view>
      </view>
    </view>
  </view>

  <!-- 分页组件 -->
  <view class="pagination" wx:if="{{totalPages > 1}}">
    <view class="page-info">
      <text>第 {{currentPage}} / {{totalPages}} 页</text>
    </view>
    <view class="page-controls">
      <button class="page-btn" bindtap="onPrevPage" disabled="{{currentPage <= 1}}">上一页</button>
      <view class="page-jump">
        <text bindtap="onPageInputTap">跳转到</text>
        <input
          wx:if="{{showPageInput}}"
          class="page-input"
          type="number"
          value="{{pageInputValue}}"
          bindinput="onPageInputChange"
          bindconfirm="onPageInputConfirm"
          bindblur="onPageInputCancel"
          focus="{{showPageInput}}"
        />
        <text wx:else bindtap="onPageInputTap" class="page-current">{{currentPage}}</text>
        <text>页</text>
      </view>
      <button class="page-btn" bindtap="onNextPage" disabled="{{currentPage >= totalPages}}">下一页</button>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{words.length === 0}}">
    <view class="empty-icon">📚</view>
    <text class="empty-text">暂无错词记录</text>
    <text class="empty-desc">错词会在测试模式下自动收集</text>
    <button class="empty-action" bindtap="showAddWordDialog">手动添加单词</button>
  </view>

  <!-- 添加单词对话框 -->
  <view class="modal-overlay" wx:if="{{showAddDialog}}" bindtap="hideAddWordDialog">
    <view class="add-word-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">添加单词到错词本</text>
        <text class="modal-close" bindtap="hideAddWordDialog">×</text>
      </view>
      
      <view class="modal-body">
        <view class="form-group">
          <label class="form-label">单词 *</label>
          <input 
            class="form-input" 
            placeholder="请输入英文单词"
            value="{{newWordForm.word}}"
            data-field="word"
            bindinput="onFormInput"
          />
        </view>
        
        <view class="form-group">
          <label class="form-label">中文含义 *</label>
          <input 
            class="form-input" 
            placeholder="请输入中文含义"
            value="{{newWordForm.meaning}}"
            data-field="meaning"
            bindinput="onFormInput"
          />
        </view>
        
        <view class="form-group">
          <label class="form-label">音标</label>
          <input 
            class="form-input" 
            placeholder="请输入音标（可选）"
            value="{{newWordForm.phonetic}}"
            data-field="phonetic"
            bindinput="onFormInput"
          />
        </view>
        
        <view class="form-group">
          <label class="form-label">例句</label>
          <textarea 
            class="form-textarea" 
            placeholder="请输入例句（可选）"
            value="{{newWordForm.example}}"
            data-field="example"
            bindinput="onFormInput"
          ></textarea>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="hideAddWordDialog">取消</button>
        <button class="modal-btn confirm" bindtap="onAddWord">添加</button>
      </view>
    </view>
  </view>
</view> 