.container {
  min-height: 100vh;
  padding: 40rpx;
  background-color: #fff;
}

.header {
  margin-bottom: 40rpx;
  text-align: center;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.section-text {
  display: block;
  text-align: justify;
} 