const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const mistakesCollection = db.collection('mistakes')

exports.main = async (event, context) => {
  const { mistakeId, status, notes } = event

  try {
    const now = new Date()

    // 更新错题状态
    await mistakesCollection.doc(mistakeId).update({
      data: {
        status,
        lastReviewTime: now,
        reviewTimes: db.command.inc(1),
        notes: notes || ''
      }
    })

    return {
      code: 200,
      message: '更新成功'
    }
  } catch (error) {
    console.error(error)
    return {
      code: 500,
      message: '服务器错误'
    }
  }
} 