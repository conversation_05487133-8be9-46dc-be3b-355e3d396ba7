const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  
  try {
    const { type = 'created' } = event; // 'created' 或 'participated'
    const currentUser = wxContext.OPENID;
    
    if (type === 'created') {
      // 获取我创建的分享测试
      const result = await db.collection('shareTests')
        .where({
          createdBy: currentUser
        })
        .orderBy('createTime', 'desc')
        .get();
      
      const shareTests = result.data.map(test => {
        const now = new Date();
        const expireTime = new Date(test.expireTime);
        const isExpired = now > expireTime;
        
        // 确保访问者和结果数据存在
        const visitors = test.visitors || [];
        const results = test.results || [];

        // 计算唯一参与者数量（去重）
        const uniqueParticipants = new Set();
        visitors.forEach(visitor => {
          if (visitor.openid) {
            uniqueParticipants.add(visitor.openid);
          }
        });
        results.forEach(result => {
          if (result.participantOpenid) {
            uniqueParticipants.add(result.participantOpenid);
          }
        });

        return {
          shareId: test.shareId,
          testType: test.testType,
          libraryId: test.libraryId,
          libraryName: test.libraryName,
          createdBy: test.createdBy,
          createTime: test.createTime,
          expireTime: test.expireTime,
          expireDays: test.expireDays,
          isMultiLevel: test.isMultiLevel,
          totalLevels: test.totalLevels,
          wordsPerGroup: test.wordsPerGroup,
          wordsCount: test.wordsCount,
          // 不返回完整的words数组，只返回数量
          wordCount: test.words ? test.words.length : (test.wordsCount || 0),
          // 访问者和结果信息
          visitors: visitors,
          results: results,
          levelProgress: test.levelProgress || {},
          isExpired: isExpired,
          participantCount: uniqueParticipants.size, // 使用唯一参与者数量
          testResultCount: results.length,
          // 计算统计信息
          stats: {
            totalTests: results.length,
            averageScore: results.length > 0
              ? Math.round(results.reduce((sum, r) => sum + r.score, 0) / results.length)
              : 0,
            highestScore: results.length > 0
              ? Math.max(...results.map(r => r.score))
              : 0,
            recentActivity: results.length > 0
              ? results[results.length - 1].submitTime
              : null
          }
        };
      });
      
      return {
        success: true,
        data: shareTests
      };
      
    } else if (type === 'participated') {
      // 获取我参与的分享测试
      const result = await db.collection('shareTests')
        .where({
          'visitors.openid': currentUser
        })
        .orderBy('createTime', 'desc')
        .get();
      
      // 获取所有分享人的openid，用于批量查询用户信息
      const creatorOpenids = [...new Set(result.data.map(test => test.createdBy))];

      // 批量查询分享人信息
      const creatorsInfo = {};
      if (creatorOpenids.length > 0) {
        try {
          const creatorsResult = await db.collection('users')
            .where({
              openid: db.command.in(creatorOpenids)
            })
            .get();

          creatorsResult.data.forEach(user => {
            let displayName = '微信用户';
            if (user.wechatInfo && user.wechatInfo.nickName && user.wechatInfo.nickName.trim()) {
              displayName = user.wechatInfo.nickName;
            } else if (user.username && user.username.trim()) {
              displayName = user.username;
            }

            let avatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0';
            if (user.wechatInfo && user.wechatInfo.avatarUrl && user.wechatInfo.avatarUrl.trim()) {
              avatarUrl = user.wechatInfo.avatarUrl;
            }

            creatorsInfo[user.openid] = {
              openid: user.openid,
              nickName: displayName,
              avatarUrl: avatarUrl
            };
          });

        } catch (error) {
          console.log('查询分享人信息失败:', error);
        }
      }

      const participatedTests = result.data.map(test => {
        const now = new Date();
        const expireTime = new Date(test.expireTime);
        const isExpired = now > expireTime;

        // 找到当前用户的访问记录
        const myVisitorInfo = test.visitors.find(v => v.openid === currentUser);
        const myResults = test.results ? test.results.filter(r => r.participantOpenid === currentUser) : [];
        const myProgress = test.levelProgress ? test.levelProgress[currentUser] : null;

        // 获取分享人信息，优先使用数据库中的creatorInfo，其次使用动态查询的结果
        let creatorInfo = test.creatorInfo;
        if (!creatorInfo && creatorsInfo[test.createdBy]) {
          creatorInfo = creatorsInfo[test.createdBy];
        }
        if (!creatorInfo) {
          creatorInfo = {
            openid: test.createdBy,
            nickName: '微信用户',
            avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
          };
        }

        return {
          shareId: test.shareId,
          testType: test.testType,
          libraryName: test.libraryName,
          createdBy: test.createdBy,
          creatorInfo: creatorInfo,
          createTime: test.createTime,
          expireTime: test.expireTime,
          isExpired: isExpired,
          isMultiLevel: test.isMultiLevel,
          totalLevels: test.totalLevels,
          // 我的参与信息
          myInfo: {
            firstVisitTime: myVisitorInfo ? myVisitorInfo.firstVisitTime : null,
            lastTestTime: myVisitorInfo ? myVisitorInfo.lastTestTime : null,
            testCount: myVisitorInfo ? myVisitorInfo.testCount : 0,
            bestScore: myVisitorInfo ? myVisitorInfo.bestScore : 0,
            latestScore: myVisitorInfo ? myVisitorInfo.latestScore : 0,
            latestAccuracy: myVisitorInfo ? myVisitorInfo.latestAccuracy : 0,
            results: myResults,
            progress: myProgress
          }
        };
      });

      return {
        success: true,
        data: participatedTests
      };
    }
    
    return {
      success: false,
      message: '无效的查询类型'
    };
    
  } catch (error) {
    console.error('获取分享测试列表失败:', error);
    return {
      success: false,
      message: '获取分享测试列表失败',
      error: error
    };
  }
}; 