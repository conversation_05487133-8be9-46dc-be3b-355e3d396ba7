const app = getApp()

Page({
  data: {
    // 学习计划设置
    dailyWords: 20,
    dailyWordsArray: [10, 20, 30, 50, 100],
    
    // 学习提醒设置
    enableReminder: false,
    reminderTime: '20:00',
    reminderTimeArray: [
      '08:00', '09:00', '10:00', '11:00', '12:00',
      '13:00', '14:00', '15:00', '16:00', '17:00',
      '18:00', '19:00', '20:00', '21:00', '22:00'
    ],
    
    // 学习设置
    autoPlayAudio: true,
    showPhonetic: true,
    showExample: true,
    
    // 用户信息
    userInfo: null
  },

  onLoad() {
    this.loadUserInfo()
    this.loadSettings()
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const userInfo = await app.getUserInfo()
      this.setData({ userInfo })
    } catch (error) {
      console.error('加载用户信息失败：', error)
      wx.showToast({
        title: '加载用户信息失败',
        icon: 'none'
      })
    }
  },

  // 加载设置
  async loadSettings() {
    try {
      const db = wx.cloud.database()
      const settings = await db.collection('settings').doc(this.data.userInfo._id).get()
      
      if (settings.data) {
        this.setData({
          dailyWords: settings.data.dailyWords || 20,
          enableReminder: settings.data.enableReminder || false,
          reminderTime: settings.data.reminderTime || '20:00',
          autoPlayAudio: settings.data.autoPlayAudio !== false,
          showPhonetic: settings.data.showPhonetic !== false,
          showExample: settings.data.showExample !== false
        })
      }
    } catch (error) {
      console.error('加载设置失败：', error)
      wx.showToast({
        title: '加载设置失败',
        icon: 'none'
      })
    }
  },

  // 保存设置
  async saveSettings() {
    try {
      const db = wx.cloud.database()
      await db.collection('settings').doc(this.data.userInfo._id).set({
        data: {
          dailyWords: this.data.dailyWords,
          enableReminder: this.data.enableReminder,
          reminderTime: this.data.reminderTime,
          autoPlayAudio: this.data.autoPlayAudio,
          showPhonetic: this.data.showPhonetic,
          showExample: this.data.showExample,
          updateTime: db.serverDate()
        }
      })

      // 更新提醒
      if (this.data.enableReminder) {
        this.setReminder()
      } else {
        this.cancelReminder()
      }

      wx.showToast({
        title: '设置已保存',
        icon: 'success'
      })
    } catch (error) {
      console.error('保存设置失败：', error)
      wx.showToast({
        title: '保存设置失败',
        icon: 'none'
      })
    }
  },

  // 设置提醒
  async setReminder() {
    try {
      const [hour, minute] = this.data.reminderTime.split(':')
      await wx.requestSubscribeMessage({
        tmplIds: ['your-template-id'], // 替换为你的模板ID
        success: (res) => {
          if (res['your-template-id'] === 'accept') {
            // 用户同意订阅
            console.log('用户同意订阅提醒')
          }
        }
      })
    } catch (error) {
      console.error('设置提醒失败：', error)
    }
  },

  // 取消提醒
  async cancelReminder() {
    try {
      // 实现取消提醒的逻辑
      console.log('取消提醒')
    } catch (error) {
      console.error('取消提醒失败：', error)
    }
  },

  // 清除学习记录
  async clearRecords() {
    try {
      const db = wx.cloud.database()
      await db.collection('learning_records').where({
        _openid: this.data.userInfo._id
      }).remove()

      wx.showToast({
        title: '记录已清除',
        icon: 'success'
      })
    } catch (error) {
      console.error('清除记录失败：', error)
      wx.showToast({
        title: '清除记录失败',
        icon: 'none'
      })
    }
  },

  // 事件处理函数
  onDailyWordsChange(e) {
    this.setData({
      dailyWords: this.data.dailyWordsArray[e.detail.value]
    })
  },

  onReminderTimeChange(e) {
    this.setData({
      reminderTime: this.data.reminderTimeArray[e.detail.value]
    })
  },

  onEnableReminderChange(e) {
    this.setData({
      enableReminder: e.detail.value
    })
  },

  onAutoPlayAudioChange(e) {
    this.setData({
      autoPlayAudio: e.detail.value
    })
  },

  onShowPhoneticChange(e) {
    this.setData({
      showPhonetic: e.detail.value
    })
  },

  onShowExampleChange(e) {
    this.setData({
      showExample: e.detail.value
    })
  },

  onClearRecords() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有学习记录吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.clearRecords()
        }
      }
    })
  },

  onAbout() {
    wx.showModal({
      title: '关于我们',
      content: '墨词自习室是一款帮助用户学习英语单词的小程序。\n\n版本：1.0.0',
      showCancel: false
    })
  }
}) 