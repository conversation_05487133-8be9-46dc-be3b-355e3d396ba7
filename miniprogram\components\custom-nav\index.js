Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    title: {
      type: String,
      value: ''
    },
    showBack: {
      type: Boolean,
      value: true
    },
    background: {
      type: String,
      value: '#ffffff'
    },
    color: {
      type: String,
      value: '#000000'
    }
  },

  data: {
    statusBarHeight: 0,
    navBarHeight: 44,
    menuButtonInfo: null
  },

  lifetimes: {
    attached() {
      try {
        const windowInfo = wx.getWindowInfo();
        const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
        
        this.setData({
          statusBarHeight: windowInfo.statusBarHeight || 0,
          menuButtonInfo: menuButtonInfo,
          navBarHeight: (menuButtonInfo.top - windowInfo.statusBarHeight) * 2 + menuButtonInfo.height
        });
      } catch (error) {
        console.warn('获取导航栏信息失败:', error);
        // 降级处理
        this.setData({
          statusBarHeight: 44,
          menuButtonInfo: {},
          navBarHeight: 44
        });
      }
    }
  },

  methods: {
    onBack() {
      if (this.data.showBack) {
        wx.navigateBack({
          delta: 1
        })
      }
    }
  }
}) 